{"name": "dieco-management", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vue-tsc && vite", "build:staging": "vue-tsc && vite build --mode staging && node ./scripts/dns-prefetch.js", "build:prod": "vue-tsc && vite build --mode production && node ./scripts/dns-prefetch.js", "build": "vue-tsc && vite build", "build:skip-check": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix", "format": "prettier --write \"src/**/*.{vue,js,jsx,cjs,mjs,html,css,scss,json}\"", "check-rules": "node ./scripts/check-rules.js", "pre-commit": "npm run lint && npm run format && npm run check-rules", "type-check": "vue-tsc --noEmit"}, "overrides": {"rollup": "4.29.2"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "axios": "1.8.4", "crypto-js": "4.2.0", "dayjs": "1.11.13", "element-plus": "2.9.10", "js-cookie": "3.0.5", "lodash-es": "4.17.21", "lottie-web": "5.13.0", "mitt": "3.0.1", "moment": "2.30.1", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "8.2.0", "pinia": "3.0.2", "qrcode.vue": "3.6.0", "screenfull": "6.0.2", "vue": "3.5.13", "vue-i18n": "11.1.5", "vue-router": "4.5.0", "vxe-table": "4.13.32"}, "devDependencies": {"@antfu/eslint-config": "4.12.0", "@types/crypto-js": "4.2.2", "@types/js-cookie": "3.0.6", "@types/lodash-es": "4.17.12", "@types/node": "22.14.1", "@types/nprogress": "0.2.3", "@types/path-browserify": "1.0.3", "@vitejs/plugin-vue": "5.2.3", "@vitejs/plugin-vue-jsx": "4.1.2", "@vue/test-utils": "2.4.6", "eslint": "9.28.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-format": "1.0.1", "eslint-plugin-prettier": "5.4.1", "eslint-plugin-vue": "10.1.0", "glob": "10.4.5", "happy-dom": "17.4.4", "husky": "9.1.7", "lint-staged": "15.5.1", "node-html-parser": "7.0.1", "prettier": "3.5.3", "sass": "1.78.0", "typescript": "5.8.3", "unocss": "66.1.0-beta.12", "unplugin-auto-import": "19.1.2", "unplugin-svg-component": "0.12.1", "unplugin-vue-components": "28.5.0", "url-regex": "5.0.0", "vite": "6.3.5", "vite-svg-loader": "5.1.0", "vitest": "3.1.1", "vue-eslint-parser": "10.1.3", "vue-tsc": "2.2.8"}}