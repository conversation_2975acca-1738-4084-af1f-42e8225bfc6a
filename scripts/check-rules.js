#!/usr/bin/env node

/**
 * @name 项目规则检查脚本
 * @description 自动检查项目代码是否符合开发规范
 * @version 1.0.0
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

class ProjectRuleChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.srcPath = path.join(projectRoot, 'src');
    this.passed = 0;
    this.failed = 0;
  }

  // 检查目录结构规范
  checkDirectoryStructure() {
    console.log('🏗️  检查目录结构规范...');

    const requiredDirs = [
      'src/common/apis',
      'src/common/components',
      'src/common/utils',
      'src/common/i18n',
      'src/common/constants',
      'src/common/composables',
      'src/pages',
      'src/pinia/stores',
      'src/router',
      'src/layouts',
      'src/plugins',
      'src/http',
      'types',
    ];

    requiredDirs.forEach((dir) => {
      if (fs.existsSync(dir)) {
        this.passed++;
      } else {
        this.errors.push(`❌ 缺少必需目录: ${dir}`);
        this.failed++;
      }
    });
  }

  // 检查页面模块结构
  checkPageModuleStructure() {
    console.log('📁 检查页面模块结构...');

    const pagesDir = path.join(this.srcPath, 'pages');
    if (!fs.existsSync(pagesDir)) return;

    const modules = fs
      .readdirSync(pagesDir, { withFileTypes: true })
      .filter((dirent) => dirent.isDirectory())
      .map((dirent) => dirent.name);

    modules.forEach((module) => {
      const modulePath = path.join(pagesDir, module);
      const requiredSubDirs = ['apis', 'components', 'i18n'];

      requiredSubDirs.forEach((subDir) => {
        const subDirPath = path.join(modulePath, subDir);
        if (fs.existsSync(subDirPath)) {
          this.passed++;
        } else {
          this.warnings.push(`⚠️  页面模块 ${module} 缺少 ${subDir} 目录`);
        }
      });

      // 检查是否有index.vue
      const indexPath = path.join(modulePath, 'index.vue');
      if (fs.existsSync(indexPath)) {
        this.passed++;
      } else {
        this.errors.push(`❌ 页面模块 ${module} 缺少 index.vue`);
        this.failed++;
      }
    });
  }

  // 检查组件命名规范
  checkComponentNaming() {
    console.log('🏷️  检查组件命名规范...');

    this.checkComponentsInDirectory(path.join(this.srcPath, 'common/components'));

    // 检查页面模块中的组件
    const pagesDir = path.join(this.srcPath, 'pages');
    if (fs.existsSync(pagesDir)) {
      const modules = fs
        .readdirSync(pagesDir, { withFileTypes: true })
        .filter((dirent) => dirent.isDirectory())
        .map((dirent) => dirent.name);

      modules.forEach((module) => {
        const componentsPath = path.join(pagesDir, module, 'components');
        if (fs.existsSync(componentsPath)) {
          this.checkComponentsInDirectory(componentsPath);
        }
      });
    }
  }

  checkComponentsInDirectory(componentsDir) {
    if (!fs.existsSync(componentsDir)) return;

    const components = fs
      .readdirSync(componentsDir, { withFileTypes: true })
      .filter((dirent) => dirent.isDirectory())
      .map((dirent) => dirent.name);

    components.forEach((component) => {
      // 检查目录命名 - 应该是PascalCase
      if (!this.isPascalCase(component)) {
        this.errors.push(`❌ 组件目录命名不符合PascalCase规范: ${component}`);
        this.failed++;
      } else {
        this.passed++;
      }

      // 检查是否有index.vue
      const indexPath = path.join(componentsDir, component, 'index.vue');
      if (fs.existsSync(indexPath)) {
        this.passed++;
      } else {
        this.errors.push(`❌ 组件 ${component} 缺少 index.vue`);
        this.failed++;
      }
    });
  }

  // 检查路径别名使用
  checkPathAliases() {
    console.log('🔗 检查路径别名使用...');

    this.checkFilesForBadImports(this.srcPath);
  }

  checkFilesForBadImports(dir) {
    const files = fs.readdirSync(dir, { withFileTypes: true });

    files.forEach((file) => {
      const filePath = path.join(dir, file.name);

      if (file.isDirectory()) {
        this.checkFilesForBadImports(filePath);
      } else if (
        file.name.endsWith('.vue') ||
        file.name.endsWith('.ts') ||
        file.name.endsWith('.js')
      ) {
        const content = fs.readFileSync(filePath, 'utf8');

        // 检查是否使用了相对路径跨目录引用
        const badImportRegex = /import.*from\s+['"]\.\.\/\.\.\//g;
        const matches = content.match(badImportRegex);

        if (matches) {
          this.errors.push(`❌ 文件 ${filePath} 使用了相对路径跨目录引用: ${matches.join(', ')}`);
          this.failed++;
        } else {
          this.passed++;
        }
      }
    });
  }

  // 检查TypeScript配置
  checkTypeScriptConfig() {
    console.log('🔧 检查TypeScript配置...');

    const tsconfigPath = path.join(projectRoot, 'tsconfig.json');
    if (!fs.existsSync(tsconfigPath)) {
      this.errors.push('❌ 缺少 tsconfig.json 配置文件');
      this.failed++;
      return;
    }

    try {
      const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));

      // 检查严格模式
      if (tsconfig.compilerOptions?.strict === true) {
        this.passed++;
      } else {
        this.errors.push('❌ TypeScript未启用严格模式 (strict: true)');
        this.failed++;
      }

      // 检查路径别名配置
      const paths = tsconfig.compilerOptions?.paths;
      if (paths && paths['@/*'] && paths['@@/*']) {
        this.passed++;
      } else {
        this.errors.push('❌ 缺少路径别名配置 (@/* 和 @@/*)');
        this.failed++;
      }
    } catch (error) {
      this.errors.push(`❌ tsconfig.json 解析失败: ${error.message}`);
      this.failed++;
    }
  }

  // 检查Vue组件规范
  checkVueComponentStandards() {
    console.log('⚡ 检查Vue组件规范...');

    this.checkVueFilesInDirectory(this.srcPath);
  }

  checkVueFilesInDirectory(dir) {
    const files = fs.readdirSync(dir, { withFileTypes: true });

    files.forEach((file) => {
      const filePath = path.join(dir, file.name);

      if (file.isDirectory()) {
        this.checkVueFilesInDirectory(filePath);
      } else if (file.name.endsWith('.vue')) {
        const content = fs.readFileSync(filePath, 'utf8');

        // 检查是否使用<script setup>
        if (content.includes('<script setup')) {
          this.passed++;
        } else if (content.includes('<script>')) {
          this.warnings.push(`⚠️  文件 ${filePath} 未使用 <script setup> 语法`);
        }

        // 检查是否使用TypeScript
        if (content.includes('lang="ts"')) {
          this.passed++;
        } else {
          this.warnings.push(`⚠️  文件 ${filePath} 未使用 TypeScript`);
        }

        // 检查样式作用域
        if (content.includes('<style scoped>') || content.includes('<style scoped lang=')) {
          this.passed++;
        } else if (content.includes('<style>')) {
          this.warnings.push(`⚠️  文件 ${filePath} 样式未使用 scoped`);
        }
      }
    });
  }

  // 检查安全性配置
  checkSecurityConfig() {
    console.log('🔒 检查安全性配置...');

    // 检查加密工具是否存在
    const cryptoPath = path.join(this.srcPath, 'common/utils/crypto.ts');
    if (fs.existsSync(cryptoPath)) {
      const content = fs.readFileSync(cryptoPath, 'utf8');
      if (content.includes('encryptPasswordMD5')) {
        this.passed++;
      } else {
        this.errors.push('❌ 缺少密码加密函数 encryptPasswordMD5');
        this.failed++;
      }
    } else {
      this.errors.push('❌ 缺少加密工具文件 crypto.ts');
      this.failed++;
    }

    // 检查路由守卫
    const guardPath = path.join(this.srcPath, 'router/guard.ts');
    if (fs.existsSync(guardPath)) {
      this.passed++;
    } else {
      this.errors.push('❌ 缺少路由守卫文件 guard.ts');
      this.failed++;
    }
  }

  // 检查性能优化配置
  checkPerformanceConfig() {
    console.log('🚀 检查性能优化配置...');

    const viteConfigPath = path.join(projectRoot, 'vite.config.js');
    if (!fs.existsSync(viteConfigPath)) {
      this.errors.push('❌ 缺少 vite.config.js 配置文件');
      this.failed++;
      return;
    }

    try {
      const viteConfig = fs.readFileSync(viteConfigPath, 'utf8');

      // 检查代码分割配置
      if (viteConfig.includes('manualChunks')) {
        this.passed++;
      } else {
        this.warnings.push('⚠️  未配置代码分割 (manualChunks)');
      }

      // 检查自动导入配置
      if (viteConfig.includes('unplugin-auto-import')) {
        this.passed++;
      } else {
        this.warnings.push('⚠️  未配置自动导入插件');
      }
    } catch (error) {
      this.errors.push(`❌ vite.config.js 读取失败: ${error.message}`);
      this.failed++;
    }
  }

  // 检查代码质量工具
  checkCodeQualityTools() {
    console.log('🛠️  检查代码质量工具...');

    const packageJsonPath = path.join(projectRoot, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      this.errors.push('❌ 缺少 package.json');
      this.failed++;
      return;
    }

    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

      // 检查ESLint配置
      if (packageJson.devDependencies?.['@antfu/eslint-config']) {
        this.passed++;
      } else {
        this.errors.push('❌ 缺少 ESLint 配置 (@antfu/eslint-config)');
        this.failed++;
      }

      // 检查Prettier
      if (packageJson.devDependencies?.['prettier']) {
        this.passed++;
      } else {
        this.warnings.push('⚠️  缺少 Prettier');
      }

      // 检查必要脚本
      const requiredScripts = ['dev', 'build', 'lint', 'format'];
      requiredScripts.forEach((script) => {
        if (packageJson.scripts?.[script]) {
          this.passed++;
        } else {
          this.errors.push(`❌ 缺少必要脚本: ${script}`);
          this.failed++;
        }
      });
    } catch (error) {
      this.errors.push(`❌ package.json 解析失败: ${error.message}`);
      this.failed++;
    }
  }

  // 运行TypeScript类型检查
  runTypeCheck() {
    console.log('📝 运行TypeScript类型检查...');

    try {
      execSync('vue-tsc --noEmit', { stdio: 'pipe' });
      this.passed++;
      console.log('✅ TypeScript类型检查通过');
    } catch (error) {
      this.errors.push('❌ TypeScript类型检查失败');
      this.failed++;
      if (error.stdout) {
        console.log('错误详情:', error.stdout.toString());
      }
    }
  }

  // 工具函数
  isPascalCase(str) {
    return /^[A-Z][a-zA-Z0-9]*$/.test(str);
  }

  // 生成报告
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 项目规则检查报告');
    console.log('='.repeat(60));

    console.log(`✅ 通过检查: ${this.passed} 项`);
    console.log(`❌ 失败检查: ${this.failed} 项`);
    console.log(`⚠️  警告信息: ${this.warnings.length} 项`);

    if (this.errors.length > 0) {
      console.log('\n🚨 错误详情:');
      this.errors.forEach((error) => console.log(error));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  警告详情:');
      this.warnings.forEach((warning) => console.log(warning));
    }

    const total = this.passed + this.failed;
    const score = total > 0 ? Math.round((this.passed / total) * 100) : 0;

    console.log(`\n📈 总体评分: ${score}%`);

    if (score >= 90) {
      console.log('🎉 优秀！项目规范性很好');
    } else if (score >= 70) {
      console.log('👍 良好，但还有改进空间');
    } else {
      console.log('⚡ 需要重点关注规范性问题');
    }

    console.log('='.repeat(60));

    // 返回是否通过检查（无错误）
    return this.failed === 0;
  }

  // 主检查流程
  async run() {
    console.log('🔍 开始项目规则检查...\n');

    this.checkDirectoryStructure();
    this.checkPageModuleStructure();
    this.checkComponentNaming();
    this.checkPathAliases();
    this.checkTypeScriptConfig();
    this.checkVueComponentStandards();
    this.checkSecurityConfig();
    this.checkPerformanceConfig();
    this.checkCodeQualityTools();
    this.runTypeCheck();

    const passed = this.generateReport();

    process.exit(passed ? 0 : 1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new ProjectRuleChecker();
  checker.run().catch(console.error);
}

export default ProjectRuleChecker;
