// dns-prefetch.js
import fs from 'fs'
import path from 'path'
import { parse } from 'node-html-parser'
import { glob } from 'glob'
import urlRegex from 'url-regex'

// 获取外部链接的正则表达式
const urlPattern = /(https?:\/\/[^/]*)/i
const urls = new Set()
const excludeUrls = new Set([
    'http://www.w3.org',
    'https://vuejs.org',
    'http://localhost',
    'https://vxeui.com',
    'http://momentjs.com',
    'https://element-plus.org',
    'https://github.com',])

// 遍历dist目录中的所有HTML、JS、CSS文件
async function searchDomin() {
    
    const files = await glob('dist/**/*.{html,css,js}')
    for (const file of files) {
        const source = fs.readFileSync(file, 'utf-8')
        const matches = source.match(urlRegex({ strict: true }))
        if (matches) {
            matches.forEach((url) => {
                const match = url.match(urlPattern)
                if (match && match[1]) {
                    if (!excludeUrls.has(match[1])) {
                        urls.add(match[1])
                    }
                }
            })
        }
    }
}

// 在index.html文件<head>标签中插入link标签
async function insertLinks() {
    const files = await glob('dist/**/*.html')
    const links = [...urls].map((url) => `<link rel="dns-prefetch" href="${url}" />`).join('\n')
    
    for (const file of files) {
        const html = fs.readFileSync(file, 'utf-8')
        const root = parse(html)
        const head = root.querySelector('head')
        head.insertAdjacentHTML('afterbegin', '\n' + links)
        fs.writeFileSync(file, root.toString())
    }
}

async function main() {
    await searchDomin()
    await insertLinks()
}

main()