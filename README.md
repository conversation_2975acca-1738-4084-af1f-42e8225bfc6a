# 🏦 ding-customer-web

基于 Vue 3 + TypeScript + Vite 构建的客户管理前端应用，提供客户信息管理、交易记录、充值提现、KYC审核、账户设置等核心功能。

## 🚀 快速开始

### 环境要求

- Node.js >= 18.x
- npm / yarn / pnpm
- TypeScript 5.8.3+

### 安装依赖

```bash
npm install
```

### 开发环境启动

```bash
npm run dev
```

### 构建项目

```bash
# 开发构建
npm run build

# 测试环境构建
npm run build:staging

# 生产环境构建
npm run build:prod
```

## 🛠️ 开发规范

### 代码质量检查

```bash
# ESLint检查和修复
npm run lint

# 代码格式化
npm run format

# TypeScript类型检查
npm run type-check

# 项目规则检查
npm run check-rules

# 提交前完整检查
npm run pre-commit
```

### 📋 开发文档

- **[项目规则文档](./PROJECT_RULES.md)** - 完整的开发规范和最佳实践
- **[快速开发指南](./QUICK_GUIDE.md)** - 日常开发快速参考

### 🏗️ 技术栈

- **前端框架**: Vue 3.5.13 (Composition API + `<script setup>`)
- **构建工具**: Vite 6.3.5
- **状态管理**: Pinia 3.0.2
- **UI框架**: Element Plus 2.9.10
- **路由**: Vue Router 4.5.0
- **HTTP请求**: Axios 1.8.4
- **国际化**: vue-i18n 11.1.5
- **类型系统**: TypeScript 5.8.3
- **样式**: Sass 1.78.0 + UnoCSS 66.1.0
- **工具库**: Lodash-es, Dayjs, Crypto-JS

### 📁 项目结构

```
src/
├── common/              # 公共资源
│   ├── apis/           # 公共API
│   ├── components/     # 可复用组件
│   ├── utils/          # 工具函数
│   ├── i18n/           # 国际化
│   ├── constants/      # 常量
│   ├── composables/    # 组合式函数
│   └── assets/         # 静态资源
├── pages/              # 页面模块
├── pinia/stores/       # 状态管理
├── router/             # 路由配置
├── layouts/            # 布局组件
├── plugins/            # Vue插件
└── http/               # 网络请求
```

### 🔒 安全特性

- 密码MD5+盐值加密
- HMAC-SHA256请求签名
- Token自动续期
- 权限路由守卫
- KYC状态验证

### 🌍 国际化支持

- 中文简体 (zh-CN)
- 中文繁体 (zh-HK)
- 英文 (en-US)
- 运行时语言切换

## 📊 代码质量

项目采用严格的代码质量标准：

- ✅ TypeScript严格模式
- ✅ ESLint + Prettier代码规范
- ✅ 自动化规则检查
- ✅ 组件化架构
- ✅ 性能优化配置

运行 `npm run check-rules` 查看详细的规范检查报告。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 遵循项目规范: `npm run pre-commit`
4. 提交更改: `git commit -m 'Add new feature'`
5. 推送分支: `git push origin feature/new-feature`
6. 提交 Pull Request

## 📝 更多信息

- **Vue 3 文档**: [https://vuejs.org/](https://vuejs.org/)
- **Vite 文档**: [https://vitejs.dev/](https://vitejs.dev/)
- **Element Plus**: [https://element-plus.org/](https://element-plus.org/)
- **Pinia**: [https://pinia.vuejs.org/](https://pinia.vuejs.org/)
