# 🚀 ding-customer-web 开发快速指南

## 📋 每日开发检查清单

### ✅ 提交代码前必做

```bash
npm run lint          # ESLint检查
npm run format         # 代码格式化
npm run type-check     # TypeScript类型检查
npm run check-rules    # 项目规则检查
```

### 📁 文件命名规范

- **组件目录**: PascalCase (`UserProfile/`)
- **文件名**: `index.vue` 或 `功能名.vue`
- **API文件**: `index.ts` + `type.ts`
- **国际化**: `en-US.ts`, `zh-CN.ts`, `zh-HK.ts`

### 🔗 路径引用规范

```typescript
// ✅ 正确使用
import Layout from '@/layouts/index.vue'; // src/目录
import { formatDate } from '@@/utils/datetime'; // src/common/目录

// ❌ 禁止使用
import Layout from '../layouts/index.vue'; // 相对路径跨目录
```

### 🎯 Vue组件必备

```vue
<script setup lang="ts">
// 接口定义
interface IProps {
  title: string;
  count?: number;
}

// Props + 默认值
const props = withDefaults(defineProps<IProps>(), {
  count: 0,
});

// 响应式数据
const loading = ref(false);
const userInfo = reactive({ name: '', email: '' });

// 事件处理 - handle前缀
const handleSubmit = () => {
  // 处理逻辑
};
</script>

<template>
  <div class="app-container">
    <!-- 组件内容 -->
  </div>
</template>

<style scoped lang="scss">
// 组件样式
</style>
```

### 🔒 安全必做

- 密码: `encryptPasswordMD5(password)`
- 请求: 自动携带签名验证
- Token: 存储在Cookie中
- 敏感信息: 不存储明文

### 📱 样式优先级

1. **UnoCSS**: `class="flex-center w-full"`
2. **Element Plus**: 内置样式
3. **自定义SCSS**: 最后选择

### 🌍 国际化使用

```typescript
// setup中
const { t } = useI18n();
const title = t('dashboard.title');

// 模板中
{
  {
    $t('dashboard.welcome');
  }
}
```

### 🏪 Pinia Store

```typescript
export const useUserStore = defineStore('user', () => {
  // State
  const token = ref('');

  // Getters
  const isLoggedIn = computed(() => !!token.value);

  // Actions
  const setToken = (value: string) => {
    token.value = value;
  };

  return { token, isLoggedIn, setToken };
});
```

### 🧪 错误处理

```typescript
try {
  await apiCall();
  ElMessage.success('操作成功');
} catch (error) {
  ElMessage.error(error.message || '操作失败');
} finally {
  loading.value = false;
}
```

### 📊 性能优化

- 路由: 动态 `import()` 懒加载
- 组件: 按需导入
- 图片: < 4KB 内联，大图哈希命名
- 请求: 合并相似请求，避免重复

### 🔧 TypeScript要点

```typescript
// ✅ 正确类型定义
interface IUserInfo {
  id: number;
  name: string;
  kycStatus: 'S' | 'R' | 'P';
}

// ✅ API响应类型
type GetUserResponse = ApiResponseData<IUserInfo>;

// ❌ 禁止使用
const data: any = {}; // 不使用any
```

## 🚨 常见错误避免

### ❌ 目录结构错误

```
❌ pages/dashboard/api/         # 应为apis/
❌ pages/dashboard/component/   # 应为components/
❌ components/dialog.vue        # 应为Dialog/index.vue
```

### ❌ 导入路径错误

```typescript
❌ import '../../../common/utils/format'
❌ import 'src/common/utils/format'
✅ import '@@/utils/format'
```

### ❌ 组件定义错误

```vue
❌ <script>                    # 应使用<script setup>
❌ <style>                     # 应使用<style scoped>
❌ interface UserInfo {}       # 应为IUserInfo
```

### ❌ 安全问题

```typescript
❌ const password = 'plain'           # 明文密码
❌ localStorage.setItem('token', '')  # 敏感信息明文存储
❌ console.log(userInfo)              # 生产环境输出敏感信息
```

## 🎯 质量目标

| 指标             | 目标值 | 检查方式              |
| ---------------- | ------ | --------------------- |
| TypeScript覆盖率 | 100%   | `npm run type-check`  |
| ESLint通过率     | 100%   | `npm run lint`        |
| 组件规范性       | 90%+   | `npm run check-rules` |
| 构建成功率       | 100%   | `npm run build`       |
| 页面加载时间     | <3s    | 浏览器DevTools        |

## 📚 更多信息

- **完整规则**: 查看 `PROJECT_RULES.md`
- **架构信息**: 查看项目内存记录
- **问题反馈**: 联系项目负责人

---

**快速检查**: `npm run check-rules` 🔍  
**最后更新**: 2025-08-27
