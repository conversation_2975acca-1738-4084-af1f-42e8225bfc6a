/* eslint-disable */
/* prettier-ignore */
// biome-ignore format: off
// biome-ignore lint: off
// @ts-nocheck
// Generated by unplugin-svg-component
import 'vue'
declare module 'vue' {
  export interface GlobalComponents {
    SvgIcon: import("vue").DefineComponent<{
        name: {
            type: import("vue").PropType<"fullscreen-exit" | "fullscreen" | "icon-account-amount" | "icon-add" | "icon-address" | "icon-bank" | "icon-bankcard" | "icon-bottom" | "icon-cert-additional-default" | "icon-cert-additional" | "icon-cert-business-default" | "icon-cert-business" | "icon-cert-company-default" | "icon-cert-company" | "icon-cert-director-default" | "icon-cert-director" | "icon-cert-into" | "icon-cert-manager-default" | "icon-cert-manager" | "icon-cert-shareholder-default" | "icon-cert-shareholder" | "icon-cert-suc" | "icon-cert-warning" | "icon-chain-adres" | "icon-close" | "icon-connect" | "icon-copy-btn" | "icon-currency-us" | "icon-danger" | "icon-dashboard" | "icon-deleted" | "icon-deposit-active" | "icon-deposit" | "icon-dlg-info" | "icon-dlg-success" | "icon-download" | "icon-edit-active" | "icon-edit" | "icon-exchange-active" | "icon-exchange-c" | "icon-exchange-coin" | "icon-exchange-net" | "icon-exchange-pay" | "icon-exchange-receive" | "icon-exchange-result-bg" | "icon-exchange-swapIn" | "icon-exchange-swapOut" | "icon-exchange" | "icon-fail" | "icon-full" | "icon-gary-check" | "icon-global-account-active" | "icon-global-account" | "icon-green-check" | "icon-grey-account" | "icon-hide" | "icon-info-bg" | "icon-info" | "icon-kyc-arrow" | "icon-kyc-watch" | "icon-lang-cn" | "icon-lang-en" | "icon-lang-hk" | "icon-lang" | "icon-left-close" | "icon-open" | "icon-payee" | "icon-query" | "icon-recharge-close" | "icon-recharge-edit" | "icon-recharge" | "icon-red-close" | "icon-refresh" | "icon-reset" | "icon-rise" | "icon-setting-active" | "icon-setting-agreement" | "icon-setting-company" | "icon-setting-email" | "icon-setting-enterprise" | "icon-setting-factor-auth" | "icon-setting-password" | "icon-setting-permission" | "icon-setting-person" | "icon-setting-personal" | "icon-setting" | "icon-show" | "icon-success" | "icon-sync-info" | "icon-topup" | "icon-unbind" | "icon-usdt" | "icon-wallet-active" | "icon-wallet" | "icon-warning" | "icon-withdrawal-active" | "icon-withdrawal-aim" | "icon-withdrawal-amount" | "icon-withdrawal-fee" | "icon-withdrawal-flow-active" | "icon-withdrawal-flow" | "icon-withdrawal-record-active" | "icon-withdrawal-record" | "icon-withdrawal-result-bg" | "icon-withdrawal" | "table-empty" | "time-icon" | "deposit/icon-bank-illustration" | "deposit/icon-crypto" | "deposit/icon-deposit-account" | "deposit/icon-eth-net" | "deposit/icon-fiat-pay" | "deposit/icon-fiat-voucher">;
            default: string;
            required: true;
        };
    }, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        name: {
            type: import("vue").PropType<"fullscreen-exit" | "fullscreen" | "icon-account-amount" | "icon-add" | "icon-address" | "icon-bank" | "icon-bankcard" | "icon-bottom" | "icon-cert-additional-default" | "icon-cert-additional" | "icon-cert-business-default" | "icon-cert-business" | "icon-cert-company-default" | "icon-cert-company" | "icon-cert-director-default" | "icon-cert-director" | "icon-cert-into" | "icon-cert-manager-default" | "icon-cert-manager" | "icon-cert-shareholder-default" | "icon-cert-shareholder" | "icon-cert-suc" | "icon-cert-warning" | "icon-chain-adres" | "icon-close" | "icon-connect" | "icon-copy-btn" | "icon-currency-us" | "icon-danger" | "icon-dashboard" | "icon-deleted" | "icon-deposit-active" | "icon-deposit" | "icon-dlg-info" | "icon-dlg-success" | "icon-download" | "icon-edit-active" | "icon-edit" | "icon-exchange-active" | "icon-exchange-c" | "icon-exchange-coin" | "icon-exchange-net" | "icon-exchange-pay" | "icon-exchange-receive" | "icon-exchange-result-bg" | "icon-exchange-swapIn" | "icon-exchange-swapOut" | "icon-exchange" | "icon-fail" | "icon-full" | "icon-gary-check" | "icon-global-account-active" | "icon-global-account" | "icon-green-check" | "icon-grey-account" | "icon-hide" | "icon-info-bg" | "icon-info" | "icon-kyc-arrow" | "icon-kyc-watch" | "icon-lang-cn" | "icon-lang-en" | "icon-lang-hk" | "icon-lang" | "icon-left-close" | "icon-open" | "icon-payee" | "icon-query" | "icon-recharge-close" | "icon-recharge-edit" | "icon-recharge" | "icon-red-close" | "icon-refresh" | "icon-reset" | "icon-rise" | "icon-setting-active" | "icon-setting-agreement" | "icon-setting-company" | "icon-setting-email" | "icon-setting-enterprise" | "icon-setting-factor-auth" | "icon-setting-password" | "icon-setting-permission" | "icon-setting-person" | "icon-setting-personal" | "icon-setting" | "icon-show" | "icon-success" | "icon-sync-info" | "icon-topup" | "icon-unbind" | "icon-usdt" | "icon-wallet-active" | "icon-wallet" | "icon-warning" | "icon-withdrawal-active" | "icon-withdrawal-aim" | "icon-withdrawal-amount" | "icon-withdrawal-fee" | "icon-withdrawal-flow-active" | "icon-withdrawal-flow" | "icon-withdrawal-record-active" | "icon-withdrawal-record" | "icon-withdrawal-result-bg" | "icon-withdrawal" | "table-empty" | "time-icon" | "deposit/icon-bank-illustration" | "deposit/icon-crypto" | "deposit/icon-deposit-account" | "deposit/icon-eth-net" | "deposit/icon-fiat-pay" | "deposit/icon-fiat-voucher">;
            default: string;
            required: true;
        };
    }>>, {
        name: "fullscreen-exit" | "fullscreen" | "icon-account-amount" | "icon-add" | "icon-address" | "icon-bank" | "icon-bankcard" | "icon-bottom" | "icon-cert-additional-default" | "icon-cert-additional" | "icon-cert-business-default" | "icon-cert-business" | "icon-cert-company-default" | "icon-cert-company" | "icon-cert-director-default" | "icon-cert-director" | "icon-cert-into" | "icon-cert-manager-default" | "icon-cert-manager" | "icon-cert-shareholder-default" | "icon-cert-shareholder" | "icon-cert-suc" | "icon-cert-warning" | "icon-chain-adres" | "icon-close" | "icon-connect" | "icon-copy-btn" | "icon-currency-us" | "icon-danger" | "icon-dashboard" | "icon-deleted" | "icon-deposit-active" | "icon-deposit" | "icon-dlg-info" | "icon-dlg-success" | "icon-download" | "icon-edit-active" | "icon-edit" | "icon-exchange-active" | "icon-exchange-c" | "icon-exchange-coin" | "icon-exchange-net" | "icon-exchange-pay" | "icon-exchange-receive" | "icon-exchange-result-bg" | "icon-exchange-swapIn" | "icon-exchange-swapOut" | "icon-exchange" | "icon-fail" | "icon-full" | "icon-gary-check" | "icon-global-account-active" | "icon-global-account" | "icon-green-check" | "icon-grey-account" | "icon-hide" | "icon-info-bg" | "icon-info" | "icon-kyc-arrow" | "icon-kyc-watch" | "icon-lang-cn" | "icon-lang-en" | "icon-lang-hk" | "icon-lang" | "icon-left-close" | "icon-open" | "icon-payee" | "icon-query" | "icon-recharge-close" | "icon-recharge-edit" | "icon-recharge" | "icon-red-close" | "icon-refresh" | "icon-reset" | "icon-rise" | "icon-setting-active" | "icon-setting-agreement" | "icon-setting-company" | "icon-setting-email" | "icon-setting-enterprise" | "icon-setting-factor-auth" | "icon-setting-password" | "icon-setting-permission" | "icon-setting-person" | "icon-setting-personal" | "icon-setting" | "icon-show" | "icon-success" | "icon-sync-info" | "icon-topup" | "icon-unbind" | "icon-usdt" | "icon-wallet-active" | "icon-wallet" | "icon-warning" | "icon-withdrawal-active" | "icon-withdrawal-aim" | "icon-withdrawal-amount" | "icon-withdrawal-fee" | "icon-withdrawal-flow-active" | "icon-withdrawal-flow" | "icon-withdrawal-record-active" | "icon-withdrawal-record" | "icon-withdrawal-result-bg" | "icon-withdrawal" | "table-empty" | "time-icon" | "deposit/icon-bank-illustration" | "deposit/icon-crypto" | "deposit/icon-deposit-account" | "deposit/icon-eth-net" | "deposit/icon-fiat-pay" | "deposit/icon-fiat-voucher";
    }>;
  }
}
