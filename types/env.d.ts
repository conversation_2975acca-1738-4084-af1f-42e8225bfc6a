/** 声明 vite 环境变量的类型（如果未声明则默认是 any） */
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_BASE_URL: string
  readonly VITE_ROUTER_HISTORY: "hash" | "html5"
  readonly VITE_PUBLIC_PATH: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Element Plus locale module declarations
declare module 'element-plus/dist/locale/zh-cn.mjs' {
  const zhCN: any
  export default zhCN
}

declare module 'element-plus/dist/locale/en.mjs' {
  const enUS: any
  export default enUS
}

declare module 'element-plus/dist/locale/zh-hk.mjs' {
  const zhHK: any
  export default zhHK
}
