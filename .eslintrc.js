module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2021: true
  },
  extends: [
    'plugin:vue/vue3-recommended',
    'eslint:recommended',
    'prettier' // 确保 prettier 在最后，以覆盖其他配置中的样式规则
  ],
  parser: 'vue-eslint-parser', // 解析 .vue 文件
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  plugins: [
    'vue',
    'prettier' // 将 Prettier 作为 ESLint 规则运行
  ],
  rules: {
    'prettier/prettier': 'warn', // 将 Prettier 的格式差异显示为 ESLint 警告
    'vue/multi-word-component-names': 'off', // 对于 views/下的 index.vue 这类文件可以关闭此规则
    'no-unused-vars': 'warn', // 未使用的变量给出警告
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off', // 生产环境禁用 console
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off' // 生产环境禁用 debugger
    // 您可以在此添加更多自定义规则
  },
  globals: {
    // defineProps: 'readonly', // 如果您在项目中频繁使用且不想每次都导入
    // defineEmits: 'readonly',
    // defineExpose: 'readonly',
    // withDefaults: 'readonly',
  }
}
