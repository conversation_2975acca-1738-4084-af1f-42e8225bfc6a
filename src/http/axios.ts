import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios"
import { useUserStore } from "@/pinia/stores/user"
import { getToken, setToken } from "@/common/utils/cache/cookies"
import axios from "axios"
import { get, merge } from "lodash-es"
import { getTimestamp } from "@/common/composables/useSafeRequest"
import HmacSHA256 from "crypto-js/hmac-sha256"
import { CacheKey } from "@/common/constants/cache-key"
import Cookies from "js-cookie"
import Base64 from "crypto-js/enc-base64"
import { createSignSignature } from "@/common/utils/signatureUtils"
import { LoadingService } from "@/common/components/LottieLoading/LoadingService"

let initServerTime = 0; // 第一次服务端时间，单位毫秒
let serverTimeOffset : number | undefined = undefined; // 客户端与服务器的时间差，单位毫秒

/**
 * @description 生成签名
 * @param {object} data - 需要签名的数据
 * @param {string[]} exclude - 排除签名的字段
 * @returns {string}
 */
function createSignature(data: any, exclude: string[] = []): string {
  const secret = Cookies.get(CacheKey.LOGIN_SIGN_KEY) || ""
    // 字典序排序后拼接
  const paramString = createSignSignature(data)
  
  console.log(`secret: ${Base64.parse(secret)}`)
  console.log(`signStr: ${paramString}`)
  return HmacSHA256(paramString, Base64.parse(secret)).toString()  
}

/** 退出登录并强制刷新页面（会重定向到登录页） */
function logout() {
  useUserStore().logout()
  location.reload()
}

/** 创建请求实例 */
function createInstance() {
  // 创建一个 axios 实例命名为 instance
  const instance = axios.create()
  let userConfig: CustomConfigInstance = {}
  // 请求拦截器
  instance.interceptors.request.use(
    // 发送之前
    config => {
      // @ts-expect-error 忽略自定义字段的ts 验证
      userConfig = config?.customConfig || {}
      return config
    },
    // 发送失败
    error => Promise.reject(error)
  )
  // 响应拦截器（可根据具体业务作出相应的调整）
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // 从响应头中获取服务器时间 (Date header)
      const serverDateHeader = response.data && response.data.timestamp
      if (serverDateHeader) {
        try {
          const serverTime = new Date(serverDateHeader).getTime()
          // 计算服务器时间与当前客户端时间的差值
          // 此处 Date.now() 是响应到达客户端的时间
          serverTimeOffset = serverTime - Date.now()
        }
        catch (e) {
          console.error("Failed to parse server date header for time calibration:", e)
        }
      }
      //返回的token不为空，则更新
      const token = response.data && response.data.token
      if (token) {
        setToken(token)
      }
      // apiData 是 api 返回的数据
      const apiData = response.data
      // 二进制数据则直接返回
      const responseType = response.request?.responseType
      if (responseType === "blob" || responseType === "arraybuffer") return apiData
      // 这个 code 是和后端约定的业务 code
      const code = apiData.respCode || ""
      switch (code) {
        case "C00000":
          // 本系统采用 code === 0 来表示没有业务错误
          return apiData
        case "C00004":
        case "C00005":
        case "C00006":
          // Token 过期时
          return logout()
        default:
          // 不是正确的 code
          if (userConfig.showError) {
            ElMessage.error(apiData.respDesc || "系统错误")
          }
          return Promise.reject(new ApiError(code, apiData.respDesc || "系统错误"))
      }
    },
    (error) => {
      // status 是 HTTP 状态码
      const status = get(error, "response.status")
      const message = get(error, "response.data.respDesc")
      switch (status) {
        case 400:
          error.message = "请求错误"
          break
        case 401:
          // Token 过期时
          error.message = message || "未授权"
          logout()
          break
        case 403:
          error.message = message || "拒绝访问"
          break
        case 404:
          error.message = "请求地址出错"
          break
        case 408:
          error.message = "请求超时"
          break
        case 500:
          error.message = "服务器内部错误"
          break
        case 501:
          error.message = "服务未实现"
          break
        case 502:
          error.message = "网关错误"
          break
        case 503:
          error.message = "服务不可用"
          break
        case 504:
          error.message = "网关超时"
          break
        case 505:
          error.message = "HTTP 版本不受支持"
          break
      }
      if (userConfig.showError) {
        ElMessage.error(error.message || "系统错误")
      }
      return Promise.reject(new ApiError(status, error.message || "系统错误"))
    }
  )
  return instance
}

/** 创建请求方法 */
function createRequest(instance: AxiosInstance, customConfig: CustomConfigInstance) {
  return async <T>(config: AxiosRequestConfig, customConfig?: CustomConfigInstance): Promise<T> => {
    const token = getToken()
    // if (initServerTime === 0) {
    //   initServerTime = await getTimestamp()
    // }
    const mergedCustomConfig = merge({}, defaultCustomConfig, customConfig || {})
    let loadingStartTime = 0
    if (mergedCustomConfig.showLoading) {
      loadingStartTime = Date.now()
      LoadingService.show()
    }

    if (serverTimeOffset === undefined) {
      initServerTime = await getTimestamp()
    }
    const timestamp = (serverTimeOffset === undefined ? initServerTime : (Date.now() + serverTimeOffset)).toString()
    // 默认配置
    const defaultConfig: AxiosRequestConfig = {
      // 接口地址
      baseURL: import.meta.env.VITE_BASE_URL,
      // 请求头
      headers: {
        // 初始值为 -1 表示未校准 则使用请求的服务器时间
        "X-Accept-Timestamp": timestamp,
        // 携带 Token
        "Authorization": token ? `Bearer ${token}` : undefined,
        "Content-Type": "application/json",
        "X-Accept-Device": "PC",
        "X-Accept-Language": localStorage.getItem("locale") === 'en-US' ? 'en' :  localStorage.getItem("locale") || "zh-CN",
      },
      // 请求体
      data: {},
      // 请求超时
      timeout: 30000,
      // 跨域请求时是否携带 Cookies
      withCredentials: false
    }
    // 将默认配置 defaultConfig 和传入的自定义配置 config 进行合并成为 mergeConfig
    const mergeConfig = merge(defaultConfig, config, { customConfig: mergedCustomConfig })

    const dataToSign = { ...mergeConfig.params, ...mergeConfig.data, timestamp: timestamp }
    const sign = createSignature(dataToSign, mergeConfig.customConfig?.excludeFromSign)
    if (mergeConfig.headers && sign) {
      mergeConfig.headers.sign = sign
    }
    try {
      return await instance(mergeConfig)
    } finally {
      if (mergedCustomConfig.showLoading) {
        const elapsedTime = Date.now() - loadingStartTime
        const remainingTime = 1000 - elapsedTime
        if (remainingTime > 0) {
          setTimeout(() => {
            LoadingService.hide()
          }, remainingTime)
        } else {
          LoadingService.hide()
        }
      }
    }
  }
}

/** 用于请求的实例 */
const instance = createInstance()

/** 自定义配置 */
export interface CustomConfigInstance {
  /** 是否消费错误信息 */
  showError?: boolean
  /** 排除签名的字段 */
  excludeFromSign?: string[]
  /** 是否显示 loading */
  showLoading?: boolean
}
export const defaultCustomConfig: CustomConfigInstance = {
  showError: true,
  excludeFromSign: [],
  showLoading: true
}

/** 用于请求的方法 */
export const request = createRequest(instance, defaultCustomConfig)


// 统一错误
export class ApiError extends Error {
  code?: string

  constructor(code: string, message: string) {
    super(message);
    this.code = code;
    this.message = message;
  }
}