<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useUserStore } from '@/pinia/stores/user';
import type { DropdownInstance } from 'element-plus';
import { useAppStore } from '@/pinia/stores/app';
import { useI18n } from 'vue-i18n';
import { setLocale, LocaleType } from '@@/i18n';
const { t } = useI18n();
const router = useRouter();
const userStore = useUserStore();
const dropdown = ref<DropdownInstance>();
const appStore = useAppStore();

const lang = ref(localStorage.getItem('locale') || 'zh-CN');

/** 切换语言 */
function changeLang(langValue: string) {
  setLocale(langValue as LocaleType);
  lang.value = langValue;
}

/** 登出 */
function logout() {
  dropdown.value?.handleClose();
  userStore.logout();
  router.push('/login');
}

function goToPersonal() {
  dropdown.value?.handleClose();
  router.push('/setting?activeItem=account-info');
}

const toggleSidebar = () => {
  appStore.toggleSidebar(false);
};
const isCollapse = computed(() => appStore.sidebar.opened === 'closed');
</script>

<template>
  <div class="navigation-bar">
    <div class="right-menu">
      <!-- <svgIcon @click="toggleSidebar" style="width:18px" class="cursor-pointer" :class="{ 'rotate-180': isCollapse }" name="icon-left-close" /> -->

      <div class="right-menu-item flex items-center">
        <el-dropdown ref="dropdown" trigger="hover">
          <div
            class="right-menu-item user w-36px h-36px text-18px hover:bg-[#F8F9FA] rounded-50% m-0 mr-4px justify-center"
          >
            <svgIcon v-if="lang === 'zh-CN'" name="icon-lang-cn" />
            <svgIcon v-else-if="lang === 'zh-HK'" name="icon-lang-hk" />
            <svgIcon v-else name="icon-lang-en" />
          </div>

          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="changeLang('zh-CN')">中文</el-dropdown-item>
              <el-dropdown-item @click="changeLang('zh-HK')">繁体</el-dropdown-item>
              <el-dropdown-item @click="changeLang('en-US')">English</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-dropdown ref="dropdown" trigger="hover" placement="bottom-end">
          <div
            class="right-menu-item user hover:bg-[#F8F9FA] rounded-18px leading-36px px-12px m-0 group"
          >
            <span class="text-14px font-400 text-[#222527]">{{ userStore.nickName }}</span>
            <svgIcon name="icon-bottom" class="text-12px ml-4px group-hover:rotate-180" />
          </div>
          <template #dropdown>
            <div class="dropdown-content">
              <h4>{{ t('common.navigationBar.welcome') }}</h4>
              <p>{{ userStore.email }}</p>

              <div class="dropdown-content-menu">
                <div class="dropdown-content-item" @click="goToPersonal">
                  {{ t('common.navigationBar.accountInfo') }}
                </div>
                <div class="dropdown-content-item" @click="logout">
                  {{ t('common.navigationBar.logout') }}
                </div>
              </div>
            </div>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.dropdown-content {
  width: 240px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 14px 0;

  h4 {
    font-weight: 600;
    font-size: 16px;
    color: #222527;
    letter-spacing: 0;
    line-height: 22px;
    margin: 0 20px;
  }
  p {
    font-weight: 400;
    font-size: 14px;
    color: #222527;
    letter-spacing: 0;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 8px 20px 0;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e6eb;
  }
  .dropdown-content-menu {
    margin-top: 8px;
    .dropdown-content-item {
      padding: 0 20px;
      font-weight: 400;
      font-size: 14px;
      color: #222527;
      letter-spacing: 0;
      line-height: 34px;
      cursor: pointer;
      &:hover {
        background-color: var(--el-dropdown-menuItem-hover-fill);
      }
    }
  }
}

.navigation-bar {
  height: var(--v3-navigationbar-height);
  overflow: hidden;
  color: var(--v3-navigationbar-text-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 0 auto;
  flex: 1;
  .right-menu {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-bottom: 1px solid #eee;
    padding: 0 40px 0 32px;
    flex: 1;
    &-item {
      cursor: pointer;
    }
    .user {
      display: flex;
      align-items: center;
      .el-avatar {
        margin-right: 10px;
      }
      span {
        font-size: 16px;
      }
    }
  }
}

/* Add this to your existing styles */
.rotate-180 {
  transform: rotate(180deg);
}
</style>
