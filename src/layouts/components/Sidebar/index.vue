<script lang="ts" setup>
import { usePermissionStore } from '@/pinia/stores/permission';
import { useRoute, RouteRecordRaw } from 'vue-router';
import { computed, ref } from 'vue';
import Link from './Link.vue';
import { useI18n } from 'vue-i18n';
import { Logo } from '../index';
import { useAppStore } from '@/pinia/stores/app';

const permissionStore = usePermissionStore();
const route = useRoute();

const { t } = useI18n(); // 使用 i18n
const appStore = useAppStore();
const menuKey = ref(0);
const activeMenu = computed(() => route.meta.activeMenu || route.path);

watch(activeMenu, (newVal) => {
  menuKey.value++;
});

const filteredRoutes = computed(() => {
  return permissionStore.dynamicRoutes.filter((route) => !route.meta?.hidden);
});
const isCollapse = computed(() => appStore.sidebar.opened === 'closed');

const toggleSidebar = () => {
  appStore.toggleSidebar(false);
};
</script>

<template>
  <div :class="!isCollapse ? 'sidebar-open' : 'sidebar-close'">
    <div class="left-menu h-80px">
      <Logo />
    </div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu router unique-opened active-text-color="#409EFF" :default-active="activeMenu" :collapse="isCollapse"
        :collapse-transition="true" :key="menuKey">
        <template v-for="item in filteredRoutes" :key="item.path">
          <Link v-for="subItem in item.children" :key="subItem.path" :to="subItem.path">
          <el-tooltip v-if="!subItem.meta?.hidden" placement="right" :disabled="!isCollapse" :show-after="100"
            :hide-after="100" effect="dark" :offset="25">
            <template #content>
              <div class="sidebar-tooltip-content">{{ t(`router.${subItem.meta?.title}` ||
                '') }}</div>
            </template>
            <el-menu-item :index="subItem.path" v-if="!subItem.meta?.hidden">

              <div class="flex items-center justify-center meun-view">
                <SvgIcon :name="activeMenu !== subItem.path
                  ? subItem.meta?.svgIcon
                  : subItem.meta?.svgIconActive
                  " class="menu-svg-icon" />
                <span v-if="!isCollapse" class="ml-10px">{{
                  t(`router.${subItem.meta?.title}` || '')
                }}</span>
              </div>
            </el-menu-item>
          </el-tooltip>
          </Link>
        </template>
      </el-menu>
    </el-scrollbar>
    <div class="flex pb-20px items-center pl-16px pr-16px color-#fff w-240px gap-2"
      :class="[isCollapse ? 'justify-center w-88px flex-wrap' : 'justify-between w-240px h-56px']">
      <el-tooltip placement="right" :disabled="!isCollapse" :show-after="100" :hide-after="100" effect="dark"
        :offset="25">
        <template #content>
          <div class="sidebar-tooltip-content">{{ t('router.setting') }}</div>
        </template>
        <div class="flex items-center cursor-pointer hover:bg-[#152443] rounded-6px" @click="$router.push('/setting')"
          :class="[
            isCollapse ? 'w-56px h-56px justify-center' : 'w-166px pl-20px justify-between',
            activeMenu === '/setting' ? 'color-[#FF0064]' : '',
          ]">
          <svgIcon class="text-14px" :name="activeMenu === '/setting' ? 'icon-setting-active' : 'icon-setting'" />
          <p class="flex-1 ml-14px text-14px whitespace-nowrap overflow-hidden text-ellipsis" v-if="!isCollapse">
            {{ t('router.setting') }}
          </p>
        </div>
      </el-tooltip>
      <div class="flex items-center justify-center cursor-pointer w-40px h-40px hover:bg-[#152443] rounded-50%"
        @click="toggleSidebar">
        <svgIcon style="width: 18px" class="cursor-pointer" :class="{ 'rotate-180': isCollapse }"
          name="icon-left-close" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.sidebar-open {
  width: 240px;
  transition: width 0.3s;
}

.sidebar-close {
  width: 88px;
  transition: width 0.3s;

  .el-scrollbar {
    height: calc(100% - 80px - 124px);

    :deep(.el-menu) {
      width: 88px;

      .el-menu-item {
        span {
          opacity: 0;
          transform: translateX(10px);
          pointer-events: none;
        }
      }
    }
  }

  .layout-logo-container {
    padding: 0;
    justify-content: center;
  }
}

.el-scrollbar {
  height: calc(100% - 80px - 56px);
  transition: width 0.3s;

  :deep(.scrollbar-wrapper) {
    // 限制水平宽度
    overflow-x: hidden;
  }

  :deep(.el-menu) {
    background: #030814;
    border: none;
    padding: 0 16px;
    width: 240px;
    overflow-x: hidden;
    transition: width 0.3s;

    .el-menu-item {
      margin-bottom: 2px;
      width: 100%;

      &.is-active {
        background: #ff0064;
        border-radius: 6px;

        span {
          color: #fff;
          font-weight: 600;
        }

        svg {
          color: #fff;
        }
      }

      &:hover {
        background: #152443;
        border-radius: 6px;
      }
    }
  }

  .el-menu--collapse {
    .el-menu-item {
      padding: 0 21px !important;
      transition: padding 0.3s;
    }
  }

  // 滚动条
  :deep(.el-scrollbar__bar) {
    &.is-horizontal {
      // 隐藏水平滚动条
      display: none;
    }
  }
}

.meun-view {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #fff;

  .menu-svg-icon {
    flex-shrink: 0;
    font-size: 14px;
  }

  span {
    transition:
      opacity 0.3s,
      transform 0.3s;
    opacity: 1;
    transform: translateX(0);
    display: inline-block;
  }
}

:deep(.el-icon.el-sub-menu__icon-arrow) {
  display: none;
}

.sidebar-close {
  .meun-view {
    span {
      opacity: 0;
      transform: translateX(10px);
      pointer-events: none;
    }
  }
}

.absolute .flex-1 {
  transition:
    opacity 0.3s,
    transform 0.3s;
  opacity: 1;
  transform: translateX(0);
  display: inline-block;
}

.sidebar-close .absolute .flex-1 {
  opacity: 0;
  transform: translateX(10px);
  pointer-events: none;
}

.sidebar-tooltip-content {
  font-size: 14px !important;
  font-family: PingFangSC-Regular;
  font-weight: 400;
}
</style>
