<script lang="ts" setup>
import { useSettingsStore } from '@/pinia/stores/settings';
import { Footer } from '../index';
import { usePermissionStore } from '@/pinia/stores/permission';
const permissionStore = usePermissionStore();

const cachedRoutes = computed(() => {
  // 从路由表中获取所有设置了 keepAlive: true 的路由名称
  if (!permissionStore.dynamicRoutes?.length || !permissionStore.dynamicRoutes[0]?.children) {
    return [];
  }
  return permissionStore.dynamicRoutes[0].children
    ?.filter((record) => record.meta?.keepAlive)
    .map((record) => record.name)
    .filter((name): name is string => name !== undefined && name !== null); // 过滤掉 undefined 或 null
});
</script>

<template>
  <section class="app-main">
    <div class="app-scrollbar">
      <!-- key 采用 route.path 和 route.fullPath 有着不同的效果，大多数时候 path 更通用 -->
      <router-view v-slot="{ Component, route }">
        <transition name="el-fade-in" mode="out-in">
          <keep-alive :include="cachedRoutes">
            <component :is="Component" :key="route.path" class="app-container-grow" />
          </keep-alive>
        </transition>
      </router-view>
      <!-- 页脚 -->
      <!-- <Footer /> -->
    </div>
    <!-- 返回顶部 -->
    <el-backtop />
    <!-- 返回顶部（固定 Header 情况下） -->
    <el-backtop target=".app-scrollbar">
      <div class="el-backtop__inner">
        <svgIcon class="color-#030814 text-16px" name="icon-topup" />
      </div>
    </el-backtop>
  </section>
</template>

<style lang="scss" scoped>
@import '@@/assets/styles/mixins.scss';

.app-main {
  width: 100%;
  display: flex;
}

.app-scrollbar {
  flex-grow: 1;
  overflow: auto;
  @extend %scrollbar;
  display: flex;
  flex-direction: column;

  .app-container-grow {
    flex-grow: 1;
  }
}

:deep(.el-backtop) {
  width: 36px;
  height: 36px;
}
</style>
