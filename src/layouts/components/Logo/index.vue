<script lang="ts" setup>
import { computed } from 'vue';
import { useSettingsStore } from '@/pinia/stores/settings';
import { LayoutModeEnum } from '@/common/constants/app-key';
import { useAppStore } from '@/pinia/stores/app';
const appStore = useAppStore();
const isCollapse = computed(() => appStore.sidebar.opened === 'closed');
interface Props {
  collapse?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  collapse: true,
});

const settingsStore = useSettingsStore();
</script>

<template>
  <div class="layout-logo-container">
    <img
      class="w-117px h-40px"
      v-if="!isCollapse"
      src="@@/assets/images/logo/logo-white.png"
      alt=""
    />
    <img class="w-30px" v-else src="@@/assets/images/logo/logo.png" alt="" />
  </div>
</template>

<style lang="scss" scoped>
.layout-logo-container {
  width: 100%;
  background: #030814;
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: 32px;
}

.layout-mode-top {
  height: var(--v3-navigationbar-height);
  line-height: var(--v3-navigationbar-height);
}

.collapse {
  .layout-logo {
    width: 32px;
    height: 32px;
    vertical-align: middle;
    display: inline-block;
  }
  .layout-logo-text {
    display: none;
  }
}
</style>
