<script lang="ts" setup>
import { useSettingsStore } from '@/pinia/stores/settings';
import { AppMain, NavigationBar, Sidebar } from './components';
import { getCssVar, setCssVar } from '@@/utils/css';
import { usePermissionStore } from '@/pinia/stores/permission';
import { useAppStore } from '@/pinia/stores/app';

const appStore = useAppStore();

const settingsStore = useSettingsStore();

// #region 隐藏标签栏时删除其高度，是为了让 Logo 组件高度和 Header 区域高度始终一致
const cssVarName = '--v3-tagsview-height';
const v3TagsviewHeight = getCssVar(cssVarName);

const permissionStore = usePermissionStore();
// #endregion

permissionStore.setAllRoutes();
const isCollapse = computed(() => appStore.sidebar.opened === 'closed');
</script>

<template>
  <div class="app-wrapper">
    <!-- 左侧菜单 -->
    <div class="main-container">
      <Sidebar class="sidebar-container" />
    </div>
    <!-- 右侧内容 -->
    <div :class="!isCollapse ? 'content-open' : 'content-close'">
      <NavigationBar class="navigation-bar" />
      <!-- 页面主体内容 -->
      <AppMain class="app-main" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '@@/assets/styles/mixins.scss';
$transition-time: 1s;

.app-wrapper {
  @extend %clearfix;
  width: 100%;
  background: #fff;
  display: flex;
  height: 100vh;
  overflow: hidden;

  .content {
    width: calc(100% - 240px);
    transition: width $transition-time;
  }

  .closed-content {
    width: calc(100% - 88px);
    transition: width 0.2s;
  }
}

.fixed-header {
  z-index: 1002;
  flex: 1;
  .logo {
    width: var(--v3-sidebar-width);
  }
}

.layout-header {
  background-color: #fff;
}

.main-container {
  min-height: 100%;
  background-color: #ffffff;
  margin: 0 auto;
  display: flex;
}

.sidebar-container {
  background-color: #030814;
  height: 100%;
  z-index: 1001;
  overflow: hidden;
  flex: none;
}

.app-main {
  transition: padding-left $transition-time;
  height: 100vh;
  overflow: auto;
  // width: calc(100% - var(--v3-sidebar-width));
}

.content-close {
  width: calc(100vw - 88px);
  transition: width 0.3s;
}

.content-open {
  width: calc(100vw - 240px);
  transition: width 0.3s;
}
</style>
