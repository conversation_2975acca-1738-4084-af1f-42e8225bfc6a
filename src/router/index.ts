import type { RouteRecordRaw } from 'vue-router';
import { routerConfig } from '@/router/config';
import { registerNavigationGuard } from '@/router/guard';
import { createRouter } from 'vue-router';
import { flatMultiLevelRoutes } from './helper';

const Layouts = () => import('@/layouts/index.vue');

/**
 * @name 常驻路由
 * @description 除了 redirect/403/404/login 等隐藏页面，其他页面建议设置唯一的 Name 属性
 */
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/403',
    component: () => import('@/pages/error/403.vue'),
    meta: {
      hidden: true,
      title: '403',
    },
  },
  {
    path: '/404',
    component: () => import('@/pages/error/404.vue'),
    meta: {
      hidden: true,
      title: '404',
    },
    alias: '/:pathMatch(.*)*',
  },
  {
    path: '/login/:email?',
    name: 'Login',
    component: () => import('@/pages/login/index.vue'),
    meta: {
      hidden: true,
      title: 'login',
    },
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/pages/login/register.vue'),
    meta: {
      title: 'register',
      hidden: true,
    },
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('@/pages/login/ForgotPassword.vue'),
    meta: {
      title: 'forgotPassword',
      hidden: true,
    },
  },
  {
    path: '/kyc-rejected',
    component: () => import('@/pages/kyc/rejected.vue'),
    name: 'KycRejected',
    meta: {
      title: 'kycRejected',
      hidden: true,
    },
  },
];

/**
 * @name 动态路由
 * @description 用来放置有权限 (Roles 属性) 的路由
 * @description 必须带有唯一的 Name 属性
 */
export const dynamicRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    component: Layouts,
    name: 'Index',
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/pages/dashboard/index.vue'),
        name: 'Dashboard',
        meta: {
          title: 'dashboard',
          svgIcon: 'icon-wallet',
          svgIconActive: 'icon-wallet-active',
        },
      },
      {
        path: '/global-account',
        component: () => import('@/pages/globalAccount/index.vue'),
        name: 'globalAccount',
        meta: {
          title: 'globalAccount',
          svgIcon: 'icon-global-account',
          svgIconActive: 'icon-global-account-active',
        },
      },
      {
        path: '/recharge',
        component: () => import('@/pages/deposit/index.vue'),
        name: 'Deposit',
        meta: {
          title: 'deposit',
          svgIcon: 'icon-deposit',
          svgIconActive: 'icon-deposit-active',
        },
      },
      {
        path: '/deposit/record',
        component: () => import('@/pages/deposit/record.vue'),
        name: 'DepositRecord',
        meta: {
          title: 'depositRecord',
          hidden: true,
        },
      },
      {
        path: '/exchange',
        component: () => import('@/pages/exchange/index.vue'),
        name: 'Exchange',
        meta: {
          title: 'exchange',
          svgIcon: 'icon-exchange',
          svgIconActive: 'icon-exchange-active',
        },
      },
      {
        path: '/exchange/result',
        component: () => import('@/pages/exchange/result.vue'),
        name: 'ExchangeResult',
        meta: {
          title: 'exchangeResult',
          hidden: true,
        },
      },
      {
        path: '/withdrawal',
        component: () => import('@/pages/withdrawal/index.vue'),
        name: 'Withdrawal',
        meta: {
          title: 'withdrawal',
          svgIcon: 'icon-withdrawal',
          svgIconActive: 'icon-withdrawal-active',
        },
      },
      {
        path: '/withdrawal/result',
        component: () => import('@/pages/withdrawal/result.vue'),
        name: 'WithdrawalResult',
        meta: {
          title: 'withdrawalResult',
          hidden: true,
        },
      },
      {
        path: '/trade-record',
        component: () => import('@/pages/tradeRecord/index.vue'),
        name: 'TradeRecord',
        meta: {
          title: 'tradeRecord',
          svgIcon: 'icon-withdrawal-record',
          svgIconActive: 'icon-withdrawal-record-active',
        },
      },
      {
        path: '/kyc',
        component: () => import('@/pages/kyc/index.vue'),
        name: 'Kyc',
        meta: {
          title: 'kyc',
          svgIcon: 'icon-withdrawal-record',
          svgIconActive: 'icon-withdrawal-record-active',
          hidden: true,
        },
      },
      {
        path: '/kyc-initial',
        component: () => import('@/pages/kyc/initial.vue'),
        name: 'KycInitial',
        meta: {
          title: 'kycInitial',
          hidden: true,
        },
      },
      {
        path: '/setting',
        component: () => import('@/pages/setting/layout/index.vue'),
        name: 'Setting',
        meta: {
          title: 'setting',
          svgIcon: 'icon-edit',
          svgIconActive: 'icon-edit-active',
          hidden: true,
        },
      },
      {
        path: '/trade-flow',
        component: () => import('@/pages/tradeRecord/flow.vue'),
        name: 'TradeFlow',
        meta: {
          title: 'tradeFlow',
          svgIcon: 'icon-withdrawal-flow',
          svgIconActive: 'icon-withdrawal-flow-active',
        },
      },
    ],
  },
];

/** 路由实例 */
export const router = createRouter({
  history: routerConfig.history,
  routes: constantRoutes,
  // 滚动行为优化
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0, behavior: 'smooth' };
    }
  },
});

/** 重置路由 */
export function resetRouter() {
  try {
    // 注意：所有动态路由路由必须带有 Name 属性，否则可能会不能完全重置干净
    router.getRoutes().forEach((route) => {
      const { name, meta } = route;
      if (name && meta.roles?.length) {
        router.hasRoute(name) && router.removeRoute(name);
      }
    });
  } catch {
    // 强制刷新浏览器也行，只是交互体验不是很好
    location.reload();
  }
}

// 注册路由导航守卫
registerNavigationGuard(router);
