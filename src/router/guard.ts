import type { Router, RouteLocationNormalized } from 'vue-router';
import { usePermissionStore } from '@/pinia/stores/permission';
import { useUserStore } from '@/pinia/stores/user';
import { routerConfig } from '@/router/config';
import { isWhiteList } from '@/router/whitelist';
import { setRouteChange } from '@@/composables/useRouteListener';
import { useTitle } from '@@/composables/useTitle';
import i18n from '@/common/i18n';
import { getToken } from '@@/utils/cache/cookies';
import NProgress from 'nprogress';

NProgress.configure({ showSpinner: false });

const { setTitle } = useTitle();

const LOGIN_PATH = '/login';

export function registerNavigationGuard(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, _from) => {
    NProgress.start();
    const userStore = useUserStore();
    const permissionStore = usePermissionStore();
    // 如果没有登录
    if (!getToken()) {
      // 如果在免登录的白名单中，则直接进入
      if (isWhiteList(to)) return true;
      // 其他没有访问权限的页面将被重定向到登录页面
      return LOGIN_PATH;
    }
    // 如果已经登录，并准备进入 Login 页面，则重定向到主页
    if (to.path === LOGIN_PATH) return '/';
    // 如果用户已经获得其权限角色
    if (userStore.roles.length !== 0) return true;

    if (permissionStore.dynamicRoutes.length > 0) {
      const kycPath = await gotoKyc(to);
      console.log(kycPath);
      if (kycPath) {
        return kycPath;
      }
      return true;
    }
    // 否则要重新获取权限角色
    try {
      await permissionStore.setAllRoutes();
      // 设置 replace: true, 因此导航将不会留下历史记录
      return { ...to, replace: true };
    } catch (error) {
      // 过程中发生任何错误，都直接重置 Token，并重定向到登录页面
      userStore.resetToken();
      ElMessage.error((error as Error).message || '路由守卫发生错误');
      return LOGIN_PATH;
    }
  });

  // 全局后置钩子
  router.afterEach(async (to) => {
    setRouteChange(to);

    // 国际化处理路由标题
    const title = to.meta.title ? i18n.global.t(`router.${to.meta.title}`) : undefined;
    setTitle(title);
    NProgress.done();
  });
}

const gotoKyc = async (to: RouteLocationNormalized) => {
  try {
    const userStore = useUserStore();
    // 获取用户信息
    if (userStore.token && to.path !== '/kyc-rejected' && userStore.kycStatus !== 'S') {
      await userStore.getInfo();
      // 判断kyc是否被拒绝
      if (userStore.kycStatus === 'R') {
        // 跳转到拒绝页面
        return { path: '/kyc-rejected', replace: true };
      } else if (
        userStore.kycStatus !== 'S' &&
        to.path !== '/index' &&
        to.path !== '/kyc-initial' &&
        to.path !== '/kyc'
      ) {
        // 使用 replace 而不是 push，避免在历史记录中留下痕迹
        return { path: '/kyc-initial', replace: true };
      }
    }
    return '';
  } catch (error) {
    return '';
  }
};
