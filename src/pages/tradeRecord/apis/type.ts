export type transLogRequestData = ApiRequestData<{
  sysSeqId?: string;
  transType?: string;
  transCurrency?: string;
  transStat?: string;
  startTime?: string;
  endTime?: string;
  pageNum?: number;
  pageSize?: number;
  dateRange?: Array<string>;
}>;

export type transFlowRequestData = ApiRequestData<{
  currencyList?: Array<string>;
  transTypeList?: Array<string>;
  startTime?: string;
  endTime?: string;
  pageNum?: number;
  pageSize?: number;
  dateRange?: Array<string>;
}>;

export type transDetailRequestData = ApiRequestData<{
  sysSeqId: string;
}>;

export type transLogResponseData = ApiResponseData<{
  total: number;
  transLogResponseList: Array<transLogResponseList>;
}>;

export type walletAcctResponseData = ApiResponseData<{
  total: number;
  queryWalletInfoVOs: Array<walletAcctResponseList>;
}>;

export type walletAcctResponseList = {
  balanceAvl: number;
  balanceWay: number;
  balanceTotal: number;
  acctType: string;
  currency: string;
};

export type transLogResponseList = {
  id: number;
  sysSeqId: string;
  sysDate: string;
  merCustId: string;
  userCustId: string;
  userId: string;
  transType: string;
  transAmt: number;
  transCurrency: string;
  feeAmt: number;
  feeCurrency: string;
  feeFlag: string;
  calcMode: string;
  realAmt: number;
  realCurrency: string;
};

export type transDetailResponseData = ApiResponseData<{
  id: number;
  sysSeqId: string;
  sysDate: string;
  merCustId: string;
  userCustId: string;
  userId: string;
  transType: string;
  transAmt: number;
  transCurrency: string;
  feeAmt: number;
  feeCurrency: string;
  feeFlag: string;
  calcMode: string;
  realAmt: number;
  realCurrency: string;
  transStat: string;
  traceId: string;
  respCode: string;
  respDesc: string;
  createTime: string;
  updateTime: string;
  extension1: string;
  extension2: string;
  extension3: string;
  fileUrl: string;
  merCustName: string;
}>;

export type transFlowResponseData = ApiResponseData<{
  total: number;
  list: Array<transFlowResponseList>;
}>;

export type transFlowResponseList = {
  reqSeqId: string;
  transType: string;
  transSubType: string;
  currency: string;
  transAmount: number;
  createTime: string;
  sysSeqId: string;
  transLogDate: string;
  remark: string;
  adjustedBalance: number;
};

export type withdrawDetailRequestData = {
  sysSeqId: string;
};

export type withdrawDetailResponseData = ApiResponseData<{
  basic?: withdrawDetailResponseDataBasic;
  payer?: withdrawDetailResponseDataPayer;
  payee?: withdrawDetailResponseDataPayee;
  supplementUrl?: string;
  chineseCertiUrl?: string;
  englishCertiUrl?: string;
  cantoneseCertiUrl?: string;
}>;

export type withdrawDetailResponseDataPayee = {
  merCustId?: string;
  payeeCtry?: string;
  payeeName?: string;
  swiftCode?: string;
  acctName?: string;
  acctNo?: string;
  bankAddr?: string;
  bankCtry?: string;
  bankName?: string;
};

export type withdrawDetailResponseDataPayer = {
  englishName?: string;
  chineseName?: string;
  businessAddress?: string;
  businessCity?: string;
  businessProv?: string;
  mainBusinessAddress?: string;
  transactionPurposeLevel1?: string;
  transactionPurposeLevel2?: string;
  transactionRemarks?: string;
  hasData?: boolean;
};

export type withdrawDetailResponseDataBasic = {
  respCode?: string;
  respDesc?: string;
  traceId?: string;
  id?: number;
  sysSeqId?: string;
  sysDate?: string;
  merCustId?: string;
  userCustId?: string;
  userId?: string;
  transType?: string;
  transAmt?: number;
  transCurrency?: string;
  feeAmt?: number;
  feeCurrency?: string;
  feeFlag?: string;
  calcMode?: string;
  realAmt?: number;
  realCurrency?: string;
  transStat?: string;
  createTime?: string;
  updateTime?: string;
  extension1?: string;
  extension2?: string;
  extension3?: string;
  fileUrl?: string;
  merCustName?: string;
};

export type rechargeDetailRequestData = {
  sysSeqId: string;
};

export type rechargeDetailResponseData = ApiResponseData<{
  basic: withdrawDetailResponseDataBasic;
  payee: withdrawDetailResponseDataPayee;
  chineseCertiUrl: string;
  englishCertiUrl: string;
  cantoneseCertiUrl: string;
}>;
