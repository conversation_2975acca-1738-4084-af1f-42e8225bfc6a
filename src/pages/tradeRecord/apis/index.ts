import type * as record from './type';
import { request } from '@/http/axios';

/** 查询交易记录 */
export function transLogApi(data: record.transLogRequestData) {
  return request<record.transLogResponseData>({
    url: 'transLog/query',
    method: 'post',
    data,
  });
}

/** 查询交易流水 */
export function transFlowApi(data: record.transFlowRequestData) {
  return request<record.transFlowResponseData>({
    url: 'acctTransLog/query',
    method: 'post',
    data,
  });
}

/** 查询交易详情 */
export function queryTransDetailApi(data: record.transDetailRequestData) {
  return request<record.transDetailResponseData>({
    url: 'transLog/detail',
    method: 'post',
    data,
  });
}

/** 查询账户所有明细 */
export function queryWalletAcctApi() {
  return request<record.walletAcctResponseData>({
    url: 'walletAcct/queryAll',
    method: 'post',
  });
}

/** 查询提现详情 */
export function queryWithdrawDetailApi(data: record.withdrawDetailRequestData) {
  return request<record.withdrawDetailResponseData>({
    url: 'transLog/WithdrawDetail',
    method: 'post',
    data,
  });
}

/** 查询充值详情 */
export function queryRechargeDetailApi(data: record.rechargeDetailRequestData) {
  return request<record.rechargeDetailResponseData>({
    url: 'transLog/RechargeDetail',
    method: 'post',
    data,
  });
}
