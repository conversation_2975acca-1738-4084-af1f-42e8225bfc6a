<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { BusinessEnumType } from '@@/apis/common/type';
import { transLogApi, transLogExport } from './apis';
import { transLogResponseList } from './apis/type';
import { formatNumber } from '@@/utils/math';
import moment from 'moment';
import TransactionDrawer from './components/flowDetail.vue';
import FxDrawer from './components/fxDetail.vue';

const showDrawer = ref(false);
const showFxDrawer = ref(false);
const transactionData = ref<transLogResponseList>();
const { t, locale } = useI18n();
const enumStore = useEnumStore();
const form = ref({
  transType: [],
  transCurrency: [],
  transStat: [],
  pageNum: 1,
  pageSize: 10,
  dateRange: [],
  cryptoNet: '',
});

const total = ref(0);
const loading = ref(false);
const tableData = ref<transLogResponseList[]>([]);

const shortcuts = computed(() => [
  {
    text: t('tradeRecord.shortcuts.day1'),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate());
      start.setHours(0, 0, 0, 0);
      return [start, end];
    },
  },
  {
    text: t('tradeRecord.shortcuts.day7'),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 6);
      start.setHours(0, 0, 0, 0);
      return [start, end];
    },
  },
  {
    text: t('tradeRecord.shortcuts.day30'),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 29);
      start.setHours(0, 0, 0, 0);
      return [start, end];
    },
  },
]);
const tradeTypeList = enumStore.getEnumList(BusinessEnumType.TRADE_TYPE_MER_CONSOLE);
const currencyList = enumStore.getEnumList(BusinessEnumType.CCY);
const statusList = enumStore.getEnumList(BusinessEnumType.TRADE_STATUS);
const networkList = enumStore.getEnumList(BusinessEnumType.CRYPTO_NET);
const tradeTypeMap = ref<Map<string, string>>(new Map());

onMounted(async () => {
  getList();
});

watch(
  tradeTypeList,
  (newList) => {
    newList.forEach((item) => {
      tradeTypeMap.value.set(item.enumCode, item.enumDescCn);
    });
  },
  { immediate: true }
);
const transAmtWidth = ref(0);
const feeAmtWidth = ref(0);
const realAmtWidth = ref(0);
const getList = async () => {
  loading.value = true;
  // 将日期转换为时间戳
  const startTime =
    form.value.dateRange && form.value.dateRange[0]
      ? moment(form.value.dateRange[0]).format('YYYY-MM-DD HH:mm:ss')
      : '';
  const endTime =
    form.value.dateRange && form.value.dateRange[1]
      ? moment(form.value.dateRange[1]).format('YYYY-MM-DD HH:mm:ss')
      : '';

  try {
    const res = await transLogApi({
      transType: form.value.transType?.join(','),
      transCurrency: form.value.transCurrency?.join(','),
      transStat: form.value.transStat?.join(','),
      startTime,
      endTime,
      pageNum: form.value.pageNum,
      pageSize: form.value.pageSize,
      cryptoNet: form.value.cryptoNet,
    });
    tableData.value = res.data.transLogResponseList;
    total.value = res.data.total;

    tableData.value.forEach((item) => {
      item.transAmt =
        item.transType === 'CRYPTO_RECHARGE' || item.transType === 'FIAT_RECHARGE'
          ? item.realAmt
          : item.transAmt;
    });
    // 计算金额列宽度
    const maxTransAmt = Math.max(...tableData.value.map((item) => item.transAmt));
    transAmtWidth.value =
      Math.trunc(maxTransAmt).toString().length * 7.6 +
      Math.trunc((Math.trunc(maxTransAmt).toString().length - 1) / 3) * 6.42;
    // 计算手续费列宽度
    const maxFeeAmt = Math.max(...tableData.value.map((item) => item.feeAmt));
    feeAmtWidth.value =
      Math.trunc(maxFeeAmt).toString().length * 7.6 +
      Math.trunc((Math.trunc(maxFeeAmt).toString().length - 1) / 3) * 6.42;
    // 计算入账金额列宽度
    const maxRealAmt = Math.max(...tableData.value.map((item) => item.realAmt));
    realAmtWidth.value =
      Math.trunc(maxRealAmt).toString().length * 7.6 +
      Math.trunc((Math.trunc(maxRealAmt).toString().length - 1) / 3) * 6.42;
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 下载
const download = async () => {
  // 将日期转换为时间戳
  const startTime =
    form.value.dateRange && form.value.dateRange[0]
      ? moment(form.value.dateRange[0]).format('YYYY-MM-DD HH:mm:ss')
      : '';
  const endTime =
    form.value.dateRange && form.value.dateRange[1]
      ? moment(form.value.dateRange[1]).format('YYYY-MM-DD HH:mm:ss')
      : '';
  try {
    const res = await transLogExport({
      transType: form.value.transType?.join(','),
      transCurrency: form.value.transCurrency?.join(','),
      transStat: form.value.transStat?.join(','),
      startTime,
      endTime,
      cryptoNet: form.value.cryptoNet,
    });
    if (locale.value === 'zh-CN') {
      window.open(res.data.fileUrlCn);
    } else if (locale.value === 'en-US') {
      window.open(res.data.fileUrlEn);
    } else {
      window.open(res.data.fileUrlHk);
    }
  } catch (error) {
    console.error(error);
  }
};

// 重置
const handleReset = () => {
  form.value = {
    transType: [],
    transCurrency: [],
    transStat: [],
    pageNum: 1,
    pageSize: 10,
    dateRange: [],
    cryptoNet: '',
  };
};

const handleSizeChange = (val: number) => {
  form.value.pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  form.value.pageNum = val;
  getList();
};

const getStatusClass = (status: string) => {
  const params = statusList.value.filter((item) => {
    if (item.enumCode === status) {
      return item;
    }
  });
  switch (status) {
    case 'I':
      return { color: '#61555A', bgColor: '#F9F9F9', name: params[0].enumDescCn };
    case 'P':
      return { color: '#007BFF', bgColor: '#EBF6FF', name: params[0].enumDescCn };
    case 'S':
      return { color: '#3EB342', bgColor: '#EBF7EB', name: params[0].enumDescCn };
    case 'F':
      return { color: '#FD3627', bgColor: '#FFF1EC', name: params[0].enumDescCn };
    default:
      return { color: '#D18801', bgColor: '#FFFCEB', name: params[0].enumDescCn };
  }
};

const detail = (data: transLogResponseList) => {
  transactionData.value = data;
  if (data.transType === 'FX') {
    // 换汇类型详情
    showFxDrawer.value = true;
  } else {
    // 其他
    showDrawer.value = true;
  }
};
</script>

<template>
  <div class="app-container min-w-1000px pb-140px">
    <h1
      class="text-28px text-[#222527] font-600 font-family-[PingFangSC-Semibold] m-0 mb-24px leading-[40px]"
    >
      {{ t('tradeRecord.title') }}
    </h1>
    <el-form :inline="true" :model="form" class="demo-form-inline" hide-required-asterisk>
      <el-form-item class="w-362px mr-16px">
        <el-select
          class="w-100%"
          multiple
          v-model="form.transType"
          clearable
          :placeholder="t('tradeRecord.pleaseSelect')"
        >
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('tradeRecord.transactionType')
            }}</span>
          </template>
          <el-option
            v-for="info in tradeTypeList"
            :key="info.enumCode"
            :label="info.enumDescCn"
            :value="info.enumCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="w-362px mr-16px">
        <el-select
          class="w-100%"
          multiple
          v-model="form.transCurrency"
          clearable
          :placeholder="t('tradeRecord.pleaseSelect')"
        >
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('tradeRecord.transactionCurrency')
            }}</span>
          </template>
          <el-option
            v-for="info in currencyList"
            :key="info.enumCode"
            :label="info.enumDescCn"
            :value="info.enumCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="w-362px mr-16px">
        <el-select
          class="w-100%"
          multiple
          v-model="form.transStat"
          clearable
          :placeholder="t('tradeRecord.pleaseSelect')"
        >
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('tradeRecord.transactionStatus')
            }}</span>
          </template>
          <el-option
            v-for="info in statusList"
            :key="info.enumCode"
            :label="info.enumDescCn"
            :value="info.enumCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="w-362px mr-16px">
        <el-select
          class="w-100%"
          v-model="form.cryptoNet"
          clearable
          :placeholder="t('tradeRecord.pleaseSelect')"
        >
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('tradeRecord.network')
            }}</span>
          </template>
          <el-option
            v-for="info in networkList"
            :key="info.enumCode"
            :label="info.enumDescCn"
            :value="info.enumCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="w-740px mr-16px">
        <div class="date-picker-with-prefix">
          <span
            class="date-prefix text-[#6B7275] text-14px mr-8px font-family-[PingFangSC-Regular]"
            >{{ t('tradeRecord.transactionTime') }}</span
          >
          <el-date-picker
            v-model="form.dateRange"
            type="datetimerange"
            :shortcuts="shortcuts"
            range-separator=""
            clearable
            :start-placeholder="t('tradeRecord.startTime')"
            :end-placeholder="t('tradeRecord.endTime')"
          />
        </div>
      </el-form-item>
      <el-form-item>
        <el-button
          @click="handleReset"
          class="w-76px text-14px text-[#222527] font-family-[PingFangSC-Regular] reset-btn btn-hover-scale-sm"
        >
          <SvgIcon name="icon-reset" class="mr-9px" />
          {{ t('tradeRecord.reset') }}
        </el-button>
        <el-button
          type="primary"
          @click="getList"
          class="w-76px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm"
        >
          <SvgIcon name="icon-query" class="mr-9px" />
          {{ t('tradeRecord.query') }}
        </el-button>
      </el-form-item>
    </el-form>

    <div class="w-100%">
      <div class="flex justify-between">
        <p class="text-14px font-400 text-[#222527] mb-20px">
          {{ t('tradeRecord.total', { total }) }}
        </p>
        <ElButton
          @click="download"
          class="text-14px text-[#222527] font-family-[PingFangSC-Regular] reset-btn btn-hover-scale-sm"
        >
          <SvgIcon name="icon-download" class="mr-9px" />
          {{ t('tradeRecord.export') }}
        </ElButton>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        row-key="sysSeqId"
        header-cell-class-name="!bg-[#F8F9FA] font-family-[PingFangSC-Semibold] !text-12px !text-[#6B7275]"
        :header-row-style="{ height: '44px' }"
        :row-style="{ height: '48px' }"
      >
        <template #empty>
          <div class="empty-table">
            <p>{{ t('tradeRecord.noData') }}</p>
          </div>
        </template>
        <el-table-column type="index" :label="t('tradeRecord.index')" fixed width="50" />
        <el-table-column
          prop="sysSeqId"
          :label="t('tradeRecord.transactionFlow')"
          min-width="120"
        />
        <el-table-column :label="t('tradeRecord.createTime')" width="180">
          <template #default="{ row }">
            {{ moment(row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column :label="t('tradeRecord.completeTime')" width="180">
          <template #default="{ row }">
            {{ row.completeTime ? moment(row.completeTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </template>
        </el-table-column>
        <el-table-column :label="t('tradeRecord.transactionType')" width="140">
          <template #default="{ row }">
            {{ tradeTypeMap.get(row.transType) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('tradeRecord.network')" width="90">
          <template #default="{ row }"> {{ row.cryptoNetwork }} </template>
        </el-table-column>
        <el-table-column :label="t('tradeRecord.transactionStatus')" width="140">
          <template #default="{ row }">
            <div
              class="flex items-center leading-24px w-fit px-12px rounded-12px text-12px"
              :style="{
                backgroundColor: getStatusClass(row.transStat).bgColor,
                color: getStatusClass(row.transStat).color,
              }"
            >
              {{ getStatusClass(row.transStat).name }}

              <el-tooltip placement="top" v-if="row.transStat === 'F'">
                <template #content>
                  <p style="margin: 0">{{ t('tradeRecord.failureReason') }}：{{ row.respDesc }}</p>
                </template>
                <svg-icon name="icon-info" class="text-12px color-[#C49191] ml-4px" />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="transAmt" align="left" min-width="160">
          <template #header>
            <div style="display: inline-flex; align-items: center; gap: 4px">
              <span>{{ t('tradeRecord.transactionAmount') }}</span>
              <el-tooltip placement="top">
                <template #content>
                  <p style="margin: 0">{{ t('tradeRecord.tooltips.transactionAmount.deposit') }}</p>
                  <p style="margin: 0">
                    {{ t('tradeRecord.tooltips.transactionAmount.withdrawal') }}
                  </p>
                  <p style="margin: 0">
                    {{ t('tradeRecord.tooltips.transactionAmount.exchange') }}
                  </p>
                </template>
                <svg-icon name="icon-info" class="text-12px color-[#A7ADB0]" />
              </el-tooltip>
            </div>
          </template>
          <template #default="{ row }">
            <div class="flex font-ftdin">
              <div class="flex text-right">
                <span class="block text-right" :style="`width: ${transAmtWidth}px`">{{
                  formatNumber(row.transAmt, 2).toString().split('.')[0]
                }}</span>
                <span class="block text-left shrink-0"
                  >.{{
                    row.transCurrency === 'USD'
                      ? row.transAmt.toFixed(2).toString().split('.')[1]
                      : row.transAmt.toFixed(6).toString().split('.')[1]
                  }}
                </span>
              </div>

              <span class="ml-2px">{{ row.transCurrency }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="feeAmt"
          :label="t('tradeRecord.feeAmount')"
          align="left"
          min-width="160"
        >
          <template #default="{ row }">
            <!-- 非换汇类型 -->
            <div class="flex font-ftdin" v-if="row.transType !== 'FX'">
              <div class="flex text-right">
                <span class="block text-right" :style="`width: ${feeAmtWidth + 10}px`"
                  >{{ row.feeAmt > 0 ? '-' : ''
                  }}{{ formatNumber(row.feeAmt, 2).toString().split('.')[0] }}</span
                >
                <span class="block text-left shrink-0"
                  >.{{
                    row.feeCurrency === 'USD'
                      ? row.feeAmt.toFixed(2).toString().split('.')[1]
                      : row.feeAmt.toFixed(6).toString().split('.')[1]
                  }}
                </span>
              </div>
              <span class="ml-2px">{{ row.feeCurrency }}</span>
            </div>
            <!-- 换汇类型没有手续费金额 -->
            <div class="flex font-ftdin" v-else>--</div>
          </template>
        </el-table-column>

        <el-table-column prop="realAmt" align="left" min-width="160">
          <template #header>
            <div style="display: inline-flex; align-items: center; gap: 4px">
              <span>{{ t('tradeRecord.accountingAmount') }}</span>
              <el-tooltip placement="top">
                <template #content>
                  <p style="margin: 0">{{ t('tradeRecord.tooltips.accountingAmount.deposit') }}</p>
                  <p style="margin: 0">
                    {{ t('tradeRecord.tooltips.accountingAmount.withdrawal') }}
                  </p>
                  <p style="margin: 0">{{ t('tradeRecord.tooltips.accountingAmount.exchange') }}</p>
                </template>
                <svg-icon name="icon-info" class="text-12px color-[#A7ADB0]" />
              </el-tooltip>
            </div>
          </template>
          <template #default="{ row }">
            <div class="flex font-ftdin">
              <div class="flex text-right">
                <span class="block text-right" :style="`width: ${realAmtWidth}px`">{{
                  formatNumber(row.realAmt, 2).toString().split('.')[0]
                }}</span>
                <span class="block text-left shrink-0"
                  >.{{
                    row.realCurrency === 'USD'
                      ? row.realAmt.toFixed(2).toString().split('.')[1]
                      : row.realAmt.toFixed(6).toString().split('.')[1]
                  }}
                </span>
              </div>
              <span class="ml-2px">{{ row.realCurrency }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column :label="t('tradeRecord.feeDeductionMethod')" min-width="140">
          <template #default="{ row }">
            <!-- 非换汇类型 -->
            <div v-if="row.transType !== 'FX'">
              {{
                row.feeFlag === 'I'
                  ? t('tradeRecord.feeTypes.internal')
                  : t('tradeRecord.feeTypes.external')
              }}
            </div>
            <!-- 换汇类型没有手续费扣除方式 -->
            <div v-else>--</div>
          </template>
        </el-table-column>

        <el-table-column :label="t('globalAccount.table.operation')" width="90" fixed="right">
          <template #default="{ row }">
            <ElButton
              v-if="row.transType === 'FIAT_WITHDRAW' || row.transType === 'FIAT_RECHARGE' || row.transType === 'FX'"
              link
              type="primary"
              @click="detail(row)"
              style="color: #030814"
              class="btn-hover-scale-sm"
            >
              <SvgIcon name="icon-open" />
            </ElButton>
          </template>
        </el-table-column>
      </el-table>

      <div class="flex justify-end mt-24px">
        <el-pagination
          v-model:current-page="form.pageNum"
          v-model:page-size="form.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          size="small"
          layout="prev,pager,next,sizes,jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>

    <TransactionDrawer
      v-model:visible="showDrawer"
      :data="transactionData"
      :statusList="statusList"
    />

    <!-- 换汇详情 -->
    <FxDrawer
      v-model:visible="showFxDrawer"
      :data="transactionData"
      :statusList="statusList"
    />
  </div>
</template>

<style lang="scss" scoped>
.content-open .app-container {
  width: calc(100vw - 240px);
}

.content-close .app-container {
  width: calc(100vw - 88px);
}
.date-picker-with-prefix {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding-left: 12px;
}

.date-picker-with-prefix:hover {
  border-color: var(--el-border-color-hover);
}

.date-picker-with-prefix .el-date-editor {
  border: none;
  flex: 1;
}

.date-picker-with-prefix :deep(.el-input__wrapper) {
  box-shadow: none !important;
}

.date-picker-with-prefix :deep(.el-range-input) {
  text-align: left;
}

.date-picker-with-prefix :deep(.el-range__icon) {
  display: none;
}

:deep(.el-table) {
  --el-table-header-bg-color: #f8f9fa;
  --el-table-header-text-color: #6b7275;

  .el-table__header-wrapper {
    border-radius: 12px;
    overflow: hidden;
    tr {
      --el-table-border: none;
      .el-table__cell {
        padding: 10px 0;
      }
    }
  }

  tbody tr {
    .el-table__cell {
      padding: 12px 0;
    }
  }
}

.status-dot {
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}
.reset-btn:hover {
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
}
</style>
