export default {
  tradeRecord: {
    noData: 'No data',
    flow: {
      title: 'Account Flow',
      accountingAmount: 'Bookkeeping Amount',
    },
    accountingFlow: 'Accounting Flow No.',
    accountingType: 'Accounting Type',
    accountingCurrency: 'Accounting Currency',
    accountingTime: 'Accounting Time',
    accountingAmount: 'Transaction Amount',
    accountBalance: 'Account Balance',
    export: 'Download',
    accountingDescription: 'Accounting Description',
    title: 'Transaction Flow',
    total: 'Total {total} items',
    transactionType: 'Transaction Type',
    transactionCurrency: 'Transaction Currency',
    transactionStatus: 'Transaction Status',
    transactionTime: 'Transaction Time',
    createTime: 'Created Time',
    completeTime: 'Completed Time',
    network: 'Blockchain',
    startTime: 'Start Time',
    endTime: 'End Time',
    reset: 'Reset',
    query: 'Query',
    pleaseSelect: 'Please select',
    index: 'No.',
    transactionFlow: 'Transaction ID',
    transactionAmount: 'Initiate amount',
    feeAmount: 'Fee Amount',
    feeDeductionMethod: 'Fee Deduction Method',
    depositAmount: 'Deposit Amount',
    failureReason: 'Failure Reason',
    jumpTo: 'Jump to',
    statusTypes: {
      accepted: 'Accepted',
      processing: 'Processing',
      success: 'Success',
      failed: 'Failed',
    },
    feeTypes: {
      internal: 'Internal',
      external: 'External',
    },
    tooltips: {
      transactionAmount: {
        deposit: 'Deposit: Deposit initiation amount',
        withdrawal: 'Withdrawal: Withdrawal initiation amount',
        exchange: 'Conversion: Conversion-out amount',
      },
      accountingAmount: {
        deposit: 'Deposit: Deposit amount received',
        withdrawal: 'Withdrawal: Withdrawal amount received',
        exchange: 'Conversion: Conversion-in amount',
      },
    },
    shortcuts: {
      day1: 'Last 1 day',
      day7: 'Last 7 days',
      day30: 'Last 30 days',
    },
    transDetail: {
      title: 'Transaction Details',
      fxTitle: 'Foreign Exchange Details',
      transactionFlow: 'Transaction No',
      transactionTime: 'Transaction Time',
      transactionType: 'Transaction Type',
      transactionAmount: 'Requested Amount',
      exchangeOutAmount: "Exchange-out Amount",
      exchangeInAmount: "Exchange-in Amount",
      network: 'Blockchain Network',
      currencyPair: "Currency Pair",
      exchangeRate: "Exchange Rate",
      currencyPairTip: "The format of the currency pair is [base currency][reference currency], and the value of the exchange rate represents how many reference currency is equivalent to 1 unit of base currency",
      feeAmount: 'Fee Amount',
      accountingAmount: 'Accounting Amount',
      transactionStatus: 'Transaction Status',
      close: 'Close',
      failureReason: 'Failure Reason: ',
      basicInfo: 'Basic Information',
      transType: 'Transaction Type',
      sysSeqId: 'Transaction NO',
      transStat: 'Transaction Status',
      transAmt: 'Requested Amount',
      feeAmt: 'Fee Amount',
      realAmt: 'Transaction Amount',
      createTime: 'Created Time',
      updateTime: 'Completed Time',
      payerInfo: 'Payer Detail',
      payerName: 'Payer Name',
      streetAddr: 'Address',
      city: 'City',
      province: 'Province/State',
      country: 'Country/Region',
      paymentReason: 'Reason',
      remark: 'Reference',
      payeeInfo: 'Receipt Detail',
      payeeName: 'Recipient Name',
      bankName: 'Bank Name',
      bankAccount: 'Bank Account Number',
      bankAddr: 'Bank Address',
      swiftCode: 'Swift Code',
      downloadCerti: 'Download Certificate',
      viewSupplement: 'View Transaction Supplement',
    },
  },
};
