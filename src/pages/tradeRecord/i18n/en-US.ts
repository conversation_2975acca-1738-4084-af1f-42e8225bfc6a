export default {
  tradeRecord: {
    noData: 'No data',
    flow: {
      title: 'Account Flow',
      accountingAmount: 'Bookkeeping Amount',
    },
    accountingFlow: 'Accounting Flow No.',
    accountingType: 'Accounting Type',
    accountingCurrency: 'Accounting Currency',
    accountingTime: 'Accounting Time',
    accountingAmount: 'Transaction Amount',
    accountBalance: 'Account Balance',
    accountingDescription: 'Accounting Description',
    title: 'Transaction Flow',
    total: 'Total {total} items',
    transactionType: 'Transaction Type',
    transactionCurrency: 'Transaction Currency',
    transactionStatus: 'Transaction Status',
    transactionTime: 'Creation Time',
    completeTime: 'Completion Time',
    startTime: 'Start Time',
    endTime: 'End Time',
    reset: 'Reset',
    query: 'Query',
    pleaseSelect: 'Please select',
    index: 'No.',
    transactionFlow: 'Transaction ID',
    transactionAmount: 'Initiate amount',
    feeAmount: 'Fee Amount',
    feeDeductionMethod: 'Fee Deduction Method',
    depositAmount: 'Deposit Amount',
    failureReason: 'Failure Reason',
    jumpTo: 'Jump to',
    statusTypes: {
      accepted: 'Accepted',
      processing: 'Processing',
      success: 'Success',
      failed: 'Failed',
    },
    feeTypes: {
      internal: 'Internal',
      external: 'External',
    },
    tooltips: {
      transactionAmount: {
        deposit: 'Deposit: Deposit initiation amount',
        withdrawal: 'Withdrawal: Withdrawal initiation amount',
        exchange: 'Exchange: Exchange-out amount',
      },
      accountingAmount: {
        deposit: 'Deposit: Deposit amount received',
        withdrawal: 'Withdrawal: Withdrawal amount received',
        exchange: 'Exchange: Exchange-in amount',
      },
    },
    shortcuts: {
      day1: 'Last 1 day',
      day7: 'Last 7 days',
      day30: 'Last 30 days',
    },
    transDetail: {
      title: 'Transaction Details',
      transactionFlow: 'Transaction ID',
      transactionTime: 'Transaction Time',
      transactionType: 'Transaction Type',
      transactionAmount: 'Initiate amount',
      feeAmount: 'Fee Amount',
      accountingAmount: 'Accounting Amount',
      transactionStatus: 'Transaction Status',
      close: 'Close',
      failureReason: 'Failure Reason: ',
      basicInfo: 'Basic Information',
      transType: 'Transaction Type',
      sysSeqId: 'Transaction ID',
      transStat: 'Transaction Status',
      transAmt: 'Initiated Amount',
      feeAmt: 'Fee Amount',
      realAmt: 'Transaction Amount',
      createTime: 'Creation Time',
      updateTime: 'Completion Time',
      payerInfo: 'Payer Information',
      payerName: 'Payer Name',
      streetAddr: 'Street Address',
      city: 'City',
      province: 'Province/State',
      country: 'Country/Region',
      paymentReason: 'Payment Reason',
      remark: 'Remark',
      payeeInfo: 'Payee Information',
      payeeName: 'Payee Name',
      bankName: 'Bank Name',
      bankAccount: 'Bank Account',
      bankAddr: 'Bank Address',
      swiftCode: 'Swift Code',
      downloadCerti: 'Download Certificate',
      viewSupplement: 'View Transaction Supplement',
    },
  },
};
