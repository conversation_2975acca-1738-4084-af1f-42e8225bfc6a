export default {
  tradeRecord: {
    noData: '暂无数据',
    title: '交易流水',
    total: '共{total}条',
    transactionType: '交易类型',
    transactionCurrency: '交易币种',
    transactionStatus: '交易状态',
    transactionTime: '交易时间',
    createTime: '创建时间',
    completeTime: '完成时间',
    network: '区块链',
    export: '下载',
    startTime: '开始时间',
    endTime: '结束时间',
    reset: '重置',
    query: '查询',
    pleaseSelect: '请选择',
    index: '序号',
    transactionFlow: '交易流水',
    transactionAmount: '发起金额',
    feeAmount: '手续费金额',
    accountingAmount: '交易金额',
    feeDeductionMethod: '手续费扣费方式',
    depositAmount: '充值金额',
    failureReason: '失败原因',
    jumpTo: '跳至',
    statusTypes: {
      accepted: '已受理',
      processing: '处理中',
      success: '成功',
      failed: '失败',
    },
    feeTypes: {
      internal: '内扣',
      external: '外扣',
    },
    tooltips: {
      transactionAmount: {
        deposit: '充值：充值发起金额',
        withdrawal: '提现：提现发起金额',
        exchange: '兑换：换出金额',
      },
      accountingAmount: {
        deposit: '充值：充值入账金额',
        withdrawal: '提现：提现付出金额',
        exchange: '兑换：换入金额',
      },
    },
    shortcuts: {
      day1: '近1天',
      day7: '近7天',
      day30: '近30天',
    },
    flow: {
      title: '账户流水',
      accountingAmount: '记账金额',
    },
    accountingFlow: '记账流水号',
    accountingType: '记账类型',
    accountingCurrency: '记账币种',
    accountingTime: '记账时间',
    accountBalance: '账户余额',
    accountingDescription: '记账说明',
    transDetail: {
      title: '交易详情',
      fxTitle: '换汇详情',
      transactionFlow: '交易流水',
      transactionTime: '交易时间',
      transactionType: '交易类型',
      transactionAmount: '发起金额',
      exchangeOutAmount: "换出金额",
      exchangeInAmount: "换入金额",
      network: '区块链网络',
      currencyPair: "币种对",
      exchangeRate: "汇率",
      currencyPairTip: "币种对的格式为 [基准货币][参考货币]，汇率的值表示每1单位基准货币对应多少参考货币",
      feeAmount: '手续费金额',
      accountingAmount: '入账金额',
      transactionStatus: '交易状态',
      close: '关闭',
      failureReason: '失败原因：',
      basicInfo: '基本信息',
      transType: '交易类型',
      sysSeqId: '交易流水号',
      transStat: '交易状态',
      transAmt: '发起金额',
      feeAmt: '手续费金额',
      realAmt: '交易金额',
      createTime: '创建时间',
      updateTime: '完成时间',
      payerInfo: '付款方信息',
      payerName: '付款方名称',
      streetAddr: '街道地址',
      city: '城市',
      province: '省/州',
      country: '国家/地区',
      paymentReason: '付款原因',
      remark: '附言',
      payeeInfo: '收款方信息',
      payeeName: '收款方名称',
      bankName: '银行名称',
      bankAccount: '银行账号',
      bankAddr: '银行地址',
      swiftCode: 'Swift代码',
      downloadCerti: '凭证下载',
      viewSupplement: '查看交易补充材料',
    },
  },
};
