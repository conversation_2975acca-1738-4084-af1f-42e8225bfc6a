export default {
  tradeRecord: {
    noData: '暫無數據',
    title: '交易流水',
    total: '共{total}條',
    transactionType: '交易類型',
    transactionCurrency: '交易幣種',
    transactionStatus: '交易狀態',
    transactionTime: '創建時間',
    completeTime: '完成時間',
    startTime: '開始時間',
    endTime: '結束時間',
    reset: '重置',
    query: '查詢',
    pleaseSelect: '請選擇',
    index: '序號',
    transactionFlow: '交易流水',
    transactionAmount: '發起金額',
    feeAmount: '手續費金額',
    accountingAmount: '交易金額',
    feeDeductionMethod: '手續費扣費方式',
    depositAmount: '充值金額',
    failureReason: '失敗原因',
    jumpTo: '跳至',
    statusTypes: {
      accepted: '已受理',
      processing: '處理中',
      success: '成功',
      failed: '失敗',
    },
    feeTypes: {
      internal: '內扣',
      external: '外扣',
    },
    tooltips: {
      transactionAmount: {
        deposit: '充值：充值發起金額',
        withdrawal: '提現：提現發起金額',
        exchange: '兌換：換出金額',
      },
      accountingAmount: {
        deposit: '充值：充值入賬金額',
        withdrawal: '提現：提現付出金額',
        exchange: '兌換：換入金額',
      },
    },
    shortcuts: {
      day1: '近1天',
      day7: '近7天',
      day30: '近30天',
    },
    flow: {
      title: '賬戶流水',
      accountingAmount: '記帳金額',
    },
    accountingFlow: '會計流水號',
    accountingType: '會計類型',
    accountingCurrency: '會計幣種',
    accountingTime: '會計時間',
    accountBalance: '賬戶餘額',
    accountingDescription: '會計說明',
    transDetail: {
      title: '交易詳情',
      transactionFlow: '交易流水',
      transactionTime: '交易時間',
      transactionType: '交易類型',
      transactionAmount: '發起金額',
      feeAmount: '手續費金額',
      accountingAmount: '入賬金額',
      transactionStatus: '交易狀態',
      close: '關閉',
      failureReason: '失敗原因：',
      basicInfo: '基本信息',
      transType: '交易類型',
      sysSeqId: '交易流水號',
      transStat: '交易狀態',
      transAmt: '發起金額',
      feeAmt: '手續費金額',
      realAmt: '交易金額',
      createTime: '創建時間',
      updateTime: '完成時間',
      payerInfo: '付款方信息',
      payerName: '付款方名稱',
      streetAddr: '街道地址',
      city: '城市',
      province: '省/州',
      country: '國家/地區',
      paymentReason: '付款原因',
      remark: '附言',
      payeeInfo: '收款方信息',
      payeeName: '收款方名稱',
      bankName: '銀行名稱',
      bankAccount: '銀行賬號',
      bankAddr: '銀行地址',
      swiftCode: 'Swift編號',
      downloadCerti: '憑證下載',
      viewSupplement: '查看交易補充材料',
    },
  },
};
