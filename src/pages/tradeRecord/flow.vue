<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { BusinessEnumType } from '@@/apis/common/type';
import { transFlowApi, queryTransDetailApi } from './apis';
import { transFlowResponseList } from './apis/type';
import TransactionDrawer from './components/transDetail.vue';
import moment from 'moment';
import { formatNumber } from '@@/utils/math';

const balanceWidth = ref(0);
const transAmountWidth = ref(0);
const { t } = useI18n();
const enumStore = useEnumStore();
const form = ref({
  transType: [],
  transCurrency: [],
  transStat: [],
  pageNum: 1,
  pageSize: 10,
  dateRange: [],
});

const total = ref(0);
const loading = ref(false);
const tableData = ref<transFlowResponseList[]>([]);

const showDrawer = ref(false);
const transactionData = ref({});
const shortcuts = computed(() => [
  {
    text: t('tradeRecord.shortcuts.day1'),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate());
      start.setHours(0, 0, 0, 0);
      return [start, end];
    },
  },
  {
    text: t('tradeRecord.shortcuts.day7'),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 6);
      start.setHours(0, 0, 0, 0);
      return [start, end];
    },
  },
  {
    text: t('tradeRecord.shortcuts.day30'),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 29);
      start.setHours(0, 0, 0, 0);
      return [start, end];
    },
  },
]);
const tradeTypeList = enumStore.getEnumList(BusinessEnumType.TRANS_TYPE);
const currencyList = enumStore.getEnumList(BusinessEnumType.CCY);
const tradeTypeMap = ref<Map<string, string>>(new Map());

onMounted(async () => {
  getList();
});

watch(
  tradeTypeList,
  (newList) => {
    newList.forEach((item) => {
      tradeTypeMap.value.set(item.enumCode, item.enumDescCn);
    });
  },
  { immediate: true }
);

const getList = async () => {
  loading.value = true;
  // 将日期转换为时间戳
  const startTime =
    form.value.dateRange && form.value.dateRange[0]
      ? moment(form.value.dateRange[0]).format('YYYY-MM-DD HH:mm:ss')
      : '';
  const endTime =
    form.value.dateRange && form.value.dateRange[1]
      ? moment(form.value.dateRange[1]).format('YYYY-MM-DD HH:mm:ss')
      : '';

  try {
    const res = await transFlowApi({
      transTypeList: form.value.transType,
      currencyList: form.value.transCurrency,
      startTime,
      endTime,
      pageNum: form.value.pageNum,
      pageSize: form.value.pageSize,
    });
    tableData.value = res.data.list;
    total.value = res.data.total;
    const maxTransAmt = Math.max(...tableData.value.map((item) => item.transAmount));
    transAmountWidth.value =
      Math.trunc(maxTransAmt).toString().length * 8.44 +
      Math.trunc((Math.trunc(maxTransAmt).toString().length - 1) / 3) * 8.42;
    const maxBalance = Math.max(...tableData.value.map((item) => item.adjustedBalance));
    balanceWidth.value =
      Math.trunc(maxBalance).toString().length * 8.44 +
      Math.trunc((Math.trunc(maxBalance).toString().length - 1) / 3) * 8.42;
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 重置
const handleReset = () => {
  form.value = {
    transType: [],
    transCurrency: [],
    transStat: [],
    pageNum: 1,
    pageSize: 10,
    dateRange: [],
  };
};
// 查询
const handleSearch = () => {
  getList();
};

const handleSizeChange = (val: number) => {
  form.value.pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  form.value.pageNum = val;
  getList();
};

// 交易详情
const handleClickDetail = async (row: any) => {
  const { sysSeqId } = row;
  const { data } = await queryTransDetailApi({ sysSeqId: sysSeqId });
  transactionData.value = data;
  showDrawer.value = true;
};
</script>

<template>
  <div class="app-container w-100% min-w-800px pb-140px">
    <h1
      class="text-28px text-[#222527] font-600 font-family-[PingFangSC-Semibold] m-0 mb-24px leading-[40px]"
    >
      {{ t('tradeRecord.flow.title') }}
    </h1>
    <el-form :inline="true" :model="form" class="demo-form-inline" hide-required-asterisk>
      <el-form-item class="w-362px mr-16px">
        <el-select
          class="w-100%"
          multiple
          v-model="form.transType"
          clearable
          :placeholder="t('tradeRecord.pleaseSelect')"
        >
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('tradeRecord.accountingType')
            }}</span>
          </template>
          <el-option
            v-for="info in tradeTypeList"
            :key="info.enumCode"
            :label="info.enumDescCn"
            :value="info.enumCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="w-362px mr-16px">
        <el-select
          class="w-100%"
          multiple
          v-model="form.transCurrency"
          clearable
          :placeholder="t('tradeRecord.pleaseSelect')"
        >
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('tradeRecord.accountingCurrency')
            }}</span>
          </template>
          <el-option
            v-for="info in currencyList"
            :key="info.enumCode"
            :label="info.enumDescCn"
            :value="info.enumCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="w-740px mr-16px">
        <div class="date-picker-with-prefix">
          <span
            class="date-prefix text-[#6B7275] text-14px mr-8px font-family-[PingFangSC-Regular]"
            >{{ t('tradeRecord.accountingTime') }}</span
          >
          <el-date-picker
            v-model="form.dateRange"
            type="datetimerange"
            :shortcuts="shortcuts"
            range-separator=""
            clearable
            :start-placeholder="t('tradeRecord.startTime')"
            :end-placeholder="t('tradeRecord.endTime')"
          />
        </div>
      </el-form-item>

      <el-form-item>
        <el-button
          @click="handleReset"
          class="w-76px text-14px text-[#222527] font-family-[PingFangSC-Regular] reset-btn btn-hover-scale-sm"
        >
          <SvgIcon name="icon-reset" class="mr-9px" />
          {{ t('tradeRecord.reset') }}
        </el-button>
        <el-button
          type="primary"
          @click="getList"
          class="w-76px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm"
        >
          <SvgIcon name="icon-query" class="mr-9px" />
          {{ t('tradeRecord.query') }}
        </el-button>
      </el-form-item>
    </el-form>

    <div class="w-100%">
      <p class="text-14px font-400 text-[#222527] mb-20px">
        {{ t('tradeRecord.total', { total }) }}
      </p>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        header-cell-class-name="!bg-[#F8F9FA] font-family-[PingFangSC-Semibold] !text-12px !text-[#6B7275]"
        :header-row-style="{ height: '44px' }"
        :row-style="{ height: '48px' }"
      >
        <template #empty>
          <div class="empty-table">
            <p>{{ t('tradeRecord.noData') }}</p>
          </div>
        </template>
        <el-table-column type="index" :label="t('tradeRecord.index')" fixed width="50" />
        <el-table-column prop="transId" :label="t('tradeRecord.accountingFlow')" min-width="140" />
        <el-table-column :label="t('tradeRecord.accountingTime')" width="180">
          <template #default="{ row }">
            {{ row.createTime ? moment(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </el-table-column>
        <el-table-column :label="t('tradeRecord.accountingType')" width="100">
          <template #default="{ row }">
            {{ tradeTypeMap.get(row.transType) }}
          </template>
        </el-table-column>

        <el-table-column :label="t('tradeRecord.accountingCurrency')" width="100">
          <template #default="{ row }">
            {{ row.currency }}
          </template>
        </el-table-column>

        <el-table-column align="left" min-width="140">
          <template #header>
            <div style="display: inline-flex; align-items: center; gap: 4px">
              <span>{{ t('tradeRecord.flow.accountingAmount') }}</span>
            </div>
          </template>
          <template #default="{ row }">
            <div class="flex text-right font-ftdin">
              <span class="block text-right" :style="`width: ${transAmountWidth}px`">{{
                formatNumber(row.transAmount, 2).toString().split('.')[0]
              }}</span>
              <span class="w-50px block text-left shrink-0"
                >.{{
                  row.currency === 'USD'
                    ? row.transAmount.toFixed(2).toString().split('.')[1]
                    : row.transAmount.toFixed(6).toString().split('.')[1]
                }}</span
              >
            </div>
          </template>
        </el-table-column>

        <el-table-column :label="t('tradeRecord.accountBalance')" align="left" min-width="140">
          <template #default="{ row }">
            <div class="flex text-right font-ftdin">
              <span class="block text-right" :style="`width: ${balanceWidth}px`">{{
                formatNumber(row.adjustedBalance, 2).toString().split('.')[0]
              }}</span>
              <span class="w-50px block text-left shrink-0"
                >.{{
                  row.currency === 'USD'
                    ? row.adjustedBalance.toFixed(2).toString().split('.')[1]
                    : row.adjustedBalance.toFixed(6).toString().split('.')[1]
                }}</span
              >
            </div>
          </template>
        </el-table-column>

        <el-table-column :label="t('tradeRecord.transactionFlow')" min-width="240">
          <template #default="{ row }">
            <el-button
              @click="handleClickDetail(row)"
              class="p-0 color-#ff0064"
              text
              type="primary"
              >{{ row.sysSeqId }}</el-button
            >
          </template>
        </el-table-column>

        <el-table-column :label="t('tradeRecord.transactionTime')" min-width="200">
          <template #default="{ row }">
            {{ row.transLogDate ? moment(row.transLogDate).format('YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="feeAmt"
          :label="t('tradeRecord.accountingDescription')"
          min-width="100"
        >
          <template #default="{ row }">
            {{ row.remark }}
          </template>
        </el-table-column>
      </el-table>

      <div class="flex justify-end mt-24px">
        <el-pagination
          v-model:current-page="form.pageNum"
          v-model:page-size="form.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          size="small"
          layout="prev,pager,next,sizes,jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>

    <TransactionDrawer v-model:visible="showDrawer" :data="transactionData" />
  </div>
</template>

<style lang="scss" scoped>
.date-picker-with-prefix {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding-left: 12px;
}

.date-picker-with-prefix:hover {
  border-color: var(--el-border-color-hover);
}

.date-picker-with-prefix .el-date-editor {
  border: none;
  flex: 1;
}

.date-picker-with-prefix :deep(.el-input__wrapper) {
  box-shadow: none !important;
}

.date-picker-with-prefix :deep(.el-range-input) {
  text-align: left;
}

.date-picker-with-prefix :deep(.el-range__icon) {
  display: none;
}

:deep(.el-table) {
  --el-table-header-bg-color: #f8f9fa;
  --el-table-header-text-color: #6b7275;

  .el-table__header-wrapper {
    border-radius: 12px;
    overflow: hidden;
    tr {
      --el-table-border: none;
      .el-table__cell {
        padding: 10px 0;
      }
    }
  }

  tbody tr {
    .el-table__cell {
      padding: 12px 0;
    }
  }
}

.status-dot {
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}
.reset-btn:hover {
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
}
</style>
