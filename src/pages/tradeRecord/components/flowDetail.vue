<template>
  <el-drawer
    v-model="showDrawer"
    size="684"
    style="--el-drawer-padding-primary: 0px"
    :destroy-on-close="true"
    @close="handleClose"
    header-class="drawer-header"
    :show-close="false"
  >
    <template #header>
      <div
        class="h-60px border-b-[1px] border-b-[#E5E6EB] border-b-solid mb-0 flex justify-between flex-row flex-items-center text-18px font-600 color-[#222527] py-18px px-24px"
      >
        <span>{{ $t('tradeRecord.transDetail.title') }}</span>

        <svgIcon
          name="icon-close"
          class="text-16px cursor-pointer color-#6B7275"
          @click="handleClose"
        />
      </div>
    </template>
    <el-form
      label-width="100px"
      label-position="top"
      hide-required-asterisk
      class="transaction-form p-24px pb-70px"
      disabled
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <h4 class="m-0 mb-24px font-600 text-16px color-#222527">
            {{ $t('tradeRecord.transDetail.basicInfo') }}
          </h4>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.transType')">
            <span>{{ tradeTypeMap.get(flowDetail.basic?.transType) }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.sysSeqId')">
            <span>{{ flowDetail.basic.sysSeqId }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.transStat')">
            <div
              class="flex items-center leading-24px w-fit px-12px rounded-12px text-12px"
              :style="{
                backgroundColor: getStatusClass(flowDetail.basic.transStat).bgColor,
                color: getStatusClass(flowDetail.basic.transStat).color,
              }"
            >
              {{ getStatusClass(flowDetail.basic.transStat).name }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.transAmt')">
            <div class="flex text-right font-ftdin">
              <span class="block text-right"
                >{{ formatNumber(flowDetail.basic.transAmt, 2) }}
                {{ flowDetail.basic.transCurrency }}</span
              >
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.feeAmt')">
            <div class="flex text-right font-ftdin">
              <span class="block text-right"
                >{{ formatNumber(flowDetail.basic.feeAmt, 2) }}
                {{ flowDetail.basic.feeCurrency }}</span
              >
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.realAmt')">
            <div class="flex text-right font-ftdin">
              <span class="block text-right"
                >{{ formatNumber(flowDetail.basic.realAmt, 2) }}
                {{ flowDetail.basic.realCurrency }}</span
              >
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.createTime')">
            <div class="flex text-right font-ftdin">
              <span class="block text-right"
                >{{ moment(flowDetail.basic.createTime).format('YYYY-MM-DD HH:mm:ss') }}
              </span>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.updateTime')">
            <div class="flex text-right font-ftdin">
              <span class="block text-right">{{
                flowDetail.basic.completeTime
                  ? moment(flowDetail.basic.completeTime).format('YYYY-MM-DD HH:mm:ss')
                  : '-'
              }}</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24" v-if="props.data.transType === 'FIAT_WITHDRAW'">
        <el-divider />
        <el-col :span="24">
          <h4 class="m-0 mb-24px font-600 text-16px color-#222527">
            {{ $t('tradeRecord.transDetail.payerInfo') }}
          </h4>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.payerName')">
            <span>{{ flowDetail.payer.englishName }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.streetAddr')">
            <span>{{ flowDetail.payer.businessAddress }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.city')">
            <span>{{ flowDetail.payer.businessCity }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.province')">
            <span>{{ flowDetail.payer.businessProv }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.country')">
            <span>{{ flowDetail.payer.mainBusinessAddress }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.paymentReason')">
            <span
              >{{ flowDetail.payer.transactionPurposeLevel1 }}/{{
                flowDetail.payer.transactionPurposeLevel2
              }}</span
            >
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.remark')">
            <span>{{ flowDetail.payer.transactionRemarks }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider />

      <el-row
        :gutter="24"
        v-if="
          (props.data.transType === 'FIAT_RECHARGE' && flowDetail.payee.hasData) ||
          props.data.transType === 'FIAT_WITHDRAW'
        "
      >
        <el-col :span="24">
          <h4 class="m-0 mb-24px font-600 text-16px color-#222527">
            {{ $t('tradeRecord.transDetail.payeeInfo') }}
          </h4>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.payeeName')">
            <span>{{ flowDetail.payee.payeeName }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.country')">
            <span>{{ flowDetail.payee.payeeCtry }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.bankName')">
            <span>{{ flowDetail.payee.acctName || flowDetail.payee.bankName }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.bankAccount')">
            <span>{{ flowDetail.payee.acctNo }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.bankAddr')">
            <span>{{ flowDetail.payee.bankAddr }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.swiftCode')">
            <span>{{ flowDetail.payee.swiftCode }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div
        style="border-top: 1px solid #e5e6eb"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px"
      >
        <el-button
          v-if="flowDetail.basic.transStat === 'S'"
          @click="downloadCerti"
          text
          class="h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px btn-hover-scale-sm"
          >{{ $t('tradeRecord.transDetail.downloadCerti') }}</el-button
        >

        <el-button
          v-if="flowDetail.basic.supplementUrl"
          @click="downloadSupplement"
          text
          class="h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px btn-hover-scale-sm"
          >{{ $t('tradeRecord.transDetail.viewSupplement') }}</el-button
        >

        <el-button
          @click="handleClose"
          text
          class="min-w-68px h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px btn-hover-scale-sm"
          >{{ $t('tradeRecord.transDetail.close') }}</el-button
        >
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { BusinessEnumType } from '@@/apis/common/type';
import { queryWithdrawDetailApi, queryRechargeDetailApi } from '../apis';
import moment from 'moment';
import { formatNumber } from '@@/utils/math';
const { t, locale } = useI18n();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});
import { useEnumStore } from '@/pinia/stores/enumStore';
const enumStore = useEnumStore();
const emit = defineEmits(['update:visible']);
const tradeTypeMap = ref<Map<string, string>>(new Map());
const tradeTypeList = enumStore.getEnumList(BusinessEnumType.TRADE_TYPE_MER_CONSOLE);
const statusList = enumStore.getEnumList(BusinessEnumType.TRADE_STATUS);
watch(
  tradeTypeList,
  (newVal) => {
    newVal.forEach((item) => {
      tradeTypeMap.value.set(item.enumCode, item.enumDescCn);
    });
  },
  { immediate: true }
);
// 交易目的筛选项目
const transactionPurposeOptions = enumStore.getEnumList(BusinessEnumType.TRANSACTION_PURPOSE);
// Internal state management
const showDrawer = ref(props.visible);
const flowDetail = ref<any>({
  basic: {
    respCode: '',
    respDesc: '',
    traceId: '',
    id: 0,
    sysSeqId: '',
    sysDate: '',
    merCustId: '',
    userCustId: '',
    userId: '',
    transType: '',
    transAmt: 0,
    transCurrency: '',
    feeAmt: 0,
    feeCurrency: '',
    feeFlag: '',
    calcMode: '',
    realAmt: 0,
    realCurrency: '',
    transStat: '',
    createTime: '',
    updateTime: '',
    extension1: '',
    extension2: '',
    extension3: '',
    fileUrl: '',
    merCustName: '',
  },
  payer: {
    englishName: '',
    chineseName: '',
    businessAddress: '',
    businessCity: '',
    businessProv: '',
    mainBusinessAddress: '',
    transactionPurposeLevel1: '',
    transactionPurposeLevel2: '',
    transactionRemarks: '',
    hasData: false,
  },
  payee: {
    payeeCtry: '',
    payeeName: '',
    swiftCode: '',
    acctName: '',
    acctNo: '',
    bankAddr: '',
    bankCtry: '',
    bankName: '',
  },
});
watch(
  () => props.visible,
  (newVal) => {
    showDrawer.value = newVal;
    if (newVal) {
      queryDetail();
    }
  },
  { immediate: true }
);

const queryDetail = async () => {
  let res;
  if (props.data.transType === 'FIAT_RECHARGE') {
    res = await queryRechargeDetailApi({
      sysSeqId: props.data.sysSeqId,
    });
  } else {
    res = await queryWithdrawDetailApi({
      sysSeqId: props.data.sysSeqId,
    });
  }
  flowDetail.value = res.data;
  flowDetail.value.payer.hasData =
    props.data.transType === 'FIAT_RECHARGE' ? flowDetail.value.payer.hasData : true;

  transactionPurposeOptions.value.forEach((item) => {
    if (item.enumCode === flowDetail.value.payer.transactionPurposeLevel1) {
      flowDetail.value.payer.transactionPurposeLevel1 = item.enumDescCn;
      item.children?.forEach((child) => {
        if (child.enumCode === flowDetail.value.payer.transactionPurposeLevel2) {
          flowDetail.value.payer.transactionPurposeLevel2 = child.enumDescCn;
        }
      });
    }
  });
};

// Close handler
const handleClose = () => {
  showDrawer.value = false;
  emit('update:visible', false);
};

const getStatusClass = (status: string) => {
  const params = statusList.value.filter((item) => {
    if (item.enumCode === status) {
      return item;
    }
  });

  switch (status) {
    case 'I':
      return { color: '#61555A', bgColor: '#F9F9F9', name: params[0].enumDescCn };
    case 'P':
      return { color: '#007BFF', bgColor: '#EBF6FF', name: params[0].enumDescCn };
    case 'S':
      return { color: '#3EB342', bgColor: '#EBF7EB', name: params[0].enumDescCn };
    case 'F':
      return { color: '#FD3627', bgColor: '#FFF1EC', name: params[0].enumDescCn };
    default:
      return { color: '#D18801', bgColor: '#FFFCEB', name: params[0].enumDescCn };
  }
};

const downloadCerti = () => {
  if (locale.value === 'zh-CN') {
    window.open(flowDetail.value.chineseCertiUrl, '_blank', 'noopener,noreferrer');
  } else if (locale.value === 'zh-HK') {
    window.open(flowDetail.value.cantoneseCertiUrl, '_blank', 'noopener,noreferrer');
  } else {
    window.open(flowDetail.value.englishCertiUrl, '_blank', 'noopener,noreferrer');
  }
};

const downloadSupplement = () => {
  window.open(flowDetail.value.supplementUrl, '_blank', 'noopener,noreferrer');
};
</script>

<style lang="scss" scoped>
.transaction-form {
  padding: 0 20px;
}

.el-form-item {
  margin-bottom: 16px;
}

::v-deep(.el-form-item__label) {
  font-weight: 400;
  color: #606266 !important;
  padding: 0 !important;
}

.fee-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

/* Optimize tag styles */
.el-tag {
  margin-left: 8px;
  vertical-align: middle;
}

/* Responsive layout */
@media (max-width: 768px) {
  .el-col {
    width: 100% !important;
  }
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #e5e6eb;
  }
}
.transaction-form {
  ::v-deep(.el-form-item__content) {
    word-wrap: break-word; /* Long word wrap */
    word-break: break-all; /* Force break */
    white-space: pre-wrap; /* Preserve whitespace and wrap */
  }
}

.status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
}
.drawer-footer {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
}
</style>
