<template>
  <el-drawer
    v-model="showDrawer"
    size="400"
    style="--el-drawer-padding-primary: 0px"
    :destroy-on-close="true"
    @close="handleClose"
    header-class="drawer-header"
    :show-close="false"
  >
    <template #header>
      <div
        class="h-60px border-b-[1px] border-b-[#E5E6EB] border-b-solid mb-0 flex justify-between flex-row flex-items-center text-18px font-600 color-[#222527] py-18px px-24px"
      >
        <span>{{ $t('tradeRecord.transDetail.title') }}</span>

        <svgIcon
          name="icon-close"
          class="text-16px cursor-pointer color-#6B7275"
          @click="handleClose"
        />
      </div>
    </template>
    <el-form
      label-width="100px"
      label-position="top"
      hide-required-asterisk
      class="transaction-form p-24px pb-70px"
      disabled
    >
      <el-form-item :label="$t('tradeRecord.transDetail.transactionFlow') + '：'">
        <span>{{ data.sysSeqId }}</span>
      </el-form-item>
      <el-form-item :label="$t('tradeRecord.transDetail.transactionTime') + '：'">
        {{ formatTime(data.createTime) }}
      </el-form-item>

      <el-form-item :label="$t('tradeRecord.transDetail.transactionType') + '：'">
        {{ tradeTypeMap.get(data.transType) }}
      </el-form-item>
      <el-form-item :label="$t('tradeRecord.transDetail.transactionAmount') + '：'">
        {{ data.transAmt }} {{ data.transCurrency }}
      </el-form-item>

      <el-form-item :label="$t('tradeRecord.transDetail.feeAmount') + '：'">
        <span class="fee-wrapper">
          {{ data.feeAmt }} {{ data.feeCurrency }}
          <el-tag type="info" size="small">{{
            data.feeFlag === 'I'
              ? $t('tradeRecord.feeTypes.internal')
              : $t('tradeRecord.feeTypes.external')
          }}</el-tag>
        </span>
      </el-form-item>
      <el-form-item :label="$t('tradeRecord.transDetail.accountingAmount') + '：'">
        {{ data.realAmt }} {{ data.realCurrency }}
      </el-form-item>

      <el-form-item :label="$t('tradeRecord.transDetail.transactionStatus') + '：'">
        <div class="flex items-center">
          <div
            class="flex items-center leading-24px w-fit px-12px rounded-12px text-12px"
            :style="{
              backgroundColor: getStatusClass(data.transStat).bgColor,
              color: getStatusClass(data.transStat).color,
            }"
          >
            {{ getStatusClass(data.transStat).name }}

            <el-tooltip placement="top" v-if="data.transStat === 'F'">
              <template #content>
                <p style="margin: 0">{{ t('tradeRecord.failureReason') }}：{{ data.respDesc }}</p>
              </template>
              <svg-icon name="icon-info" class="text-12px color-[#C49191] ml-4px" />
            </el-tooltip>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div
        style="border-top: 1px solid #e5e6eb"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px"
      >
        <el-button
          @click="handleClose"
          text
          class="w-68px h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px btn-hover-scale-sm"
          >{{ $t('tradeRecord.transDetail.close') }}</el-button
        >
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { BusinessEnumType } from '@@/apis/common/type';
import dayjs from 'dayjs';

const { t } = useI18n();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});
// Get enum
interface EnumItem {
  enumCode: string;
  enumDescCn: string;
  extendField?: string;
}
import { useEnumStore } from '@/pinia/stores/enumStore';
const enumStore = useEnumStore();
const emit = defineEmits(['update:visible']);
const tradeTypeMap = ref<Map<string, string>>(new Map());
const tradeTypeList = enumStore.getEnumList(BusinessEnumType.TRADE_TYPE_MER_CONSOLE);
const statusList = enumStore.getEnumList(BusinessEnumType.TRADE_STATUS);
watch(
  tradeTypeList,
  (newVal) => {
    newVal.forEach((item) => {
      tradeTypeMap.value.set(item.enumCode, item.enumDescCn);
    });
  },
  { immediate: true }
);

// Internal state management
const showDrawer = ref(props.visible);
watch(
  () => props.visible,
  (newVal) => {
    showDrawer.value = newVal;
  },
  { immediate: true }
);

// Format time
const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};

// Close handler
const handleClose = () => {
  showDrawer.value = false;
  emit('update:visible', false);
};

const getStatusClass = (status: string) => {
  const params = statusList.value.filter((item) => {
    if (item.enumCode === status) {
      return item;
    }
  });

  switch (status) {
    case 'I':
      return { color: '#61555A', bgColor: '#F9F9F9', name: params[0].enumDescCn };
    case 'P':
      return { color: '#007BFF', bgColor: '#EBF6FF', name: params[0].enumDescCn };
    case 'S':
      return { color: '#3EB342', bgColor: '#EBF7EB', name: params[0].enumDescCn };
    case 'F':
      return { color: '#FD3627', bgColor: '#FFF1EC', name: params[0].enumDescCn };
    default:
      return { color: '#D18801', bgColor: '#FFFCEB', name: params[0].enumDescCn };
  }
};
</script>

<style lang="scss" scoped>
.transaction-form {
  padding: 0 20px;
}

.el-form-item {
  margin-bottom: 16px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
  padding: 0 !important;
}

.fee-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

/* Optimize tag styles */
.el-tag {
  margin-left: 8px;
  vertical-align: middle;
}

/* Responsive layout */
@media (max-width: 768px) {
  .el-col {
    width: 100% !important;
  }
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #e5e6eb;
  }
}
.transaction-form {
  ::v-deep(.el-form-item__content) {
    word-wrap: break-word; /* Long word wrap */
    word-break: break-all; /* Force break */
    white-space: pre-wrap; /* Preserve whitespace and wrap */
  }
}

.status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
}
.drawer-footer {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
}
</style>
