<template>
  <el-drawer
    v-model="showDrawer"
    size="684"
    style="--el-drawer-padding-primary: 0px"
    :destroy-on-close="true"
    @close="handleClose"
    header-class="drawer-header"
    :show-close="false"
  >
    <template #header>
      <div
        class="h-60px border-b-[1px] border-b-[#E5E6EB] border-b-solid mb-0 flex justify-between flex-row flex-items-center text-18px font-600 color-[#222527] py-18px px-24px"
      >
        <span>{{ $t('tradeRecord.transDetail.fxTitle') }}</span>

        <svgIcon
          name="icon-close"
          class="text-16px cursor-pointer color-#6B7275"
          @click="handleClose"
        />
      </div>
    </template>
    <el-form
      label-width="100px"
      label-position="top"
      hide-required-asterisk
      class="transaction-form p-24px pb-70px"
      disabled
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <h4 class="m-0 mb-24px font-600 text-16px color-#222527">
            {{ $t('tradeRecord.transDetail.basicInfo') }}
          </h4>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.transType')">
            <span>{{ tradeTypeMap.get(props.data.transType) }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.sysSeqId')">
            <span>{{ props.data.sysSeqId }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.transStat')">
            <div
              class="flex items-center leading-24px w-fit px-12px rounded-12px text-12px"
              :style="{
                backgroundColor: getStatusClass(props.data.transStat).bgColor,
                color: getStatusClass(props.data.transStat).color,
              }"
            >
              {{ getStatusClass(props.data.transStat).name }}
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.exchangeOutAmount')">
            <div class="flex items-center text-right font-ftdin h-32px">
              <span class="leading-29px">{{ formatNumber(fxDetail.exchangeOutAmount, 2) }}</span>
              <img  />
              <span class="leading-29px">{{ fxDetail.exchangeOutCurrency }}</span>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.exchangeInAmount')">
            <div class="flex items-center text-right font-ftdin h-32px">
              <span class="leading-29px"
                >{{ formatNumber(fxDetail.exchangeInAmount, 2) }}</span
              >
              <i class="icon-currency-us"></i>
              <span class="leading-29px">{{ fxDetail.exchangeInCurrency }}</span>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.network')">
            <div class="flex items-center text-right font-ftdin h-32px">
              <span class="leading-29px">{{ fxDetail.blockchainNetwork }}</span>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.currencyPair')">
            <div class="flex text-right font-ftdin">
              <span class="block text-right"
                >{{ fxDetail.currencyPair }}
              </span>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.exchangeRate')">
            <div class="flex text-right font-ftdin">
              <span class="block text-right"
                >{{ fxDetail.exchangeRate }}
              </span>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.createTime')">
            <div class="flex text-right font-ftdin">
              <span class="block text-right"
                >{{ moment(fxDetail.createTime).format('YYYY-MM-DD HH:mm:ss') }}
              </span>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('tradeRecord.transDetail.updateTime')">
            <div class="flex text-right font-ftdin">
              <span class="block text-right">{{
                fxDetail.completeTime
                  ? moment(fxDetail.completeTime).format('YYYY-MM-DD HH:mm:ss')
                  : '-'
              }}</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div
        style="border-top: 1px solid #e5e6eb"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px"
      >
        <el-button
          @click="handleClose"
          text
          class="min-w-68px h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px btn-hover-scale-sm"
          >{{ $t('tradeRecord.transDetail.close') }}</el-button
        >
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { BusinessEnumType } from '@@/apis/common/type';
import { queryForeignExchangeDetailApi } from '../apis';
import moment from 'moment';
import { formatNumber } from '@@/utils/math';
import { exchangeDetailResponseData } from '../apis/type';
import { getCryptoIcon, getCurrencyIcon } from '@/common/utils/imageUtils'
const { t, locale } = useI18n();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});
import { useEnumStore } from '@/pinia/stores/enumStore';
const enumStore = useEnumStore();
const emit = defineEmits(['update:visible']);
const tradeTypeMap = ref<Map<string, string>>(new Map());
const tradeTypeList = enumStore.getEnumList(BusinessEnumType.TRADE_TYPE_MER_CONSOLE);
const statusList = enumStore.getEnumList(BusinessEnumType.TRADE_STATUS);
watch(
  tradeTypeList,
  (newVal) => {
    newVal.forEach((item) => {
      tradeTypeMap.value.set(item.enumCode, item.enumDescCn);
    });
  },
  { immediate: true }
);

// Internal state management
const showDrawer = ref(props.visible);
const fxDetail = ref<exchangeDetailResponseData['data']>({
  exchangeOutAmount: 0,
  exchangeOutCurrency: '',
  exchangeInAmount: 0,
  exchangeInCurrency: '',
  blockchainNetwork: '',
  currencyPair: '',
  exchangeRate: '',
  createTime: '',
  completeTime: '',
});
watch(
  () => props.visible,
  (newVal) => {
    showDrawer.value = newVal;
    if (newVal) {
      queryDetail();
    }
  },
  { immediate: true }
);

const queryDetail = () => {
  queryForeignExchangeDetailApi(props.data.sysSeqId, props.data.transType).then((res) => {
    fxDetail.value = res.data;
  });
};

// Close handler
const handleClose = () => {
  showDrawer.value = false;
  emit('update:visible', false);
};

const getStatusClass = (status: string) => {  
  const params = statusList.value.filter((item) => {
    if (item.enumCode === status) {
      return item;
    }
  });

  if (params.length === 0) {
    return { color: '#D18801', bgColor: '#FFFCEB', name: '' };
  }

  switch (status) {
    case 'I':
      return { color: '#61555A', bgColor: '#F9F9F9', name: params[0].enumDescCn };
    case 'P':
      return { color: '#007BFF', bgColor: '#EBF6FF', name: params[0].enumDescCn };
    case 'S':
      return { color: '#3EB342', bgColor: '#EBF7EB', name: params[0].enumDescCn };
    case 'F':
      return { color: '#FD3627', bgColor: '#FFF1EC', name: params[0].enumDescCn };
    default:
      return { color: '#D18801', bgColor: '#FFFCEB', name: params[0].enumDescCn };
  }
};

</script>

<style lang="scss" scoped>
.transaction-form {
  padding: 0 20px;
}

.icon-currency-us {
  margin: 0 8px 0 12px;
  display: block;
  width: 20px;
  height: 20px;
  background-image: url('/src/common/assets/icons/icon-currency-us.svg');
  background-size: cover;
}

.el-form-item {
  margin-bottom: 16px;
}

::v-deep(.el-form-item__label) {
  font-weight: 400;
  color: #606266 !important;
  padding: 0 !important;
}

.fee-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

/* Optimize tag styles */
.el-tag {
  margin-left: 8px;
  vertical-align: middle;
}

/* Responsive layout */
@media (max-width: 768px) {
  .el-col {
    width: 100% !important;
  }
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #e5e6eb;
  }
}
.transaction-form {
  ::v-deep(.el-form-item__content) {
    word-wrap: break-word; /* Long word wrap */
    word-break: break-all; /* Force break */
    white-space: pre-wrap; /* Preserve whitespace and wrap */
  }
}

.status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
}
.drawer-footer {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
}
</style>
