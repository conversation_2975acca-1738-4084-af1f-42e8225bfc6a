<script lang="ts" setup>
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import NumberFlipAnimation from '@@/components/LoadAnimation/NumberFlipAnimation.vue';
const { t } = useI18n();
const router = useRouter();

const assetsType = ref('USD');
const showBalance = ref(true);
const tableType = ref<string>('');
const assetType = ref<string>('');
import { transLogApi } from '../../tradeRecord/apis';
import { exchangeRateApi, walletAcctApi, feeQuotaApi } from '@@/apis/common';
import { transLogResponseList } from '../../tradeRecord/apis/type';
import { formatNumber } from '@@/utils/math';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { BusinessEnumType } from '@@/apis/common/type';
import DataBoardOverview from './DataBoardOverview.vue';
const getStatusClass = (status: string) => {
  switch (status) {
    case 'I':
      return { color: '#61555A', bgColor: '#F9F9F9', name: t('dataBoard.statusTypes.accepted') };
    case 'P':
      return { color: '#007BFF', bgColor: '#EBF6FF', name: t('dataBoard.statusTypes.processing') };
    case 'S':
      return { color: '#3EB342', bgColor: '#EBF7EB', name: t('dataBoard.statusTypes.success') };
    case 'F':
      return { color: '#FD3627', bgColor: '#FFF1EC', name: t('dataBoard.statusTypes.failed') };
    default:
      return { color: '', name: '' };
  }
};

const assetList = ref<transLogResponseList[]>([]);
const assetListWidth = ref(0);
// 获取资产动态
const getAssetDynamic = async (transStat: string) => {
  try {
    tableType.value = transStat;
    const res = await transLogApi({
      transStat: transStat,
      pageNum: 1,
      pageSize: 10,
    });
    assetList.value = res.data.transLogResponseList || [];
    const maxValue = Math.max(...assetList.value.map((item) => item.transAmt));
    assetListWidth.value =
      Math.trunc(maxValue).toString().length * 8.44 +
      Math.trunc((Math.trunc(maxValue).toString().length - 1) / 3) * 8.42;
  } catch (error) {
    console.error(error);
  }
};

const exchangeRate = ref<
  {
    currency: string;
    exchangeRate: string;
    fluctuation: string;
  }[]
>([]);

// 控制刷新图标旋转状态
const isRefreshing = ref(false);

// 获取实时汇率
const exchangeRateApiResp = async () => {
  try {
    const res = await exchangeRateApi();
    exchangeRate.value = res.data.exchangeRateInfoVOs;
  } catch (error) {
    console.error(error);
  }
};

/**
 * 处理刷新图标点击事件，添加旋转动画效果
 * 动画完成后才触发原有的API调用
 */
const handleRefreshClick = async () => {
  if (isRefreshing.value) return; // 防止重复点击

  isRefreshing.value = true;

  // 等待旋转动画完成后再调用API
  setTimeout(async () => {
    await exchangeRateApiResp();
    isRefreshing.value = false;
  }, 500); // 500ms 与CSS动画时长匹配
};
const walletInfo = ref<{
  balanceAvl: string;
  balanceFreeze: string;
  balanceWay: string;
  balanceTotal: string;
}>({
  balanceAvl: '0.00',
  balanceFreeze: '0.00',
  balanceWay: '0.00',
  balanceTotal: '0.00',
});
// 获取账户余额
const walletAcctApiResp = async () => {
  try {
    const res = await walletAcctApi();
    walletInfo.value = res.data;
  } catch (error) {
    console.error(error);
  }
};
const tradeTypeMap = ref<Map<string, string>>(new Map());
const enumStore = useEnumStore();
const tradeTypeList = enumStore.getEnumList(BusinessEnumType.TRADE_TYPE_MER_CONSOLE);

onMounted(async () => {
  getAssetDynamic('');
  exchangeRateApiResp();
  walletAcctApiResp();
});

watch(
  tradeTypeList,
  (newList) => {
    // 将tradeTypeList转成map
    newList.forEach((item: any) => {
      tradeTypeMap.value.set(item.enumCode, item.enumDescCn);
    });
  },
  { immediate: true }
);

const amtToStr = (amt: string) => {
  if (assetsType.value === 'USD') {
    return formatNumber(Number(amt), 2);
  } else {
    return formatNumber(Number(amt), 6);
  }
};

// 计算当前显示的金额数值（用于动画组件）
const currentDisplayAmount = computed(() => {
  try {
    const amount = Number(walletInfo.value.balanceTotal) || 0;
    return amount;
  } catch (error) {
    console.error('金额数据解析错误:', error);
    return 0;
  }
});

// 计算小数位数
const currentDecimals = computed(() => {
  return assetsType.value === 'USD' ? 2 : 6;
});

// 监听资产类型变化，触发动画重新渲染
const animationKey = ref(0);
watch(assetsType, () => {
  // 通过改变 key 值强制重新渲染动画组件
  animationKey.value++;
});

/**
 * 计算金额显示区域的动态宽度
 * 根据当前显示金额的实际值动态调整宽度，确保视觉一致性和适当的对齐
 */
const amountDisplayMaxWidth = computed(() => {
  // 获取当前显示的金额值
  const currentAmount = currentDisplayAmount.value;
  const decimals = assetsType.value === 'USD' ? 2 : 6;

  // 格式化当前金额以获取显示字符串
  const formattedAmount = formatNumber(currentAmount, decimals);

  // 根据字体大小 28px 和字体 ftdin 估算字符宽度
  // 数字字符平均宽度约为 16px (28px * 0.57)
  // 逗号和小数点宽度约为 8px
  const digitWidth = 16;
  const punctuationWidth = 8;

  // 计算字符数量
  const digitCount = formattedAmount.replace(/[^0-9]/g, '').length;
  const punctuationCount = formattedAmount.replace(/[0-9]/g, '').length;

  const totalWidth = digitCount * digitWidth + punctuationCount * punctuationWidth;

  // // 设置最小宽度以避免过小的显示区域，同时添加适当的缓冲空间
  // const minWidth = 10; // 最小宽度
  // const calculatedWidth = totalWidth; // 添加20px的缓冲空间

  // // return `${Math.max(minWidth, calculatedWidth)}px`;
  return `${totalWidth}px`;
});
</script>

<template>
  <div class="flex justify-between px-40px py-32px w-100% min-w-1100px">
    <div class="flex-1 mr-24px min-w-0">
      <div class="w-100% border-1 border-[#EDEDEE] border-solid rounded-12px p-24px">
        <h4 class="m-0 color-#222527 leading-24px text-18px font-600 flex items-center">
          {{ t('dataBoard.totalAssets') }}
          <svg-icon v-if="showBalance" @click="showBalance = !showBalance" name="icon-show"
            class="ml-6px text-16px cursor-pointer" />
          <svg-icon v-if="!showBalance" @click="showBalance = !showBalance" name="icon-hide"
            class="ml-6px text-16px cursor-pointer" />
        </h4>

        <div class="flex justify-between flex-wrap align-center">
          <div class="flex items-end mt-24px">
            <div class="flex items-center" :style="{ width: amountDisplayMaxWidth }">
              <NumberFlipAnimation v-if="showBalance" :key="`balance-${animationKey}`" :value="currentDisplayAmount"
                :duration="2000" :decimals="currentDecimals" separator="," fontSize="28px" fontWeight="600"
                color="#222527" fontFamily="ftdin" class=" leading-24px" />
              <span v-else class="text-28px font-600 color-#222527 m-0 mr-12px leading-24px font-ftdin">
                ******
              </span>
            </div>
            <el-dropdown ref="dropdown" trigger="hover" disabled>
              <div class="right-menu-item cursor-pointer color-#222527 font-600 text-20px leading-17px">
                <span>{{ assetsType }}</span>
                <!-- <svg-icon name="icon-bottom" class="ml-4px text-16px color-#6B7275" /> -->
              </div>

              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="assetsType = 'USD'">USD</el-dropdown-item>
                  <!-- 隐藏其他币种金额，只展示usd -->
                  <!-- <el-dropdown-item @click="assetsType = 'USDT'">USDT</el-dropdown-item>
                  <el-dropdown-item @click="assetsType = 'USDC'">USDC</el-dropdown-item> -->
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="flex justify-between align-center mt-24px">
            <el-button type="primary" @click="router.push('/recharge')"
              class="bg-#FF0064 border-none btn-hover-scale-sm font-400 px-20px py-6px">{{ t('dataBoard.recharge')
              }}</el-button>
            <el-button type="primary" @click="router.push('/exchange')"
              class="bg-#FF0064 border-none ml-8px btn-hover-scale-sm font-400 px-20px py-6px">{{
                t('dataBoard.exchange')
              }}</el-button>
            <el-button type="primary" @click="router.push('/withdrawal')"
              class="bg-#FF0064 border-none ml-8px btn-hover-scale-sm font-400 px-20px py-6px">{{
                t('dataBoard.withdrawal')
              }}</el-button>
          </div>
        </div>
      </div>

      <DataBoardOverview />

      <div class="w-100% border-1 border-[#EDEDEE] border-solid rounded-12px p-24px mt-24px">
        <div class="flex justify-between align-center mb-28px">
          <h4 class="m-0 color-#222527 leading-24px text-18px font-600 flex items-center">
            {{ t('dataBoard.assetDynamics') }}
          </h4>

          <ul class="flex items-center bg-[#F5F5F5] rounded-6px p-4px">
            <li @click="getAssetDynamic('')"
              class="h-24px leading-24px text-center rounded-4px text-14px font-400 cursor-pointer px-8px min-w-60px"
              :class="tableType === '' ? 'bg-#fff color-#030814' : 'color-#6B7275'">
              {{ t('dataBoard.recent') }}
            </li>
            <li @click="getAssetDynamic('P')"
              class="h-24px leading-24px text-center rounded-4px text-14px font-400 cursor-pointer px-8px min-w-60px"
              :class="tableType === 'P' ? 'bg-#fff color-#030814' : 'color-#6B7275'">
              {{ t('dataBoard.processingStatus') }}
            </li>
            <li @click="getAssetDynamic('S,F')"
              class="h-24px leading-24px text-center rounded-4px text-14px font-400 cursor-pointer px-8px min-w-60px"
              :class="tableType === 'S,F' ? 'bg-#fff color-#030814' : 'color-#6B7275'">
              {{ t('dataBoard.completed') }}
            </li>
          </ul>
        </div>

        <el-table :data="assetList" style="width: 100%; color: #222527">
          <el-table-column prop="type" :label="t('dataBoard.type')">
            <template #default="{ row }">
              {{ tradeTypeMap.get(row.transType) }}
            </template>
          </el-table-column>

          <el-table-column prop="transCurrency" :label="t('dataBoard.currency')" />

          <el-table-column prop="transAmt" :label="t('dataBoard.amount')" :min-width="assetListWidth + 60" align="left">
            <template #default="{ row }">
              <div class="flex text-right font-ftdin">
                <span class="block text-right" :style="`width: ${assetListWidth}px`">{{
                  formatNumber(row.transAmt, 2).toString().split('.')[0]
                }}</span>
                <span class="w-60px block text-left shrink-0">.{{
                  row.transCurrency === 'USD'
                    ? row.transAmt.toFixed(2).toString().split('.')[1]
                    : row.transAmt.toFixed(6).toString().split('.')[1]
                }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" :label="t('dataBoard.status')">
            <template #default="{ row }">
              <div class="px-12px color-#fff text-12px leading-24px w-fit rounded-12px" :style="{
                backgroundColor: getStatusClass(row.transStat).bgColor,
                color: getStatusClass(row.transStat).color,
              }">
                {{ getStatusClass(row.transStat).name }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="w-405px flex-shrink-0">
      <!-- <div class="w-100% border-1 border-[#EDEDEE] border-solid rounded-12px p-24px">
        <div class="flex justify-between align-center mb-28px">
          <h4 class="m-0 color-#222527 leading-24px text-18px font-600 flex items-center">
            账户限额
          </h4>

          <ul class="flex items-center bg-[#F5F5F5] rounded-6px p-4px">
            <li
              @click="feeQuotaType = 'day'"
              class="h-24px leading-24px text-center rounded-4px text-14px font-400 cursor-pointer px-8px min-w-60px"
              :class="feeQuotaType === 'day' ? 'bg-#fff color-#030814' : 'color-#6B7275'"
            >
              日
            </li>
            <li
              @click="feeQuotaType = 'month'"
              class="h-24px leading-24px text-center rounded-4px text-14px font-400 cursor-pointer px-8px min-w-60px"
              :class="feeQuotaType === 'month' ? 'bg-#fff color-#030814' : 'color-#6B7275'"
            >
              月
            </li>
          </ul>
        </div>

        <div class="flex justify-between items-center">
          <div class="flex items-end color-#222527">
            <span class="text-14px font-400">已用</span>
            <span class="text-28px font-600 ml-8px leading-26px">10%</span>
          </div>

          <el-dropdown ref="dropdown" trigger="hover">
            <div
              class="right-menu-item cursor-pointer color-#222527 font-600 text-16px leading-16px"
            >
              <span>{{ assetsType }}</span>
              <svg-icon name="icon-bottom" class="ml-4px text-12px color-#6B7275" />
            </div>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="assetsType = 'USD'">USD</el-dropdown-item>
                <el-dropdown-item @click="assetsType = 'USDT'">USDT</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <div class="w-100% h-10px bg-[#F5F5F5] rounded-5px mt-16px">
          <div class="w-10% h-10px bg-[#FF0064] rounded-5px"></div>
        </div>

        <div class="flex justify-between items-center mt-24px">
          <span class="text-14px font-400 color-#6B7275">已用</span>
          <span class="text-14px font-400 color-#222527 font-ftdin">500,000.00</span>
        </div>
        <div class="flex justify-between items-center mt-16px">
          <span class="text-14px font-400 color-#6B7275">总计</span>
          <span class="text-14px font-400 color-#222527 font-ftdin">500,000.00</span>
        </div>
      </div> -->

      <div class="p-24px border-1 border-[#EDEDEE] border-solid rounded-12px">
        <h4 class="m-0 color-#222527 leading-24px text-18px font-600 flex items-center">
          {{ t('dataBoard.todayExchangeRate') }}
          <svg-icon @click="handleRefreshClick" name="icon-refresh" :class="[
            'ml-6px text-16px cursor-pointer',
            { 'refresh-icon-rotating': isRefreshing, 'pointer-events-none': isRefreshing }
          ]" />
        </h4>

        <ul class="mt-24px">
          <li v-for="(item, index) in exchangeRate" :key="item.currency"
            class="flex justify-between items-center text-14px font-400 p-12px rounded-6px leading-20px"
            :class="[index === 1 ? '' : 'bg-[#f8f9fa]']">
            <span class="flex-1 text-left">{{ item.currency }}</span>
            <span class="flex-1 text-center font-ftdin">{{ item.exchangeRate }}</span>
            <span class="flex-1 text-right font-ftdin" :class="Number(item.fluctuation) > 0
              ? 'color-#3EB342'
              : Number(item.fluctuation) < 0
                ? 'color-#FD3627'
                : 'color-#6B7275'
              ">
              {{ Number(item.fluctuation) > 0 ? '+' : '' }}{{ Number(item.fluctuation) }}%
              <svg-icon v-if="Number(item.fluctuation) > 0" name="icon-rise" class="ml-2px text-8px" />
              <svg-icon v-if="Number(item.fluctuation) < 0" name="icon-full" class="ml-2px text-8px" />
            </span>
          </li>
        </ul>
      </div>

      <div
        class="w-100% h-226px p-24px rounded-12px mt-24px overflow-hidden bg-[url(@@/assets/images/home/<USER>">
        <h6 class="color-#fff text-20px font-600 m-0">{{ t('dataBoard.depositCrypto') }}</h6>
        <p class="text-14px font-400 leading-20px color-#fff m-0 mt-16px w-230px h-80px mt-16px">
          {{ t('dataBoard.depositDescription') }}
        </p>
        <el-button type="primary" class="w-104px bg-#FF0064 h-35px mt-20px btn-hover-scale-sm"
          @click="router.push('/recharge?type=crypto')">{{ t('dataBoard.startUsing') }}</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.icon-currency-us {
  width: 14px;
  height: 14px;
  background-image: url('/src/common/assets/icons/icon-currency-us.svg');
  background-size: cover;
}

.select-icon {
  margin-right: 8px;
  color: #1ba27a;
  font-size: 16px;
}

.select-type-option {
  display: flex;
  align-items: center;
}

:deep(.el-table) {
  --el-table-header-bg-color: #f8f9fa;
  --el-table-header-text-color: #6b7275;

  .el-table__header-wrapper {
    border-radius: 6px;
    overflow: hidden;

    tr {
      --el-table-border: none;

      .el-table__cell {
        padding: 10px 0;
      }
    }
  }

  tbody tr {
    .el-table__cell {
      padding: 12px 0;
    }
  }
}

.status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
}

/* 刷新图标旋转动画 */
.refresh-icon-rotating {
  animation: refresh-rotate 0.5s ease-in-out;
}

@keyframes refresh-rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
