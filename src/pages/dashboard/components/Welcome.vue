<script lang="ts" setup>
import { useUserStore } from "@/pinia/stores/user"
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const { t } = useI18n()
const userStore = useUserStore()
const isExpanded = ref(true)
const router = useRouter()

const toggleExpand = () => {
  isExpanded.value = true
}
</script>

<template>
  <div class="dashboard-container">
    <div class="flex justify-between">
      <div class="w-50% pb-32px">
        <h4 v-if="userStore.kycStatus === 'I'" class="text-28px text-[#222527] font-600 m-0 mb-16px flex items-center">
          <img src="@@/assets/images/home/<USER>" class="w-36px h36px mr-16px">
          <p>{{ t('welcome.kycStatus.initial.title') }}</p>
        </h4>

        <h4 v-if="userStore.kycStatus === 'D'" class="text-28px text-[#222527] font-600 m-0 mb-16px flex items-center">
          <img src="@@/assets/images/home/<USER>" class="w-36px h36px mr-16px">
          <p>{{ t('welcome.kycStatus.pending.title') }}</p>
        </h4>

        <h4 v-if="userStore.kycStatus === 'E'"  class="text-28px text-[#222527] font-600 m-0 mb-16px flex items-center">
          <img src="@@/assets/images/home/<USER>" class="w-36px h36px mr-16px">
          <p>{{ t('welcome.kycStatus.edit.title') }}</p>
        </h4>
        <p v-if="userStore.kycStatus === 'I'" class="text-14px text-[#222527] font-500 m-0 mb-8px leading-20px">
          {{ t('welcome.kycStatus.initial.description') }}
        </p>
        <p v-if="userStore.kycStatus === 'I'" class="text-12px text-[#6B7275] m-0 leading-20px">
          {{ t('welcome.kycStatus.initial.disclaimer') }}
        </p>
        <p v-if="userStore.kycStatus === 'D'" class="text-14px text-[#222527] font-500 m-0 mb-8px leading-20px">
          {{ t('welcome.kycStatus.pending.description') }}
        </p>
        <p v-if="userStore.kycStatus === 'E'" class="text-14px text-[#222527] font-500 m-0 mb-8px leading-20px">
          {{ t('welcome.kycStatus.edit.description') }}
        </p>


        <div v-if="userStore.kycStatus !== 'D'" class="p-24px flex items-center justify-between mt-40px w-100% h-96px bg-[#ff0064] rounded-12px cursor-pointer btn-hover-scale-sm" @click="router.push('/kyc')">
          <div class="leading-24px color-#fff text-18px font-600">
            <p class="m-0" v-if="userStore.kycStatus !== 'E'">{{ t('welcome.kycStatus.initial.cta.line1') }}</p>
            <p class="m-0" v-if="userStore.kycStatus !== 'E'">{{ t('welcome.kycStatus.initial.cta.line2') }}</p>
            <p class="m-0" v-if="userStore.kycStatus === 'E'">{{ t('welcome.kycStatus.edit.cta') }}</p>
          </div>

          <svg-icon v-if="userStore.kycStatus === 'E'" name="icon-edit" class="text-40px color-#fff" />
          <img v-else src="@@/assets/images/home/<USER>" />
        </div>
      </div>

      <div class="w-50% pl-64px">
        <img  class="w-100%" src="@@/assets/images/home/<USER>" />
      </div>
    </div>


    <div class="p-24px w-100% border-1 border-[#EDEDEE] border-solid rounded-12px"
    :class="isExpanded ? 'w-100%' : 'w-50% mr-64px'">
      <div class="flex items-center justify-between">
        <img src="@@/assets/images/home/<USER>" />
        <p class="flex-1 ml-8px text-18px color-#222527 font-600 m-0">{{ t('welcome.accountCapabilities.title') }}</p>
        <!-- <el-button v-if="!isExpanded" link class="ml-8px p-0 text-14px text-[#FF0064]" size="small" @click="toggleExpand">{{ t('welcome.accountCapabilities.expandButton') }}</el-button> -->
      </div>

      <div v-if="isExpanded" class="flex mt-16px">
        <img class="w-492px h-276px" src="@@/assets/images/home/<USER>">

        <div class="text-14px color-#6B7275 leading-20px font-400 ml-64px">
          <p class="mb-20px">{{ t('welcome.accountCapabilities.paragraph1') }}</p>
          <p class="mb-20px">{{ t('welcome.accountCapabilities.paragraph2') }}</p>
          <p class="mb-20px">{{ t('welcome.accountCapabilities.paragraph3') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.dashboard-container {
  padding:124px 40px 100px;
  background-color: #fff;
  min-width:960px;
  p{
    margin:0;
  }
}
</style>
