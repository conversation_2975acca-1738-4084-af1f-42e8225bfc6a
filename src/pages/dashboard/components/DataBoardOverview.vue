<template>
  <div class="w-100% border-1 border-[#EDEDEE] border-solid rounded-12px p-24px mt-24px">
    <div class="flex justify-between align-center">
      <h4 class="m-0 color-#222527 leading-24px text-18px font-600 flex items-center">
        {{ t('dataBoard.assetOverview') }}
        <svg-icon
          v-if="showAssets"
          @click="showAssets = !showAssets"
          name="icon-show"
          class="ml-6px text-16px cursor-pointer"
        />
        <svg-icon
          v-if="!showAssets"
          @click="showAssets = !showAssets"
          name="icon-hide"
          class="ml-6px text-16px cursor-pointer"
        />
      </h4>
    </div>
    <ul class="mt-24px text-14px" v-if="assetOverview.length > 0">
      <li
        v-for="(item, key) in assetOverview"
        :key="item.currency"
        class="w-100% rounded-6px px-24px parent-item"
        :class="key !== 1 ? 'bg-#F8F9FA' : ''"
      >
        <div class="flex items-center h-56px" @click="item.isExpand = !item.isExpand">
          <div class="flex items-center w-165px flex-shrink-0">
            <div class="w-16px h-16px mr-4px">
              <img
                v-if="item.cryptoNetAccountInfo && item.cryptoNetAccountInfo.length > 0"
                class="w-100% h-100% cursor-pointer arrow-icon"
                :class="item.isExpand || item.isHovered ? 'arrow-expanded' : 'arrow-collapsed'"
                src="@@/assets/images/home/<USER>"
                alt=""
              />
            </div>
            <img
              v-if="item.acctType === 'FIAT'"
              class="w-20px h-20px"
              :src="getCurrencyIcon(item.currency)"
              alt=""
            />
            <img
              v-if="item.acctType === 'CRYPTO'"
              class="w-20px h-20px"
              :src="getCryptoIcon(item.currency)"
              alt=""
            />
            <span class="text-14px font-400 text-[#222527] ml-8px">{{ item.currency }}</span>
          </div>

          <div class="flex-1">
            <div class="flex justify-end font-ftdin" v-if="showAssets">
              <span class="block text-right">{{
                formatNumber(item.balanceAvl, 2).toString().split('.')[0]
              }}</span>
              <span class="w-48px block text-left shrink-0"
                >.{{
                  item.balanceAvl
                    .toFixed(item.acctType === 'FIAT' ? 2 : 6)
                    .toString()
                    .split('.')[1]
                }}</span
              >
            </div>

            <div class="flex justify-center font-ftdin" v-else>******</div>
          </div>

          <div class="flex-1">
            <div class="flex justify-end font-ftdin" v-if="showAssets">
              <span class="block text-right">{{
                formatNumber(item.balanceWay, 2).toString().split('.')[0]
              }}</span>
              <span class="w-48px block text-left shrink-0"
                >.{{
                  item.balanceWay
                    .toFixed(item.acctType === 'FIAT' ? 2 : 6)
                    .toString()
                    .split('.')[1]
                }}</span
              >
            </div>

            <div class="flex justify-center font-ftdin" v-else>******</div>
          </div>

          <div class="flex-1">
            <div class="flex justify-end font-ftdin" v-if="showAssets">
              <span class="block text-right">{{
                formatNumber(item.balanceTotal, 2).toString().split('.')[0]
              }}</span>
              <span class="w-48px block text-left shrink-0"
                >.{{
                  item.balanceTotal
                    .toFixed(item.acctType === 'FIAT' ? 2 : 6)
                    .toString()
                    .split('.')[1]
                }}</span
              >
            </div>

            <div class="flex justify-center font-ftdin" v-else>******</div>
          </div>
        </div>

        <ul
          v-if="item.isExpand || item.isHovered"
          class="w-100% children animated-children expanded-state"
        >
          <li
            v-for="(info, key) in item.cryptoNetAccountInfo"
            :key="key"
            class="w-100% rounded-6px"
          >
            <div class="flex items-center h-56px">
              <div class="flex items-center w-165px pl-24px">
                <div class="w-16px h-16px mr-4px"></div>
                <img
                  class="w-20px h-20px"
                  :src="getCryptoIcon(info.currency + '-' + info.blockchainNetwork)"
                  alt=""
                />

                <span class="text-14px font-400 text-[#222527] ml-8px"
                  >{{ info.currency }}-{{ info.blockchainNetwork }}</span
                >
              </div>

              <div class="flex-1">
                <div class="flex justify-end font-ftdin" v-if="showAssets">
                  <span class="block text-right">{{
                    formatNumber(info.balanceAvl, 2).toString().split('.')[0]
                  }}</span>
                  <span class="w-48px block text-left shrink-0"
                    >.{{
                      info.balanceAvl
                        .toFixed(info.acctType === 'FIAT' ? 2 : 6)
                        .toString()
                        .split('.')[1]
                    }}</span
                  >
                </div>
                <div class="flex justify-center font-ftdin" v-else>******</div>
              </div>

              <div class="flex-1">
                <div class="flex justify-end font-ftdin" v-if="showAssets">
                  <span class="block text-right">{{
                    formatNumber(info.balanceWay, 2).toString().split('.')[0]
                  }}</span>
                  <span class="w-48px block text-left shrink-0"
                    >.{{
                      info.balanceWay
                        .toFixed(info.acctType === 'FIAT' ? 2 : 6)
                        .toString()
                        .split('.')[1]
                    }}</span
                  >
                </div>
                <div class="flex justify-center font-ftdin" v-else>******</div>
              </div>

              <div class="flex-1">
                <div class="flex justify-end font-ftdin" v-if="showAssets">
                  <span class="block text-right">{{
                    formatNumber(info.balanceTotal, 2).toString().split('.')[0]
                  }}</span>
                  <span class="w-48px block text-left shrink-0"
                    >.{{
                      info.balanceTotal
                        .toFixed(info.acctType === 'FIAT' ? 2 : 6)
                        .toString()
                        .split('.')[1]
                    }}</span
                  >
                </div>
                <div class="flex justify-center font-ftdin" v-else>******</div>
              </div>
            </div>
          </li>
        </ul>
      </li>

      <li class="leading-44px flex px-24px">
        <div class="text-12px color-#6B7275 font-600 w-165px text-center">
          {{ t('dataBoard.currency') }}
        </div>
        <div class="text-12px color-#6B7275 font-600 flex-1 text-right">
          {{ t('dataBoard.availableAmount') }}
        </div>
        <div class="text-12px color-#6B7275 font-600 flex-1 text-right">
          {{ t('dataBoard.processingStatus') }}
        </div>
        <div class="text-12px color-#6B7275 font-600 flex-1 text-right">
          {{ t('dataBoard.total') }}
        </div>
      </li>
    </ul>
    <div v-else>
      <div class="flex justify-center items-center">
        <span class="text-14px font-400 text-[#222527] my-20px ml-8px">{{
          t('dataBoard.noAsset')
        }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { queryWalletAcctApi } from '../../tradeRecord/apis';
import { formatNumber } from '@@/utils/math';
const assetOverview: any = ref([]);
const showAssets = ref(true);
import { getCryptoIcon, getCurrencyIcon } from '@/common/utils/imageUtils';
onMounted(async () => {
  getWalletAcct();
});

/**
 * 处理父级元素悬停状态
 * @param item - 当前资产项
 * @param isHovered - 是否悬停状态
 */
const onParentHover = (item: any, isHovered: boolean) => {
  // 只有当有子账户信息时才响应悬停
  if (item.cryptoNetAccountInfo && item.cryptoNetAccountInfo.length > 0) {
    item.isHovered = isHovered;
  }
};

const getWalletAcct = async () => {
  try {
    const res = await queryWalletAcctApi();
    assetOverview.value = res.data.queryWalletInfoVOs || [];
    // 将assetOverview里新增一个是否展开字段和悬停状态字段
    assetOverview.value.forEach((item: any) => {
      item.isExpand = false;
      item.isHovered = false;
    });
    // 将assetOverview.value 中 currency 为 USD 的项移动到数组的第一个
    const usdItem = assetOverview.value.find((item: any) => item.currency === 'USD');
    if (usdItem) {
      assetOverview.value = [
        usdItem,
        ...assetOverview.value.filter((item: any) => item.currency !== 'USD'),
      ];
    }
  } catch (error) {
    console.error(error);
  }
};
</script>

<style scoped lang="scss">
.children li {
  position: relative;
}

.children li::before {
  content: '';
  position: absolute;
  top: -30px;
  left: 29px;
  height: 59px;
  width: 15px;
  border-left: 1px solid #e5e6eb;
  border-bottom: 1px solid #e5e6eb;
}

.children li:nth-child(1)::before {
  height: 46px;
  top: -18px;
}

/* 父级元素悬停效果 */
.parent-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

/* 展开状态统一样式 */
.expanded-state {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
  animation: slideInFromTop 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.expanded-state li {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.9;
  transform: translateX(0);
}

/* 箭头旋转动画 */
.arrow-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.arrow-expanded {
  transform: rotate(0deg);
}

.arrow-collapsed {
  transform: rotate(-90deg);
}

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-5px) scale(0.98);
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
