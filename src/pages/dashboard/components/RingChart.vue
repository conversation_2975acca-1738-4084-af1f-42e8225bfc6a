<template>
  <div class="ring-chart-container">
    <svg :width="size" :height="size" viewBox="0 0 200 200" class="ring-chart">
      <!-- 背景轨道 -->
      <circle cx="100" cy="100" r="80" fill="none" stroke="#F0F0F0" stroke-width="20" />
      
      
      <!-- 处理中金额弧段 -->
      <path 
        :d="getArcPath(processingAmount, availableAmount, 80)" 
        fill="#030814" 
        stroke="none"
      />
      
      <!-- 可用金额弧段 -->
      <path 
        :d="getArcPath(availableAmount, 0, 80)" 
        fill="#FF0064" 
        stroke="none" 
      />
      
      <!-- 中心文字 -->
      <text x="100" y="100" text-anchor="middle" dominant-baseline="middle" class="center-text">
        {{ $t('ringChart.assetDistribution') }}
      </text>
    </svg>

    <div class="legend">
      <div class="legend-item">
        <span class="dot available-dot"></span>
        <span>{{ $t('ringChart.availableAmount') }}</span>
      </div>
      <div class="legend-item">
        <span class="dot processing-dot"></span>
        <span>{{ $t('ringChart.processing') }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useI18n } from 'vue-i18n';

export default defineComponent({
  name: 'RingChart',
  props: {
    availableAmount: {
      type: Number,
      default: 70,
      validator: (value: number) => value >= 0
    },
    processingAmount: {
      type: Number,
      default: 30,
      validator: (value: number) => value >= 0
    },
    size: {
      type: Number,
      default: 200
    },
    arcAngle: {
      type: Number,
      default: 360, // 控制弧度的总角度（180度表示半圆）
    },
    gapAngle: {
      type: Number,
      default: 10, // 弧段之间的间隔角度
    },
    cornerRadius: {
      type: Number,
      default: 30, // 控制圆角的程度
    }
  },
  setup() {
    const { t } = useI18n();
    return { t };
  },
  methods: {
    getArcPath(value: number, offset: number, radius: number): string {
      const total = this.availableAmount + this.processingAmount;
      if (total === 0) return '';
      
      const center = { x: 100, y: 100 };
      const strokeWidth = 20; // 与 stroke-width 相同
      
      // 计算内外半径
      const innerRadius = radius - strokeWidth / 2;
      const outerRadius = radius + strokeWidth / 2;
      
      // 计算角度
      const startAngle = -90 + (offset / total) * this.arcAngle;
      const endAngle = -90 + ((offset + value) / total) * this.arcAngle - this.gapAngle/2;
      
      // 计算四个点：内外圆弧的起点和终点
      const innerStart = this.polarToCartesian(center.x, center.y, innerRadius, startAngle);
      const outerStart = this.polarToCartesian(center.x, center.y, outerRadius, startAngle);
      const innerEnd = this.polarToCartesian(center.x, center.y, innerRadius, endAngle);
      const outerEnd = this.polarToCartesian(center.x, center.y, outerRadius, endAngle);
      
      // 确定是大弧还是小弧
      const largeArcFlag = endAngle - startAngle <= 180 ? 0 : 1;
      
      // 使用cornerRadius创建圆角路径
      return `
        M ${innerStart.x} ${innerStart.y}
        A ${this.cornerRadius} ${this.cornerRadius} 0 0 1 ${outerStart.x} ${outerStart.y}
        A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${outerEnd.x} ${outerEnd.y}
        A ${this.cornerRadius} ${this.cornerRadius} 0 0 1 ${innerEnd.x} ${innerEnd.y}
        A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${innerStart.x} ${innerStart.y}
        Z
      `;
    },
    polarToCartesian(centerX: number, centerY: number, radius: number, angleInDegrees: number) {
      const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
      return {
        x: centerX + (radius * Math.cos(angleInRadians)),
        y: centerY + (radius * Math.sin(angleInRadians))
      };
    }
  }
});
</script>

<style scoped>
.ring-chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: Arial, sans-serif;
}

.center-text {
  fill: #222527;
  font-size: 14px;
  font-weight: 400;
}

.legend {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  width: 140px;
  justify-content: space-between;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  line-height:20px;
}

.dot {
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.available-dot {
  background-color: #FF0064;
}

.processing-dot {
  background-color: #030814;
}
</style>
