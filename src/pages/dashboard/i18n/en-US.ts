export default {
  welcome: {
    // KYC status related texts
    kycStatus: {
      initial: {
        title: 'Welcome! Please verify your Ding account now',
        description:
          'Please click on the highlighted area below to start the business verification process. Before starting, we recommend that you prepare important information about your company, shareholders, and legal representatives.',
        disclaimer:
          'According to relevant compliance requirements, Ding needs to verify your business information and ensure the security of your account funds. Ding promises 100% confidentiality of your information and will not use your information for other purposes or behaviors.',
        cta: {
          line1: 'What are you waiting for?',
          line2: 'Click to verify your business →',
        },
      },
      pending: {
        title: 'Hello! Please wait for our review results',
        description:
          'Your materials have been submitted for review. It generally takes 1-3 business days from the time of submission to the end of the review! In case of special circumstances, please contact our operations staff.',
      },
      edit: {
        title: 'Hello! Please modify or supplement verification information',
        description:
          'Please click on the highlighted area below to continue the business verification process. We will prompt you for the parts that need to be supplemented or modified.',
        cta: 'Click to modify or supplement business information materials →',
      },
    },
    // Account capabilities introduction
    accountCapabilities: {
      title: 'About Ding Account Capabilities',
      expandButton: 'Expand to read',
      paragraph1:
        'Activate your account and easily start your global fund management journey! We automatically set up multiple mainstream currency accounts for you, supporting stablecoins (such as USDT, USDC) and major fiat currencies (USD, HKD, offshore RMB, EUR, etc.), covering the key bridge between the Web3 ecosystem and traditional finance. Whether you focus on on-chain applications, digital asset operations, or involve cross-border settlement, global payments and other scenarios, our account system can provide stable, secure, and compliant fund custody and transfer capabilities.',
      paragraph2:
        'Each currency account has clear fund classification, real-time dynamics at your fingertips, helping you respond quickly to market changes. At the same time, the platform supports free expansion of currencies, on-chain and off-chain interconnection, and enterprise-level fund flow management, meeting complex operation needs such as high-frequency deposits and withdrawals, batch fund distribution, and smart contract interaction.',
      paragraph3:
        'In this era of increasing sensitivity to speed and transparency, we are committed to creating a flexible and reliable global account foundation for Web3 enterprises, allowing you to focus on business innovation without worrying about fund transfers. Compliance, security, low latency, from the first transaction, help your business connect globally and release growth potential.',
    },
  },
  dataBoard: {
    assetOverview: 'Asset Overview',
    recharge: 'Deposit',
    exchange: 'Conversion',
    withdrawal: 'Withdraw',
    totalAssets: 'Estimated Total Assets',
    availableAmount: 'Available',
    total: 'Total',
    noAsset: 'No Asset',
    processing: 'Processing',
    assetDynamics: 'Asset Dynamics',
    recent: 'Recent',
    processingStatus: 'Processing',
    completed: 'Completed',
    type: 'Type',
    currency: 'Currency',
    amount: 'Amount',
    status: 'Status',
    statusTypes: {
      processing: 'Processing',
      success: 'Success',
      failed: 'Failed',
      accepted: 'Accepted',
    },
    tradeTypes: {
      RECHARGE: 'Deposit',
      WITHDRAWAL: 'Withdrawal',
      EXCHANGE: 'Conversion',
      TRANSFER: 'Transfer',
    },
    todayExchangeRate: "Today's Conversion Rate Reference",
    depositCrypto: 'Deposit Cryptocurrency',
    depositDescription:
      'Fill in your deposit address to quickly add funds through blockchain and start your personalized Web3 journey',
    startUsing: 'Get Started',
    pleaseSelect: 'Please select',
  },
  ringChart: {
    assetDistribution: 'Asset Distribution',
    availableAmount: 'Available',
    processing: 'Processing',
  },
};
