<script lang="ts" setup>
import { onMounted,ref } from 'vue'
import { useUserStore } from "@/pinia/stores/user"
import Welcome from './components/Welcome.vue'
import DataBoard from './components/DataBoard.vue'
const userStore = useUserStore()
// await userStore.getInfo();

onMounted(async () => {
  await userStore.getInfo();
})
</script>

<template>
  <div class="home-container pb-100px">
    <Welcome v-if="userStore.kycStatus !== 'S'" />
    <DataBoard v-if="userStore.kycStatus === 'S'" />
  </div>
</template>

<style lang="scss" scoped>
</style>
