<template>
  <div class="app-container w-100% pb-100px">
    <div class="content-view min-w-600px min-h-390px flex flex-col justify-between mt-107px">
      <div>
        <el-row class="flex items-center">
          <div class="bg-[#FFEBF0]" style="border-radius: 6px;padding: 1px 3px;">
            <SvgIcon name="icon-exchange-pay" class="text-14px color-[#FF0064]" />
          </div>
          
          <span class="text-18px font-600 text-[#222527] ml-8px">{{ t('exchange.pay') }}</span>
        </el-row>

        <el-row
          class="mt-32px items-center pb-6px border-b-1 border-b-color-[#E5E6EB] border-b-style-solid"
        >
          <el-input
            ref="inputRef"
            v-model="inputAmt"
            class="flex-1 text-28px font-400"
            @blur="handleInputBlur"
            @input="handleInputChange"
            :placeholder="placeholder"
          />
          <div class="flex items-center justify-end">
            <el-button
              class="pl-24px pr-24px color-[#FF0064] font-14 font-400"
              text
              @click="handleMaxClick"
              >{{ t('exchange.max') }}</el-button
            >
            <div class="w-70px flex items-center justify-between">
              <div class="icon-currency-us" v-if="exchangeType === 'FIAT_EXCHANGE_CRYPTO'"></div>
              <SvgIcon
                name="icon-usdt"
                class="text-18px color-[#1BA27A]"
                v-if="exchangeType !== 'FIAT_EXCHANGE_CRYPTO'"
              />
              <span class="text-16px font-600 text-[#222527] ml-8px">{{
                exchangeType === 'FIAT_EXCHANGE_CRYPTO' ? 'USD' : 'USDT'
              }}</span>
            </div>
          </div>
        </el-row>
        <el-button
          class="color-[#FF0064] font-14 font-400 p-0 pt-16px"
          text
          @click="gotoRecharge"
          >{{ t('exchange.recharge') }}</el-button
        >
      </div>

      <el-row class="justify-center items-center">
        <el-divider class="flex-1" />
        <div
          @click="handleExchangeType"
          class="w-36px h-36px border-rd-50% bg-#030814 flex items-center justify-center ml-13px mr-13px cursor-pointer btn-hover-scale-sm"
        >
          <SvgIcon name="icon-exchange-c" class="text-20px color-white" />
        </div>
        <el-divider class="flex-1" />
      </el-row>

      <div>
        <el-row class="flex items-center">
          <div class="bg-[#FFEBF0]" style="border-radius: 6px;padding: 1px 3px;">
            <SvgIcon name="icon-exchange-receive" class="text-14px color-[#FF0064]" />
          </div>
          
          <span class="text-18px font-600 text-[#222527] ml-8px">{{ t('exchange.receive') }}</span>
        </el-row>

        <el-row
          class="mt-32px items-center justify-end pb-6px border-b-1 border-b-color-[#E5E6EB] border-b-style-solid"
        >
          <el-input
            ref="exchangeRef"
            v-model="exchangeAmt"
            v-if="!loading"
            disabled
            class="flex-1 text-28px font-400"
          />
          <LoadAnimation class="flex-1" v-else />
          <div class="w-70px flex items-center justify-between">
            <div class="icon-currency-us" v-if="exchangeType !== 'FIAT_EXCHANGE_CRYPTO'"></div>
            <SvgIcon
              name="icon-usdt"
              class="text-18px color-[#1BA27A]"
              v-if="exchangeType === 'FIAT_EXCHANGE_CRYPTO'"
            />
            <span class="text-16px font-600 text-[#222527] ml-8px">{{
              exchangeType === 'FIAT_EXCHANGE_CRYPTO' ? 'USDT' : 'USD'
            }}</span>
          </div>
        </el-row>
        <!-- <el-button class="color-[#FF0064] font-14 font-400 p-0 pt-16px " text>{{ t('exchange.recharge') }}</el-button> -->
      </div>
    </div>

    <el-row class="w-600px mt-16px flex-wrap">
      <div class="tip-view">{{ t('exchange.fee') }}：{{ fee }}</div>
      <div class="tip-view">{{ t('exchange.rate') }}：1 USD = 1 USDT</div>
      <div class="tip-view">{{ t('exchange.tip') }}</div>
    </el-row>

    <el-button type="primary" class="mt-32px w-230px h-32px btn-hover-scale-sm" @click="handleConfirm">{{
      t('exchange.confirmBtn')
    }}</el-button>
  </div>
</template>

<script lang="ts" setup>
/*
import { t } from '@@/i18n'
import LoadAnimation from '@@/components/LoadAnimation/LoadAnimation.vue'
import { InputInstance } from 'element-plus'
import { handleExchange, handleFeeCalc, queryWalletAcctDetail } from './apis/index'
import { ref, computed } from 'vue'
import { FeeEnumType } from './apis/type'
import { subtract, safeNumber, formatNumber, truncate } from '@@/utils/math'

// 初始化配置
const exchangeType = ref<FeeEnumType>(FeeEnumType.CRYPTO_EXCHANGE_FIAT) // 固定为数币兑法币
const fee = ref('0.00')
const inputAmt = ref<string>('')
const exchangeAmt = ref<string>('')
const loading = ref(false)
const router = useRouter()

// 新增小数位数配置 
// const decimalPlaces = computed(() =>
//   exchangeType.value === FeeEnumType.CRYPTO_EXCHANGE_FIAT ? 6 : 2
// )
// 所有情况都只保留两位小数
const decimalPlaces = ref(2)

// 计算属性（仅修改placeholder逻辑）
const placeholder = computed(() => {
  return t('exchange.inputPlaceholder')
})

// 新增输入处理逻辑
const handleInputChange = () => {
  let value = inputAmt.value.replace(/[^0-9.]/g, '') // 仅保留数字和小数点

  // 确保只有一个小数点
  const parts = value.split('.')
  if (parts.length > 2) {
    value = parts[0] + '.' + parts[1]
  }

  // 处理小数部分（最多两位）
  if (parts[1]) {
    parts[1] = parts[1].slice(0, 2)
    value = parts.join('.')
  }

  // 处理前导小数点（如.50 → 0.50）
  if (value.startsWith('.')) {
    value = '0' + value
  }

  inputAmt.value = value
}

// 修改最大值点击处理
const handleMaxClick = async () => {
  try {
    // 查询当前账户余额显示在页面上
    const res = await queryWalletAcctDetail(exchangeType.value)
    const maxAmount = res.data.balanceAvl || 0
    inputAmt.value = formattedAmt(maxAmount) || ''

    await calculateFee()
  } catch (error) {
    console.error('设置最大金额失败:', error)
  }
}

const calculateFee = async () => {
  try {
    let amount = safeNumber(inputAmt.value)

    if (amount <= 0) return

    // 根据decimalPlaces进行精确处理
    const precision = decimalPlaces.value
    amount = truncate(amount, precision)
    
    loading.value = true
    const feeRes = await handleFeeCalc({
      transAmt: amount,
      feeType: exchangeType.value,
    })

    // 计算换入金额（使用精确减法）
    fee.value = formattedAmt(feeRes.data.feeAmt) || '0.00'
    const feeAmount = safeNumber(feeRes.data.feeAmt)
    exchangeAmt.value = formattedAmt(subtract(amount, feeAmount, precision)) || '0.00'
    loading.value = false
  } catch (error) {
    console.error('计算手续费失败:', error)
    loading.value = false
  }
}

const formattedAmt = (amt: number | string, decimalPlaces: number = 2) => {
  if (amt === '' || amt === undefined) return

  const amount = safeNumber(amt)
  
  if (amount === 0 && amt !== 0) {
    ElMessage.warning(t('exchange.validation.enterValidAmount'))
    return ''
  }

  return formatNumber(amount, decimalPlaces)
}

// 输入框失焦处理
const handleInputBlur = async () => {
  const formatted = formattedAmt(inputAmt.value)
  inputAmt.value = formatted || ''

  await calculateFee()
}

// 切换兑换类型（当前固定为CRYPTO_EXCHANGE_FIAT，如需开放其他类型可取消注释）
const handleExchangeType = () => {
  // 添加过渡动画类
  const contentView = document.querySelector('.content-view');
  contentView?.classList.add('exchange-rotating');
  
  // 延迟执行实际的交换逻辑，等待动画完成
  setTimeout(() => {
    // 切换兑换类型
    exchangeType.value = exchangeType.value === FeeEnumType.CRYPTO_EXCHANGE_FIAT
    ? FeeEnumType.FIAT_EXCHANGE_CRYPTO
    : FeeEnumType.CRYPTO_EXCHANGE_FIAT
    inputAmt.value = ''
    exchangeAmt.value = ''
    fee.value = '0.00'
    
    // 移除动画类
    contentView?.classList.remove('exchange-rotating');
  }, 500); // 500ms 与 CSS 动画时长匹配
}
// 新增确认兑换方法
const handleConfirm = async () => {
  try {
    let amount = truncate(safeNumber(inputAmt.value), decimalPlaces.value)

    if (!amount || amount <= 0) {
      ElMessage.error(t('exchange.validation.enterValidAmount'))
      return
    }
    // 换出金额不能小于或等于手续费
    if(amount < 1){
      ElMessage.error(t('exchange.validation.amountLessThanOne'))
      return
    }
    // 换出金额不能小于或等于手续费
    if(amount <= parseFloat(fee.value)){
      ElMessage.error(t('exchange.validation.amountGreaterThanZero'))
      return
    }

    // 2. 构造请求参数（根据实际接口需求调整）
    const params = {
      sysSeqId: String(Date.now()), // 13位时间戳
      transCurrency: 'USD', // 固定值
      transAmt: amount,
      network: 'ETH', // 固定值
      coinSymbol: 'USDT', // 固定值
      targetCurrency: exchangeType.value === FeeEnumType.CRYPTO_EXCHANGE_FIAT ? 'USD' : 'USDT', // 法兑数为 USDT 数兑法 USD
    }
    // 3. 调用兑换接口
    const res = await handleExchange(params)
    router.push({
      path: '/exchange/result',
      query: {
        sysSeqId: res.data.sysSeqId,
      },
    })
  } catch (error) {
    console.error('兑换失败:', error)
  }
}
function gotoRecharge() {
  router.push('/recharge')
}
*/
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content-view {
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 12px;
  padding: 24px;
  transition: transform 0.5s ease;
}

/* 添加旋转动画类 */
.exchange-rotating {
  animation: rotate-exchange 0.5s ease;
}

@keyframes rotate-exchange {
  0% {
    transform: rotateX(0deg);
  }
  50% {
    transform: rotateX(90deg);
  }
  100% {
    transform: rotateX(0deg);
  }
}

.tip-view {
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-weight: 400;
  font-size: 14px;
  color: #6b7275;
  letter-spacing: 0;
  line-height: 20px;
  margin-right: 16px;
  margin-bottom: 16px;
}

.icon-currency-us {
  width: 16px;
  height: 16px;
  background-image: url('/src/common/assets/icons/icon-currency-us.svg');
  background-size: cover;
}

:deep(.el-input__wrapper) {
  box-shadow: none;
  border: none;
  padding: 0;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  box-shadow: none;
  border: none;
  background-color: transparent;
}

:deep(.el-button) {
  height: auto;
}

:deep(.el-input__inner) {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}
</style>