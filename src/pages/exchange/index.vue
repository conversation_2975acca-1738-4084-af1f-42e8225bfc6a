<template>
  <div class="app-container mx-auto pb-100px overflow-auto">
    <!-- 左边具体内容 -->
    <div class="p-24px flex flex-col min-w-834px min-h-700px  content-view">
      <!-- 标题栏 -->
      <el-row class="flex flex-row justify-between">
        <div class="title-label">{{ t('exchange.title') }}</div>
        <div class="flex justify-between items-center w-300px h-40px bg-#F5F5F5 border-rd-6px cursor-pointer" @click="handleExchangeType">
          <div class="line-height-40px text-center w-132px">
            <span class="text-18px text-[#222527] font-600">{{ exchangeType === FeeEnumType.CRYPTO_EXCHANGE_FIAT ? 'Fiat' : 'Crypto' }}</span>
          </div>
          <div class="w-36px h-36px border-rd-50% rotate-90 bg-#030814 flex items-center justify-center btn-hover-scale-sm">
            <SvgIcon name="icon-exchange-c" class="text-20px color-white" />
          </div>
          <div class="line-height-40px text-center w-132px">
            <span class="text-18px text-[#222527] font-600">{{ exchangeType === FeeEnumType.CRYPTO_EXCHANGE_FIAT ? 'Crypto' : 'Fiat' }}</span>
          </div>
        </div>
      </el-row>
      
      <div class="mt-24px w-1px bg-#E5E6EB absolute top-88px bottom-32px left-50%" />
      <FiatExchange class="flex-1" v-if="exchangeType === FeeEnumType.CRYPTO_EXCHANGE_FIAT" />
      <CryptoExchange class="flex-1" v-if="exchangeType === FeeEnumType.FIAT_EXCHANGE_CRYPTO" />
    </div>

    <!-- 右边提示信息 -->
    <div class="ml-24px p-24px bg-#F8F9FA b-rd-12px w-262px">
      <div>
        <p class="text-16px text-#222527 font-600">{{ t('withdrawal.aboutWithdrawalAmount') }}</p>
        <p class="text-#6B7275 text-14px mt-8px line-height-20px">
          <span>{{ t('withdrawal.aboutWithdrawalAmountTip') }}</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { t } from '@@/i18n'
import { FeeEnumType } from './apis/type'
import FiatExchange from './components/FiatExchange.vue'
import CryptoExchange from './components/CryptoExchange.vue'

// 初始化配置
const exchangeType = ref<FeeEnumType>(FeeEnumType.CRYPTO_EXCHANGE_FIAT)
// 切换兑换类型（当前固定为CRYPTO_EXCHANGE_FIAT，如需开放其他类型可取消注释）
const handleExchangeType = () => {
  // 添加过渡动画类
  const contentView = document.querySelector('.content-view');
  contentView?.classList.add('exchange-rotating');
  
  // 延迟执行实际的交换逻辑，等待动画完成
  setTimeout(() => {
    // 切换兑换类型
    exchangeType.value = exchangeType.value === FeeEnumType.CRYPTO_EXCHANGE_FIAT
    ? FeeEnumType.FIAT_EXCHANGE_CRYPTO
    : FeeEnumType.CRYPTO_EXCHANGE_FIAT
    
    // 移除动画类
    contentView?.classList.remove('exchange-rotating');
  }, 500); // 500ms 与 CSS 动画时长匹配
}
</script>

<style lang="scss" scoped>

p {
  margin: 0;
}

.app-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
}

.content-view {
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 12px;
  padding: 24px;
  position: relative;
}

/* 添加旋转动画类 */
.exchange-rotating {
  animation: rotate-exchange 0.5s ease;
}
// 左右翻转动画
@keyframes rotate-exchange {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}
.title-label {
  font-weight: 600;
  font-size: 28px;
  color: #222527;
}

:deep(.el-button) {
  height: auto;
}
</style>