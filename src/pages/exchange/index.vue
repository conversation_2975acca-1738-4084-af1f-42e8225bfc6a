<script lang="ts" setup>
import { ref, watch, nextTick, onMounted, onUnmounted, computed, provide } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import QuoteExchange from './components/QuoteExchange.vue';
import OneTOneExchange from './components/OneTOneExchange.vue';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { BusinessEnumType, FeeQuotaResponseData } from '@/common/apis/common/type';
import { feeQuotaApi } from '@/common/apis/common';
import { FeeEnumType } from './apis/type';

const { t } = useI18n();
const router = useRouter();
const route = useRoute();

// 获取枚举数据
const enumStore = useEnumStore();
const fiatCurrencyList = enumStore.getEnumList(BusinessEnumType.FIAT_CCY);
const coinSymbolList = enumStore.getEnumList(BusinessEnumType.CURRENCY);
const cryptoNetworkList = enumStore.getEnumList(BusinessEnumType.CRYPTO_NET);

// Active tab state with smooth animation support
const activeTab = ref<'quote' | '1t1'>('quote');
// Tab container and slider refs for animation
const tabsRef = ref<HTMLElement>();
const sliderRef = ref<HTMLElement>();
const quoteExchangeRef = ref<any>();
const exchangeRate = ref<string>('-');
const rateUpdateTime = ref<string>('-');
const countdown = ref<number>(0);

// 牌价-处理子组件传递的汇率信息
const handleRateInfoUpdate = (data: { exchangeRate: string; rateUpdateTime: string; countdown: number }) => {
    if (data.exchangeRate) {
        exchangeRate.value = data.exchangeRate;
    }
    if (data.rateUpdateTime) {
        rateUpdateTime.value = data.rateUpdateTime;
    }
    countdown.value = data.countdown;
    console.log('handleRateInfoUpdate', data)
};

// 1:1兑换相关的变量定义
const animatedTodayPercent = ref(0);
const animatedMonthPercent = ref(0);
const rateString = ref('--');
const fee = ref('--');

// 1:1兑换类型管理
const exchangeType = ref<FeeEnumType>(FeeEnumType.FIAT_EXCHANGE_CRYPTO);
const isAnimating = ref(false);
const rotationDegree = ref(90); // 追踪当前旋转角度

// 额度配置
const feeQuota = ref<FeeQuotaResponseData['data'] | null>(null);
const animationDuration = 400; // 动画持续时间（毫秒）
const isDataLoaded = ref(false); // 数据加载标识

// 切换兑换类型函数声明
const handleExchangeType = () => {
  if (isAnimating.value) return // 防止重复点击

  // 开始动画
  isAnimating.value = true

  // 累积旋转180度
  rotationDegree.value += 180

  // 延迟切换数据，让动画效果更自然
  setTimeout(() => {
    // 切换兑换类型
    exchangeType.value = exchangeType.value === FeeEnumType.CRYPTO_EXCHANGE_FIAT
      ? FeeEnumType.FIAT_EXCHANGE_CRYPTO
      : FeeEnumType.CRYPTO_EXCHANGE_FIAT

    rateString.value = '--'
    fee.value = '--'
  }, 150)

  // 动画结束后重置状态
  setTimeout(() => {
    isAnimating.value = false
  }, 600) // 600ms匹配动画时长
}

// 通过 provide 向子组件传递数据
provide('fee', fee);
provide('rateString', rateString);
provide('exchangeType', exchangeType);
provide('handleExchangeType', handleExchangeType);
provide('rotationDegree', rotationDegree);

// 计算属性：今日额度百分比
const todayPercent = computed(() => {
  if (!feeQuota.value) return 0
  return (
    exchangeType.value === FeeEnumType.FIAT_EXCHANGE_CRYPTO ?
      (feeQuota.value.fiatExchangeCryptoDailyUsed / feeQuota.value.dailyFiatToCrypto) :
      (feeQuota.value.cryptoExchangeFiatDailyUsed / feeQuota.value.dailyCryptoToFiat)
  ) * 100
})

// 计算属性：今日额度显示文本
const todayAmount = computed(() => {
  if (!feeQuota.value) return '--'
  return (
    exchangeType.value === FeeEnumType.FIAT_EXCHANGE_CRYPTO ?
      `${feeQuota.value.fiatExchangeCryptoDailyUsed}/${feeQuota.value.dailyFiatToCrypto}` :
      `${feeQuota.value.cryptoExchangeFiatDailyUsed}/${feeQuota.value.dailyCryptoToFiat}`
  )
})

// 计算属性：月最大使用额度
const maxMonthAmount = computed(() => {
  if (!feeQuota.value) return 0
  return (
    exchangeType.value === FeeEnumType.FIAT_EXCHANGE_CRYPTO ?
      (feeQuota.value.fiatExchangeCryptoLast30DaysUsed > feeQuota.value.fiatExchangeCryptoMonthlyUsed ? feeQuota.value.fiatExchangeCryptoLast30DaysUsed : feeQuota.value.fiatExchangeCryptoMonthlyUsed) :
      (feeQuota.value.cryptoExchangeFiatLast30DaysUsed > feeQuota.value.cryptoExchangeFiatMonthlyUsed ? feeQuota.value.cryptoExchangeFiatLast30DaysUsed : feeQuota.value.cryptoExchangeFiatMonthlyUsed)
  )
})

// 计算属性：月额度百分比
const monthPercent = computed(() => {
  if (!feeQuota.value) return 0
  return (
    exchangeType.value === FeeEnumType.FIAT_EXCHANGE_CRYPTO ?
      (maxMonthAmount.value / feeQuota.value.monthlyFiatToCrypto) :
      (maxMonthAmount.value / feeQuota.value.monthlyCryptoToFiat)
  ) * 100
})

// 计算属性：月额度显示文本
const monthAmount = computed(() => {
  if (!feeQuota.value) return '--'
  return (
    exchangeType.value === FeeEnumType.FIAT_EXCHANGE_CRYPTO ?
      `${maxMonthAmount.value}/${feeQuota.value.monthlyFiatToCrypto}` :
      `${maxMonthAmount.value}/${feeQuota.value.monthlyCryptoToFiat}`
  )
})

// 进度条动画函数
const animateProgress = (targetValue: number, animatedRef: typeof animatedTodayPercent) => {
  const startTime = performance.now()
  const startValue = animatedRef.value
  const targetDiff = targetValue - startValue

  const animate = (currentTime: number) => {
    const elapsed = (currentTime - startTime) < 0 ? 0 : (currentTime - startTime)
    const progress = Math.min(elapsed / animationDuration, 1)

    // 将缓动函数替换为直接使用 progress，实现匀速
    animatedRef.value = startValue + (targetDiff * progress)

    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      animatedRef.value = targetValue
    }
  }

  requestAnimationFrame(animate)
}

// 进度条动画完成回调
const onProgressSectionTransitionComplete = () => {
  // 进度条区域的 Transition 完成，现在可以安全地触发动画
  // 但需要确保数据已经加载完成
  if (isDataLoaded.value) {
    nextTick(() => {
      animateProgress(todayPercent.value, animatedTodayPercent)
      animateProgress(monthPercent.value, animatedMonthPercent)
    })
  }
}



/**
 * 更新滑动指示器位置
 * 根据当前激活的tab计算并设置滑动指示器的位置和宽度
 */
const updateSliderPosition = async () => {
    await nextTick();
    if (!tabsRef.value || !sliderRef.value) return;

    const activeButton = tabsRef.value.querySelector('.tab-button.active') as HTMLElement;
    if (!activeButton) return;

    const tabsRect = tabsRef.value.getBoundingClientRect();
    const buttonRect = activeButton.getBoundingClientRect();

    // 计算相对于tabs容器的位置
    const left = buttonRect.left - tabsRect.left;
    const width = buttonRect.width;

    // 应用平滑过渡动画
    sliderRef.value.style.transform = `translateX(${left}px)`;
    sliderRef.value.style.width = `${width}px`;
};

/**
 * 切换tab标签
 * @param tab - 要切换到的tab类型
 */
const switchTab = async (tab: 'quote' | '1t1') => {
    if (activeTab.value === tab) return;

    // 如果切换到quote标签，清除当前汇率数据并重置状态
    if (tab === 'quote') {
        // 清除右侧汇率显示数据
        exchangeRate.value = '-';
        rateUpdateTime.value = '-';
        countdown.value = 0;

        // 等待DOM更新后调用子组件的清除方法
        await nextTick();
        if (quoteExchangeRef.value && typeof quoteExchangeRef.value.clearRateData === 'function') {
            quoteExchangeRef.value.clearRateData();
        }
    }

    activeTab.value = tab;
    await updateSliderPosition();
};

// 监听数据加载完成
watch(isDataLoaded, (loaded) => {
  if (loaded) {
    // 数据加载完成，立即触发动画（如果Transition已经完成）
    nextTick(() => {
      animateProgress(todayPercent.value, animatedTodayPercent)
      animateProgress(monthPercent.value, animatedMonthPercent)
    })
  }
})

// 监听兑换类型变化，重置动画值
watch(exchangeType, () => {
  // 重置动画值，等待 Transition 完成后再触发动画
  animatedTodayPercent.value = 0
  animatedMonthPercent.value = 0
})

// Watch for route changes to update active tab
watch(
    () => route.query.type,
    async (newType) => {
        if (newType === '1t1') {
            activeTab.value = '1t1';
        } else {
            activeTab.value = 'quote';
        }
        await updateSliderPosition();
    },
    { immediate: true }
);

// Watch for active tab changes to update slider position
watch(activeTab, updateSliderPosition);


onMounted(async () => {
    // 初始化滑动指示器位置
    await updateSliderPosition();

    // 监听窗口大小变化，重新计算滑动指示器位置
    window.addEventListener('resize', updateSliderPosition);
    
    // 加载1:1兑换所需的额度数据
    try {
        const res = await feeQuotaApi();
        feeQuota.value = res.data;
        // 标记数据加载完成
        isDataLoaded.value = true;
    } catch (error) {
        console.error('加载额度数据失败:', error);
    }
});

// 组件卸载时清理事件监听器
onUnmounted(() => {
    window.removeEventListener('resize', updateSliderPosition);
});
</script>

<template>
    <div class="exchange-container">
        <div class="exchange-content">
            <!-- Main Content Area -->
            <div class="exchange-main">
                <!-- Header with title and tabs aligned -->
                <div class="exchange-header">
                    <h1 class="exchange-title">{{ t('exchange.title') }}</h1>
                    <!-- Tab Navigation with smooth slider -->
                    <div ref="tabsRef" class="exchange-tabs">
                        <!-- 滑动指示器背景 -->
                        <div ref="sliderRef" class="tab-slider"></div>
                        <button class="tab-button" :class="{ active: activeTab === 'quote' }"
                            @click="switchTab('quote')">
                            {{ t('exchange.dingxQuote') }}
                        </button>
                        <button class="tab-button" :class="{ active: activeTab === '1t1' }" @click="switchTab('1t1')">
                            {{ t('exchange.oneToOneExchange') }}
                        </button>
                    </div>
                </div>

                <!-- Exchange Form Content with smooth transitions -->
                <div class="exchange-form-content">
                    <Transition  v-show="activeTab === 'quote'" name="fade" mode="out-in">
                        <QuoteExchange key="quote" ref="quoteExchangeRef"
                            :coin-symbol-list="coinSymbolList" :cryptoNetworkList="cryptoNetworkList"
                            :fiat-currency-list="fiatCurrencyList" @update-rate-info="handleRateInfoUpdate" />
                    </Transition>
                    <Transition v-show="activeTab === '1t1'" name="fade" mode="out-in">
                        <OneTOneExchange key="1t1" ref="oneTOneExchangeRef" :coin-symbol-list="coinSymbolList"
                            :cryptoNetworkList="cryptoNetworkList" :fiat-currency-list="fiatCurrencyList" />
                    </Transition>
                </div>
            </div>

            <!-- Sidebar with smooth transitions -->
            <div class="exchange-sidebar-container">
                <!-- 牌价右边提示信息 -->
                <Transition v-if="activeTab === 'quote'" name="slide-fade" mode="out-in" appear>
                    <div class="p-24px bg-#F8F9FA b-rd-12px w-262px min-w-262px" :key="activeTab">
                        <Transition name="fade-slide" appear>
                            <div key="operation-tip" class="sidebar-item-animated" style="--animation-delay: 0ms">
                                <div class="text-16px text-#222527 font-600">{{ t('exchange.operationTip') }}</div>
                                <div class="text-#6B7275 text-14px mt-8px line-height-20px">
                                    <span>{{ t('exchange.exchangeRateLabel') }}：{{ exchangeRate }}</span>
                                </div>
                                <div class="text-#6B7275 text-12px line-height-20px mt-4px">
                                    <span>{{ t('exchange.updateTime') }}：{{ rateUpdateTime }}</span>
                                    <span v-if="countdown > 0" class="ml-8px"
                                        style="font-size: 12px; color: #FF0064;">{{ countdown }}s</span>
                                </div>
                            </div>
                        </Transition>
                        <Transition name="fade-slide" appear>
                            <div key="about-tip" class="mt-24px sidebar-item-animated" style="--animation-delay: 100ms">
                                <div class="text-16px text-#222527 font-600">{{ t('exchange.aboutExchangeTip') }}</div>
                                <div class="text-#6B7275 text-14px mt-8px line-height-20px">
                                    <span>{{ t('exchange.aboutExchangeTipDesc') }}</span>
                                </div>
                            </div>
                        </Transition>
                    </div>
                </Transition>

                <!-- 1:1右边提示信息 -->
                <Transition v-else name="slide-fade" mode="out-in" appear>
                    <div class="p-24px bg-#F8F9FA b-rd-12px w-262px min-w-262px" :key="activeTab">
                        <Transition name="fade-slide" appear @after-enter="onProgressSectionTransitionComplete">
                            <div key="operation-tip" class="sidebar-item-animated" style="--animation-delay: 0ms">
                                <p class="text-16px text-#222527 font-600">{{ t('exchange.availablePayAmountTitle') }}
                                </p>
                                <p class="text-14px text-#222527 font-400 mt-8px">{{ t('exchange.today') }}</p>
                                <!-- 进度条 -->
                                <el-progress :percentage="animatedTodayPercent" color="#FF0064" :show-text="false"
                                    :stroke-width="10" class="mt-8px" />
                                <p class="text-12px text-#6B7275 font-400 mt-4px">{{ t('exchange.usdEquivalent') }} {{
                                    todayAmount }}</p>
                                <p class="text-14px text-#222527 font-400 mt-16px">{{ t('exchange.month') }}</p>
                                <!-- 进度条 -->
                                <el-progress :percentage="animatedMonthPercent" color="#FF0064" :show-text="false"
                                    :stroke-width="10" class="mt-8px" />
                                <p class="text-12px text-#6B7275 font-400 mt-4px">{{ t('exchange.usdEquivalent') }} {{
                                    monthAmount }}</p>
                            </div>
                        </Transition>
                        <Transition name="fade-slide" appear>
                            <div key="operation-tip" class="sidebar-item-animated" style="--animation-delay: 0ms">
                                <p class="text-16px text-#222527 font-600 mt-24px">{{ t('exchange.operationTip') }}</p>
                                <p class="text-#6B7275 text-14px mt-8px line-height-20px">
                                    <span>{{ t('exchange.currentRateTip', { rateString: rateString }) }}</span>
                                </p>
                                <p class="text-#6B7275 text-14px line-height-20px">
                                    <span>{{ t('exchange.feeTip', { fee: fee }) }}</span>
                                </p>
                            </div>
                        </Transition>
                        <Transition name="fade-slide" appear>
                            <div key="about-tip" class="mt-24px sidebar-item-animated" style="--animation-delay: 100ms">
                                <p class="text-16px text-#222527 font-600">{{ t('exchange.aboutExchangeTip') }}</p>
                                <p class="text-#6B7275 text-14px mt-8px line-height-20px">
                                    <span>{{ t('exchange.aboutExchangeTipDesc') }}</span>
                                </p>
                            </div>
                        </Transition>
                    </div>
                </Transition>
            </div>
        </div>

    </div>
</template>

<style lang="scss" scoped>
.exchange-container {
    background-color: #ffffff;
    padding: 32px 40px;
    min-width: 1120px;
}

.exchange-content {
    display: flex;
    gap: 24px;
    justify-content: center;
    height: fit-content;
    margin-bottom: 32px;
}

/* Main Content Area */
.exchange-main {
    flex: 1;
    padding: 24px;
    min-width: 834px;
    background: #ffffff;
    border: 1px solid #e5e6eb;
    border-radius: 12px;
    height: fit-content;
}

/* Header with title and tabs aligned */
.exchange-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.exchange-title {
    font-family: 'PingFangSC-Semibold';
    font-weight: 600;
    font-size: 28px;
    color: #222527;
    margin: 0;
}

/* Tab Navigation with smooth animation */
.exchange-tabs {
    display: flex;
    background-color: #f5f5f5;
    border-radius: 6px;
    padding: 4px;
    width: fit-content;
    position: relative;
    overflow: hidden;
}

/* 滑动指示器 - 提供平滑的背景滑动效果 */
.tab-slider {
    position: absolute;
    top: 4px;
    left: 4px;
    height: 32px;
    background: linear-gradient(135deg, #030814 0%, #1a1d29 100%);
    border-radius: 4px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 1;
    /* 确保在移动设备上也有硬件加速 */
    will-change: transform, width;
    transform: translateX(0px);
    box-shadow:
        0 2px 8px rgba(3, 8, 20, 0.15),
        0 1px 3px rgba(3, 8, 20, 0.1);

    /* 动画期间增强阴影效果 */
    transition:
        all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        box-shadow 0.3s ease;

    /* 添加微妙的光泽效果 */
    &::before {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        right: 1px;
        height: 50%;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        border-radius: 3px 3px 0 0;
        pointer-events: none;
    }
}

.tab-button {
    border: none;
    height: 32px;
    width: 144px;
    background: transparent;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 400;
    cursor: pointer;
    font-family: 'PingFangSC-Regular';
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #030814;
    position: relative;
    z-index: 2;
    /* 确保文字在滑动指示器之上 */
    transform: scale(1);
    will-change: transform, color;

    /* 激活状态 */
    &.active {
        color: #ffffff;
        font-weight: 500;
        transform: scale(1.02);
    }

    /* 悬停效果 */
    &:hover:not(.active) {
        color: #666;
        transform: scale(1.01);
    }

    /* 点击效果 */
    &:active {
        transform: scale(0.98);
        transition-duration: 0.1s;
    }

    /* 焦点状态 */
    &:focus-visible {
        outline: 2px solid #030814;
        outline-offset: 2px;
    }
}

.exchange-form-content {
    width: 100%;
}

/* Sidebar */
.exchange-sidebar-container {
    flex-shrink: 0;
    width: 264px;
}

/* Vue Transition Animations */

/* 淡入淡出过渡效果 - 用于主要内容区域 */
.fade-enter-active,
.fade-leave-active {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-enter-from {
    opacity: 0;
    transform: translateY(10px);
}

.fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

/* 滑动淡入过渡效果 - 用于侧边栏内容 */
.slide-fade-enter-active,
.slide-fade-leave-active {
    transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-fade-enter-from {
    opacity: 0;
    transform: translateY(15px) scale(0.98);
}

.slide-fade-leave-to {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
}
</style>
