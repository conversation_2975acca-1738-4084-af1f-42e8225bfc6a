<template>
  <div class="app-container mx-auto pb-100px overflow-auto">
    <!-- 左边具体内容 -->
    <div class="p-24px flex flex-col min-w-834px min-h-700px  content-view">
      <!-- 标题栏 -->
      <el-row class="flex flex-row justify-between">
        <div class="title-label">{{ t('exchange.title') }}</div>
        <div class="flex justify-between items-center w-300px h-40px bg-#F5F5F5 border-rd-6px cursor-pointer" @click="handleExchangeType">
          <div class="line-height-40px text-center w-132px">
            <span class="text-18px text-[#222527] font-600">{{ exchangeType === FeeEnumType.CRYPTO_EXCHANGE_FIAT ? 'Fiat' : 'Crypto' }}</span>
          </div>
          <div class="w-36px h-36px border-rd-50% rotate-90 bg-#030814 flex items-center justify-center btn-hover-scale-sm">
            <SvgIcon name="icon-exchange-c" class="text-20px color-white" />
          </div>
          <div class="line-height-40px text-center w-132px">
            <span class="text-18px text-[#222527] font-600">{{ exchangeType === FeeEnumType.CRYPTO_EXCHANGE_FIAT ? 'Crypto' : 'Fiat' }}</span>
          </div>
        </div>
      </el-row>
      
      <div class="mt-24px w-1px bg-#E5E6EB absolute top-88px bottom-32px left-50%" />
      <FiatExchange class="flex-1" v-if="exchangeType === FeeEnumType.CRYPTO_EXCHANGE_FIAT" />
      <CryptoExchange class="flex-1" v-if="exchangeType === FeeEnumType.FIAT_EXCHANGE_CRYPTO" />
    </div>

    <!-- 右边提示信息 -->
    <div class="ml-24px p-24px bg-#F8F9FA b-rd-12px w-262px">
      <div>
        <p class="text-16px text-#222527 font-600">{{ t('withdrawal.aboutWithdrawalAmount') }}</p>
        <p class="text-#6B7275 text-14px mt-8px line-height-20px">
          <span>{{ t('withdrawal.aboutWithdrawalAmountTip') }}</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { t } from '@@/i18n'
import LoadAnimation from '@@/components/LoadAnimation/LoadAnimation.vue'
import { InputInstance } from 'element-plus'
import { handleExchange, handleFeeCalc } from './apis/index'
import { ref, computed } from 'vue'
import { FeeEnumType } from './apis/type'
import { subtract, safeNumber, formatNumber, truncate } from '@@/utils/math'
import FiatExchange from './components/FiatExchange.vue'
import CryptoExchange from './components/CryptoExchange.vue'

// 初始化配置
const exchangeType = ref<FeeEnumType>(FeeEnumType.CRYPTO_EXCHANGE_FIAT)
const fee = ref('0.00')
const inputAmt = ref<string>('')
const exchangeAmt = ref<string>('')
const loading = ref(false)
const router = useRouter()

// 所有情况都只保留两位小数
const decimalPlaces = ref(2)

// 计算属性（仅修改placeholder逻辑）
const placeholder = computed(() => {
  return t('exchange.inputPlaceholder')
})

// 新增输入处理逻辑
const handleInputChange = () => {
  let value = inputAmt.value.replace(/[^0-9.]/g, '') // 仅保留数字和小数点

  // 确保只有一个小数点
  const parts = value.split('.')
  if (parts.length > 2) {
    value = parts[0] + '.' + parts[1]
  }

  // 处理小数部分（最多两位）
  if (parts[1]) {
    parts[1] = parts[1].slice(0, 2)
    value = parts.join('.')
  }

  // 处理前导小数点（如.50 → 0.50）
  if (value.startsWith('.')) {
    value = '0' + value
  }

  inputAmt.value = value
}

const calculateFee = async () => {
  try {
    let amount = safeNumber(inputAmt.value)

    if (amount <= 0) return

    // 根据decimalPlaces进行精确处理
    const precision = decimalPlaces.value
    amount = truncate(amount, precision)
    
    loading.value = true
    const feeRes = await handleFeeCalc({
      transAmt: amount,
      feeType: exchangeType.value,
    })

    // 计算换入金额（使用精确减法）
    fee.value = formattedAmt(feeRes.data.feeAmt) || '0.00'
    const feeAmount = safeNumber(feeRes.data.feeAmt)
    exchangeAmt.value = formattedAmt(subtract(amount, feeAmount, precision)) || '0.00'
    loading.value = false
  } catch (error) {
    console.error('计算手续费失败:', error)
    loading.value = false
  }
}

const formattedAmt = (amt: number | string, decimalPlaces: number = 2) => {
  if (amt === '' || amt === undefined) return

  const amount = safeNumber(amt)
  
  if (amount === 0 && amt !== 0) {
    ElMessage.warning(t('exchange.validation.enterValidAmount'))
    return ''
  }

  return formatNumber(amount, decimalPlaces)
}

// 输入框失焦处理
const handleInputBlur = async () => {
  const formatted = formattedAmt(inputAmt.value)
  inputAmt.value = formatted || ''

  await calculateFee()
}

// 切换兑换类型（当前固定为CRYPTO_EXCHANGE_FIAT，如需开放其他类型可取消注释）
const handleExchangeType = () => {
  // 添加过渡动画类
  const contentView = document.querySelector('.content-view');
  contentView?.classList.add('exchange-rotating');
  
  // 延迟执行实际的交换逻辑，等待动画完成
  setTimeout(() => {
    // 切换兑换类型
    exchangeType.value = exchangeType.value === FeeEnumType.CRYPTO_EXCHANGE_FIAT
    ? FeeEnumType.FIAT_EXCHANGE_CRYPTO
    : FeeEnumType.CRYPTO_EXCHANGE_FIAT
    inputAmt.value = ''
    exchangeAmt.value = ''
    fee.value = '0.00'
    
    // 移除动画类
    contentView?.classList.remove('exchange-rotating');
  }, 500); // 500ms 与 CSS 动画时长匹配
}
// 新增确认兑换方法
const handleConfirm = async () => {
  try {
    let amount = truncate(safeNumber(inputAmt.value), decimalPlaces.value)

    if (!amount || amount <= 0) {
      ElMessage.error(t('exchange.validation.enterValidAmount'))
      return
    }
    // 换出金额不能小于或等于手续费
    if(amount < 1){
      ElMessage.error(t('exchange.validation.amountLessThanOne'))
      return
    }
    // 换出金额不能小于或等于手续费
    if(amount <= parseFloat(fee.value)){
      ElMessage.error(t('exchange.validation.amountGreaterThanZero'))
      return
    }

    // 2. 构造请求参数（根据实际接口需求调整）
    const params = {
      sysSeqId: String(Date.now()), // 13位时间戳
      transCurrency: 'USD', // 固定值
      transAmt: amount,
      network: 'ETH', // 固定值
      coinSymbol: 'USDT', // 固定值
      targetCurrency: exchangeType.value === FeeEnumType.CRYPTO_EXCHANGE_FIAT ? 'USD' : 'USDT', // 法兑数为 USDT 数兑法 USD
    }
    // 3. 调用兑换接口
    const res = await handleExchange(params)
    router.push({
      path: '/exchange/result',
      query: {
        sysSeqId: res.data.sysSeqId,
      },
    })
  } catch (error) {
    console.error('兑换失败:', error)
  }
}
function gotoRecharge() {
  router.push('/recharge')
}
</script>

<style lang="scss" scoped>

p {
  margin: 0;
}

.app-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
}

.content-view {
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 12px;
  padding: 24px;
  position: relative;
}

/* 添加旋转动画类 */
.exchange-rotating {
  animation: rotate-exchange 0.5s ease;
}
// 左右翻转动画
@keyframes rotate-exchange {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}
.title-label {
  font-weight: 600;
  font-size: 28px;
  color: #222527;
}

:deep(.el-button) {
  height: auto;
}
</style>