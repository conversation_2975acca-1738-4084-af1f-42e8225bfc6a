<template>
  <div class="py-24px min-w-834px min-h-700px overflow-auto">
    <!-- 左边具体内容 -->
    <div class="relative flex flex-col justify-between w-100%">
      <!-- 分割线容器 - 相对定位水平居中 -->
      <div
        class="absolute top-0 left-50% transform -translate-x-50% w-32px flex flex-col items-center justify-center h-606px z-10">
        <!-- 上连接线 -->
        <div class="w-1px bg-#E5E6EB flex-1" />
        <!-- 图标容器，上下各留4px边距 -->
        <div class="my-4px">
          <div
            class="w-32px h-32px border-rd-50% flex items-center justify-center btn-hover-scale-sm exchange-icon-container"
            @click="handleExchangeType" :style="{ transform: `rotate(${rotationDegree}deg)` }">
            <SvgIcon name="icon-exchange-c" class="color-white" />
          </div>
        </div>
        <!-- 下连接线 -->
        <div class="w-1px bg-#E5E6EB flex-1" />
      </div>
      <Transition name="fade" mode="out-in">
        <FiatExchange class="flex-1" :coin-symbol-list="coinSymbolList" :fiat-currency-list="fiatCurrencyList"
          :crypto-network-list="cryptoNetworkList" v-if="exchangeType === FeeEnumType.FIAT_EXCHANGE_CRYPTO"
          key="fiat-exchange" />
        <CryptoExchange class="flex-1" :coin-symbol-list="coinSymbolList" :fiat-currency-list="fiatCurrencyList"
          :crypto-network-list="cryptoNetworkList" v-else key="crypto-exchange" />
      </Transition>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { t } from '@@/i18n'
import { FeeEnumType } from '../apis/type'
import FiatExchange from './FiatExchange.vue'
import CryptoExchange from './CryptoExchange.vue'
import { useEnumStore } from '@/pinia/stores/enumStore'
import { BusinessEnumType, FeeQuotaResponseData } from '@/common/apis/common/type'
import { feeQuotaApi } from '@/common/apis/common'
// 初始化配置
const exchangeType = ref<FeeEnumType>(FeeEnumType.FIAT_EXCHANGE_CRYPTO)
const isAnimating = ref(false)
const rotationDegree = ref(90) // 追踪当前旋转角度

const fee = ref('--')
const rateString = ref('--')
provide('fee', fee)
provide('rateString', rateString)

const enumStore = useEnumStore();
const fiatCurrencyList = enumStore.getEnumList(BusinessEnumType.FIAT_CCY);
const coinSymbolList = enumStore.getEnumList(BusinessEnumType.CURRENCY);
const cryptoNetworkList = enumStore.getEnumList(BusinessEnumType.CRYPTO_NET);

// 切换兑换类型
const handleExchangeType = () => {
  if (isAnimating.value) return // 防止重复点击

  // 开始动画
  isAnimating.value = true

  // 累积旋转180度
  rotationDegree.value += 180

  // 延迟切换数据，让动画效果更自然
  setTimeout(() => {
    // 切换兑换类型
    exchangeType.value = exchangeType.value === FeeEnumType.CRYPTO_EXCHANGE_FIAT
      ? FeeEnumType.FIAT_EXCHANGE_CRYPTO
      : FeeEnumType.CRYPTO_EXCHANGE_FIAT

    rateString.value = '--'
    fee.value = '--'
  }, 150)

  // 动画结束后重置状态
  setTimeout(() => {
    isAnimating.value = false
  }, 600) // 600ms匹配动画时长
}

// 额度配置
const feeQuota = ref<FeeQuotaResponseData['data'] | null>(null);

// 进度条动画相关
const animatedTodayPercent = ref(0)
const animatedMonthPercent = ref(0)
const animationDuration = 400 // 动画持续时间（毫秒）
const isDataLoaded = ref(false) // 数据加载标识

const todayPercent = computed(() => {
  if (!feeQuota.value) return 0
  return (
    exchangeType.value === FeeEnumType.FIAT_EXCHANGE_CRYPTO ?
      (feeQuota.value.fiatExchangeCryptoDailyUsed / feeQuota.value.dailyFiatToCrypto) :
      (feeQuota.value.cryptoExchangeFiatDailyUsed / feeQuota.value.dailyCryptoToFiat)
  ) * 100
})
const todayAmount = computed(() => {
  if (!feeQuota.value) return '--'
  return (
    exchangeType.value === FeeEnumType.FIAT_EXCHANGE_CRYPTO ?
      `${feeQuota.value.fiatExchangeCryptoDailyUsed}/${feeQuota.value.dailyFiatToCrypto}` :
      `${feeQuota.value.cryptoExchangeFiatDailyUsed}/${feeQuota.value.dailyCryptoToFiat}`
  )
})

// 月额度显示的时候，要判断30天和一个月的使用情况
const maxMonthAmount = computed(() => {
  if (!feeQuota.value) return 0
  return (
    exchangeType.value === FeeEnumType.FIAT_EXCHANGE_CRYPTO ?
      (feeQuota.value.fiatExchangeCryptoLast30DaysUsed > feeQuota.value.fiatExchangeCryptoMonthlyUsed ? feeQuota.value.fiatExchangeCryptoLast30DaysUsed : feeQuota.value.fiatExchangeCryptoMonthlyUsed) :
      (feeQuota.value.cryptoExchangeFiatLast30DaysUsed > feeQuota.value.cryptoExchangeFiatMonthlyUsed ? feeQuota.value.cryptoExchangeFiatLast30DaysUsed : feeQuota.value.cryptoExchangeFiatMonthlyUsed)
  )
})
const monthPercent = computed(() => {
  if (!feeQuota.value) return 0
  return (
    exchangeType.value === FeeEnumType.FIAT_EXCHANGE_CRYPTO ?
      (maxMonthAmount.value / feeQuota.value.monthlyFiatToCrypto) :
      (maxMonthAmount.value / feeQuota.value.monthlyCryptoToFiat)
  ) * 100
})
const monthAmount = computed(() => {
  if (!feeQuota.value) return '--'
  return (
    exchangeType.value === FeeEnumType.FIAT_EXCHANGE_CRYPTO ?
      `${maxMonthAmount.value}/${feeQuota.value.monthlyFiatToCrypto}` :
      `${maxMonthAmount.value}/${feeQuota.value.monthlyCryptoToFiat}`
  )
})

// 进度条动画函数
const animateProgress = (targetValue: number, animatedRef: Ref<number>) => {
  const startTime = performance.now()
  const startValue = animatedRef.value
  const targetDiff = targetValue - startValue

  const animate = (currentTime: number) => {
    const elapsed = (currentTime - startTime) < 0 ? 0 : (currentTime - startTime)
    const progress = Math.min(elapsed / animationDuration, 1)

    // 将缓动函数替换为直接使用 progress，实现匀速
    animatedRef.value = startValue + (targetDiff * progress)

    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      animatedRef.value = targetValue
    }
  }

  requestAnimationFrame(animate)
}

const onProgressSectionTransitionComplete = () => {
  // 进度条区域的 Transition 完成，现在可以安全地触发动画
  // 但需要确保数据已经加载完成
  if (isDataLoaded.value) {
    nextTick(() => {
      animateProgress(todayPercent.value, animatedTodayPercent)
      animateProgress(monthPercent.value, animatedMonthPercent)
    })
  }
}

// 监听数据加载完成
watch(isDataLoaded, (loaded) => {
  if (loaded) {
    // 数据加载完成，立即触发动画（如果Transition已经完成）
    nextTick(() => {
      animateProgress(todayPercent.value, animatedTodayPercent)
      animateProgress(monthPercent.value, animatedMonthPercent)
    })
  }
})

// 监听兑换类型变化，重置动画值
watch(exchangeType, () => {
  // 重置动画值，等待 Transition 完成后再触发动画
  animatedTodayPercent.value = 0
  animatedMonthPercent.value = 0
})
onMounted(() => {
  feeQuotaApi().then((res) => {
    feeQuota.value = res.data;

    // 标记数据加载完成
    isDataLoaded.value = true
  })
})


// 显式导出组件以解决 TypeScript 导入问题
defineOptions({
  name: 'OneTOneExchange'
})

</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

// .app-container {
//   display: flex;
//   flex-direction: row;
//   align-items: flex-start;
//   justify-content: center;
//   width: 100%;
// }

// .content-view {
//   background: #ffffff;
//   border: 1px solid #e5e6eb;
//   border-radius: 12px;
//   padding: 24px;
//   position: relative;
//   width: calc(100% - 310px);
// }

/* Vue Transition Animations - 性能优化版本 */

/* 淡入淡出过渡效果 - 用于主要内容区域 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
  will-change: opacity, transform;
}

.fade-enter-from {
  opacity: 0;
  transform: translate3d(0, 8px, 0);
}

.fade-leave-to {
  opacity: 0;
  transform: translate3d(0, -8px, 0);
}

/* 滑动淡入过渡效果 - 用于侧边栏内容 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
  will-change: opacity, transform;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translate3d(0, 12px, 0) scale3d(0.98, 0.98, 1);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translate3d(0, -8px, 0) scale3d(0.98, 0.98, 1);
}

/* 淡入滑动动画 - 简化版本 */
.fade-slide-enter-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
  transition-delay: var(--animation-delay, 0ms);
  will-change: opacity, transform;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translate3d(0, 15px, 0);
}

/* 侧边栏项目动画效果 - 移除初始opacity设置，让Vue Transition控制 */
.sidebar-item-animated {
  /* 移除 opacity: 0 和 transform，让 Vue Transition 组件控制动画 */
}

/* 动画完成后移除 will-change */
.fade-enter-active,
.slide-fade-enter-active,
.fade-slide-enter-active,
.sidebar-item-animated {
  &::after {
    content: '';
    animation: removeWillChange 0.1s ease 0.4s forwards;
  }
}

@keyframes removeWillChange {
  to {
    will-change: auto;
  }
}

/* 确保过渡期间元素层级正确 */
.fade-enter-active,
.slide-fade-enter-active {
  z-index: 10;
}

.fade-leave-active,
.slide-fade-leave-active {
  z-index: 9;
}

.title-label {
  font-weight: 600;
  font-size: 28px;
  color: #222527;
}

.exchange-icon-container {
  transition: transform 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center;
}

:deep(.el-button) {
  height: auto;
}

/* 交换图标旋转动画优化 */
.exchange-icon-container {
  position: relative;
  background: linear-gradient(135deg, #030814 0%, #1a1d29 100%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
  box-shadow:
    0 2px 8px rgba(3, 8, 20, 0.15),
    0 1px 3px rgba(3, 8, 20, 0.1);

  /* 动画期间增强阴影效果 */
  transition:
    all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 0.3s ease;

  /* 添加微妙的光泽效果 */
  &::before {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    height: 50%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    border-radius: 50% 50% 0 0;
    pointer-events: none;
  }
}
</style>