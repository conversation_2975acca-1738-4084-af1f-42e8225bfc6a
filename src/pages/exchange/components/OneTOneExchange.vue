<template>
  <div class="py-24px min-w-834px min-h-700px overflow-auto">
    <!-- 左边具体内容 -->
    <div class="relative flex flex-col justify-between w-100%">
      <!-- 分割线容器 - 相对定位水平居中 -->
      <div
        class="absolute top-0 left-50% transform -translate-x-50% w-32px flex flex-col items-center justify-center h-606px z-10">
        <!-- 上连接线 -->
        <div class="w-1px bg-#E5E6EB flex-1" />
        <!-- 图标容器，上下各留4px边距 -->
        <div class="my-4px">
          <div
            class="w-32px h-32px border-rd-50% flex items-center justify-center btn-hover-scale-sm exchange-icon-container"
            @click="handleExchangeType" :style="{ transform: `rotate(${rotationDegree}deg)` }">
            <SvgIcon name="icon-exchange-c" class="color-white" />
          </div>
        </div>
        <!-- 下连接线 -->
        <div class="w-1px bg-#E5E6EB flex-1" />
      </div>
      <Transition name="fade" mode="out-in">
        <FiatExchange class="flex-1" :coin-symbol-list="coinSymbolList" :fiat-currency-list="fiatCurrencyList"
          :crypto-network-list="cryptoNetworkList" v-if="exchangeType === FeeEnumType.FIAT_EXCHANGE_CRYPTO"
          key="fiat-exchange" />
        <CryptoExchange class="flex-1" :coin-symbol-list="coinSymbolList" :fiat-currency-list="fiatCurrencyList"
          :crypto-network-list="cryptoNetworkList" v-else key="crypto-exchange" />
      </Transition>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, inject, provide, Ref } from 'vue'
import { t } from '@@/i18n'
import { FeeEnumType } from '../apis/type'
import FiatExchange from './FiatExchange.vue'
import CryptoExchange from './CryptoExchange.vue'
import { useEnumStore } from '@/pinia/stores/enumStore'
import { BusinessEnumType } from '@/common/apis/common/type'
// 从父组件注入的数据
const exchangeType = inject<Ref<FeeEnumType>>('exchangeType', ref(FeeEnumType.FIAT_EXCHANGE_CRYPTO))
const handleExchangeType = inject<() => void>('handleExchangeType', () => {})
const rotationDegree = inject<Ref<number>>('rotationDegree', ref(90))

const enumStore = useEnumStore();
const fiatCurrencyList = enumStore.getEnumList(BusinessEnumType.FIAT_CCY);
const coinSymbolList = enumStore.getEnumList(BusinessEnumType.CURRENCY);
const cryptoNetworkList = enumStore.getEnumList(BusinessEnumType.CRYPTO_NET);

// 显式导出组件以解决 TypeScript 导入问题
defineOptions({
  name: 'OneTOneExchange'
})

</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

/* 淡入淡出过渡效果 - 用于主要内容区域 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
  will-change: opacity, transform;
}

.fade-enter-from {
  opacity: 0;
  transform: translate3d(0, 8px, 0);
}

.fade-leave-to {
  opacity: 0;
  transform: translate3d(0, -8px, 0);
}

/* 滑动淡入过渡效果 - 用于侧边栏内容 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
  will-change: opacity, transform;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translate3d(0, 12px, 0) scale3d(0.98, 0.98, 1);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translate3d(0, -8px, 0) scale3d(0.98, 0.98, 1);
}

/* 淡入滑动动画 - 简化版本 */
.fade-slide-enter-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
  transition-delay: var(--animation-delay, 0ms);
  will-change: opacity, transform;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translate3d(0, 15px, 0);
}

/* 侧边栏项目动画效果 - 移除初始opacity设置，让Vue Transition控制 */
.sidebar-item-animated {
  /* 移除 opacity: 0 和 transform，让 Vue Transition 组件控制动画 */
}

/* 动画完成后移除 will-change */
.fade-enter-active,
.slide-fade-enter-active,
.fade-slide-enter-active,
.sidebar-item-animated {
  &::after {
    content: '';
    animation: removeWillChange 0.1s ease 0.4s forwards;
  }
}

@keyframes removeWillChange {
  to {
    will-change: auto;
  }
}

/* 确保过渡期间元素层级正确 */
.fade-enter-active,
.slide-fade-enter-active {
  z-index: 10;
}

.fade-leave-active,
.slide-fade-leave-active {
  z-index: 9;
}

.title-label {
  font-weight: 600;
  font-size: 28px;
  color: #222527;
}

.exchange-icon-container {
  transition: transform 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center;
}

:deep(.el-button) {
  height: auto;
}

/* 交换图标旋转动画优化 */
.exchange-icon-container {
  position: relative;
  background: linear-gradient(135deg, #030814 0%, #1a1d29 100%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
  box-shadow:
    0 2px 8px rgba(3, 8, 20, 0.15),
    0 1px 3px rgba(3, 8, 20, 0.1);

  /* 动画期间增强阴影效果 */
  transition:
    all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 0.3s ease;

  /* 添加微妙的光泽效果 */
  &::before {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    height: 50%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    border-radius: 50% 50% 0 0;
    pointer-events: none;
  }
}
</style>