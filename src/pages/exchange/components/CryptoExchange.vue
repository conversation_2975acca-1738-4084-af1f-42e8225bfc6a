<template>
  <div class="relative mt-6px mb-8px flex flex-col h-100% flex-items-end">
    <el-form 
      ref="formRef" 
      :model="formData" 
      :rules="formRules" 
      class="mt-30px justify-between w-100% flex flex-row" 
    >
      <!-- 换出 -->
      <div class="w-50% h-100% pr-24px">
        <el-row class="flex justify-between items-center relative pt-16px pb-16px">
          <div class="dashed-line absolute top-56px left-12px h-46px" />
          <div class="flex items-center max-w-55% line-height-20px">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px;">
              <SvgIcon name="icon-exchange-pay" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.pay') }} Crypto
            </div>
          </div>
        </el-row>

        <el-row class="mt-64px flex justify-between items-center relative">
          <div class="dashed-line absolute top-40px left-12px h-87px" />
          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-exchange-coin" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.selectPayCurrency') }}
            </div>
          </div>
        </el-row>

        <!-- 选择币种 -->
        <el-form-item prop="transCurrency" class="mt-16px ml-36px">
          <el-select 
            v-model="formData.transCurrency" 
            class="cus-select w-100% text-16px" 
            :placeholder="t('exchange.selectPayCurrency')" 
          >
            <el-option
              v-for="item in fiatCurrencyList"
              :key="item.value"
              :label="item.value"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-row class="mt-64px flex justify-between items-center relative">
          <div class="dashed-line absolute top-40px left-12px h-87px" />
          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-exchange-net" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.selectPayNetwork') }}
            </div>
          </div>
        </el-row>

        <!-- 选择网络 -->
        <el-form-item prop="net" class="mt-16px ml-36px">
          <el-select 
            v-model="formData.net" 
            class="cus-select w-100% text-16px" 
            :placeholder="t('exchange.selectPayNetwork')" 
          >
            <el-option
              v-for="item in netList"
              :key="item.value"
              :label="item.value"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 金额输入框 -->
        <el-row class="mt-64px flex justify-between items-center relative">
          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-exchange-swapOut" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.inputPayAmount') }}
            </div>
          </div>
        </el-row>
        <el-form-item prop="transAmt" class="mt-16px ml-36px">
          <div class="h-40px w-100% flex flex-row">
            <el-input
              v-model="formData.transAmt"
              class="custom-input"
              :placeholder="t('exchange.inputPayAmount')"
              :formatter="(value: string) => value.replace(/[^\d.]/g, '').replace(/(\..*)\./g, '$1').replace(/^(\d+\.?\d{0,2}).*/, '$1')"
            >
              <template #suffix>
                <div class="flex items-center color-#FF0064 cursor-pointer">{{ t('exchange.max') }}</div>
              </template>
            </el-input>
          </div>
        </el-form-item>

        <el-button class="color-[#FF0064] font-14 font-400 ml-36px mt-16px border-none">{{ t('exchange.recharge') }}</el-button>
      </div>
      <!-- 换入 -->
      <div class="w-50% h-100% pl-24px">
        <el-row class="flex justify-between items-center relative pt-16px pb-16px">
          <div class="dashed-line absolute top-56px left-12px h-46px" />
          <div class="flex items-center max-w-55% line-height-20px">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px;">
              <SvgIcon name="icon-exchange-receive" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.receive') }} Fiat
            </div>
          </div>
        </el-row>

        <el-row class="mt-64px flex justify-between items-center relative">
          <div class="dashed-line absolute top-40px left-12px h-87px" />
          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-exchange-coin" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.inputReceiveAmount') }}
            </div>
          </div>
        </el-row>

        <!-- 选择币种 -->
        <el-form-item prop="transCurrency" class="mt-16px ml-36px">
          <el-select 
            v-model="formData.transCurrency" 
            class="cus-select w-100% text-16px" 
            :placeholder="t('exchange.inputReceiveAmount')" 
          >
            <el-option
              v-for="item in fiatCurrencyList"
              :key="item.value"
              :label="item.value"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 金额 -->
        <el-row class="mt-64px flex justify-between items-center relative">
          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-exchange-swapIn" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.confirmReceiveAmount') }}
            </div>
          </div>
        </el-row>
        <el-form-item prop="transAmt" class="mt-16px ml-36px">
          <div class="h-40px w-100% flex flex-row">
            <el-input
              v-model="formData.transAmt"
              class="custom-input"
              disabled
              :placeholder="t('exchange.wait')"
            >
            </el-input>
          </div>
        </el-form-item>
      </div>
    </el-form>

    <el-button type="primary" class="border-none mt-48px w-108px h-32px bg-#FF0064 btn-hover-scale-sm" @click="handleConfirm">{{
      t('exchange.confirmBtn')
    }}</el-button>
  </div>
</template>

<script  lang="ts" setup>
import { t } from '@@/i18n'
import LoadAnimation from '@@/components/LoadAnimation/LoadAnimation.vue'
import { InputInstance, ElForm, FormRules } from 'element-plus'
import { ref, computed } from 'vue'

const formRef = ref<InstanceType<typeof ElForm> | null>(null);
const formData = ref<{
  transCurrency: string;
  transAmt: string;
  network: string;
  coinSymbol: string;
  targetCurrency: string;
}>({
  transCurrency: '',
  transAmt: '',
  network: '',
  coinSymbol: '',
  targetCurrency: '',
});

const formRules = computed(() =>  {
  const allRules: FormRules = {
    
  };
  return allRules;
});

</script>

<style lang="scss" scoped>
.dashed-line {
  width: 1px;
  height: 95px;
  background-image: linear-gradient(to bottom, #E5E6EB 0%, #E5E6EB 80%, transparent 50%);
  background-size: 100% 10px;
  background-repeat: repeat-y
}

:deep(.el-select) {
  --el-select-input-focus-border-color: #E5E6EB;
}
:deep(.el-button, .el-button.is-round) {
  padding: 0;
}
.custom-input {
  width: 100%;
  
  :deep(.el-input__wrapper) {
    border: 1px solid #E5E6EB;
    box-shadow: none;
    border-radius: 6px;
    height: 40px;
    padding: 0 12px;
    background-color: #ffffff;
    
    &:hover {
      border-color: #E5E6EB;
    }
    
    &.is-focus {
      border-color: #E5E6EB;
    }
  }
  
  :deep(.el-input__inner) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-size: 14px;
    color: #222527;
    
    &::placeholder {
      color: #A8ABB2;
    }
  }
  
  :deep(.el-input__suffix) {
    .el-input__suffix-inner {
      .el-input__icon {
        color: #FF0064;
        
        &:hover {
          color: #FF0064;
        }
      }
    }
  }
}

.cus-select {
  height: 40px;
  :deep(.el-select__wrapper) {
    height: 40px;

    &:hover {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
    
    &.is-focus {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }

    &.is-focused {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
  }
}
</style>