<template>
  <div class="relative flex flex-col flex-items-end py-24px min-h-536px  min-w-834px">
    <el-form ref="formRef" :model="formData" :rules="formRules"
      class="justify-between w-100% min-h-536px flex flex-row">
      <!-- 换出 -->
      <Transition name="fade-slide" mode="out-in">
        <div :key="`left-${formData.outCurrency}-${formData.targetCurrency}`"
          class="flex-1 h-100% pr-24px exchange-left-panel">
          <el-row class="mt-8px flex justify-between items-center relative">
            <div class="dashed-line absolute top-40px left-12px h-87px" />
            <div class="flex items-center w-[calc(100%-40px)]">
              <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
                <SvgIcon name="icon-exchange-swapOut" class="text-14px color-[#FF0064]" />
              </div>
              <div class="font-600 text-[#222527] ml-12px">
                {{ t('exchange.selectPayCurrency') }}
              </div>
            </div>
          </el-row>

          <!-- 选择币种 -->
          <el-form-item prop="outCurrency" class="mt-16px ml-36px custom-form-item">
            <CurrencySelect v-model="formData.outCurrency" :options="currencyList"
              :placeholder="t('exchange.selectPayCurrency')" :get-currency-icon="getCurrencyIcon"
              @change="handleOutCurrencyChange" />
          </el-form-item>

          <!-- 换出币种网络选择 - 仅数字货币显示 -->
          <template v-if="isOutCurrencyCrypto">
            <el-row class="mt-40px flex justify-between items-center relative">
              <div class="dashed-line absolute top-40px left-12px h-87px" />
              <div class="flex items-center w-[calc(100%-40px)]">
                <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
                  <SvgIcon name="icon-exchange-net" class="text-14px color-[#FF0064]" />
                </div>
                <div class="font-600 text-[#222527] ml-12px">
                  {{ t('exchange.selectPayNetwork') }}
                </div>
              </div>
            </el-row>

            <!-- 选择网络 -->
            <el-form-item prop="network" class="mt-16px ml-36px custom-form-item">
              <CurrencySelect v-model="formData.network" :options="cryptoNetworkList"
                :placeholder="t('exchange.selectPayNetwork')" :get-currency-icon="getCryptoIcon"
                @change="handleNetworkChange" />
            </el-form-item>
          </template>


          <!-- 金额输入框 -->
          <el-row class="mt-40px flex justify-between items-center relative">
            <div class="flex items-center w-[calc(100%-40px)]">
              <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
                <SvgIcon name="icon-exchange-swapOut" class="text-14px color-[#FF0064]" />
              </div>
              <div class="font-600 text-[#222527] ml-12px">
                {{ t('exchange.payAmount') }}
              </div>
            </div>
          </el-row>
          <el-form-item prop="transAmt" class="mt-16px ml-36px custom-form-item">
            <div class="h-40px w-100% flex flex-row">
              <el-input v-model.trim="formData.transAmt" class="custom-input"
                :placeholder="t('exchange.inputPayAmount')" @input="handleOutAmountInput" @blur="handleAmountBlur"
                :formatter="formatCurrency">
                <template #suffix>
                  <div @click="handleMaxClick" class="flex items-center color-#FF0064 cursor-pointer">
                    {{ t('exchange.max') }}</div>
                </template>
              </el-input>
            </div>
          </el-form-item>

          <span @click="gotoRecharge" class="color-[#FF0064] text-14px font-400 ml-36px border-none">{{
            t('exchange.recharge') }}</span>
        </div>
      </Transition>
      <div class="h-full w-32px bottom-24px flex flex-col items-center justify-center min-h-536px">
        <!-- 上连接线 -->
        <div class="w-1px bg-#E5E6EB flex-1" />
        <!-- 图标容器，上下各留4px边距 -->
        <div class="my-4px">
          <div
            class="w-32px h-32px border-rd-50% flex items-center justify-center btn-hover-scale-sm exchange-icon-container"
            @click="handleExchangeType" :style="{ transform: `rotate(${rotationDegree}deg)` }">
            <SvgIcon name="icon-exchange-c" class="color-white" />
          </div>
        </div>
        <!-- 下连接线 -->
        <div class="w-1px bg-#E5E6EB flex-1" />
      </div>
      <!-- 换入 -->
      <Transition name="fade-slide" mode="out-in">
        <div :key="`right-${formData.outCurrency}-${formData.targetCurrency}`"
          class="flex-1 h-100% pl-24px exchange-right-panel">

          <el-row class="mt-8px flex justify-between items-center relative">
            <div class="dashed-line absolute top-40px left-12px h-87px" />
            <div class="flex items-center w-[calc(100%-40px)]">
              <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
                <SvgIcon name="icon-exchange-swapIn" class="text-14px color-[#FF0064]" />
              </div>
              <div class="font-600 text-[#222527] ml-12px">
                {{ t('exchange.inputReceiveAmount') }}
              </div>
            </div>
          </el-row>

          <!-- 选择币种 -->
          <el-form-item prop="targetCurrency" class="mt-16px ml-36px custom-form-item">
            <CurrencySelect v-model="formData.targetCurrency" :options="currencyList"
              :placeholder="t('exchange.inputReceiveAmount')" :get-currency-icon="getCurrencyIcon"
              @change="handleTargetCurrencyChange" />
          </el-form-item>

          <!-- 换入币种网络选择 - 仅数字货币显示 -->
          <template v-if="isTargetCurrencyCrypto">
            <div class="flex-1">
              <el-row class="mt-40px flex justify-between items-center relative">
                <div class="dashed-line absolute top-40px left-12px h-87px" />
                <div class="flex items-center w-[calc(100%-40px)]">
                  <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
                    <SvgIcon name="icon-exchange-net" class="text-14px color-[#FF0064]" />
                  </div>
                  <div class="font-600 text-[#222527] ml-12px">
                    {{ t('exchange.selectReceiveNetwork') }}
                  </div>
                </div>
              </el-row>

              <el-form-item prop="inNetwork" class="mt-16px ml-36px custom-form-item">
                <CurrencySelect v-model="formData.inNetwork" :options="cryptoNetworkList"
                  :placeholder="t('exchange.selectReceiveNetworkPlaceholder')" :get-currency-icon="getCryptoIcon"
                  @change="handleInNetworkChange" />
              </el-form-item>
            </div>
          </template>


          <!-- 金额 -->
          <el-row class="mt-40px flex justify-between items-center relative">
            <div class="flex items-center w-[calc(100%-40px)]">
              <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
                <SvgIcon name="icon-exchange-swapIn" class="text-14px color-[#FF0064]" />
              </div>
              <div class="font-600 text-[#222527] ml-12px">
                {{ t('exchange.receiveAmount') }}
              </div>
            </div>
          </el-row>
          <el-form-item prop="exchangeAmt" class="mt-16px ml-36px custom-form-item">
            <div class="h-40px w-100% flex flex-row">
              <el-input v-model.trim="formData.exchangeAmt" class="custom-input"
                :placeholder="t('exchange.inputAmountPlaceholder')" @input="handleInAmountInput"
                @blur="handleAmountBlur" :formatter="formatCurrency">
              </el-input>
            </div>
          </el-form-item>
          <div class="flex flex-col items-end">

            <!-- 按钮区域 -->
            <div class="flex justify-end gap-8px" :class="isTargetCurrencyCrypto ? 'mt-60px' : 'mt-84px'">
              <el-button type="primary"
                class="border-none w-108px h-32px pl-26px pr-26px pt-6px pb-6px btn-hover-scale-sm" :class="btnPrice
                  ? 'bg-#FF0064 border-#FF0064'
                  : 'bg-[#D2D2D2] border-[#D2D2D2]'
                  " :disabled="!btnPrice || loading || inputChanged" @click="handleConfirm">
                {{ t('exchange.confirmBtn') }}
              </el-button>
            </div>
          </div>
        </div>
      </Transition>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { t } from '@@/i18n'
import { ElForm, FormRules } from 'element-plus'
import { ref, computed, nextTick, onUnmounted } from 'vue'
import { queryWalletAcctDetail, quoteExchangeOrder } from '../apis'
import { safeNumber, formatNumber, } from '@@/utils/math'
import CurrencySelect from './CurrencySelect.vue'
import { formatDateTime } from '@/common/utils/datetime'
import { getCurrencyIcon, getCryptoIcon } from '@/common/utils/imageUtils'

// 格式化货币输入
const formatCurrency = (value: string) => {
  return String(value || '').replace(/[^\d.]/g, '').replace(/(\..*)\./, '$1').replace(/^(\d+\.?\d{0,2}).*/, '$1')
}

// 格式化汇率显示
const formatExchangeRate = (rate: string, fromCcy: string, toCcy: string) => {
  return `1 ${fromCcy}≈${rate} ${toCcy}`
}

const rotationDegree = ref(90) // 追踪当前旋转角度
const isAnimating = ref(false)
// 计算按钮是否可用
const isButtonEnabled = computed(() => {
  // 检查是否同时选择了兑换币种和接收币种
  const hasBothCurrencies = formData.value.outCurrency && formData.value.targetCurrency

  // 检查是否至少有一个金额输入框已填写有效数值
  const hasValidAmount =
    (formData.value.transAmt && safeNumber(formData.value.transAmt) > 0) ||
    (formData.value.exchangeAmt && safeNumber(formData.value.exchangeAmt) > 0)

  // 同时满足两个条件时按钮可用
  return hasBothCurrencies && hasValidAmount
})

const btnPrice = ref(false)
// 跟踪输入项是否发生变化
const inputChanged = ref(false)

const props = withDefaults(
  defineProps<{
    fiatCurrencyList: any[];
    coinSymbolList: any[];
    cryptoNetworkList: any[];
  }>(),
  {
    fiatCurrencyList: () => [],
    coinSymbolList: () => [],
    cryptoNetworkList: () => [],
  }
);

// 定义 emit 事件
const emit = defineEmits<{
  'update-rate-info': [data: { exchangeRate: string; rateUpdateTime: string; countdown: number }]
}>()
const currencyList = computed(() => [...props.coinSymbolList, ...props.fiatCurrencyList])

// 判断选中的货币是否为数字货币
const isOutCurrencyCrypto = computed(() => {
  if (!formData.value.outCurrency) return false
  return props.coinSymbolList.some(coin => coin.enumCode === formData.value.outCurrency)
})

const isTargetCurrencyCrypto = computed(() => {
  if (!formData.value.targetCurrency) return false
  return props.coinSymbolList.some(coin => coin.enumCode === formData.value.targetCurrency)
})

const router = useRouter()
const formRef = ref<InstanceType<typeof ElForm> | null>(null);
const formData = ref<{
  transAmt: string;
  network: string;
  outCurrency: string;
  inNetwork: string;
  targetCurrency: string;
  exchangeAmt: string;
  inputSource: 'out' | 'in' | null; // 记录用户最后输入的金额位置：'out'=换出金额，'in'=换入金额，null=未输入
  quoteId: string; // 询价ID
  exchangeRate: string; // 汇率
  validTime: number; // 询价有效期
}>({
  transAmt: '',
  network: '',
  outCurrency: 'USD',
  targetCurrency: 'USDT',
  inNetwork: 'ETH',
  // 换入计算金额
  exchangeAmt: '',
  // 输入来源标记
  inputSource: null,
  // 询价相关
  quoteId: '',
  exchangeRate: '0',
  validTime: 50,
});

const loading = ref(false)

// 通用货币校验函数
const createCurrencyValidator = (
  currentField: 'targetCurrency' | 'outCurrency',
  otherField: 'outCurrency' | 'targetCurrency',
  emptyErrorMessage: string
) => {
  return (rule: any, value: string, callback: Function) => {
    if (!value) {
      callback(new Error(emptyErrorMessage))
      return
    }

    const otherValue = formData.value[otherField]
    if (!otherValue) {
      callback()
      return
    }

    // 检查是否都是数字货币
    const isCurrentCrypto = props.coinSymbolList.some(coin => coin.enumCode === value)
    const isOtherCrypto = props.coinSymbolList.some(coin => coin.enumCode === otherValue)
    if (isCurrentCrypto && isOtherCrypto) {
      callback(new Error(t('exchange.errors.cryptoToCryptoNotSupported')))
      return
    }

    // 检查是否选择了相同货币
    if (otherValue === value) {
      callback(new Error(t('exchange.errors.sameCurrencyNotSupported')))
      return
    }
    callback()
  }
}

const formRules = computed(() => {
  const allRules: FormRules = {
    // transAmt: [
    //   { required: true, message: t('exchange.inputPayAmount'), trigger: 'blur' },
    //   {
    //     validator: (rule: any, value: string, callback: Function) => {
    //       if (!value) {
    //         callback(new Error(t('exchange.inputPayAmount')))
    //       } else if (safeNumber(value) < 1) {
    //         callback(new Error(t('exchange.validation.amountLessThanOne')))
    //       } else {
    //         callback()
    //       }
    //     },
    //     trigger: 'blur'
    //   }
    // ],
    targetCurrency: [
      { required: true, message: t('exchange.inputReceiveAmount'), trigger: ['change', 'blur'] },
      {
        validator: createCurrencyValidator('targetCurrency', 'outCurrency', t('exchange.inputReceiveAmount')),
        trigger: ['change', 'blur']
      }
    ],
    network: [
      { required: true, message: t('exchange.selectPayNetwork'), trigger: ['change', 'blur'] },
    ],
    outCurrency: [
      { required: true, message: t('exchange.selectPayCurrency'), trigger: ['change', 'blur'] },
      {
        validator: createCurrencyValidator('outCurrency', 'targetCurrency', t('exchange.selectPayCurrency')),
        trigger: ['change', 'blur']
      }
    ],
    inNetwork: [
      { required: true, message: t('exchange.selectReceiveNetwork'), trigger: ['change', 'blur'] },
    ],

    // exchangeAmt: [
    //   {
    //     validator: (rule: any, value: string, callback: Function) => {
    //       if (value && safeNumber(value) < 1) {
    //         callback(new Error(t('exchange.validation.amountLessThanOne')))
    //       } else {
    //         callback()
    //       }
    //     },
    //     trigger: 'change'
    //   }
    // ],
  };
  return allRules;
});

const gotoRecharge = () => {
  router.push('/recharge?type=crypto')
}

// 通用的货币变更处理方法
const handleCurrencyChange = (currentField: 'outCurrency' | 'targetCurrency', value: string) => {
  // 设置当前字段的值
  formData.value[currentField] = value

  // 清除对应的网络选择
  if (currentField === 'outCurrency') {
    formData.value.network = ''
  } else if (currentField === 'targetCurrency') {
    formData.value.inNetwork = ''
  }

  // 确定另一个字段
  const otherField = currentField === 'outCurrency' ? 'targetCurrency' : 'outCurrency'
  const otherValue = formData.value[otherField]

  // 先触发当前字段的验证，让验证器检查冲突
  nextTick(() => {
    formRef.value?.validateField(currentField)

    // 如果存在另一个字段的值，也验证另一个字段
    if (otherValue) {
      formRef.value?.validateField(otherField)
    }
  })
}

// 记录上一次的表单数据，用于比较是否有实际变化
const prevFormData = ref({
  outCurrency: '',
  network: '',
  targetCurrency: '',
  inNetwork: '',
  transAmt: '',
  exchangeAmt: '',
  inputSource: null as 'out' | 'in' | null
})

// 防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null
  return function (...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn(...args)
      timer = null
    }, delay) as unknown as number
  }
}

// 自动触发汇兑试算函数
const autoTriggerQuoteExchange = debounce(async (force = false) => {
  // 检查是否有必要的数据变更
  const hasOutCurrencyChanged = prevFormData.value.outCurrency !== formData.value.outCurrency
  const hasNetworkChanged = prevFormData.value.network !== formData.value.network
  const hasTargetCurrencyChanged = prevFormData.value.targetCurrency !== formData.value.targetCurrency
  const hasInNetworkChanged = prevFormData.value.inNetwork !== formData.value.inNetwork
  const hasTransAmtChanged = prevFormData.value.transAmt !== formData.value.transAmt
  const hasExchangeAmtChanged = prevFormData.value.exchangeAmt !== formData.value.exchangeAmt
  const hasInputSourceChanged = prevFormData.value.inputSource !== formData.value.inputSource

  // 如果没有实际变化，则不触发
  if (!force && !hasOutCurrencyChanged && !hasNetworkChanged && !hasTargetCurrencyChanged &&
    !hasInNetworkChanged && !hasTransAmtChanged && !hasExchangeAmtChanged && !hasInputSourceChanged) {
    return
  }

  // 更新上一次的表单数据
  prevFormData.value = JSON.parse(JSON.stringify(formData.value))

  // 检查是否满足触发条件
  const hasBothCurrencies = formData.value.outCurrency && formData.value.targetCurrency
  const hasValidAmount =
    (formData.value.transAmt && safeNumber(formData.value.transAmt) > 0) ||
    (formData.value.exchangeAmt && safeNumber(formData.value.exchangeAmt) > 0)

  // 检查网络选择是否完整
  const isOutNetworkRequired = isOutCurrencyCrypto.value
  const isInNetworkRequired = isTargetCurrencyCrypto.value
  const hasRequiredNetworks =
    (!isOutNetworkRequired || formData.value.network) &&
    (!isInNetworkRequired || formData.value.inNetwork)

  if (hasBothCurrencies && hasValidAmount && hasRequiredNetworks && !loading.value) {
    // 调用汇兑试算
    await handleTrial()
  } else {
    // 当汇率校验失败时，自动清除当前显示的汇率数据
    clearRateData()
  }
}, 300)

const handleOutCurrencyChange = (value: string) => {
  handleCurrencyChange('outCurrency', value)
  // 如果选择的是数字货币，自动设置网络为ETH
  const isCrypto = props.coinSymbolList.some(coin => coin.enumCode === value)
  if (isCrypto) {
    formData.value.network = 'ETH'
  }
  // 标记输入已变化
  inputChanged.value = true
  // 禁用提交按钮
  btnPrice.value = false
  // 自动触发汇兑试算
  autoTriggerQuoteExchange()
}

const handleNetworkChange = (value: string) => {
  formData.value.network = value
  // 标记输入已变化
  inputChanged.value = true
  // 禁用提交按钮
  btnPrice.value = false
  // 自动触发汇兑试算
  autoTriggerQuoteExchange()
}

const handleInNetworkChange = (value: string) => {
  formData.value.inNetwork = value
  // 标记输入已变化
  inputChanged.value = true
  // 禁用提交按钮
  btnPrice.value = false
  // 自动触发汇兑试算
  autoTriggerQuoteExchange()
}

const handleTargetCurrencyChange = (value: string) => {
  handleCurrencyChange('targetCurrency', value)
  // 如果选择的是数字货币，自动设置网络为ETH
  const isCrypto = props.coinSymbolList.some(coin => coin.enumCode === value)
  if (isCrypto) {
    formData.value.inNetwork = 'ETH'
  }
  // 标记输入已变化
  inputChanged.value = true
  // 禁用提交按钮
  btnPrice.value = false
  // 自动触发汇兑试算
  autoTriggerQuoteExchange()
}

// 处理换出金额输入
const handleOutAmountInput = () => {
  formData.value.inputSource = 'out'
  // 清空换入金额，实现互斥输入
  formData.value.exchangeAmt = ''
  // 标记输入已变化
  inputChanged.value = true
  // 禁用提交按钮
  btnPrice.value = false
}

// 处理换入金额输入
const handleInAmountInput = () => {
  formData.value.inputSource = 'in'
  // 清空换出金额，实现互斥输入
  formData.value.transAmt = ''
  // 标记输入已变化
  inputChanged.value = true
  // 禁用提交按钮
  btnPrice.value = false
}

// 处理金额输入框失焦事件
const handleAmountBlur = () => {
  // 自动触发汇兑试算
  autoTriggerQuoteExchange()
}

// 切换兑换类型
const handleExchangeType = () => {
  if (isAnimating.value) return // 防止重复点击

  // 开始动画
  isAnimating.value = true

  // 累积旋转180度
  rotationDegree.value += 180

  // 交换货币类型和网络
  const tempOutCurrency = formData.value.outCurrency
  const tempNetwork = formData.value.network
  const tempTargetCurrency = formData.value.targetCurrency
  const tempInNetwork = formData.value.inNetwork

  // 交换货币
  formData.value.outCurrency = tempTargetCurrency
  formData.value.targetCurrency = tempOutCurrency

  // 交换网络
  formData.value.network = tempInNetwork
  formData.value.inNetwork = tempNetwork

  if (formData.value.inputSource === 'out') {
    formData.value.exchangeAmt = ''
  } else if (formData.value.inputSource === 'in') {
    formData.value.transAmt = ''
  }

  // 标记输入已变化
  inputChanged.value = true
  // 禁用提交按钮
  btnPrice.value = false

  // 动画结束后重置状态并触发汇兑试算
  setTimeout(() => {
    isAnimating.value = false
    // 自动触发汇兑试算
    autoTriggerQuoteExchange()
  }, 600) // 600ms匹配动画时长
}

// 修改最大值点击处理
const handleMaxClick = async () => {

  formRef.value?.validateField(['network', 'outCurrency'], async (validObj: any) => {
    try {
      if (validObj) {
        // 查询当前账户余额显示在页面上
        const res = await queryWalletAcctDetail({
          network: formData.value.network,
          coinSymbol: formData.value.outCurrency,
        })
        const maxAmount = res.data.balanceAvl || 0
        formData.value.transAmt = formattedAmt(maxAmount) || ''
      }
    } catch (error) {
      console.error('设置最大金额失败:', error)
    }
  })

}

const formattedAmt = (amt: number | string, decimalPlaces: number = 2) => {
  if (amt === '' || amt === undefined) return

  const amount = safeNumber(amt)

  if (amount === 0 && amt !== 0) {
    ElMessage.warning(t('exchange.validation.enterValidAmount'))
    return ''
  }

  return formatNumber(amount, decimalPlaces)
}

const handleTrial = async () => {
  try {
    // 1. 校验表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    loading.value = true
    // 询价操作
    // 如果 inputSource 为 null，则默认设置为 'out'
    if (formData.value.inputSource === null) {
      formData.value.inputSource = 'out'
    }

    // 2. 构造询价请求参数
    const queryParams = {
      fromCcy: formData.value.outCurrency,
      toCcy: formData.value.targetCurrency,
      fromAmt: formData.value.inputSource === 'out' ? safeNumber(formData.value.transAmt) : undefined,
      toAmt: formData.value.inputSource === 'in' ? safeNumber(formData.value.exchangeAmt) : undefined,
      lockSide: formData.value.inputSource === 'out' ? 'SELL' : 'BUY',
      cryptoNet: isOutCurrencyCrypto.value ? formData.value.network : isTargetCurrencyCrypto.value ? formData.value.inNetwork : undefined
    }

    // 3. 调用询价接口
    // const queryRes = await quoteExchangeQuery(queryParams)

    const queryRes = {
      data: {
        "quoteId": "Q20230415001",
        "fromAmt": 1000.00,
        "toAmt": 850.50,
        "quoteCreateTime": "2023-04-15T10:30:00Z",
        "rate": "0.8505",
        "curPair": "USD/CNY",
      }
    }
    // 4. 保存询价结果
    formData.value.quoteId = queryRes.data.quoteId
    // 使用新的API响应字段
    formData.value.exchangeRate = queryRes.data.rate

    // 格式化数据并存储到变量中
    const formattedRateUpdateTime = formatDateTime(queryRes.data.quoteCreateTime)
    const formattedExchangeRateValue = formatExchangeRate(queryRes.data.rate, formData.value.outCurrency, formData.value.targetCurrency)

    // 更新存储变量
    formattedExchangeRate.value = formattedExchangeRateValue
    lastRateUpdateTime.value = formattedRateUpdateTime

    // 向父组件发送汇率信息
    emit('update-rate-info', {
      exchangeRate: formattedExchangeRateValue,
      rateUpdateTime: formattedRateUpdateTime,
      countdown: formData.value.validTime
    })

    // 根据锁定方向设置金额
    if (formData.value.inputSource === 'out') {
      formData.value.exchangeAmt = formattedAmt(queryRes.data.toAmt) || '0.00'
    } else {
      formData.value.transAmt = formattedAmt(queryRes.data.fromAmt) || '0.00'
    }
    // 启用下单按钮
    btnPrice.value = true

    // 重置输入变化状态
    inputChanged.value = false

    // 设置询价有效期倒计时
    startQuoteCountdown(formData.value.validTime)
  } catch (error) {
    console.error(t('exchange.errors.quoteExchangeFailed'), error)
    // 汇率校验失败时，清除汇率数据
    clearRateData()
  } finally {
    loading.value = false
  }
}

const handleConfirm = async () => {
  try {
    // 1. 校验表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    loading.value = true
    // 下单操作
    // 2. 构造下单请求参数
    const orderParams = {
      quoteId: formData.value.quoteId,
      fromCcy: formData.value.outCurrency,
      toCcy: formData.value.targetCurrency,
      fromAmt: safeNumber(formData.value.transAmt),
      toAmt: safeNumber(formData.value.exchangeAmt),
      lockSide: formData.value.inputSource === 'out' ? 'SELL' : 'BUY',
      cryptoNet: isOutCurrencyCrypto.value ? formData.value.network : isTargetCurrencyCrypto.value ? formData.value.inNetwork : undefined
    }

    // 3. 调用下单接口
    const orderRes = await quoteExchangeOrder(orderParams)

    // 4. 跳转到结果页
    router.push({
      path: '/exchange/result',
      query: {
        orderId: orderRes.data.orderId,
      },
    })
  } catch (error) {
    console.error(t('exchange.errors.quoteExchangeFailed'), error)
    ElMessage.error(t('exchange.errors.operationFailed'))
  } finally {
    loading.value = false
  }
}

// 询价倒计时
const quoteCountdownTimer = ref<number | null>(null)
const quoteCountdown = ref(0)

// 存储格式化后的汇率数据和更新时间
const formattedExchangeRate = ref<string>('-')
const lastRateUpdateTime = ref<string>('-')

// 清除汇率数据的函数
const clearRateData = () => {
  // 清除汇率相关数据
  formData.value.quoteId = ''
  formData.value.exchangeRate = '0'
  formattedExchangeRate.value = '-'
  lastRateUpdateTime.value = '-'
  quoteCountdown.value = 0

  // 清除倒计时器
  if (quoteCountdownTimer.value) {
    window.clearInterval(quoteCountdownTimer.value)
    quoteCountdownTimer.value = null
  }

  // 禁用提交按钮
  btnPrice.value = false

  // 向父组件发送清空的汇率信息
  emit('update-rate-info', {
    exchangeRate: '-',
    rateUpdateTime: '-',
    countdown: 0
  })
}

// 开始询价倒计时
const startQuoteCountdown = (seconds: number) => {
  // 清除之前的计时器
  if (quoteCountdownTimer.value) {
    window.clearInterval(quoteCountdownTimer.value)
  }

  quoteCountdown.value = seconds
  // 设置新的计时器
  quoteCountdownTimer.value = window.setInterval(() => {
    quoteCountdown.value--

    // 每次倒计时变化时发送更新给父组件
    emit('update-rate-info', {
      exchangeRate: formattedExchangeRate.value,
      rateUpdateTime: lastRateUpdateTime.value,
      countdown: quoteCountdown.value
    })

    if (quoteCountdown.value <= 0) {
      // 倒计时结束，重置询价状态
      window.clearInterval(quoteCountdownTimer.value as number)
      quoteCountdownTimer.value = null
      btnPrice.value = false

      // 自动触发新的询价请求
      autoTriggerQuoteExchange(true)
    }
  }, 1000)
}

// 组件卸载时清除计时器
onUnmounted(() => {
  if (quoteCountdownTimer.value) {
    window.clearInterval(quoteCountdownTimer.value)
  }
})

// 显式导出组件以解决 TypeScript 导入问题
defineOptions({
  name: 'QuoteExchange'
})

// 暴露方法给父组件调用
defineExpose({
  clearRateData
})
</script>

<style lang="scss" scoped>
.dashed-line {
  width: 1px;
  height: 95px;
  background-image: linear-gradient(to bottom, #E5E6EB 0%, #E5E6EB 80%, transparent 50%);
  background-size: 100% 10px;
  background-repeat: repeat-y
}

:deep(.el-select) {
  --el-select-input-focus-border-color: #E5E6EB;
}

.btn-first {
  padding: 0;

  /* 按钮禁用状态样式 */
  &.is-disabled {
    color: #c0c4cc !important;
    cursor: not-allowed !important;
    background: #FFFFFF !important;
    border: 1px solid #E5E6EB !important;
  }
}

.custom-form-item {
  /* 为错误提示预留固定高度空间，避免布局跳动 */
  min-height: 56px;

  :deep(.el-form-item__label) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #222527;
    margin-bottom: 8px;
    line-height: 20px;
  }
}

:deep(.el-form-item__error) {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #FD3627FF;
}

.custom-input {
  width: 100%;

  :deep(.el-input__wrapper) {
    border: 1px solid #E5E6EB;
    box-shadow: none;
    border-radius: 6px;
    height: 40px;
    padding: 0 12px;
    background-color: #ffffff;
    transition: border-color 0.2s ease;
    will-change: auto;

    &:hover {
      border-color: #E5E6EB;
    }

    &.is-focus {
      border-color: #E5E6EB;
    }

    /* 禁用状态优化 */
    &.is-disabled {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
      color: #c0c4cc;
      cursor: not-allowed;

      .el-input__inner {
        background-color: transparent;
        border: none;
        color: inherit;
        cursor: inherit;
      }
    }
  }

  :deep(.el-input__inner) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-size: 14px;
    color: #222527;
    transition: color 0.2s ease;

    &::placeholder {
      color: #A8ABB2;
    }

    /* 禁用状态下的优化 */
    &:disabled {
      background-color: transparent;
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }

  :deep(.el-input__suffix) {
    .el-input__suffix-inner {
      .el-input__icon {
        color: #FF0064;
        transition: color 0.2s ease;

        &:hover {
          color: #FF0064;
        }
      }
    }
  }
}

/* Vue Transition Animations - 性能优化版本 */

/* 淡入淡出过渡效果 - 用于主要内容区域 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
  will-change: opacity, transform;
}

.fade-enter-from {
  opacity: 0;
  transform: translate3d(0, 8px, 0);
}

.fade-leave-to {
  opacity: 0;
  transform: translate3d(0, -8px, 0);
}

/* 滑动淡入过渡效果 - 用于侧边栏内容 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: opacity 0.25s ease, transform 0.25s ease;
  will-change: opacity, transform;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translate3d(0, 12px, 0) scale3d(0.98, 0.98, 1);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translate3d(0, -8px, 0) scale3d(0.98, 0.98, 1);
}

/* 淡入滑动动画 - 简化版本 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
  transition-delay: var(--animation-delay, 0ms);
  will-change: opacity, transform;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translate3d(0, 15px, 0);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translate3d(0, -15px, 0);
}

/* 侧边栏项目动画效果 - 移除初始opacity设置，让Vue Transition控制 */
.sidebar-item-animated {
  /* 移除 opacity: 0 和 transform，让 Vue Transition 组件控制动画 */
}

/* 动画完成后移除 will-change */
.fade-enter-active,
.slide-fade-enter-active,
.fade-slide-enter-active,
.sidebar-item-animated {
  &::after {
    content: '';
    animation: removeWillChange 0.1s ease 0.4s forwards;
  }
}

@keyframes removeWillChange {
  to {
    will-change: auto;
  }
}

/* 确保过渡期间元素层级正确 */
.fade-enter-active,
.slide-fade-enter-active {
  z-index: 10;
}

.fade-leave-active,
.slide-fade-leave-active {
  z-index: 9;
}

/* 左侧面板动画 - 从下往上渐现 */
// .exchange-left-panel {
//   &.fade-slide-enter-from {
//     transform: translateY(40px);
//   }

//   &.fade-slide-leave-to {
//     transform: translateY(40px);
//   }
// }

/* 右侧面板动画 - 从下往上渐现 */
// .exchange-right-panel {
//   &.fade-slide-enter-from {
//     transform: translateY(40px);
//   }

//   &.fade-slide-leave-to {
//     transform: translateY(40px);
//   }
// }

.exchange-icon-container {
  transition: transform 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center;
}

/* 交换图标旋转动画优化 */
.exchange-icon-container {
  position: relative;
  background: linear-gradient(135deg, #030814 0%, #1a1d29 100%);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
  box-shadow:
    0 2px 8px rgba(3, 8, 20, 0.15),
    0 1px 3px rgba(3, 8, 20, 0.1);

  /* 动画期间增强阴影效果 */
  transition:
    all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 0.3s ease;

  /* 添加微妙的光泽效果 */
  &::before {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    height: 50%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    border-radius: 50% 50% 0 0;
    pointer-events: none;
  }
}
</style>