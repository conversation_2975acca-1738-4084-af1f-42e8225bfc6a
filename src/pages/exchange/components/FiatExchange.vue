<template>
  <div class="relative mt-6px mb-8px flex flex-col h-100% flex-items-end">
    <el-form 
      ref="formRef" 
      :model="formData" 
      :rules="formRules" 
      class="mt-30px justify-between w-100% flex flex-row" 
    >
      <!-- 换出 -->
      <div class="w-50% h-100% pr-24px">
        <el-row class="flex justify-between items-center relative pt-16px pb-16px">
          <div class="dashed-line absolute top-56px left-12px h-46px" />
          <div class="flex items-center max-w-55% line-height-20px">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px;">
              <SvgIcon name="icon-exchange-pay" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.pay') }} Fiat
            </div>
          </div>
        </el-row>

        <el-row class="mt-64px flex justify-between items-center relative">
          <div class="dashed-line absolute top-40px left-12px h-87px" />
          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-exchange-coin" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.selectPayCurrency') }}
            </div>
          </div>
        </el-row>

        <!-- 选择币种 -->
        <el-form-item prop="transCurrency" class="mt-16px ml-36px custom-form-item">
          <el-select 
            v-model="formData.transCurrency" 
            class="cus-select w-100% text-16px" 
            :placeholder="t('exchange.selectPayCurrency')" 
            @change="handleCurrencyChange"
          >
            <el-option 
              v-for="info in fiatCurrencyList" 
              :key="info.enumCode" 
              :label="info.enumDescCn"
              :value="info.enumCode" />
          </el-select>
        </el-form-item>

        <!-- 金额输入框 -->
        <el-row class="mt-64px flex justify-between items-center relative">
          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-exchange-swapOut" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.inputPayAmount') }}
            </div>
          </div>
        </el-row>
        <el-form-item prop="transAmt" class="mt-16px ml-36px custom-form-item">
          <div class="h-40px w-100% flex flex-row">
            <el-input
              v-model.trim="formData.transAmt"
              class="custom-input"
              :placeholder="t('exchange.inputPayAmount')"
               @blur="handleInputBlur"
              :formatter="(value: string) => value.replace(/[^\d.]/g, '').replace(/(\..*)\./g, '$1').replace(/^(\d+\.?\d{0,2}).*/, '$1')"
            >
              <template #suffix>
                <div @click="handleMaxClick" class="flex items-center color-#FF0064 cursor-pointer">{{ t('exchange.max') }}</div>
              </template>
            </el-input>
          </div>
        </el-form-item>

        <el-button class="color-[#FF0064] font-14 font-400 ml-36px mt-16px border-none">{{ t('exchange.recharge') }}</el-button>
      </div>
      <!-- 换入 -->
      <div class="w-50% h-100% pl-24px">
        <el-row class="flex justify-between items-center relative pt-16px pb-16px">
          <div class="dashed-line absolute top-56px left-12px h-46px" />
          <div class="flex items-center max-w-55% line-height-20px">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px;">
              <SvgIcon name="icon-exchange-receive" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.receive') }} Crypto
            </div>
          </div>
        </el-row>

        <el-row class="mt-64px flex justify-between items-center relative">
          <div class="dashed-line absolute top-40px left-12px h-87px" />
          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-exchange-coin" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.inputReceiveAmount') }}
            </div>
          </div>
        </el-row>

        <!-- 选择币种 -->
        <el-form-item prop="coinSymbol" class="mt-16px ml-36px custom-form-item">
          <el-select 
            v-model="formData.coinSymbol" 
            class="cus-select w-100% text-16px" 
            :placeholder="t('exchange.inputReceiveAmount')" 
          >
            <el-option 
              v-for="info in coinSymbolList" 
              :key="info.enumCode" 
              :label="info.enumDescCn"
              :value="info.enumCode" />
          </el-select>
        </el-form-item>

        <el-row class="mt-64px flex justify-between items-center relative">
          <div class="dashed-line absolute top-40px left-12px h-87px" />
          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-exchange-net" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.selectReceiveNetwork') }}
            </div>
          </div>
        </el-row>

        <!-- 选择网络 -->
        <el-form-item prop="network" class="mt-16px ml-36px custom-form-item">
          <el-select 
            v-model="formData.network" 
            class="cus-select w-100% text-16px" 
            :placeholder="t('exchange.selectReceiveNetwork')" 
          >
            <el-option 
              v-for="info in cryptoNetworkList" 
              :key="info.enumCode" 
              :label="info.enumDescCn"
              :value="info.enumCode" />
          </el-select>
        </el-form-item>

        <!-- 金额 -->
        <el-row class="mt-64px flex justify-between items-center relative">
          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-exchange-swapIn" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('exchange.confirmReceiveAmount') }}
            </div>
          </div>
        </el-row>
        <el-form-item class="mt-16px ml-36px custom-form-item">
          <div class="h-40px w-100% flex flex-row">
            <el-input
              v-model="exchangeAmt"
              class="custom-input"
              disabled
              :placeholder="t('exchange.wait')"
            >
            </el-input>
          </div>
        </el-form-item>
      </div>
    </el-form>

    <el-button type="primary" class="border-none mt-48px w-108px h-32px bg-#FF0064 btn-hover-scale-sm" @click="handleConfirm">{{
      t('exchange.confirmBtn')
    }}</el-button>
  </div>
</template>

<script  lang="ts" setup>
import { t } from '@@/i18n'
import LoadAnimation from '@@/components/LoadAnimation/LoadAnimation.vue'
import { InputInstance, ElForm, FormRules, ElMessage } from 'element-plus'
import { ref, computed } from 'vue'
import { useEnumStore } from '@/pinia/stores/enumStore'
import { BusinessEnumType } from '@/common/apis/common/type'
import { handleExchange, handleFeeCalc, queryWalletAcctDetail } from '../apis'
import { subtract, safeNumber, formatNumber, truncate } from '@@/utils/math'
import { FeeEnumType } from '../apis/type'

const router = useRouter()
const enumStore = useEnumStore();
const formRef = ref<InstanceType<typeof ElForm> | null>(null);
const formData = ref<{
  transCurrency: string;
  transAmt: string;
  network: string;
  coinSymbol: string;
  targetCurrency: string;
}>({
  transCurrency: '',
  transAmt: '',
  network: '',
  coinSymbol: '',
  targetCurrency: '',
});

const loading = ref(false)
// 换入计算金额
const exchangeAmt = ref('')
const fiatCurrencyList = enumStore.getEnumList(BusinessEnumType.FIAT_CCY);
const coinSymbolList = enumStore.getEnumList(BusinessEnumType.CURRENCY);
const cryptoNetworkList = enumStore.getEnumList(BusinessEnumType.CRYPTO_NET);

const formRules = computed(() =>  {
  const allRules: FormRules = {
    transCurrency: [
      { required: true, message: t('exchange.selectPayCurrency'), trigger: ['change', 'blur'] },
    ],
    transAmt: [
      { required: true, message: t('exchange.inputPayAmount'), trigger: 'blur' },
      { 
        validator: (rule: any, value: string, callback: Function) => {
          if (!value) {
            callback(new Error(t('exchange.inputPayAmount')))
          } else {
            callback()
          }
        }, 
        trigger: 'blur' 
      }
    ],
    targetCurrency: [
      { required: true, message: t('exchange.inputReceiveAmount'), trigger: ['change', 'blur'] },
    ],
    network: [
      { required: true, message: t('exchange.selectReceiveNetwork'), trigger: ['change', 'blur'] },
    ],
    coinSymbol: [
      { required: true, message: t('exchange.selectPayNetwork'), trigger: ['change', 'blur'] },
    ],
  };
  return allRules;
});

const handleInputBlur = () => {
  try {
    formRef.value?.validateField(['transCurrency'], async (validObj: any) => {
      if (validObj) {
        // 查询当前账户余额显示在页面上
        await calculateFee()
      }
    })
  } catch (error) {
    console.error('设置最大金额失败:', error)
  }
}

const handleCurrencyChange = () => {
  
}

// 修改最大值点击处理
const handleMaxClick = async () => {
  try {
    formRef.value?.validateField(['transCurrency'], async (validObj: any) => {
      if (validObj) {
        // 查询当前账户余额显示在页面上
        const res = await queryWalletAcctDetail({
          transCurrency: formData.value.transCurrency,
        })
        const maxAmount = res.data.balanceAvl || 0
        formData.value.transAmt = formattedAmt(maxAmount) || ''

        await calculateFee()
      }
    })
  } catch (error) {
    console.error('设置最大金额失败:', error)
  }
}

const calculateFee = async () => {
  try {
    let amount = safeNumber(formData.value.transAmt)

    if (amount <= 0) return

    // 根据decimalPlaces进行精确处理
    amount = truncate(amount, 2)
    
    loading.value = true
    const feeRes = await handleFeeCalc({
      transAmt: amount,
      feeType: FeeEnumType.FIAT_EXCHANGE_CRYPTO,
    })

    // 计算换入金额（使用精确减法）
    const feeAmount = safeNumber(feeRes.data.feeAmt)
    exchangeAmt.value = formattedAmt(subtract(amount, feeAmount, 2)) || '0.00'
    loading.value = false
  } catch (error) {
    console.error('计算手续费失败:', error)
    loading.value = false
  }
}

const formattedAmt = (amt: number | string, decimalPlaces: number = 2) => {
  if (amt === '' || amt === undefined) return

  const amount = safeNumber(amt)
  
  if (amount === 0 && amt !== 0) {
    ElMessage.warning(t('exchange.validation.enterValidAmount'))
    return ''
  }

  return formatNumber(amount, decimalPlaces)
}

const handleConfirm = async () => {
  try {
    // 1. 校验表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构造请求参数
    const params = {
      sysSeqId: String(Date.now()), // 13位时间戳
      transCurrency: formData.value.transCurrency,
      transAmt: safeNumber(formData.value.transAmt),
      network: formData.value.network,
      coinSymbol: formData.value.coinSymbol,
      targetCurrency: formData.value.targetCurrency, // 法兑数为 USDT 数兑法 USD
    }
    // 3. 调用兑换接口
    const res = await handleExchange(params)
    router.push({
      path: '/exchange/result',
      query: {
        sysSeqId: res.data.sysSeqId,
      },
    })
  } catch (error) {
    console.error('兑换失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.dashed-line {
  width: 1px;
  height: 95px;
  background-image: linear-gradient(to bottom, #E5E6EB 0%, #E5E6EB 80%, transparent 50%);
  background-size: 100% 10px;
  background-repeat: repeat-y
}

:deep(.el-select) {
  --el-select-input-focus-border-color: #E5E6EB;
}
:deep(.el-button, .el-button.is-round) {
  padding: 0;
}
.custom-form-item {
  :deep(.el-form-item__label) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #222527;
    margin-bottom: 8px;
    line-height: 20px;
  }
}
:deep(.el-form-item__error) {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #FD3627FF;
}

.custom-input {
  width: 100%;
  
  :deep(.el-input__wrapper) {
    border: 1px solid #E5E6EB;
    box-shadow: none;
    border-radius: 6px;
    height: 40px;
    padding: 0 12px;
    background-color: #ffffff;
    
    &:hover {
      border-color: #E5E6EB;
    }
    
    &.is-focus {
      border-color: #E5E6EB;
    }
  }
  
  :deep(.el-input__inner) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-size: 14px;
    color: #222527;
    
    &::placeholder {
      color: #A8ABB2;
    }
  }
  
  :deep(.el-input__suffix) {
    .el-input__suffix-inner {
      .el-input__icon {
        color: #FF0064;
        
        &:hover {
          color: #FF0064;
        }
      }
    }
  }
}

.cus-select {
  height: 40px;
  :deep(.el-select__wrapper) {
    height: 40px;

    &:hover {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
    
    &.is-focus {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }

    &.is-focused {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
  }
}
</style>