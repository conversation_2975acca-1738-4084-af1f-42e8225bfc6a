<template>
  <div class="relative mb-8px flex flex-col h-100% flex-items-end">
    <el-form ref="formRef" :model="formData" :rules="formRules" class="justify-between w-100% flex flex-row">
      <!-- 换出 -->
      <Transition name="fade-slide" appear>
        <div class="w-50% h-100% pr-24px exchange-left-panel">
          <el-row class="flex justify-between items-center relative pt-16px pb-16px">
            <div class="dashed-line absolute top-56px left-12px h-46px" />
            <div class="flex items-center max-w-55% line-height-20px">
              <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px;">
                <SvgIcon name="icon-exchange-pay" class="text-14px color-[#FF0064]" />
              </div>
              <div class="font-600 text-[#222527] ml-12px">
                {{ t('exchange.pay') }} Fiat
              </div>
            </div>
          </el-row>

          <el-row class="mt-64px flex justify-between items-center relative">
            <div class="dashed-line absolute top-40px left-12px h-87px" />
            <div class="flex items-center w-[calc(100%-40px)]">
              <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
                <SvgIcon name="icon-exchange-coin" class="text-14px color-[#FF0064]" />
              </div>
              <div class="font-600 text-[#222527] ml-12px">
                {{ t('exchange.selectPayCurrency') }}
              </div>
            </div>
          </el-row>

          <!-- 选择币种 -->
          <el-form-item prop="transCurrency" class="mt-16px ml-36px custom-form-item">
            <!-- <el-select 
            v-model="formData.transCurrency" 
            class="cus-select w-100% text-16px" 
            :placeholder="t('exchange.selectPayCurrency')" 
            @change="selectChange"
          >
            <el-option 
              v-for="info in fiatCurrencyList" 
              :key="info.enumCode" 
              :label="info.enumDescCn"
              :value="info.enumCode" />
          </el-select> -->
            <CurrencySelect v-model="formData.transCurrency" :options="fiatCurrencyList"
              :placeholder="t('exchange.selectPayCurrency')" :get-currency-icon="getCurrencyIcon"
              @change="handleTransCurrencyChange" />
          </el-form-item>

          <!-- 金额输入框 -->
          <el-row class="mt-64px flex justify-between items-center relative">
            <div class="flex items-center w-[calc(100%-40px)]">
              <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
                <SvgIcon name="icon-exchange-swapOut" class="text-14px color-[#FF0064]" />
              </div>
              <div class="font-600 text-[#222527] ml-12px">
                {{ t('exchange.inputPayAmount') }}
              </div>
            </div>
          </el-row>
          <el-form-item prop="transAmt" class="mt-16px ml-36px custom-form-item">
            <div class="h-40px w-100% flex flex-row">
              <el-input v-model.trim="formData.transAmt" class="custom-input"
                :placeholder="t('exchange.inputPayAmount')" @blur="selectChange"
                :formatter="(value: string) => value.replace(/[^\d.]/g, '').replace(/(\..*)\./g, '$1').replace(/^(\d+\.?\d{0,2}).*/, '$1')">
                <template #suffix>
                  <div @click="handleMaxClick" class="flex items-center color-#FF0064 cursor-pointer">{{
                    t('exchange.max') }}</div>
                </template>
              </el-input>
            </div>
          </el-form-item>

          <el-button text @click="gotoRecharge" class="color-[#FF0064] font-14 font-400 ml-36px mt-16px border-none">{{
            t('exchange.recharge') }}</el-button>
        </div>
      </Transition>
      <div class="w-32px max-h-700px" />
      <!-- 换入 -->
      <Transition name="fade-slide" appear>
        <div class="w-50% h-100% pl-24px exchange-right-panel">
          <el-row class="flex justify-between items-center relative pt-16px pb-16px">
            <div class="dashed-line absolute top-56px left-12px h-46px" />
            <div class="flex items-center max-w-55% line-height-20px">
              <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px;">
                <SvgIcon name="icon-exchange-receive" class="text-14px color-[#FF0064]" />
              </div>
              <div class="font-600 text-[#222527] ml-12px">
                {{ t('exchange.receive') }} Crypto
              </div>
            </div>
          </el-row>

          <el-row class="mt-64px flex justify-between items-center relative">
            <div class="dashed-line absolute top-40px left-12px h-87px" />
            <div class="flex items-center w-[calc(100%-40px)]">
              <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
                <SvgIcon name="icon-exchange-coin" class="text-14px color-[#FF0064]" />
              </div>
              <div class="font-600 text-[#222527] ml-12px">
                {{ t('exchange.inputReceiveAmount') }}
              </div>
            </div>
          </el-row>

          <!-- 选择币种 -->
          <el-form-item prop="coinSymbol" class="mt-16px ml-36px custom-form-item">
            <!-- <el-select 
            v-model="formData.coinSymbol" 
            class="cus-select w-100% text-16px" 
            :placeholder="t('exchange.inputReceiveAmount')" 
            @change="selectChange"
          >
            <el-option 
              v-for="info in coinSymbolList" 
              :key="info.enumCode" 
              :label="info.enumDescCn"
              :value="info.enumCode" />
          </el-select> -->
            <CurrencySelect v-model="formData.coinSymbol" :options="coinSymbolList"
              :placeholder="t('exchange.inputReceiveAmount')" :get-currency-icon="getCryptoIcon"
              @change="handleCoinSymbolChange" />
          </el-form-item>

          <el-row class="mt-64px flex justify-between items-center relative">
            <div class="dashed-line absolute top-40px left-12px h-87px" />
            <div class="flex items-center w-[calc(100%-40px)]">
              <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
                <SvgIcon name="icon-exchange-net" class="text-14px color-[#FF0064]" />
              </div>
              <div class="font-600 text-[#222527] ml-12px">
                {{ t('exchange.selectReceiveNetwork') }}
              </div>
            </div>
          </el-row>

          <!-- 选择网络 -->
          <el-form-item prop="network" class="mt-16px ml-36px custom-form-item">
            <!-- <el-select 
            v-model="formData.network" 
            class="cus-select w-100% text-16px" 
            :placeholder="t('exchange.selectReceiveNetwork')" 
            @change="selectChange"
          >
            <el-option 
              v-for="info in cryptoNetworkList" 
              :key="info.enumCode" 
              :label="info.enumDescCn"
              :value="info.enumCode" />
          </el-select> -->
            <CurrencySelect v-model="formData.network" :options="cryptoNetworkList"
              :placeholder="t('exchange.selectReceiveNetwork')" :get-currency-icon="getCryptoIcon"
              @change="handleNetworkChange" />
          </el-form-item>

          <!-- 金额 -->
          <el-row class="mt-64px flex justify-between items-center relative">
            <div class="flex items-center w-[calc(100%-40px)]">
              <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
                <SvgIcon name="icon-exchange-swapIn" class="text-14px color-[#FF0064]" />
              </div>
              <div class="font-600 text-[#222527] ml-12px">
                {{ t('exchange.confirmReceiveAmount') }}
              </div>
            </div>
          </el-row>
          <el-form-item prop="exchangeAmt" class="mt-16px ml-36px custom-form-item">
            <div class="h-40px w-100% flex flex-row">
              <el-input v-model="formData.exchangeAmt" class="custom-input" disabled :placeholder="t('exchange.wait')">
              </el-input>
            </div>
          </el-form-item>
        </div>
      </Transition>
    </el-form>

    <el-button type="primary"
      class="border-none mt-48px min-w-108px min-h-32px pl-26px pr-26px pt-6px pb-6px bg-#FF0064 btn-hover-scale-sm"
      @click="handleConfirm">{{
        t('exchange.confirmBtn')
      }}</el-button>
  </div>
</template>

<script lang="ts" setup>
import { t } from '@@/i18n'
import { InputInstance, ElForm, FormRules, ElMessage } from 'element-plus'
import { ref, computed } from 'vue'
import { handleExchange, handleFeeCalc, queryWalletAcctDetail } from '../apis'
import { subtract, safeNumber, formatNumber, truncate } from '@@/utils/math'
import { FeeEnumType } from '../apis/type'
import { getCryptoIcon, getCurrencyIcon } from '@/common/utils/imageUtils';
import CurrencySelect from './CurrencySelect.vue';

const props = withDefaults(
  defineProps<{
    fiatCurrencyList: any[];
    coinSymbolList: any[];
    cryptoNetworkList: any[];
  }>(),
  {
    fiatCurrencyList: () => [],
    coinSymbolList: () => [],
    cryptoNetworkList: () => [],
  }
);
const fee = inject('fee') as any
const rateString = inject('rateString') as any

const router = useRouter()
const formRef = ref<InstanceType<typeof ElForm> | null>(null);
const formData = ref<{
  transCurrency: string;
  transAmt: string;
  network: string;
  coinSymbol: string;
  targetCurrency: string;
  exchangeAmt: string;
}>({
  transCurrency: '',
  transAmt: '',
  network: '',
  coinSymbol: '',
  targetCurrency: '',
  // 换入计算金额
  exchangeAmt: '',
});

const loading = ref(false)

const formRules = computed(() => {
  const allRules: FormRules = {
    transCurrency: [
      { required: true, message: t('exchange.selectPayCurrency'), trigger: ['change', 'blur'] },
      {
        validator: (rule: any, value: string, callback: Function) => {
          if (!value) {
            callback(new Error(t('exchange.selectPayCurrency')))
          } else {
            if (formData.value.coinSymbol) {
              rateString.value = `1 ${formData.value.transCurrency} = 1 ${formData.value.coinSymbol}`
            }
            callback()
          }
        },
        trigger: ['change', 'blur']
      }
    ],
    transAmt: [
      { required: true, message: t('exchange.inputPayAmount'), trigger: 'blur' },
      {
        validator: (rule: any, value: string, callback: Function) => {
          if (!value) {
            callback(new Error(t('exchange.inputPayAmount')))
          } else if (safeNumber(value) < 1) {
            callback(new Error(t('exchange.validation.amountLessThanOne')))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    targetCurrency: [
      { required: true, message: t('exchange.inputReceiveAmount'), trigger: ['change', 'blur'] },
    ],
    network: [
      { required: true, message: t('exchange.selectReceiveNetwork'), trigger: ['change', 'blur'] },
    ],
    coinSymbol: [
      { required: true, message: t('exchange.inputReceiveAmount'), trigger: ['change', 'blur'] },
      {
        validator: (rule: any, value: string, callback: Function) => {
          if (!value) {
            callback(new Error(t('exchange.inputReceiveAmount')))
          } else {
            if (formData.value.transCurrency) {
              rateString.value = `1 ${formData.value.transCurrency} = 1 ${formData.value.coinSymbol}`
            }
            callback()
          }
        },
        trigger: ['change', 'blur']
      }
    ],
    exchangeAmt: [
      {
        validator: (rule: any, value: string, callback: Function) => {
          if (value && safeNumber(value) < 1) {
            callback(new Error(t('exchange.validation.amountLessThanOne')))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ],
  };
  return allRules;
});

const gotoRecharge = () => {
  router.push('/recharge?type=fiat')
}
// 换出的币种
const handleTransCurrencyChange = (value: string) => {
  formData.value.transCurrency = value
  selectChange()
}
// 换入的币种
const handleCoinSymbolChange = (value: string) => {
  formData.value.coinSymbol = value
  selectChange()
}
// 选择网络
const handleNetworkChange = (value: string) => {
  formData.value.network = value
  selectChange()
}

const selectChange = () => {
  // 'transAmt', 'network', 'coinSymbol', 'transCurrency'
  if (formData.value.transAmt && formData.value.network && formData.value.transCurrency && formData.value.coinSymbol) {
    calculateFee()
  }
}

// 修改最大值点击处理
const handleMaxClick = async () => {
  formRef.value?.validateField(['transCurrency'], async (validObj: any) => {
    try {
      if (validObj) {
        // 查询当前账户余额显示在页面上
        const res = await queryWalletAcctDetail({
          transCurrency: formData.value.transCurrency,
        })
        const maxAmount = res.data.balanceAvl || 0
        formData.value.transAmt = formattedAmt(maxAmount) || ''

        if (formData.value.network && formData.value.transCurrency && formData.value.coinSymbol) {
          await calculateFee()
        }
      }
    } catch (error) {
      console.error('设置最大金额失败:', error)
    }
  })
}

const calculateFee = async () => {
  fee.value = '--'
  formData.value.exchangeAmt = ''
  formRef.value?.validateField(['transAmt', 'network', 'coinSymbol', 'transCurrency'], async (validObj: any) => {
    if (validObj) {
      try {
        let amount = safeNumber(formData.value.transAmt)

        // 根据decimalPlaces进行精确处理
        amount = truncate(amount, 2)

        loading.value = true
        const feeRes = await handleFeeCalc({
          transAmt: amount,
          feeType: FeeEnumType.FIAT_EXCHANGE_CRYPTO,
          cryptoNet: formData.value.network,
        })

        // 计算换入金额（使用精确减法）
        const feeAmount = safeNumber(feeRes.data.feeAmt)
        fee.value = formattedAmt(feeRes.data.feeAmt) || '0.00'

        formData.value.exchangeAmt = formattedAmt(subtract(amount, feeAmount, 2)) || '0.00'
        loading.value = false

        formRef.value?.validateField(['exchangeAmt'])
      } catch (error) {
        console.error('计算手续费失败:', error)
        loading.value = false
      }
    }
  })
}

const formattedAmt = (amt: number | string, decimalPlaces: number = 2) => {
  if (amt === '' || amt === undefined) return

  const amount = safeNumber(amt)

  if (amount === 0 && amt !== 0) {
    ElMessage.warning(t('exchange.validation.enterValidAmount'))
    return ''
  }

  return formatNumber(amount, decimalPlaces)
}

const handleConfirm = async () => {
  try {
    // 1. 校验表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 2. 构造请求参数
    const params = {
      sysSeqId: String(Date.now()), // 13位时间戳
      transCurrency: formData.value.transCurrency,
      transAmt: safeNumber(formData.value.transAmt),
      network: formData.value.network,
      coinSymbol: formData.value.coinSymbol,
      targetCurrency: formData.value.coinSymbol,
    }
    // 3. 调用兑换接口
    const res = await handleExchange(params)
    router.push({
      path: '/exchange/result',
      query: {
        sysSeqId: res.data.sysSeqId,
      },
    })
  } catch (error) {
    console.error('兑换失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.dashed-line {
  width: 1px;
  height: 95px;
  background-image: linear-gradient(to bottom, #E5E6EB 0%, #E5E6EB 80%, transparent 50%);
  background-size: 100% 10px;
  background-repeat: repeat-y
}

:deep(.el-select) {
  --el-select-input-focus-border-color: #E5E6EB;
}

:deep(.el-button, .el-button.is-round) {
  padding: 0;
}

.custom-form-item {
  :deep(.el-form-item__label) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #222527;
    margin-bottom: 8px;
    line-height: 20px;
  }
}

:deep(.el-form-item__error) {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #FD3627FF;
}

.custom-input {
  width: 100%;

  :deep(.el-input__wrapper) {
    border: 1px solid #E5E6EB;
    box-shadow: none;
    border-radius: 6px;
    height: 40px;
    padding: 0 12px;
    background-color: #ffffff;
    transition: border-color 0.2s ease;
    will-change: auto;

    &:hover {
      border-color: #E5E6EB;
    }

    &.is-focus {
      border-color: #E5E6EB;
    }

    /* 禁用状态优化 */
    &.is-disabled {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
      color: #c0c4cc;
      cursor: not-allowed;

      .el-input__inner {
        background-color: transparent;
        border: none;
        color: inherit;
        cursor: inherit;
      }
    }
  }

  :deep(.el-input__inner) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-size: 14px;
    color: #222527;
    transition: color 0.2s ease;

    &::placeholder {
      color: #A8ABB2;
    }

    /* 禁用状态下的优化 */
    &:disabled {
      background-color: transparent;
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }

  :deep(.el-input__suffix) {
    .el-input__suffix-inner {
      .el-input__icon {
        color: #FF0064;
        transition: color 0.2s ease;

        &:hover {
          color: #FF0064;
        }
      }
    }
  }
}

.cus-select {
  height: 40px;

  :deep(.el-select__wrapper) {
    height: 40px;

    &:hover {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }

    &.is-focus {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }

    &.is-focused {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
  }
}

/* Vue Transition Animations - 性能优化版本 */

/* 淡入滑动动画 - 使用 transition 替代 animation */
.fade-slide-enter-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
  will-change: opacity, transform;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translate3d(0, 15px, 0);
}

/* 动画完成后清理 will-change */
.fade-slide-enter-active {
  animation: cleanupWillChange 0.1s ease 0.45s forwards;
}

@keyframes cleanupWillChange {
  to {
    will-change: auto;
  }
}
</style>