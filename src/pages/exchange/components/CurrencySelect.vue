<template>
    <el-select ref="selectRef" v-model="selectedValue" :placeholder="placeholder" class="cus-select w-100% text-16px"
        @change="handleChange">
        <template #prefix>
            <img v-if="selectedValue" :src="getCurrencyIcon(selectedValue)" :alt="selectedValue"
                class="mr-8px w-20px h-20px" />
        </template>
        <el-option value="1" hidden></el-option>
        <div v-for="item in options" :key="item.enumCode" :style="{
            width: '100%',
            cursor: 'pointer',
            color: item.enumCode == selectedValue ? '#ff0064' : '#222527',
            fontSize: '14px',
            padding: '16px',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            fontWeight: item.enumCode == selectedValue ? '600' : '400',
            background: item.enumCode == selectedValue ? '#F8F9FA' : 'white',
        }" @click="handleOptionClick(item.enumCode)">
            <img :src="getCurrencyIcon(item.enumCode)" :alt="item.enumCode" width="20" height="20"
                style="margin-right: 14px" />
            <span class="ml-8px font-family-[PingFangSC-Regular] font-400 text-16px text-[#222527] leading-20px">
                {{ item.enumDescCn }}
            </span>
        </div>
    </el-select>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface CurrencyOption {
    enumCode: string
    enumDescCn: string
}

interface Props {
    modelValue: string
    options: CurrencyOption[]
    placeholder?: string
    getCurrencyIcon: (code: string) => string
}

interface Emits {
    (e: 'update:modelValue', value: string): void
    (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: ''
})

const emit = defineEmits<Emits>()

const selectRef = ref()

const selectedValue = computed({
    get: () => props.modelValue,
    set: (value: string) => {
        emit('update:modelValue', value)
    }
})

const handleOptionClick = (enumCode: string) => {
    selectedValue.value = enumCode
    emit('change', enumCode)
    selectRef.value?.blur()
}

const handleChange = (value: string) => {
    emit('change', value)
    selectRef.value?.blur()
}

// 暴露 ref 供父组件使用
defineExpose({
    selectRef
})
</script>

<script lang="ts">
// 添加默认导出以解决导入问题
export default {
    name: 'CurrencySelect'
}
</script>

<style scoped>
.cus-select {
    /* 你可以在这里添加自定义样式 */
}
</style>