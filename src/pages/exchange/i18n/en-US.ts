export default {
  exchange: {
    title: "Exchange",
    inputPlaceholder: "Please enter",
    pay: "Pay",
    receive: "Receive",
    max: "Max",
    recharge: "Deposit",
    fee: "Fee",
    rate: "Rate",
    tip: "The amount received will be transferred to the corresponding currency account",
    confirmBtn: "Confirm Exchange",
    validation: {
      enterValidNumber: 'Please enter a valid number',
      enterValidAmount: 'Please enter a valid amount',
      amountLessThanOne: "The amount paid cannot be less than 1",
      amountGreaterThanZero: "The amount paid should be greater than 0"
    },
    selectPayCurrency: "Please select the currency you want to pay",
    inputPayAmount: "Please enter the amount you want to pay",
    inputReceiveAmount: "Please enter the amount you want to receive",
    selectReceiveNetwork: "Please select the blockchain network of the currency you want to receive",
    confirmReceiveAmount: "Please confirm the amount you want to receive",
    selectPayNetwork: "Please select the blockchain network of the currency you want to pay",
    wait: "Please wait..."
  },
  exchangeResult: {
    fail: "Transaction failed",
    success: "Transaction success",
    processing:"Transaction processing",
    exchangeAmount: "Receive amount",
    preExchangeAmount:"Estimated Exchange Amount",
    errorCode: "Error code",
    payAmount: "Pay amount",
    feeAmount: "Fee",
    transactionTime: "Transaction time",
    transactionNumber: "Transaction number",
    errorReason: "Error reason",
    fee: "Fee",
    rate: "Rate",
    tip: "The amount received will be transferred to the corresponding currency account",
    confirmBtn: "Confirm Exchange",
    backBtn: "Back"
  }
}
