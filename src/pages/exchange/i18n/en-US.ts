export default {
  exchange: {
    title: "Conversion",
    inputPlaceholder: "Please enter",
    pay: "Pay",
    receive: "Receive",
    max: "Max",
    recharge: "Deposit",
    fee: "Fee",
    rate: "Rate",
    tip: "The amount received will be transferred to the corresponding currency account",
    confirmBtn: "Confirm Conversion",
    // New translations
    dingxQuote: "DingX Quote",
    oneToOneExchange: "1:1 Exchange",
    payAmount: "Pay Amount",
    receiveAmount: "Receive Amount",
    exchangeRateLabel: "Rate",
    updateTime: "Update Time",
    selectReceiveNetworkPlaceholder: "Please select the blockchain network for the receiving currency",
    inputAmountPlaceholder: "Please enter",
    // Error messages
    errors: {
      cryptoToCryptoNotSupported: "Crypto to crypto exchange is not supported",
      sameCurrencyNotSupported: "Same currency exchange is not supported",
      operationFailed: "Operation failed, please try again",
      setMaxAmountFailed: "Failed to set maximum amount",
      calculateFeeFailed: "Failed to calculate fee",
      exchangeFailed: "Exchange failed",
      quoteExchangeFailed: "Quote exchange operation failed"
    },
    validation: {
      enterValidNumber: 'Please enter a valid number',
      enterValidAmount: 'Please enter a valid amount',
      amountLessThanOne: "The amount paid cannot be less than 1",
      amountGreaterThanZero: "The amount paid should be greater than 0"
    },
    selectPayCurrency: "Please select the currency you want to pay",
    inputPayAmount: "Please enter the amount you want to pay",
    inputReceiveAmount: "Please enter the amount you want to receive",
    selectReceiveNetwork: "Please select the blockchain network of the currency you want to receive",
    confirmReceiveAmount: "Please confirm the amount you want to receive",
    selectPayNetwork: "Please select the blockchain network of the currency you want to pay",
    wait: "Please wait...",
    operationTip: "Operation Tip",
    currentRateTip: "Current Conversion rate: {rateString}",
    feeTip: "Fee: {fee}",
    aboutExchangeTip: "About Conversion",
    aboutExchangeTipDesc: "The amount received will be transferred to the corresponding Ding currency account.",
    availablePayAmountTitle: "Available Conversion Amount",
    today: "Today",
    usdEquivalent : "USD Equivalent",
    month: "This month",
  },
  exchangeResult: {
    fail: "Transaction failed",
    success: "Transaction success",
    processing:"Transaction processing",
    exchangeAmount: "Receive amount",
    preExchangeAmount:"Estimated Conversion Amount",
    errorCode: "Error code",
    payAmount: "Pay amount",
    feeAmount: "Fee",
    transactionTime: "Transaction time",
    transactionNumber: "Transaction number",
    errorReason: "Error reason",
    fee: "Fee",
    rate: "Rate",
    tip: "The amount received will be transferred to the corresponding currency account",
    confirmBtn: "Confirm Conversion",
    backBtn: "Back"
  }
}
