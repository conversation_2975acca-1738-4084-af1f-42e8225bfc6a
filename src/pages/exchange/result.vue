<template>
  <div class="app-container w-100% h-100%">
    <div class="content-view w-600px h-328px flex flex-col justify-between mt-107px">
      <div class="pt-24px pl-24px pr-24px w-100% h-100%">
        <!-- 统一状态展示区域 -->
        <el-row class="justify-between items-center">
          <div class="flex items-center">
            <SvgIcon v-if="currentStatus.icon === 'icon-success'" name="icon-success" :class="['text-40px', currentStatus.colorClass]" />
            <SvgIcon v-else-if="currentStatus.icon === 'icon-fail'" name="icon-fail" :class="['text-40px', currentStatus.colorClass]" />
            <SvgIcon v-else name="icon-success" :class="['text-40px', currentStatus.colorClass]" />
            
            <div class="text-18px font-600 text-[#222527] ml-16px">
              {{ currentStatus.text }}
            </div>
          </div>

          <div class="justify-end items-center text-right">
            <div class="text-18px font-400 text-[#6B7275]" v-if="transactionData.transStat === 'S'">
              {{ t('exchangeResult.exchangeAmount') }}
            </div>
            <div class="text-18px font-400 text-[#6B7275]" v-if="transactionData.transStat === 'F'">
              {{ t('exchangeResult.errorCode') }}
            </div>
            <div class="text-18px font-400 text-[#6B7275]" v-if="transactionData.transStat === 'I'">
              {{ t('exchangeResult.preExchangeAmount') }}
            </div>
            <div class="text-28px font-600 text-[#222527] mt-8px">
              {{
                shouldShowSuccessDetails
                  ? formatCurrency(transactionData.realAmt, transactionData.realCurrency)
                  : transactionData.respCode
              }}
            </div>
          </div>
        </el-row>

        <el-divider class="mt-24px mb-24px" />

        <!-- 动态详情渲染 -->
        <template v-if="shouldShowSuccessDetails">
          <el-row class="detail-item justify-between items-center">
            <span class="text-14px font-400 text-[#6B7275]">
              {{ t('exchangeResult.payAmount') }}
            </span>
            <span class="text-14px font-400 text-[#222527]">
              {{ formatCurrency(transactionData.transAmt, transactionData.transCurrency) }}
            </span>
          </el-row>

          <el-row class="detail-item justify-between items-center">
            <span class="text-14px font-400 text-[#6B7275]">
              {{ t('exchangeResult.feeAmount') }}
            </span>
            <span class="text-14px font-400 text-[#222527]">
              {{ formatCurrency(transactionData.feeAmt, transactionData.feeCurrency) }}
            </span>
          </el-row>

          <el-row class="detail-item justify-between items-center">
            <span class="text-14px font-400 text-[#6B7275]">
              {{ t('exchangeResult.transactionTime') }}
            </span>
            <span class="text-14px font-400 text-[#222527]">
              {{ transactionData.createTime }}
            </span>
          </el-row>

          <el-row class="detail-item justify-between items-center">
            <span class="text-14px font-400 text-[#6B7275]">
              {{ t('exchangeResult.transactionNumber') }}
            </span>
            <span class="text-14px font-400 text-[#222527]">
              {{ transactionData.sysSeqId }}
            </span>
          </el-row>
        </template>

        <template v-else-if="shouldShowFailureDetails">
          <el-row class="detail-item justify-between items-center">
            <span class="text-14px font-400 text-[#6B7275]">
              {{ t('exchangeResult.transactionTime') }}
            </span>
            <span class="text-14px font-400 text-[#222527]">
              {{ transactionData.createTime }}
            </span>
          </el-row>

          <el-row class="detail-item justify-between items-center">
            <span class="text-14px font-400 text-[#6B7275]">
              {{ t('exchangeResult.transactionNumber') }}
            </span>
            <span class="text-14px font-400 text-[#222527]">
              {{ transactionData.sysSeqId }}
            </span>
          </el-row>

          <el-row class="detail-item justify-between items-center">
            <span class="text-14px font-400 text-[#6B7275]">
              {{ t('exchangeResult.errorReason') }}
            </span>
            <span class="text-14px font-400 text-[#222527]">
              {{ transactionData.respDesc || t('exchangeResult.systemError') }}
            </span>
          </el-row>
        </template>
      </div>
    </div>

    <el-button type="primary" class="mt-32px w-78px h-32px" @click="handleBack">
      {{ t('exchangeResult.backBtn') }}({{ countdown }}S)
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { t } from '@@/i18n'
import { useRoute, useRouter } from 'vue-router'
import { queryOrderDetail } from './apis/index'
import { StatusEnumType, FeeEnumType, CryptoExchangeFiatTransLogDTO } from './apis/type'
import { onBeforeRouteLeave } from 'vue-router'
import { isReallyEmpty } from '@@/utils/validate'

type StatusConfigType = {
  icon: string, // 请确认实际图标名称
  colorClass: string,
  text: string,
  showDetails: true,
}

// 状态配置映射
const statusConfig = {
  [StatusEnumType.S]: {
    icon: 'icon-success',
    colorClass: 'text-[#3EB342]',
    text: t('exchangeResult.processing'),
    showDetails: true,
  },
  [StatusEnumType.F]: {
    icon: 'icon-fail',
    colorClass: 'text-[#FF0000]',
    text: t('exchangeResult.fail'),
    showDetails: true,
  },
  [StatusEnumType.I]: {
    icon: 'icon-success', // 请确认实际图标名称
    colorClass: 'text-[#FFA500]',
    text: t('exchangeResult.processing'),
    showDetails: true,
  },
}

// 计算属性
const currentStatus = computed(
  () => {
    let key = transactionData.value.transStat as StatusEnumType
    return (statusConfig[key] || statusConfig[StatusEnumType.F]) as StatusConfigType
  }
)

const shouldShowSuccessDetails = computed(() =>
  {
    let key = transactionData.value.transStat as StatusEnumType
    return [StatusEnumType.S, StatusEnumType.I].includes(key)
  }
)

const shouldShowFailureDetails = computed(
  () => transactionData.value.transStat === StatusEnumType.F
)

// 数据逻辑
const route = useRoute()
const router = useRouter()
const countdown = ref(10)
const transactionData = ref<
  Omit<CryptoExchangeFiatTransLogDTO, 'id' | 'userId' | 'merCustId'> & {
    errorMessage?: string
  }
>({
  sysSeqId: '',
  transType: FeeEnumType.CRYPTO_EXCHANGE_FIAT,
  transAmt: 0,
  transCurrency: '',
  feeAmt: 0,
  feeCurrency: '',
  feeFlag: '',
  calcMode: '',
  realAmt: 0,
  realCurrency: '',
  transStat: StatusEnumType.S,
  sysDate: '',
  respCode: '',
  respDesc: '',
  createTime: '',
})

// 初始化交易数据
const initTransactionData = async () => {
  const sysSeqId = route.query.sysSeqId as string
  const res = await queryOrderDetail({ sysSeqId })
  Object.assign(transactionData.value, res.data.cryptoExchangeFiatTransLogDTO)
  transactionData.value.transStat = 'S'

  startCountdown()
}

const formatCurrency = (amount?: string | number | undefined, currency?: string | undefined) => {
  if (isReallyEmpty(amount) || isReallyEmpty(currency)) return ''
  const decimals = currency?.toUpperCase() === 'USDT' ? 6 : 2
  const formattedAmount = Number(parseFloat(amount + '').toFixed(decimals + 2)).toFixed(decimals)

  // 仅对 USDT 移除末尾冗余零
  if (decimals === 6) {
    formattedAmount.replace(/\.?0+$/, (m) => (m.includes('.') ? m : ''))
  }

  return `${formattedAmount} ${currency}`
}

// 新增定时器引用
const timer = ref<number | null>(null)

// 返回首页
const handleBack = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
  router.replace({ name: 'Exchange' })
}

// 倒计时逻辑
const startCountdown = () => {
  timer.value = setInterval(() => {
    countdown.value = Math.max(0, countdown.value - 1)
    if (countdown.value === 0) {
      handleBack()
    }
  }, 1000)
}

onMounted(() => {
  initTransactionData()
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
})
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.content-view {
  background-image: url('/src/common/assets/icons/icon-exchange-result-bg.svg');
  background-size: cover;
}
.detail-item {
  margin-top: 16px;
  &:first-child {
    margin-top: 0;
  }
}
</style>