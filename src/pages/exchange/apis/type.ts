/** 手续费类型 */
export enum FeeEnumType {
  /** 数币兑换法币 */
  CRYPTO_EXCHANGE_FIAT = 'CRYPTO_EXCHANGE_FIAT',
  /** 法币兑数币 */
  FIAT_EXCHANGE_CRYPTO = 'FIAT_EXCHANGE_CRYPTO',
  /** 数币提现 */
  CRYPTO_WITHDRAW = 'CRYPTO_WITHDRAW',
  /** 法币提现 */
  FIAT_WITHDRAW = 'FIAT_WITHDRAW',
  /** 法币账户绑定 */
  FIAT_ACCT_BIND = 'FIAT_ACCT_BIND',
  /** 数币地址绑定 */
  CRYPTO_ADDR_BIND = 'CRYPTO_ADDR_BIND',
  /** 法币账户修改 */
  FIAT_ACCT_MODIFY = 'FIAT_ACCT_MODIFY',
  /** 法币账户删除 */
  FIAT_ACCT_DEL = 'FIAT_ACCT_DEL',
  /** 数币地址修改 */
  CRYPTO_ADDR_MODIFY = 'CRYPTO_ADDR_MODIFY',
  /** 数币地址删除 */
  CRYPTO_ADDR_DEL = 'CRYPTO_ADDR_DEL',
}

export enum StatusEnumType {
  /** 成功 */
  S = 'S',
  /** 失败 */
  F = 'F',
  /** 已受理 */
  I = 'I',
}

export type FeeCalcData = {
  /** 交易金额 */
  transAmt: number;
  /** 手续费类型 */
  feeType: FeeEnumType;
  /** 网络 */
  cryptoNet?: string;
};

/** 手续费 */
export type FeeCalcResponseData = ApiResponseData<{
  feeAmt: number;
}>;

/** 牌价兑换询价请求参数 */
export type QuoteExchangeQueryData = {
  /** 兑出币种 */
  fromCcy: string;
  /** 兑入币种 */
  toCcy: string;
  /** 兑出金额(若锁兑出则必填) */
  fromAmt?: number;
  /** 兑入金额(若锁兑入则必填) */
  toAmt?: number;
  /** 锁定方向：SELL-锁兑出/BUY-锁兑入 */
  lockSide: string;
  /** 区块链网络：ETH/TRON，牌价数兑法，牌价法兑数时必填 */
  cryptoNet?: string;
};

/** 牌价兑换询价响应数据 */
export type QuoteExchangeQueryResponseData = ApiResponseData<{
  /** 询价单号 */
  quoteId: string;
  /** 兑出金额 */
  fromAmt: number;
  /** 兑入金额 */
  toAmt: number;
  /** 创建时间 */
  quoteCreateTime: string;
  /** 费率 */
  rate: string;
  /** 货币对 */
  curPair: string;
}>;

/** 牌价兑换下单请求参数 */
export type QuoteExchangeOrderData = {
  /** 询价单号 */
  quoteId: string;
  /** 兑出币种 */
  fromCcy?: string;
  /** 兑入币种 */
  toCcy?: string;
  /** 兑出金额 */
  fromAmt?: number;
  /** 兑入金额 */
  toAmt?: number;
  /** 锁定方向：SELL-锁兑出/BUY-锁兑入 */
  lockSide?: string;
  /** 区块链网络：ETH/TRON，牌价数兑法，牌价法兑数时必填 */
  cryptoNet?: string;
};

/** 牌价兑换下单响应数据 */
export type QuoteExchangeOrderResponseData = ApiResponseData<{
  /** 订单ID */
  orderId: string;
}>;

export type HandleExchangeData = ApiRequestData<{
  /** 请求流水号 */
  sysSeqId: string;
  /** 换入币种 */
  transCurrency?: string;
  /** 交易金额 */
  transAmt?: number;
  /** 链 */
  network?: string;
  /** 换出金额 */
  coinSymbol?: string;
}>;

export type HandleExchangeResponseData = ApiResponseData<{
  /** ID */
  id: number;
  /** 客户号 */
  merCustId: string;
  /** 系统流水号 */
  sysSeqId: string;
  /** 系统日期 */
  sysDate: string;
  /** 用户号 */
  userId: string;
  /** 交易类型 */
  transType: string;
  /** 交易金额 */
  transAmt: number;
  /**  */
  transCurrency: string;
  /** 手续费 */
  feeAmt: number;
  /**  */
  feeCurrency: string;
  /**  */
  feeFlag: string;
  /**  */
  calcMode: string;
  /**  */
  realAmt: number;
  /**  */
  realCurrency: string;
  /** 交易状态 */
  transStat: string;
}>;

export type QueryCryptoExchangeConfigData = ApiRequestData<{
  /** 申请ID */
  sysSeqId: string;
}>;

export type CryptoExchangeConfigResponseData = ApiResponseData<{
  /** 用户ID */
  userId: string;
  /** 客户号 */
  merCustId: string;
  /** 数兑法公式 */
  cryptoExchangeFiatCalcMode: string;
  /** 法兑数公式 */
  fiatExchangeCryptoCalcMode: string;
  /** 法币提现公式 */
  fiatWithdrawCalcMode: string;
  /** 数币提现公式 */
  cryptoWithdrawCalcMode: string;
  /** 数兑法公式描述 */
  cryptoExchangeFiatCalcModeDesc: string;
  /** 法兑数公式描述 */
  fiatExchangeCryptoCalcModeDesc: string;
  /** 法币提现公式描述 */
  fiatWithdrawCalcModeDesc: string;
  /** 数币提现公式描述 */
  cryptoWithdrawCalcModeDesc: string;
}>;

export type QueryOrderDetailData = ApiRequestData<{
  /** 申请ID */
  sysSeqId: string;
}>;

export type OrderDetailResponseData = ApiResponseData<{
  cryptoExchangeFiatTransLogDTO: CryptoExchangeFiatTransLogDTO;
}>;

export type CryptoExchangeFiatTransLogDTO = {
  /** ID */
  id: number;
  /** 客户号 */
  merCustId: string;
  /** 系统流水号 */
  sysSeqId: string;
  /** 系统日期 */
  sysDate: string;
  /** 用户号 */
  userId: string;
  /** 交易类型 */
  transType: string;
  /** 交易金额 */
  transAmt: number;
  /**  */
  transCurrency: string;
  /** 手续费 */
  feeAmt: number;
  /**  */
  feeCurrency: string;
  /**  */
  feeFlag: string;
  /**  */
  calcMode: string;
  /**  */
  realAmt: number;
  /**  */
  realCurrency: string;
  /** 交易状态 */
  transStat: string;
  respCode: string;
  respDesc: string;
  createTime: string;
};

// 查询法币账户余额
export type WalletAcctDetailResponseData = ApiResponseData<{
  /** 可用余额 */
  balanceAvl: string;
  /** 冻结余额 */
  balanceFreeze: string;
  /** 待收余额 */
  balanceWay: string;
}>;
