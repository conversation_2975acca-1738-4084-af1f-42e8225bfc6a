import type * as Exchange from "./type"
import { FeeEnumType } from "./type"
import { request } from "@/http/axios"

/** 手续费查询 */
export function handleFeeCalc(data: Exchange.FeeCalcData) {
  return request<Exchange.FeeCalcResponseData>({
    url: "fee/calc",
    method: "post",
    data
  })
}

/**
 * @returns 查询法币账户余额 
 */
export function queryWalletAcctDetail(parma: any) {
  // let parma = {}
  // if (queryType === FeeEnumType.FIAT_EXCHANGE_CRYPTO) {
  //   // 法币兑数币;查询法币账户余额
  //   parma = {
  //     transCurrency: 'USD',
  //   }
  // } else if (queryType === FeeEnumType.CRYPTO_EXCHANGE_FIAT) {
  //   // 数币兑法币;查询数币账户余额
  //   parma = {
  //     network: 'ETH',
  //     coinSymbol: 'USDT',
  //   }
  // }
  return request<Exchange.WalletAcctDetailResponseData>({
    url: "walletAcct/detail",
    method: "post",
    data: parma
  })
}

/** 提交兑换 */
export function handleExchange(data?: Exchange.HandleExchangeData) {
  return request<Exchange.HandleExchangeResponseData>({
    url: "exchange/crypto/order",
    method: "post",
    data
  })
}

/** 查询兑换详情 */
export function queryOrderDetail(data?: Exchange.QueryOrderDetailData) {
    return request<Exchange.OrderDetailResponseData>({
      url: "exchange/detail/query",
      method: "post",
      data
    })
  }
