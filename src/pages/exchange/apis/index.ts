import type * as Exchange from "./type"
import { FeeEnumType } from "./type"
import { request } from "@/http/axios"

/** 手续费查询 */
export function handleFeeCalc(data: Exchange.FeeCalcData) {
  return request<Exchange.FeeCalcResponseData>({
    url: "fee/calc",
    method: "post",
    data
  })
}

/**
 * @returns 查询法币账户余额 
 */
export function queryWalletAcctDetail(parma: any) {
  // let parma = {}
  // if (queryType === FeeEnumType.FIAT_EXCHANGE_CRYPTO) {
  //   // 法币兑数币;查询法币账户余额
  //   parma = {
  //     transCurrency: 'USD',
  //   }
  // } else if (queryType === FeeEnumType.CRYPTO_EXCHANGE_FIAT) {
  //   // 数币兑法币;查询数币账户余额
  //   parma = {
  //     network: 'ETH',
  //     coinSymbol: 'USDT',
  //   }
  // }
  return request<Exchange.WalletAcctDetailResponseData>({
    url: "walletAcct/detail",
    method: "post",
    data: parma
  })
}

/** 提交兑换 */
export function handleExchange(data?: Exchange.HandleExchangeData) {
  return request<Exchange.HandleExchangeResponseData>({
    url: "exchange/crypto/order",
    method: "post",
    data
  })
}

/** 
 * 牌价兑换询价
 * @param data 询价请求参数
 * @returns 询价结果，包含汇率、换入金额、手续费、询价ID和有效期
 */
export function quoteExchangeQuery(data: Exchange.QuoteExchangeQueryData) {
  return request<Exchange.QuoteExchangeQueryResponseData>({
    url: "quote/exchange/inquiry",
    method: "post",
    data
  })
}

/** 
 * 牌价兑换下单
 * @param data 下单请求参数，包含询价ID和交易信息
 * @returns 下单结果，包含订单ID和订单状态
 */
export function quoteExchangeOrder(data: Exchange.QuoteExchangeOrderData) {
  return request<Exchange.QuoteExchangeOrderResponseData>({
    url: "quote/exchange/order",
    method: "post",
    data
  })
}

/** 查询兑换详情 */
export function queryOrderDetail(data?: Exchange.QueryOrderDetailData) {
    return request<Exchange.OrderDetailResponseData>({
      url: "exchange/detail/query",
      method: "post",
      data
    })
  }
