<template>
  <div class="app-container w-100% h-100%">
    <div class="content-view w-600px h-438px flex flex-col justify-between mt-107px">
      <div class="pt-24px pl-24px pr-24px w-100% h-100%">
        <el-row class="justify-between items-center">
          <div class="flex flex-row justify-between items-center">
            <SvgIcon name="icon-success" class="text-40px color-[#3EB342]" />
            <div class="text-18px font-600 text-[#222527] ml-16px">{{ statusPlaceholder }}</div>
          </div>
          <div class="justify-end items-center">
            <div class="text-18px font-400 text-[#6B7275] text-right">{{ t('withdrawal.expectedAmount') }}</div>
            <div class="text-28px font-600 text-[#222527] text-right mt-8px">{{ formatCurrency(transLogDTO?.realAmt, transLogDTO?.realCurrency) }}</div>
          </div>
        </el-row>

        <!-- <el-row v-else class="justify-between items-center">
          <div class="flex flex-row justify-between items-center">
            <SvgIcon name="icon-fail" class="text-40px color-[#3EB342]" />
            <div class="text-18px font-600 text-[#222527] ml-16px">{{ statusPlaceholder }}</div>
          </div>
          <div class="justify-end items-center">
            <div class="text-18px font-400 text-[#6B7275] text-right">{{ t('withdrawal.errorCode') }}</div>
            <div class="text-28px font-600 text-[#222527] text-right mt-8px">ERROR</div>
          </div>
        </el-row> -->

        <el-divider class="mt-24px mb-24px" />

        <!-- 提现金额 -->
        <el-row class="justify-between items-center">
          <span class="text-14px font-400 text-[#6B7275]">{{ t('withdrawalResult.withdrawalAmount') }}</span>
          <span class="text-14px font-400 text-[#222527]">{{ formatCurrency(transLogDTO?.transAmt, transLogDTO?.transCurrency) }}</span>
        </el-row>
        <!-- 手续费 -->
        <el-row class="justify-between items-center mt-16px">
          <span class="text-14px font-400 text-[#6B7275]">{{ t('withdrawalResult.feeAmount') }}</span>
          <span class="text-14px font-400 text-[#222527]">{{ formatCurrency(transLogDTO?.feeAmt, transLogDTO?.feeCurrency) }}</span>
        </el-row>
        
        <!-- 法币提现 -->
        <template v-if="transLogDTO?.transType === 'FIAT_WITHDRAW'">
          <el-row class="justify-between items-center mt-16px">
            <span class="text-14px font-400 text-[#6B7275]">{{ t('withdrawalResult.payeeName') }}</span>
            <span class="text-14px font-400 text-[#222527]">{{ cardInfoDTO?.payeeName }}</span>
          </el-row>
          <!-- 银行账户 -->
          <el-row class="justify-between items-center mt-16px">
            <span class="text-14px font-400 text-[#6B7275]">{{ t('withdrawalResult.bankAccount') }}</span>
            <span class="text-14px font-400 text-[#222527]">{{ cardInfoDTO?.acctName }}</span>
          </el-row>
          <!-- 账户别名 -->
          <el-row class="justify-between items-center mt-16px">
            <span class="text-14px font-400 text-[#6B7275]">{{ t('withdrawalResult.accountAlias') }}</span>
            <span class="text-14px font-400 text-[#222527]">{{ cardInfoDTO?.acctNickName }}</span>
          </el-row>
        </template>
        <!-- 数币提现 -->
        <template v-if="transLogDTO?.transType === 'CRYPTO_WITHDRAW'">
          <!-- 数币地址 -->
          <el-row class="justify-between items-center mt-16px">
            <span class="text-14px font-400 text-[#6B7275]">{{ t('withdrawalResult.walletName') }}</span>
            <span class="text-14px font-400 text-[#222222]">{{ cryptoCardInfoDTO?.walletName }}</span>
          </el-row>
          <!-- 网络 -->
          <el-row class="justify-between items-center mt-16px">
            <span class="text-14px font-400 text-[#6B7275]">{{ t('withdrawalResult.network') }}</span>
            <span class="text-14px font-400 text-[#222222]">{{ cryptoCardInfoDTO?.cryptoNet }}</span>
          </el-row>
          <!-- 地址 --> 
          <el-row class="justify-between items-center mt-16px">
            <span class="text-14px font-400 text-[#6B7275]">{{ t('withdrawalResult.address') }}</span>
            <span class="text-14px font-400 text-[#222222]">{{ cryptoCardInfoDTO?.cryptoAddr }}</span>
          </el-row>
        </template>


        <el-row class="justify-between items-center mt-16px">
          <span class="text-14px font-400 text-[#6B7275]">{{ t('withdrawalResult.transactionTime') }}</span>
          <span class="text-14px font-400 text-[#222527]">{{ transactionTime }}</span>
        </el-row>
        <el-row class="justify-between items-center mt-16px">
          <span class="text-14px font-400 text-[#6B7275]">{{ t('exchangeResult.transactionNumber') }}</span>
          <span class="text-14px font-400 text-[#222527]">{{ transLogDTO?.sysSeqId }}</span>
        </el-row>

        <!-- <div v-else>
          <el-row class="justify-between items-center">
            <span class="text-14px font-400 text-[#6B7275]">{{ t('exchangeResult.transactionTime') }}</span>
            <span class="text-14px font-400 text-[#222527]">2025-06-07 18:59:59</span>
          </el-row>
          <el-row class="justify-between items-center mt-16px">
            <span class="text-14px font-400 text-[#6B7275]">{{ t('exchangeResult.transactionNumber') }}</span>
            <span class="text-14px font-400 text-[#222527]">123123123123123</span>
          </el-row>
          <el-row class="justify-between items-center mt-16px">
            <span class="text-14px font-400 text-[#6B7275]">{{ t('exchangeResult.errorReason') }}</span>
            <span class="text-14px font-400 text-[#222527]">系统异常</span>
          </el-row>
        </div> -->
      </div>
    </div>

    <el-button @click="goback" type="primary" class="mt-32px w-78px h-32px">{{ t('exchangeResult.backBtn') }}{{ countdownStr }}</el-button>
  </div>
</template>

<script lang="ts" setup>
import { t } from '@@/i18n';
import { InputInstance } from 'element-plus';
import { queryWithdrawalOrderDetailApi } from './apis';
import { TransLogDTO, CardInfoDTO, CryptoCardInfoDTO } from './apis/type';
import moment from 'moment';
import { isReallyEmpty } from '@@/utils/validate';

const resultType = ref('lt') // lt 法币， dc 数字货币
const countdown = ref(10)
// 新增定时器引用
const timer = ref<number | null>(null)

const route = useRoute()
const router = useRouter()

const sysSeqId = ref('')
const transLogDTO = ref<TransLogDTO>({})
const cardInfoDTO = ref<CardInfoDTO>({})
const cryptoCardInfoDTO = ref<CryptoCardInfoDTO>({})

const goback = () => {
  if (timer.value) {
    clearInterval(timer.value)
  }
  router.replace({name: "Withdrawal"})
}

const queryOrderDetailResp = () => {
  queryWithdrawalOrderDetailApi({
    sysSeqId: sysSeqId.value
  }).then((res) => {
    transLogDTO.value = {
      ...res.data.transLogDTO
    }
    cardInfoDTO.value = {
      ...res.data.cardInfoDTO
    }
    cryptoCardInfoDTO.value = {
      ...res.data.cryptoCardInfoDTO
    }
    timer.value =  setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        goback()
      }
    }, 1000)
  })
}

const formatCurrency = (amount?: string | number | undefined, currency?: string | undefined) => {
  if (isReallyEmpty(amount) || isReallyEmpty(currency)) return ''
  const decimals = currency?.toUpperCase() === 'USDT' ? 6 : 2
  const formattedAmount = Number(parseFloat(amount + '').toFixed(decimals + 2)).toFixed(decimals)

  // 仅对 USDT 移除末尾冗余零
  if (decimals === 6) {
    formattedAmount.replace(/\.?0+$/, (m) => (m.includes('.') ? m : ''))
  }

  return `${formattedAmount} ${currency}`
}

onMounted(() => {
  sysSeqId.value = route.query.sysSeqId as string
  queryOrderDetailResp()
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
})

const statusPlaceholder = computed(() => {
  return t('withdrawalResult.withdrawalAccepted')
})

const transactionTime = computed(() => {
  if (transLogDTO.value.createTime) {
    return moment(transLogDTO.value.createTime).format('YYYY-MM-DD HH:mm:ss')
  }
  return ""
}) 

const countdownStr = computed(() => {
  return (countdown.value === 10 || countdown.value === 0) ? '' : `(${countdown.value}S)`
})

</script>

<style lang="scss" scoped>
.app-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.content-view {
  background-image: url('/src/common/assets/icons/icon-withdrawal-result-bg.svg');
  background-size: cover;
}
:deep(.el-input__wrapper) {
  box-shadow: none;
  border: none;
  padding: 0;
}
:deep(.el-input.is-disabled .el-input__wrapper) {
  box-shadow: none;
  border: none;
  background-color: transparent;
}
:deep(.el-button) {
  height: auto;
}
</style>
