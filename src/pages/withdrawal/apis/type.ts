import { FeeEnumType } from '../../exchange/apis/type'
import { FileInfo } from '../../kyc/apis/type'

export type QueryAddressResponseData = ApiResponseData<{
  dcWalletAddrList: DigitalAddress[]
}>

export type DigitalAddress = {
  walletName?: string
  ccy?: string
  cryptoNet?: string
  cryptoAddr?: string
  avlBal?: number
  id?: number
}

// 查询已审核法币账户信息列表
export type QueryAcctListApprovedResponseData = ApiResponseData<{
  fiatAcctInfoList: FiatAcctInfo[]
}>
// 发币账户返回
export type FiatAcctInfo = {
  merCustId?: string
  payeeName?: string
  payeeAddr?: string
  payeeCtry?: string
  acctNickName?: string
  payeeAcctType?: string
  acctNo?: string
  ccy?: string
  payMethod?: string
  acctName?: string
  bankCtry?: string
  bankAddr?: string
  swiftCode?: string
  bankName?: string
  localClearingCode?: string,
  avlBal?: number, // 可用余额
  id: number
}

// 提现下单
export type WithdrawalOrderRequestData = ApiRequestData<{
  sendSeqId?: string
  acctInfoId?: number
  transAmt?: string,
  withdrawType?: FeeEnumType
  channelType?: string
  feeMethod?: string
  transactionPurposeLevel1?: string
  transactionPurposeLevel2?: string
  transactionRemarks?: string
  fileIds?: string[]
}>
// 提现下单返回
export type WithdrawalOrderResponseData = ApiResponseData<{
  id?: number
  sysSeqId?: string
  sysDate?: string
  merCustId?: string
  userCustId?: string
  userId?: string
  transType?: string // CRYPTO_WITHDRAW("CRYPTO_WITHDRAW", "数币提现"), FIAT_WITHDRAW("FIAT_WITHDRAW", "法币提现")
  transAmt?: number
  transCurrency?: string
  feeAmt?: number
  feeCurrency?: string
  feeFlag?: string
  calcMode?: string
  realAmt?: number
  realCurrency?: string
  transStat?: string
}>

// 查询提现详情接口返回
export type WithdrawalOrderDetailResponseData = ApiResponseData<{
  transLogDTO: TransLogDTO // 主订单
  cardInfoDTO: CardInfoDTO // 法币目的信息
  cryptoCardInfoDTO: CryptoCardInfoDTO //数币目的信息
}>

export type TransLogDTO = {
  id?: number
  sysSeqId?: string
  sysDate?: string
  merCustId?: string
  userCustId?: string
  userId?: string
  transType?: string
  transAmt?: number
  transCurrency?: string
  feeAmt?: number
  feeCurrency?: string
  feeFlag?: string
  calcMode?: string
  realAmt?: number
  realCurrency?: string
  transStat?: string
  traceId?: string
  respCode?: string
  respDesc?: string
  createTime?: string
  updateTime?: string
}

export type CardInfoDTO = {
  merCustId?: string
  payeeName?: string
  payeeCtry?: string
  payeeAddr?: string
  acctNickName?: string
  payeeAcctType?: string
  acctNo?: string
  ccy?: string
  payMethod?: string
  acctName?: string
  bankCtry?: string
  bankAddr?: string
}

export type CryptoCardInfoDTO = {
  merCustId?: string
  ccy?: string
  walletName?: string
  cryptoNet?: string
  cryptoAddr?: string
}

export type FiatWithdrawFee = ApiResponseData<{
  fiatWithdrawFees?: FiatWithdrawFeeItem[]
  channelType?: string
}>

export type FiatWithdrawFeeItem = {
  feeMethod: string
  feeAmt: number
  estimatedArrivalTime: string
  estimatedArrivalTimeUS: string
  estimatedArrivalTimeCN: string
  estimatedArrivalTimeHK: string
}

export type LtWithdrawalFormData = {
  acctInfoId: string | number
  transAmt: string | number
  channelType: string
  feeMethod: string
  transactionPurposeLevel1: string
  transactionPurposeLevel2: string
  transactionRemarks: string
  fileIds: string[]
  fileInfos?: FileInfo[]
}