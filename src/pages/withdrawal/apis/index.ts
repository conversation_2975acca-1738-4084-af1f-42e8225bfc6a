import type * as Withdrawal from "./type"
import { request } from "@/http/axios"
import { FeeEnumType } from '../../exchange/apis/type'

  /** 查询数币地址 */
export function queryDigitalAddressApi() {
  return request<Withdrawal.QueryAddressResponseData>({
    url: "/dc/addr/listApproved",
    method: "post",
    data: {}
  }, {showLoading: false})
}
// 
// 查询已审核法币账户信息列表
export function queryAcctListApproved() {
  return request<Withdrawal.QueryAcctListApprovedResponseData>({
    url: "/fiat/acct/listApproved",
    method: "post",
    data: {},
  }, {showLoading: false})
}

// 提现下单接口
export function withdrawalOrderApi(data: Withdrawal.WithdrawalOrderRequestData) {
  return request<Withdrawal.WithdrawalOrderResponseData>({
    url: "/withdraw/order",
    method: "post",
    data
  })
}

// 身份验证发送邮箱验证码
export function sendEmailForIdentityVerifyApi(businessType: FeeEnumType) {
  return request<{data: {sendSeqId: string}}>({
    url: "/mail/send",
    method: "post",
    data: {businessType: businessType}
  }, { showError: false })
}

// 身份验证验证邮箱流水
export function verifyEmailForIdentityVerifyApi(data: {sendSeqId: string; verifyCode: string}) {
  return request<void>({
    url: "/mail/verify",
    method: "post",
    data
  }, { showError: false })
}

// 身份验证验证邮箱和三方验证器
export function verifyEmailAndThirdPartyForIdentityVerifyApi(data: Withdrawal.IdentityVerifyFormData) {
  return request<{data: {sysSeqId: string}}>({
    url: "/verify/verify",
    method: "post",
    data
  }, { showError: false })
}

// 手续费计算
export function calcFeeApi(data: {transAmt: string; feeType: FeeEnumType, cryptoNet?: string}) {
  return request<{data: {feeAmt: string}}>({
    url: "/fee/calc",
    method: "post",
    data
  })
}

// 法币提现手续费计算
export function calcFeeForFiatWithdrawApi(data: {transAmt: string; acctInfoId: string | number}) {
  return request<Withdrawal.FiatWithdrawFee>({
    url: "/withdraw/trialCalcFee",
    method: "post",
    data
  })
}

// 提现结果查询
export function queryWithdrawalOrderDetailApi(data: {sysSeqId: string}) {
  return request<Withdrawal.WithdrawalOrderDetailResponseData>({
    url: "/withdraw/queryDetail",
    method: "post",
    data
  })
}
