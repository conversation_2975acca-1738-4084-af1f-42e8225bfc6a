export default {
  withdrawal: {
    title: 'Withdrawal',
    withdrawalAmount: 'Withdrawal Amount',
    expectedAmount: 'Expected Amount',
    inputPlaceholder: 'Please enter',
    bankTransfer: 'Bank Transfer',
    max: 'Max',
    fee: 'Fee',
    feeAmtTip: 'Fee',
    validityTip: 'Validity',
    validityDesc: '0-1 working day',
    ltDrawalTip:
      'This amount does not deduct the fees of the intermediary bank and the receiving bank; if it encounters a local bank holiday, the arrival time will be extended',
    confirmBtn: 'Confirm Withdrawal',
    identityAuthentication: 'Identity Authentication',
    confirmBtn2: 'Confirm',
    cancelBtn: 'Cancel',
    emil: 'Email',
    emilCode: 'Email Code',
    sendCode: 'Send Code',
    resendCode: 'Resend Code',
    sendingCode: 'Sending...',
    emailError: 'Please enter a valid email address',
    emailPlaceholder: 'Please enter your email',
    verifyCodePlaceholder: 'Please enter the verification code',
    verifyCodeError: 'Please enter the correct verification code',
    verifyCodeEmptyError: 'Please send the verification code first',
    transactionPassword: 'Transaction Password',
    transactionPasswordPlaceholder: 'Please enter your transaction password',
    transactionPasswordError: 'Please enter the correct transaction password',
    amountError: 'The minimum withdrawal amount is 100.00 USD',
    amountMaxVerifyError: 'The withdrawal amount cannot be greater than the account balance',
    ltWithdrawal: 'Fiat Withdrawal',
    dcWithdrawal: 'Crypto Withdrawal',
    noAvailableAccount: 'No available fiat account',
    noDcAccount: 'No available cryptocurrency account',
    selectAccountPlaceholder: 'Select an account',
    bankAccountManagement: 'Bank Account Management',
    inputAmtPlaceholder: 'Please enter the withdrawal amount',
    balanceTip: 'Balance: ',
    feeTip: 'Fee: ',
    expectedTimeTip: 'Expected arrival time',
    expectedTimeTip1: 'Expected arrival',
    selectFeeTypePlaceholder: 'Please select the fee calculation method',
    selectPurposePlaceholder: 'Please select the transaction purpose',
    selectPurposePlaceholderAndRemark:
      'Please select the transaction purpose and fill in the payment remarks',
    remarkTip: 'Remark',
    remarkPlaceholder: 'Please enter the remark (the payee will see it)',
    transactionSupplementaryMaterials: 'Transaction Supplementary Materials (Optional)',
    uploadTipBtn: 'Go to upload',
    uploadedTip: 'Uploaded, go to modify',
    confirmBtn3: 'Confirm Withdrawal',
    accountName: 'Account name',
    paymentMethod: 'Payment method',
    accountNumber: 'Account number',
    swiftBic: 'Swift/BIC',
    bankName: 'Bank name',
    bankAddress: 'Bank address',
    aboutWithdrawalAmount: 'About withdrawal amount',
    aboutFee: 'About fee',
    aboutTime: 'About arrival time',
    aboutWithdrawalAmountTip:
      'The minimum withdrawal amount is ',
    aboutWithdrawalAmountTip2: "The current account has a withdrawal amount limit. If you need to increase the limit, please contact your dedicated sales or customer service representative to assist with the application.",
    aboutFeeTip:
      "A withdrawal fee will be deducted. In the 'SHA(shared) fee mode', in addition to the fixed fee, there may also be intermediary bank fees, which will be borne by the payee.",
    aboutTimeTip:
      'The arrival time is based on the clearing agreement between the remitting bank and the receiving bank. Due to differences in region, currency, and channel, the arrival time may vary. Please pay attention.',
    selectOnlineAddressPlaceholder: 'Select an online address',
    selectOnlineAddressPlaceholder2: 'Select a receiving address',
    onlineAddressManagement: 'Online Address Management',
    withdrawalFee: 'Withdrawal fee',
    walletName: 'Wallet name',
    network: 'Network',
    address: 'Address',
  },
  withdrawalResult: {
    fail: 'Transaction failed',
    success: 'Transaction success',
    withdrawalAmount: 'Withdrawal amount',
    feeAmount: 'Fee',
    transactionTime: 'Transaction time',
    transactionNumber: 'Transaction number',
    payeeName: 'Payee name',
    bankAccount: 'Bank account',
    accountAlias: 'Account alias',
    errorReason: 'Error reason',
    fee: 'Fee',
    backBtn: 'Back',
    withdrawalAccepted: 'Withdrawal accepted',
    walletName: 'Wallet name',
    network: 'Network',
    address: 'Address',
  },
};
