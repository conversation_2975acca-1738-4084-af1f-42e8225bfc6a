<template>
  <div class="withdrawal-container">
    <div class="withdrawal-content">
      <!-- Main Content Area -->
      <div class="withdrawal-main">
        <!-- Header with title and tabs aligned -->
        <div class="withdrawal-header">
          <h1 class="withdrawal-title">{{ t('withdrawal.title') }}</h1>
          <!-- Tab Navigation with smooth slider -->
          <div ref="tabsRef" class="withdrawal-tabs">
            <!-- 滑动指示器背景 -->
            <div ref="sliderRef" class="tab-slider"></div>
            <button class="tab-button" :class="{ active: withdrawalType === FeeEnumType.FIAT_WITHDRAW }"
              @click="switchTab(FeeEnumType.FIAT_WITHDRAW)">
              Fiat
            </button>
            <button class="tab-button" :class="{ active: withdrawalType === FeeEnumType.CRYPTO_WITHDRAW }"
              @click="switchTab(FeeEnumType.CRYPTO_WITHDRAW)">
              Crypto
            </button>
          </div>
        </div>

        <!-- Withdrawal Form Content with smooth transitions -->
        <div class="withdrawal-form-content">
          <Transition name="fade" mode="out-in">
            <LtWithdrawal v-if="withdrawalType === FeeEnumType.FIAT_WITHDRAW" ref="ltWithdrawalRef"
              :walletAddrList="fiatAcctInfoList"
              @submit="confirmLtSubmit" key="fiat" />
            <DcWithdrawal v-else ref="dcWithdrawalRef" 
              :walletAddrList="dcWalletAddrList"
              @submit="confirmDcSubmit" key="crypto" />
          </Transition>
        </div>
      </div>

      <!-- Sidebar with smooth transitions -->
      <Transition name="slide-fade" mode="out-in" appear @after-enter="onSidebarTransitionComplete">
        <div class="withdrawal-sidebar-container withdrawal-sidebar" :key="withdrawalType">
          <div key="progress-section">
            <p class="text-16px text-#222527 font-600">{{ t('withdrawal.availableWithdrawalAmount') }}</p>
            <p class="text-14px text-#222527 font-400 mt-8px">{{ t('withdrawal.today') }}</p>
            <!-- 进度条 -->
            <el-progress :percentage="animatedTodayPercent" color="#FF0064" :show-text="false" :stroke-width="10" class="mt-8px" />
            <p class="text-12px text-#6B7275 font-400 mt-4px">{{ t('withdrawal.usdEquivalent') }} {{ todayAmount }}</p>
            <p class="text-14px text-#222527 font-400 mt-16px">{{ t('withdrawal.month') }}</p>
            <!-- 进度条 -->
            <el-progress :percentage="animatedMonthPercent" color="#FF0064" :show-text="false" :stroke-width="10" class="mt-8px" />
            <p class="text-12px text-#6B7275 font-400 mt-4px">{{ t('withdrawal.usdEquivalent') }} {{ monthAmount }}</p>
          </div>
          <div v-if="withdrawalType === FeeEnumType.FIAT_WITHDRAW" key="fiat-sidebar" class="mt-24px">
            <span class="sidebar-card-title">{{ t('withdrawal.aboutWithdrawalAmount') }}</span>
            <div class="sidebar-card-content">
              <p>
                {{ t('withdrawal.aboutWithdrawalAmountTip') }}
                <span class="amount">{{ minLimteStr }}</span>
                {{ t('withdrawal.aboutWithdrawalAmountTip2') }}
              </p>
            </div>
            <span class="sidebar-card-title">{{ t('withdrawal.aboutFee') }}</span>
            <div class="sidebar-card-content">
              <p class="description">{{ t('withdrawal.aboutFeeTip') }}</p>
            </div>
            <span class="sidebar-card-title">{{ t('withdrawal.aboutTime') }}</span>
            <div class="sidebar-card-content">
              <p class="description">{{ t('withdrawal.aboutTimeTip') }}</p>
            </div>
            <span class="sidebar-card-title">{{ t('tips.compliance.title') }}</span>
            <div class="sidebar-card-content mb-0px">
              <p class="description">{{ t('tips.compliance.content') }}</p>
            </div>
          </div>
          <div v-else key="crypto-sidebar" class="mt-24px">
            <span class="sidebar-card-title">{{ t('withdrawal.aboutWithdrawalAmount') }}</span>
            <div class="sidebar-card-content">
              <p>
                {{ t('withdrawal.aboutWithdrawalAmountTip') }}
                <span class="amount">{{ minLimteStr }}</span>
                {{ t('withdrawal.aboutWithdrawalAmountTip2') }}
              </p>
            </div>
            <span class="sidebar-card-title">{{ t('tips.compliance.title') }}</span>
            <div class="sidebar-card-content mb-0px">
              <p class="description">{{ t('tips.compliance.content') }}</p>
            </div>
          </div>
        </div>
      </Transition>
    </div>

    <template v-if="showIdentityVerify">
      <IdentityVerify v-model="showIdentityVerify" @submit="verifyIdOk" @cancel="cancelVerify"
        :businessType="withdrawalType" />
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted, onUnmounted, computed, provide } from 'vue';
import { t } from '@@/i18n';
import LoadAnimation from '@@/components/LoadAnimation/LoadAnimation.vue';
import DcWithdrawal from './components/DcWithdrawal.vue';
import LtWithdrawal from './components/LtWithdrawal.vue';
import IdentityVerify from './components/IdentityVerify.vue';
import { queryAcctListApproved, queryDigitalAddressApi, withdrawalOrderApi } from './apis';
import { isReallyEmpty } from '@/common/utils/validate';
import { FeeEnumType } from '../exchange/apis/type';
import { DigitalAddress, FiatAcctInfo, LtWithdrawalFormData } from './apis/type';
import { feeQuotaApi } from '@/common/apis/common';
import { FeeQuotaResponseData } from '@/common/apis/common/type';
import { useRouter } from 'vue-router';
import { handleAuthVerification } from '@/common/utils/2FAUtil';
import BindTFADrawer from '@/common/components/BindTFA';

const router = useRouter();

// Active tab state with smooth animation support
const withdrawalType = ref<FeeEnumType>(FeeEnumType.FIAT_WITHDRAW);
// Tab container and slider refs for animation
const tabsRef = ref<HTMLElement>();
const sliderRef = ref<HTMLElement>();

const showIdentityVerify = ref(false);
const showUploadDrawer = ref(false);
const ltWithdrawalRef = ref<InstanceType<typeof LtWithdrawal> | null>(null);
const ltWithdrawalData = ref<LtWithdrawalFormData>({
  acctInfoId: '',
  transAmt: '',
  channelType: '',
  feeMethod: '',
  transactionPurposeLevel1: '',
  transactionPurposeLevel2: '',
  transactionRemarks: '',
  fileIds: [],
});
const dcWithdrawalRef = ref<InstanceType<typeof DcWithdrawal> | null>(null);
const dcWithdrawalData = ref<{ acctInfoId?: number; transAmt: string }>({ transAmt: '' });

// 可用提现账户数组
const fiatAcctInfoList = ref<FiatAcctInfo[]>([])
const dcWalletAddrList = ref<DigitalAddress[]>([])

// 提现最低额度
const feeQuota = ref<FeeQuotaResponseData['data'] | null>(null);

// 进度条动画相关
const animatedTodayPercent = ref(0)
const animatedMonthPercent = ref(0)
const animationDuration = 400 // 动画持续时间（毫秒）
const isDataLoaded = ref(false) // 数据加载标识

// 今日和月度的提现进度百分比
const todayPercent = computed(() => {
  if (!feeQuota.value) return 0
  return (
    withdrawalType.value === FeeEnumType.FIAT_WITHDRAW ? 
    (feeQuota.value.fiatWithdrawDailyUsed / feeQuota.value.dailyFiatWithdraw) :
    (feeQuota.value.cryptoWithdrawDailyUsed / feeQuota.value.dailyCryptoWithdraw)
  ) * 100
})

const todayAmount = computed(() => {
  if (!feeQuota.value) return '--'
  return (
    withdrawalType.value === FeeEnumType.FIAT_WITHDRAW ? 
    `${feeQuota.value.fiatWithdrawDailyUsed}/${feeQuota.value.dailyFiatWithdraw}` : 
    `${feeQuota.value.cryptoWithdrawDailyUsed}/${feeQuota.value.dailyCryptoWithdraw}`
  )
})

// 月额度显示的时候，要判断30天和一个月的使用情况
const maxMonthAmount = computed(() => {
  if (!feeQuota.value) return 0
  return (
    withdrawalType.value === FeeEnumType.FIAT_WITHDRAW ? 
    (feeQuota.value.fiatWithdrawLast30DaysUsed > feeQuota.value.fiatWithdrawMonthlyUsed ? feeQuota.value.fiatWithdrawLast30DaysUsed : feeQuota.value.fiatWithdrawMonthlyUsed) :
    (feeQuota.value.cryptoWithdrawLast30DaysUsed > feeQuota.value.cryptoWithdrawMonthlyUsed ? feeQuota.value.cryptoWithdrawLast30DaysUsed : feeQuota.value.cryptoWithdrawMonthlyUsed)
  )
})

const monthPercent = computed(() => {
  if (!feeQuota.value) return 0
  return (
    withdrawalType.value === FeeEnumType.FIAT_WITHDRAW ? 
    (maxMonthAmount.value / feeQuota.value.monthlyFiatWithdraw) :
    (maxMonthAmount.value / feeQuota.value.monthlyCryptoWithdraw)
  ) * 100
})

const monthAmount = computed(() => {
  if (!feeQuota.value) return '--'
  return (
    withdrawalType.value === FeeEnumType.FIAT_WITHDRAW ? 
    `${maxMonthAmount.value}/${feeQuota.value.monthlyFiatWithdraw}` : 
    `${maxMonthAmount.value}/${feeQuota.value.monthlyCryptoWithdraw}`
  )
})

// 进度条动画函数
const animateProgress = (targetValue: number, animatedRef: Ref<number>) => {
  const startTime = performance.now()
  const startValue = animatedRef.value
  const targetDiff = targetValue - startValue

  const animate = (currentTime: number) => {
    const elapsed = (currentTime - startTime) < 0 ? 0 : (currentTime - startTime) 
    const progress = Math.min(elapsed / animationDuration, 1)

    // 将缓动函数替换为直接使用 progress，实现匀速
    animatedRef.value = startValue + (targetDiff * progress)

    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      animatedRef.value = targetValue
    }
  }

  requestAnimationFrame(animate)
}
// Transition 完成后的回调函数
const onSidebarTransitionComplete = () => {
  // 侧边栏 Transition 完成，现在可以安全地触发动画
  if (isDataLoaded.value) {
    nextTick(() => {
      animateProgress(todayPercent.value, animatedTodayPercent)
      animateProgress(monthPercent.value, animatedMonthPercent)
    })
  }
}

const onProgressSectionTransitionComplete = () => {
  // 进度条区域的 Transition 完成，现在可以安全地触发动画
  // 但需要确保数据已经加载完成
  if (isDataLoaded.value) {
    nextTick(() => {
      animateProgress(todayPercent.value, animatedTodayPercent)
      animateProgress(monthPercent.value, animatedMonthPercent)
    })
  }
}

// 监听数据加载完成
watch(isDataLoaded, (loaded) => {
  if (loaded) {
    // 数据加载完成，立即触发动画（如果Transition已经完成）
    nextTick(() => {
      animateProgress(todayPercent.value, animatedTodayPercent)
      animateProgress(monthPercent.value, animatedMonthPercent)
    })
  }
})

// 监听提现类型变化，重置动画值
watch(withdrawalType, () => {
  // 重置动画值，等待 Transition 完成后再触发动画
  animatedTodayPercent.value = 0
  animatedMonthPercent.value = 0
})

// 监听提现类型变化，重置动画值
watch(withdrawalType, () => {
  // 重置动画值，等待 Transition 完成后再触发动画
  animatedTodayPercent.value = 0
  animatedMonthPercent.value = 0
})

/**
 * 更新滑动指示器位置
 * 根据当前激活的tab计算并设置滑动指示器的位置和宽度
 */
const updateSliderPosition = async () => {
  await nextTick();
  if (!tabsRef.value || !sliderRef.value) return;

  const activeButton = tabsRef.value.querySelector('.tab-button.active') as HTMLElement;
  if (!activeButton) return;

  const tabsRect = tabsRef.value.getBoundingClientRect();
  const buttonRect = activeButton.getBoundingClientRect();

  // 计算相对于tabs容器的位置
  const left = buttonRect.left - tabsRect.left;
  const width = buttonRect.width;

  // 应用平滑过渡动画
  sliderRef.value.style.transform = `translateX(${left}px)`;
  sliderRef.value.style.width = `${width}px`;
};

/**
 * 切换tab标签
 * @param tab - 要切换到的tab类型
 */
const switchTab = async (tab: FeeEnumType) => {
  if (withdrawalType.value === tab) return;

  withdrawalType.value = tab;
  await updateSliderPosition();
};

// Watch for withdrawal type changes to update slider position
watch(withdrawalType, updateSliderPosition);
// 法币和数币的货币单位
const ftCc = ref('USD');
const cryCc = ref('USDT');
provide('ftCc', ftCc);
provide('cryCc', cryCc);
// 限额提示
const minLimteStr = computed(() =>
  withdrawalType.value === FeeEnumType.FIAT_WITHDRAW ? `${feeQuota.value?.recmFiatWithdrawQuota || 100} ${ftCc.value}` : `${feeQuota.value?.recmCryptoWithdrawQuota || 100} ${cryCc.value}`
);

// 提现下单
// sendSeqId 邮箱流水验证id；有值代表验证过邮箱
const withdrawalOrderApiResp = async (sendSeqId: string, tfaSeqId: string, withdrawType: FeeEnumType) => {
  try {
    let params = {};

    if (withdrawalType.value === FeeEnumType.FIAT_WITHDRAW) {
      params = {
        ...ltWithdrawalData.value,
      };
    } else {
      params = {
        acctInfoId: dcWithdrawalData.value.acctInfoId,
        transAmt: dcWithdrawalData.value.transAmt,
      };
    }
    withdrawalOrderApi({
      sendSeqId: sendSeqId,
      tfaSeqId: tfaSeqId,
      withdrawType: withdrawType,
      ...params,
    }).then((res) => {
      // 跳转结果页面
      router.push({
        name: 'WithdrawalResult',
        query: {
          sysSeqId: res.data.sysSeqId,
        },
      });
    });
  } catch (error) { }
};

const confirmLtSubmit = (event: LtWithdrawalFormData) => {
  if (!event.acctInfoId) {
    return;
  }
  handleTFAVerify(() => {
    showIdentityVerify.value = true;
    ltWithdrawalData.value = {
      ...event,
    };
  });
};

const confirmDcSubmit = (event: { acctInfoId?: number; transAmt: string }) => {
  if (!event.acctInfoId) {
    return;
  }
  handleTFAVerify(() => {
    showIdentityVerify.value = true;
    dcWithdrawalData.value = {
      ...event,
    };
  });
};

const verifyIdOk = (payload: {sendSeqId: string, tfaSeqId: string}) => {
  showIdentityVerify.value = false;
  // 提现下单
  withdrawalOrderApiResp(payload.sendSeqId, payload.tfaSeqId, withdrawalType.value);
};

const cancelVerify = () => {
  showIdentityVerify.value = false;
};

/** 检查是否绑定双因子 */
const handleTFAVerify = (verifyOk?: () => void) => {
  handleAuthVerification(verifyOk, {
    message: t('common.twoFactorAuth.message')
  })
}

onMounted(async () => {
  getFeeQuota();
  queryDigitalAddressApiResp();
  queryAcctListApprovedResp();
  // 初始化滑动指示器位置
  await updateSliderPosition();

  // 监听窗口大小变化，重新计算滑动指示器位置
  window.addEventListener('resize', updateSliderPosition);

  handleTFAVerify()
});

// 组件卸载时清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', updateSliderPosition);
});

const getFeeQuota = async () => {
  try {
    const res = await feeQuotaApi();
    feeQuota.value = res.data;
    
    // 标记数据加载完成
    isDataLoaded.value = true
  } catch (error) {
    console.error(error);
  }
}

// 查询数币地址
const queryDigitalAddressApiResp = async () => {
  try {
    const res = await queryDigitalAddressApi()
    dcWalletAddrList.value = res.data.dcWalletAddrList || []
  } catch (error) {
    console.error(error)
  }
}

// 查询已审核法币账户信息列表
const queryAcctListApprovedResp = async () => {
  try {
    const res = await queryAcctListApproved()
    fiatAcctInfoList.value = res.data.fiatAcctInfoList || []
  } catch (error) {
    console.error(error)
  }
}

</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

.withdrawal-container {
  background-color: #ffffff;
  padding: 32px 40px;
  min-width: 1120px;
  justify-content: center;
}

.withdrawal-content {
  display: flex;
  gap: 24px;
  padding-right: 24px;
  justify-content: center;
  height: fit-content;
  margin-bottom: 32px;
}

/* Main Content Area */
.withdrawal-main {
  flex: 1;
  padding: 24px;
  min-width: 834px;
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 12px;
  height: fit-content;
}

/* Header with title and tabs aligned */
.withdrawal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.withdrawal-title {
  font-family: 'PingFangSC-Semibold';
  font-weight: 600;
  font-size: 28px;
  color: #222527;
  margin: 0;
}

/* Tab Navigation with smooth animation */
.withdrawal-tabs {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 6px;
  padding: 4px;
  width: fit-content;
  position: relative;
  overflow: hidden;
}

/* 滑动指示器 - 提供平滑的背景滑动效果 */
.tab-slider {
  position: absolute;
  top: 4px;
  left: 4px;
  height: 32px;
  background: linear-gradient(135deg, #030814 0%, #1a1d29 100%);
  border-radius: 4px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1;
  /* 确保在移动设备上也有硬件加速 */
  will-change: transform, width;
  transform: translateX(0px);
  box-shadow:
    0 2px 8px rgba(3, 8, 20, 0.15),
    0 1px 3px rgba(3, 8, 20, 0.1);

  /* 动画期间增强阴影效果 */
  transition:
    all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 0.3s ease;

  /* 添加微妙的光泽效果 */
  &::before {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    height: 50%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    border-radius: 3px 3px 0 0;
    pointer-events: none;
  }
}

.tab-button {
  border: none;
  height: 32px;
  width: 144px;
  background: transparent;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  font-family: 'PingFangSC-Regular';
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #030814;
  position: relative;
  z-index: 2;
  /* 确保文字在滑动指示器之上 */
  transform: scale(1);
  will-change: transform, color;

  /* 激活状态 */
  &.active {
    color: #ffffff;
    font-weight: 500;
    transform: scale(1.02);
  }

  /* 悬停效果 */
  &:hover:not(.active) {
    color: #666;
    transform: scale(1.01);
  }

  /* 点击效果 */
  &:active {
    transform: scale(0.98);
    transition-duration: 0.1s;
  }

  /* 焦点状态 */
  &:focus-visible {
    outline: 2px solid #030814;
    outline-offset: 2px;
  }
}

.withdrawal-form-content {
  width: 100%;
}

/* Sidebar */
.withdrawal-sidebar-container {
  flex-shrink: 0;
  width: 264px;
}

.withdrawal-sidebar {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.sidebar-card-title {
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 16px;
  color: #222527;
  display: block;
  margin-bottom: 8px;
}

.sidebar-card-content {
  margin-bottom: 24px;

  p {
    font-family: PingFangSC;
    font-weight: 400;
    text-align: left;
    font-size: 14px;
    line-height: 1.5;
    margin-top: 0;
    margin-bottom: 0;
    color: #6b7275;

    &:last-child {
      margin-bottom: 0;
    }

    .amount {
      font-weight: 600;
      color: #030814;
    }
  }

  .description {
    color: #6b7275;
  }
}

/* Vue Transition Animations */

/* 淡入淡出过渡效果 - 用于主要内容区域 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 滑动淡入过渡效果 - 用于侧边栏内容，从下往上的过渡方式 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(15px) scale(0.98);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.98);
}

/* 优化过渡期间的性能 */
.fade-enter-active *,
.fade-leave-active *,
.slide-fade-enter-active *,
.slide-fade-leave-active * {
  will-change: transform, opacity;
}

/* 确保过渡期间元素层级正确 */
.fade-enter-active,
.slide-fade-enter-active {
  z-index: 10;
}

.fade-leave-active,
.slide-fade-leave-active {
  z-index: 9;
}

:deep(.el-button) {
  height: auto;
}
</style>
