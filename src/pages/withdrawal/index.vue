<template>
  <div class="app-container mx-auto pb-100px overflow-auto">
    <!-- 左边具体内容 -->
    <div class="p-24px content-view">
      <!-- 切换提现类型 -->
      <div class="title-label">{{ t('withdrawal.title') }}</div>
      <el-tabs v-model="withdrawalType" class="withdrawal-tabs">
        <el-tab-pane :label="'Fiat'" :name="FeeEnumType.FIAT_WITHDRAW">
          <LtWithdrawal ref="ltWithdrawalRef" @submit="confirmLtSubmit" />
        </el-tab-pane>
        <el-tab-pane :label="'Crypto'" :name="FeeEnumType.CRYPTO_WITHDRAW">
          <DcWithdrawal ref="dcWithdrawalRef" @submit="confirmDcSubmit" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 右边提示信息 -->
    <div class="ml-24px p-24px bg-#F8F9FA b-rd-12px w-262px">
      <div>
        <p class="text-16px text-#222527 font-600">{{ t('withdrawal.aboutWithdrawalAmount') }}</p>
        <p class="text-#6B7275 text-14px mt-8px line-height-20px">
          <span>{{ t('withdrawal.aboutWithdrawalAmountTip') }}</span>
          <span class="font-600 text-14px color-#030814">{{ minLimteStr }}</span>
          <span>。{{ t('withdrawal.aboutWithdrawalAmountTip2') }}</span>
        </p>
      </div>

      <div v-if="withdrawalType === FeeEnumType.FIAT_WITHDRAW">
        <p class="mt-24px text-16px text-#222527 font-600">{{ t('withdrawal.aboutFee') }}</p>
        <p class="text-#6B7275 text-14px mt-8px line-height-20px">
          {{ t('withdrawal.aboutFeeTip') }}
        </p>
      </div>

      <div v-if="withdrawalType === FeeEnumType.FIAT_WITHDRAW">
        <p class="mt-24px text-16px text-#222527 font-600">{{ t('withdrawal.aboutTime') }}</p>
        <p class="text-#6B7275 text-14px mt-8px line-height-20px">
          {{ t('withdrawal.aboutTimeTip') }}
        </p>
      </div>
      <p class="mt-24px text-16px text-#222527 font-600">{{ t('tips.compliance.title') }}</p>
      <p class="text-#6B7275 text-14px mt-8px line-height-20px">
        {{ t('tips.compliance.content') }}
      </p>
    </div>

    <template v-if="showIdentityVerify">
      <IdentityVerify v-model="showIdentityVerify" @submit="verifyIdOk" @cancel="cancelVerify"
        :businessType="withdrawalType" />
    </template>
  </div>
</template>

<script lang="ts" setup>
import { t } from '@@/i18n';
import LoadAnimation from '@@/components/LoadAnimation/LoadAnimation.vue';
import DcWithdrawal from './components/DcWithdrawal.vue';
import LtWithdrawal from './components/LtWithdrawal.vue';
import IdentityVerify from './components/IdentityVerify.vue';
import { withdrawalOrderApi } from './apis';
import { isReallyEmpty } from '@/common/utils/validate';
import { FeeEnumType } from '../exchange/apis/type';
import { LtWithdrawalFormData } from './apis/type';

const withdrawalType = ref<FeeEnumType>(FeeEnumType.FIAT_WITHDRAW);
const showIdentityVerify = ref(false);
const showUploadDrawer = ref(false);
const ltWithdrawalRef = ref<InstanceType<typeof LtWithdrawal> | null>(null);
const ltWithdrawalData = ref<LtWithdrawalFormData>({
  acctInfoId: '',
  transAmt: '',
  channelType: '',
  feeMethod: '',
  transactionPurposeLevel1: '',
  transactionPurposeLevel2: '',
  transactionRemarks: '',
  fileIds: [],
});
const dcWithdrawalRef = ref<InstanceType<typeof DcWithdrawal> | null>(null);
const dcWithdrawalData = ref<{ acctInfoId?: number; transAmt: string }>({ transAmt: '' });

const router = useRouter();
// 限额提示
const minLimteStr = computed(() =>
  withdrawalType.value === FeeEnumType.FIAT_WITHDRAW ? '100.00 USD' : '100.00 USDT'
);

// 提现下单
// sendSeqId 邮箱流水验证id；有值代表验证过邮箱
const withdrawalOrderApiResp = async (sendSeqId: string, withdrawType: FeeEnumType) => {
  try {
    let params = {};

    if (withdrawalType.value === FeeEnumType.FIAT_WITHDRAW) {
      params = {
        ...ltWithdrawalData.value,
      };
    } else {
      params = {
        acctInfoId: dcWithdrawalData.value.acctInfoId,
        transAmt: dcWithdrawalData.value.transAmt,
      };
    }
    withdrawalOrderApi({
      sendSeqId: sendSeqId,
      withdrawType: withdrawType,
      ...params,
    }).then((res) => {
      // 跳转结果页面
      router.push({
        name: 'WithdrawalResult',
        query: {
          sysSeqId: res.data.sysSeqId,
        },
      });
    });
  } catch (error) { }
};

const confirmLtSubmit = (event: LtWithdrawalFormData) => {
  if (!event.acctInfoId) {
    return;
  }
  showIdentityVerify.value = true;
  ltWithdrawalData.value = {
    ...event,
  };
};

const confirmDcSubmit = (event: { acctInfoId?: number; transAmt: string }) => {
  if (!event.acctInfoId) {
    return;
  }
  dcWithdrawalData.value = {
    acctInfoId: event.acctInfoId,
    transAmt: event.transAmt,
  };
  showIdentityVerify.value = true;
};

const verifyIdOk = (sendSeqId: string) => {
  showIdentityVerify.value = false;
  // 提现下单
  withdrawalOrderApiResp(sendSeqId, withdrawalType.value);
};

const cancelVerify = () => {
  showIdentityVerify.value = false;
};
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

.app-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
}

.content-view {
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 12px;
  padding: 24px;
  position: relative;
}

.title-label {
  position: absolute;
  top: 24px;
  left: 24px;
  font-weight: 600;
  font-size: 28px;
  color: #222527;
}

:deep(.el-button) {
  height: auto;
}

.withdrawal-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 24px;
  }

  :deep(.el-tabs__nav-wrap) {
    display: flex;
    justify-content: flex-end;

    &::after {
      height: 0;
    }
  }

  :deep(.el-tabs__nav) {
    width: 300px;
    display: flex;
    background: #f5f5f5;
    border-radius: 6px;
  }

  :deep(.el-tabs__item) {
    color: #6b7275;
    height: 32px;
    width: 150px;
    padding: 0;

    &.is-active {
      color: #ffffff;
      height: 32px;
      width: 150px;
      background: #030814;
      border-radius: 6px;
    }
  }

  :deep(.el-tabs__active-bar) {
    height: 0px;
  }
}
</style>
