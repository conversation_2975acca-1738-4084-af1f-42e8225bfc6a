<template>
  <el-drawer 
    v-model="showDrawer" 
    size="400px" 
    style="--el-drawer-padding-primary: 0px"
    header-class="custom-drawer-header"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :close-on-press-escape="false"
  >
    <template #header>
      <div class="h-60px border-b-1 mb-0 flex-row flex-items-center text-18px font-500 color-[#222527] pt-18px pb-18px pl-24px">
        <span>{{ t('withdrawal.transactionSupplementaryMaterials') }}</span>
      </div>
    </template>
    <template #default>
      <div class="p-24px">
        <UploadFiles fileType="SMFWT" :limit="1" v-model:fileList="editFiles" class="w-full"/>
      </div>
      <div 
        style="border-top: 1px solid #E5E6EB;"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
          <el-button @click="cancelBtnClick" class="cancel-btn w-68px h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px">{{ t('withdrawal.cancelBtn') }}</el-button>
          <el-button @click="submitClick" color="white" class="w-68px h-32px bg-[#030814] rounded-6px" type="primary" plain>{{ t('withdrawal.confirmBtn2') }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { FileInfo } from '@/pages/kyc/apis/type';
import { t } from '@@/i18n';
import UploadFiles from '@/common/components/UploadFiles/index.vue';

const props = withDefaults(defineProps<{
  modelValue: boolean
  fileInfos: FileInfo[]
}>(), {
  modelValue: true,
  fileInfos: () => []
});

const emit = defineEmits<{
  (e: 'submit', payload: FileInfo[]): void
  (e: 'cancel'): void
  (e: 'update:modelValue', value: boolean): void
}>()

const editFiles = ref<FileInfo[]>(props.fileInfos)

// 使用计算属性实现双向绑定
const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const cancelBtnClick = () => {
  emit('cancel')
}
const submitClick = () => {
  emit('submit', editFiles.value)
}
</script>

<style lang="scss" scoped>
.el-button {
  width: 100%;
  margin-top: 10px;
  background: #030814;
  border-radius: 6px;
  border:none;
}
.cancel-btn {
  background-color: #FFFFFF !important;
  border: 1px solid #E5E6EB !important;
  color: #222527 !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
}
:deep(.el-button.text-btn) {
  background: transparent;
  margin-left: 0;
  font-size: 14px;
  color: #222527;
  margin-top: 24px;
}
:deep(.el-button.sendcode-btn) {
  background: transparent;
  font-size: 14px;
  color: #FF0064;
  margin-top: 0;
  padding: 0;
}
:deep(.el-button.is-text:hover) {
  background: transparent;
}
:deep(.el-input) {
  --el-input-focus-border-color: #E5E6EB;
  border: #E5E6EB 1px solid;
  border-radius: 6px;
}
:deep(.el-input__inner) {
  height: 40px;
  padding: 0px 12px;
}
:deep(.el-input__wrapper) {
  border-radius: 6px;
  background-color: transparent;
}
:deep(.el-form-item.is-error .el-form-item__content .el-input .el-input__wrapper) {
  background-color: #FFF2EE;
} 

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }
  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
</style>
