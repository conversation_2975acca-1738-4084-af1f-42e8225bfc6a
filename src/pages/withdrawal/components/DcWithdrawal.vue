<template>
  <div class="relative mt-6px mb-32px flex flex-row w-100% min-w-834px">
    <Transition name="fade-slide" appear>
      <div class="w-50% min-w-417px flex flex-col justify-between" style="border-right: 1px solid #E5E6EB; padding-right: 24px; position: relative;">
        <el-form 
          ref="formRef" 
          :model="formData" 
          :rules="formRules">
        
        <!-- 选择链上收款地址 -->
        <el-row class="flex justify-between items-center relative pt-16px pb-16px">
          <div class="dashed-line absolute top-56px left-12px " />

          <div class="flex items-center max-w-55% line-height-20px">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-exchange-receive" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('withdrawal.selectOnlineAddressPlaceholder') }}
            </div>
          </div>

          <el-button class="font-400 absolute right-0 text-[#FF0064] max-w-40% text-right line-height-20px text-wrap" text @click="gotoBankListClick">
            {{ t('withdrawal.onlineAddressManagement') }}
          </el-button>
        </el-row>
        <!-- 选择账户下拉框 -->
        <el-form-item prop="currDcWalletAddrId" class="select-account ml-36px">
          <el-select 
            v-model="formData.currDcWalletAddrId" 
            class="w-100%" 
            :placeholder="t('withdrawal.selectOnlineAddressPlaceholder')"
            :popper-append-to-body="false" 
            popper-class="option"
            ref="selectDcWalletAddrRef"
          >
            <template #prefix v-if="!formData.currDcWalletAddrId">
              <span style="padding-left: 4px;">
                <SvgIcon name="icon-chain-adres" class="text-26px" />
              </span>
            </template>
            <template #label>
              <div class="text-16px text-[#222527] font-600">{{ currDcWalletAddr?.walletName }}</div>
              <div class="text-16px text-[#222527] overflow-clip" style="text-overflow:ellipsis">
                <span>{{ currDcWalletAddr?.cryptoNet }}</span>
                -
                <span>{{ currDcWalletAddr?.cryptoAddr }}</span>
              </div>
            </template>
            <el-option value="1" hidden></el-option>
            <div class="w-452px">
              <div 
                @click="selectDcWalletAddr(item)"
                v-for="(item, index) in dcWalletAddrList" 
                :style="{
                  width: '100%', 
                  cursor: 'pointer', 
                  color: '#222527', 
                  fontSize: '14px', 
                  padding: '16px', 
                  background: item.id == currDcWalletAddr?.id ? '#F8F9FA' : 'white', 
                  paddingBottom: index === dcWalletAddrList.length - 1 ? '16px' : '0'
                }"
              >
                <div style="font-weight: 600;">{{ item.walletName }}</div>
                <div style="margin-top: 3px;">
                  {{ item.cryptoNet }}-{{ item.cryptoAddr }}
                </div>
              </div>
            </div>
          </el-select>
        </el-form-item>

        <!-- 提现金额 -->
        <el-row class="mt-30px flex justify-between items-center relative">
          <div class="dashed-line absolute top-40px left-12px h-107px" />

          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-withdrawal-amount" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('withdrawal.inputAmtPlaceholder') }}
            </div>
          </div>
        </el-row>

        <!-- 金额输入框 -->
        <el-form-item prop="inputAmt" class="mt-16px ml-36px">
          <div class="h-40px w-100% flex flex-row cus-input">
            <el-input
              ref="inputRef"
              v-model="formData.inputAmt"
              class="w-100% text-16px font-400"
              :placeholder="t('withdrawal.inputPlaceholder')"
              :formatter="(value: string) => value.replace(/[^\d.]/g, '').replace(/(\..*)\./g, '$1').replace(/^(\d+\.?\d{0,6}).*/, '$1')"
              @input="handleInput"
              @blur="handleInputBlur"
            />
            <div class="flex items-center justify-end">
              <el-button @click="inputMaxAmt" class="pl-8px pr-8px color-[#FF0064] font-14 font-400 mr-8px" text>{{ t('withdrawal.max') }}</el-button>
              <div class="flex flex-row items-center">
                <el-image
                  :src="getCryptoIcon(cryCc)"
                  class="w-18px h-18px"
                />
                <span class="text-16px font-400 text-[#222527] ml-4px">{{ cryCc }}</span>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-row class="flex flex-row w-100% justify-end">
          <div class="text-14px text-[#6B7275]">
            {{ t('withdrawal.balanceTip') }}{{ cryCc }} {{ currDcWalletAddr?.avlBal || '0.00' }}
          </div>
        </el-row>

        <!-- 提现手续费 -->
        <el-row class="mt-48px flex justify-between items-center relative">

          <div class="flex items-center w-[calc(100%-40px)]">
            <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
              <SvgIcon name="icon-withdrawal-fee" class="text-14px color-[#FF0064]" />
            </div>
            <div class="font-600 text-[#222527] ml-12px">
              {{ t('withdrawal.withdrawalFee') }}
            </div>
          </div>
        </el-row>

        <!-- 计费方式下拉框 -->
        <el-row class="mt-16px ml-36px">
          <div class="flex items-center justify-start bg-#F5F5F5 p-8px b-rd-4px">
            <LoadAnimation v-if="loading" class="mr-8px" />
            <span v-else class="text-14px text-[#6B7275]">{{ t('withdrawal.fee') }}: {{ feeAmt }} {{ cryCc }}</span>
          </div>
        </el-row>

        <!-- 提现按钮 -->
        <el-row class="mt-48px ml-36px w-100% flex flex-row justify-end pr-36px">
          <el-button class="min-w-108px pl-16px pr-16px h-32px text-14px font-400 bg-[#FF0064] b-rd-6px border-none btn-hover-scale-sm" type="primary" @click="submitForm">
            {{ t('withdrawal.confirmBtn3') }}
          </el-button>
        </el-row>
        </el-form>
      </div>
    </Transition>

    <!-- 账户信息 -->
    <div class="pl-24px w-50% min-w-417px flex flex-col items-end" v-if="currDcWalletAddr">
      <transition-group class="w-100%" name="fade-slide" tag="div" appear :key="currDcWalletAddr?.id || 'empty'">
        <div v-for="(item, index) in currDcAcctItems" :key="`${currDcWalletAddr?.id || 'empty'}-${item.label}`"
          class="mt-24px flex flex-row justify-between items-start w-100% bank-info-item-animated"
          :style="{ '--animation-delay': `${index * 80}ms` }">
          <div class="text-14px text-[#6B7275] max-w-25%">{{ item.label }}</div>
          <div class="flex flex-row items-start  max-w-70% justify-end">
            <div class="text-14px text-[#222527] text-right break-all">{{ item.value ?? '--' }}</div>
            <!-- 复制按钮 -->
             <el-button class="text-[#FF0064] ml-8px" text @click="copyBankInfo(item.value)">
              <SvgIcon name="icon-copy-btn" class="text-16px color-#222527" />
            </el-button>
          </div>
        </div>
      </transition-group>

      <el-button text @click="copyAllInfo"
          class="font-family-[PingFangSC-Regular] p-24px pr-0px font-400 text-14px color-[#FF0064]! text-right cursor-pointer">
          {{ t('common.copyAllInfo') }}
      </el-button>
    </div>
    <Transition name="fade-slide" appear>
      <div v-if="!currDcWalletAddr" class="pl-24px w-50% min-w-417px flex flex-col justify-center items-center">
        <img src="@@/assets/icons/icon-dcwithdrawal-nodata.png" class="w-144px h-144px" />
        <div class="text-14px text-[#6B7275] mt-16px text-center">{{ t('withdrawal.selectOnlineAddressPlaceholder2') }}</div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { t } from '@@/i18n';
import LoadAnimation from '@@/components/LoadAnimation/LoadAnimation.vue';
import { InputInstance, ElMessage, ElSelect, FormRules } from 'element-plus';
import { calcFeeApi, queryDigitalAddressApi } from '@/pages/withdrawal/apis';
import { DigitalAddress } from '../apis/type';
import { FeeEnumType } from '../../exchange/apis/type';
import { subtract, safeNumber, formatNumber } from '@@/utils/math';
import { copyText } from '@/common/utils/clipboard';
import { getCryptoIcon, getCurrencyIcon } from '@/common/utils/imageUtils';

const router = useRouter();

// 表单数据
const formData = ref({
  currDcWalletAddrId: '',
  // 输入金额
  inputAmt: '',
})

const props = withDefaults(
  defineProps<{
    walletAddrList: DigitalAddress[]
  }>(),
  {
    walletAddrList: () => []
  }
);

const inputRef = ref<InputInstance | null>(null)
const accountAmountAmt = ref<string>('')
const feeAmt = ref('0.00')
const loading = ref(false)
const cryCc = inject('cryCc') as any

// 表单引用
const formRef = ref<any>(null)
const dcWalletAddrList = ref<DigitalAddress[]>(props.walletAddrList)
const currDcWalletAddr = ref<DigitalAddress | null>(null)

const selectDcWalletAddrRef = ref<InstanceType<typeof ElSelect> | null>(null)

const currDcAcctItems = computed(() => [
  { label: t('withdrawal.walletName'), value: currDcWalletAddr.value?.walletName || '' },
  { label: t('withdrawal.network'), value: currDcWalletAddr.value?.cryptoNet || '' },
  { label: t('withdrawal.address'), value: currDcWalletAddr.value?.cryptoAddr || '' }
]);


// 表单验证规则
const formRules = computed(() => {
  const allRules: FormRules = {
    currDcWalletAddrId: [
      { required: true, message: t('withdrawal.selectOnlineAddressPlaceholder'), trigger: ['change', 'blur'] }
    ],
    inputAmt: [
      { required: true, message: t('withdrawal.inputAmtPlaceholder'), trigger: 'blur' },
      { 
        validator: (rule: any, value: string, callback: Function) => {
          if (!value) {
            callback(new Error(t('withdrawal.inputAmtPlaceholder')))
          } else if ((parseFloat(value.replace(/,/g, '')) > (parseFloat(`${currDcWalletAddr.value?.avlBal || '0'}`)))) {
            callback(new Error(t('withdrawal.amountMaxVerifyError')))
          } else if (parseFloat(value.replace(/,/g, '')) < 100) {
            callback(new Error(t('withdrawal.amountError', { unit: currDcWalletAddr.value?.ccy })))
          } else {
            callback()
          }
        }, 
        trigger: 'blur' 
      }
    ],
  }
  return allRules
})

// 查询数币地址
watch(() => props.walletAddrList, (newVal) => {
  dcWalletAddrList.value = newVal
  if (dcWalletAddrList.value.length > 0) {
    currDcWalletAddr.value = dcWalletAddrList.value[0]
    if (dcWalletAddrList.value[0].id) {
      formData.value.currDcWalletAddrId = dcWalletAddrList.value[0].id.toString()
      cryCc.value = dcWalletAddrList.value[0].ccy
    }
  }
}, { immediate: true })

const handleInput = (val: string) => {
  let value = val.replace(/[^\d.]/g, '').replace(/(\..*)\./g, '$1').replace(/^(\d+\.?\d{0,2}).*/, '$1')
  formData.value.inputAmt = value
}

const handleInputBlur = () => {
  if (formData.value.inputAmt === '') return

  formData.value.inputAmt = amtValueFormatted(formData.value.inputAmt)
  calcFeeApiResp()
}

// 点击最大值
const inputMaxAmt = () => {
  if (currDcWalletAddr.value?.avlBal) {
    formData.value.inputAmt = `${currDcWalletAddr.value?.avlBal}`

    formData.value.inputAmt = amtValueFormatted(formData.value.inputAmt)
    calcFeeApiResp()
  }
}

const amtValueFormatted = (value: string | number, digits: number = 2) => {
  const amount = safeNumber(value);
  return formatNumber(amount, digits);
}

// 计算手续费
const calcFeeApiResp = async () => {
  // 校验账户和金额
  formRef.value?.validateField(['currDcWalletAddrId', 'inputAmt'], (validObj: any) => {
    if (validObj) {
      const value = formData.value.inputAmt.replace(/,/g, '') // 移除千位分隔符
      loading.value = true
      calcFeeApi({
        transAmt: value,
        feeType: FeeEnumType.CRYPTO_WITHDRAW,
        cryptoNet: currDcWalletAddr.value?.cryptoNet
      }).then((res) => {
        feeAmt.value = amtValueFormatted(res.data.feeAmt, 2)
        const inputValue = safeNumber(value);
        const feeValue = safeNumber(res.data.feeAmt);
        accountAmountAmt.value = inputValue > feeValue ? amtValueFormatted(subtract(inputValue, feeValue), 2) : '0.00'
      }).finally(() => {
        loading.value = false
      })
    }
  })
}

// 跳转到设置页面
const gotoBankListClick = () => {
  router.push({
    name: 'Setting',
    query: {
      activeItem: 'crypto-address'
    }
  })
}

// 切换钱包
const selectDcWalletAddr = (item: DigitalAddress) => {
  currDcWalletAddr.value = item
  if (item.id) {
    formData.value.currDcWalletAddrId = item.id.toString()
  }
  selectDcWalletAddrRef.value?.blur()
  cryCc.value = item.ccy

  // 重新计算手续费
  if (formData.value.inputAmt) {
    calcFeeApiResp()
  }
}

// 复制信息
const copyAllInfo = () => {
  // 将信息转成 key value
  const items = currDcAcctItems.value.map(item => ({ label: item.label, value: item.value }))
  const text = items.map(item => `${item.label}: ${item.value}`).join('\n');
  copyBankInfo(text);
}
const copyBankInfo = (text?: string) => {
  if (!text) return
  
  copyText(text)
}

const validateForm = async () => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    return false
  }
}

// 定义子组件暴露给父组件的接口
const emit = defineEmits<{
  (e: 'submit', payload: { acctInfoId?: number, transAmt: string }): void
  (e: 'cancel'): void
}>()

const submitForm = async () =>  {
  if (!(await validateForm())) {
    return null
  }
  if(!currDcWalletAddr.value?.id){
    ElMessage.warning(t('withdrawal.noDcAccount'))
    return null
  }
  const value = formData.value.inputAmt.replace(/,/g, '') // 移除千位分隔符

  emit('submit', {
    acctInfoId: currDcWalletAddr.value?.id,
    transAmt: value
  })
}

</script>

<style lang="scss" scoped>
:deep(.el-select) {
  --el-select-input-focus-border-color: #E5E6EB;
}
.select-account {
  :deep(.el-select__wrapper) {
    height: 60px;

    &:hover {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
    
    &.is-focus {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }

    &.is-focused {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
  }
}
:deep(.el-button, .el-button.is-round) {
  padding: 0;
}
.cus-input {
  border: 1px solid #E5E6EB;
  border-radius: 6px;
  height: 40px;
  padding: 0px;
  padding-right: 12px;
  :deep(.el-input__wrapper) {
    box-shadow: none;
    background-color: #ffffff;
    
    &:hover {
      border-color: none;
      box-shadow: none;
    }
    
    &.is-focus {
      border-color: none;
      box-shadow: none;
    }

    &.is-focused {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
  }
}
.cus-select {
  height: 40px;
  :deep(.el-select__wrapper) {
    height: 40px;

    &:hover {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
    
    &.is-focus {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }

    &.is-focused {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
  }
}
.cus-textarea {
  :deep(.el-textarea__inner) {
    border-radius: 6px;
    background-color: #ffffff;
    &:hover {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
    
    &.is-focus {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }

    &.is-focused {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
  }
}
.dashed-line {
  width: 1px;
  height: 95px;
  background-image: linear-gradient(to bottom, #E5E6EB 0%, #E5E6EB 80%, transparent 50%);
  background-size: 100% 10px;
  background-repeat: repeat-y
}

/* Vue Transition Animations */

/* 淡入滑动动画 - 仿照 FiatDeposit 的动画效果 */
.fade-slide-enter-active {
  animation: fadeSlideIn 400ms ease-out forwards;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

/* 淡入滑动动画关键帧 */
@keyframes fadeSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 银行信息栏动画效果 */
.bank-info-item-animated {
  animation: fadeSlideIn 400ms ease-out forwards;
  animation-delay: var(--animation-delay, 0ms);
  opacity: 0;
  transform: translateY(20px);
}
</style>