<template>
  <div class="relative mt-6px mb-32px flex flex-row w-834px">
    <el-form 
      ref="formRef" 
      :model="formData" 
      :rules="formRules" 
      class="w-50% flex flex-col justify-between" 
      style="border-right: 1px solid #E5E6EB; padding-right: 24px; position: relative;"
    >
      
      <!-- 选择收款账户 -->
      <el-row class="flex justify-between items-center relative pt-16px pb-16px">
        <div class="dashed-line absolute top-56px left-12px " />

        <div class="flex items-center max-w-55% line-height-20px">
          <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px;">
            <SvgIcon name="icon-exchange-receive" class="text-14px color-[#FF0064]" />
          </div>
          <div class="font-600 text-[#222527] ml-12px">
            {{ t('withdrawal.selectAccountPlaceholder') }}
          </div>
        </div>

        <el-button class="font-400 absolute right-0 text-[#FF0064] max-w-40% text-right line-height-20px text-wrap" text @click="gotoBankListClick">
          {{ t('withdrawal.bankAccountManagement') }}
        </el-button>
      </el-row>
      <!-- 选择账户下拉框 -->
      <el-form-item prop="currFiatAcctAddrId" class="select-account ml-36px">
        <el-select 
          v-model="formData.currFiatAcctAddrId" 
          class="w-100% text-16px" 
          :placeholder="t('withdrawal.selectAccountPlaceholder')"
          :popper-append-to-body="false" 
          popper-class="option"
          ref="selectLtWalletAddrRef"
        >
          <template #prefix v-if="!formData.currFiatAcctAddrId">
            <span style="padding-left: 4px;">
              <SvgIcon name="icon-chain-adres" class="text-26px" />
            </span>
          </template>
          <template #label>
            <div class="text-16px text-[#222527] font-600">{{ currFiatAcct?.acctName }}</div>
            <div class="text-16px text-[#222527] overflow-clip" style="text-overflow:ellipsis">
              {{ currFiatAcct?.bankCtry }}-{{ currFiatAcct?.ccy }}-{{ currFiatAcct?.payMethod }}-{{ currFiatAcct?.acctNo }}
            </div>
          </template>
          <el-option value="1" hidden></el-option>
          <div class="w-452px">
            <div 
              @click="selectLtWalletAddr(item)"
              v-for="(item, index) in fiatAcctInfoList" 
              :key="item.id"
              :style="{
                width: '100%', 
                cursor: 'pointer', 
                color: '#222527', 
                fontSize: '14px', 
                padding: '16px', 
                background: item.id == currFiatAcct?.id ? '#F8F9FA' : 'white', 
                'padding-bottom': index === fiatAcctInfoList.length - 1 ? '16px' : '0'
              }"
            >
              <div style="font-weight: 600;">{{ item.acctName }}</div>
              <div style="margin-top: 3px;">
                {{ item?.bankCtry }}-{{ item?.ccy }}-{{ item?.payMethod }}-{{ item?.acctNo }}
              </div>
            </div>
          </div>
        </el-select>
      </el-form-item>

      <!-- 提现金额 -->
      <el-row class="mt-30px flex justify-between items-center relative">
        <div class="dashed-line absolute top-40px left-12px h-107px" />

        <div class="flex items-center w-[calc(100%-40px)]">
          <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
            <SvgIcon name="icon-withdrawal-amount" class="text-14px color-[#FF0064]" />
          </div>
          <div class="font-600 text-[#222527] ml-12px">
            {{ t('withdrawal.inputAmtPlaceholder') }}
          </div>
        </div>
      </el-row>

      <!-- 金额输入框 -->
      <el-form-item prop="inputAmt" class="mt-16px ml-36px">
        <div class="h-40px w-100% flex flex-row cus-input">
          <el-input
            ref="inputRef"
            v-model="formData.inputAmt"
            class="w-100% text-16px font-400"
            :placeholder="t('withdrawal.inputPlaceholder')"
            :formatter="(value: string) => value.replace(/[^\d.]/g, '').replace(/(\..*)\./g, '$1').replace(/^(\d+\.?\d{0,2}).*/, '$1')"
            @input="handleInput"
            @blur="handleInputBlur"
          />
          <div class="flex items-center justify-end">
            <el-button @click="inputMaxAmt" class="pl-8px pr-8px color-[#FF0064] font-14 font-400 mr-8px" text>{{ t('withdrawal.max') }}</el-button>
            <div class="icon-currency-us"></div>
            <span class="text-16px font-400 text-[#222527] ml-4px">USD</span>
          </div>
        </div>
      </el-form-item>
      <el-row class="flex flex-row w-100% justify-end">
        <div class="text-14px text-[#6B7275]">
          {{ t('withdrawal.balanceTip') }} USD {{ amtValueFormatted(currFiatAcct?.avlBal || '0.00') }}
        </div>
      </el-row>

      <!-- 选择计费方式 -->
      <el-row class="mt-48px flex justify-between items-center relative">
        <div class="dashed-line absolute top-40px left-12px h-121px" />

        <div class="flex items-center w-[calc(100%-40px)]">
          <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
            <SvgIcon name="icon-withdrawal-fee" class="text-14px color-[#FF0064]" />
          </div>
          <div class="font-600 text-[#222527] ml-12px">
            {{ t('withdrawal.selectFeeTypePlaceholder') }}
          </div>
        </div>
      </el-row>

      <!-- 计费方式下拉框 -->
      <el-form-item prop="selectFiatFee.feeItem.feeMethod" class="mt-16px ml-36px">
        <el-select 
          v-model="formData.selectFiatFee.feeItem.feeMethod" 
          class="cus-select w-100% text-16px" 
          :placeholder="t('withdrawal.selectFeeTypePlaceholder')" 
          value-key="feeMethod"
          @change="selectFiatFeeItemChange"
        >
          <el-option
            v-for="item in fiatWithdrawFees"
            :key="item.feeMethod"
            :label="item.feeMethod"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-row class="mt-0px ml-36px">
        <div class="flex flex-row flex-wrap">
          <div class="flex items-center justify-start bg-#F5F5F5 pt-4px pb-4px pl-8px pr-8px mt-8px b-rd-4px mr-12px">
            <LoadAnimation v-if="loading" class="mr-8px" />
            <span class="text-14px text-[#6B7275]">{{ t('withdrawal.feeTip') }}USD {{ isReallyEmpty(feeAmt) ? '--' : feeAmt }}</span>
          </div>
          <div class="flex items-center justify-start bg-#F5F5F5 pt-4px pb-4px pl-8px pr-8px mt-8px b-rd-4px">
            <span class="text-14px text-[#6B7275]">{{ t('withdrawal.expectedTimeTip1') }}：{{ estimatedArrivalTimeString || '--'}}</span>
          </div>
        </div>
      </el-row>

      <!-- 选择交易目的 -->
      <el-row class="mt-48px flex justify-between items-center relative">

        <div class="flex items-center w-[calc(100%-40px)]">
          <div class="bg-[#FFEBF0] w-24px lh-24px text-center" style="border-radius: 6px">
            <SvgIcon name="icon-withdrawal-aim" class="text-14px color-[#FF0064]" />
          </div>
          <div class="font-600 text-[#222527] ml-12px">
            {{ t('withdrawal.selectPurposePlaceholder') }}
          </div>
        </div>
      </el-row>

      <!-- 交易目的下拉框 -->
      <el-form-item prop="transactionPurpose" class="mt-16px ml-36px">
        <el-cascader
          :placeholder="t('businessInfo.placeholder.select')"
          class="w-100% text-16px"
          v-model="formData.transactionPurpose"
          :props="customProps"
          :options="transactionPurposeOptions"
        />
      </el-form-item>

      <el-form-item prop="inputRemark" class="ml-36px">
        <div>{{ t('withdrawal.remarkTip') }}</div>
        <el-input
          v-model="formData.inputRemark"
          class="cus-textarea w-100% text-14px font-400"
          :placeholder="t('withdrawal.remarkPlaceholder')"
          :rows="4"
          maxlength="64"
          type="textarea"
        />
      </el-form-item>

      <!-- 交易补充材料(选填) -->
      <el-row class="mt-24px flex justify-between items-center relative text-14px">
        <div class="ml-36px max-w-48%">{{ t('withdrawal.transactionSupplementaryMaterials') }}</div>

        <el-button class="text-[#FF0064] font-400" text @click="showUploadFiled">
          {{ formData.fileInfos.length > 0 ?  t('withdrawal.uploadedTip') : t('withdrawal.uploadTipBtn') }}
        </el-button>
      </el-row>

      <!-- 提现按钮 -->
      <el-form-item>
        <el-row class="mt-48px ml-36px w-100% flex flex-row justify-end">
          <el-button class="min-w-108px pl-16px pr-16px h-32px text-14px font-400 bg-[#FF0064] b-rd-6px border-none btn-hover-scale-sm" type="primary" @click="submitForm">
            {{ t('withdrawal.confirmBtn3') }}
          </el-button>
        </el-row>
      </el-form-item>
    </el-form>

    <!-- 账户信息 -->
    <div class="pl-24px w-50%" v-if="currFiatAcct">
      <el-row v-for="(item, index) in currFiatAcctItems" :key="index" class="mt-24px flex flex-row justify-between items-start w-100%">
        <div class="text-14px text-[#6B7275] max-w-25%">{{ item.label }}</div>
        <div class="flex flex-row items-start  max-w-70% justify-end">
          <div class="text-14px text-[#222527] text-right break-all">{{ item.value || '--' }}</div>
          <!-- 复制按钮 -->
           <el-button class="text-[#FF0064] ml-8px" text @click="copyBankInfo(item.value)">
            <SvgIcon name="icon-copy-btn" class="text-16px color-#222527" />
          </el-button>
        </div>
      </el-row>

      <div @click="copyAllInfo"
          class="font-family-[PingFangSC-Regular] font-400 text-14px color-[#FF0064]! text-right cursor-pointer mt-24px">
          {{ t('common.copyAllInfo') }}
      </div>
    </div>
  </div>

  <template v-if="showUploadDrawer">
    <WithdrawalUpload v-model="showUploadDrawer" @cancel="cancelUpload" :fileInfos="formData.fileInfos" @submit="uploadSubmit" />
  </template>
</template>

<script setup lang="ts">
import { t } from '@@/i18n';
import LoadAnimation from '@@/components/LoadAnimation/LoadAnimation.vue';
import WithdrawalUpload from './WithdrawalUpload.vue';
import { InputInstance, ElMessage, FormRules } from 'element-plus';
import { calcFeeForFiatWithdrawApi, queryAcctListApproved } from '@/pages/withdrawal/apis';
import { type FiatAcctInfo, FiatWithdrawFeeItem, LtWithdrawalFormData } from '@/pages/withdrawal/apis/type';
import { safeNumber, formatNumber } from '@@/utils/math';
import { BusinessEnumType } from '@@/apis/common/type';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { FileInfo } from '@/pages/kyc/apis/type';
import { isReallyEmpty } from '@/common/utils/validate';
import { copyText } from '@/common/utils/clipboard'
import { getLocale } from '@/common/i18n/index'

const enumStore = useEnumStore();
const router = useRouter();
// 表单数据
const formData = ref({
  // 输入金额
  inputAmt: '',
  // 交易备注
  inputRemark: '',
  // 交易目的
  transactionPurpose: [] as string[],
  // 当前提现账户，只需要提交 id 即可
  currFiatAcctAddrId: '',
  // 选中的手续费计算方式
  selectFiatFee: {
    channelType: '', // 这个值是在手续费接口返回之后固定
    feeItem: {
      feeMethod: '',
      feeAmt: 0,
      estimatedArrivalTime: ''
    }
  } as {
    channelType: string
    feeItem: FiatWithdrawFeeItem
  },
  // 取现附件id列表
  fileIds: [] as string[],
  fileInfos: [] as FileInfo[]
})

const currFiatAcct = ref<FiatAcctInfo | null>(null)

// 表单验证规则
const formRules = computed(() =>  {
  const allRules: FormRules = {
      currFiatAcctAddrId: [
        { required: true, message: t('withdrawal.selectAccountPlaceholder'), trigger: ['change', 'blur'] }
      ],
      inputAmt: [
      { required: true, message: t('withdrawal.inputPlaceholder') + t('withdrawal.withdrawalAmount'), trigger: 'blur' },
      { 
        validator: (rule: any, value: string, callback: Function) => {
          if (!value) {
            callback(new Error(t('withdrawal.inputPlaceholder') + t('withdrawal.withdrawalAmount')))
          } else if ((parseFloat(value.replace(/,/g, '')) > (parseFloat(`${currFiatAcct.value?.avlBal || '0'}`)))) {
            callback(new Error(t('withdrawal.amountMaxVerifyError')))
          } else if (parseFloat(value.replace(/,/g, '')) < 100) {
            callback(new Error(t('withdrawal.amountError')))
          } else {
            callback()
          }
        }, 
        trigger: 'blur' 
      }
    ],
    'selectFiatFee.feeItem.feeMethod': [
      { required: true, message: t('withdrawal.selectFeeTypePlaceholder'), trigger: ['change', 'blur'] }
    ],
    transactionPurpose: [
      { required: true, message: t('withdrawal.selectPurposePlaceholder'), trigger: ['change', 'blur'] },
      { 
        validator: (rule: any, value: string[], callback: Function) => {
          if (!value || value.length !== 2) {
            callback(new Error(t('withdrawal.selectPurposePlaceholder')))
          } else {
            callback()
          }
        }, 
        trigger: ['change', 'blur']
      }
    ],
    // inputRemark: [
    //   { required: true, message: t('withdrawal.remarkPlaceholder'), trigger: 'blur' }
    // ]
  }

  return allRules
})


// 表单引用
const formRef = ref<any>(null)

const inputRef = ref<InputInstance | null>(null)
const selectLtWalletAddrRef = ref<HTMLInputElement | null>(null)
// 手续费计算方式
const fiatWithdrawFees = ref<FiatWithdrawFeeItem[]>([])

// 行业自定义字段映射
const customProps = {
  value: 'enumCode', // 指定 value 字段为 id
  label: 'enumDescCn', // 指定 label 字段为 name
  children: 'children', // 指定 children 字段为 subs
};
// 交易目的筛选项目
const transactionPurposeOptions = enumStore.getEnumList(BusinessEnumType.TRANSACTION_PURPOSE);

// 可用提现账户数组
const fiatAcctInfoList = ref<FiatAcctInfo[]>([])
const feeAmt = ref('')
const loading = ref(false)
const showUploadDrawer = ref(false)

const currFiatAcctItems = computed(() => [
  { label: t('withdrawal.accountName'), value: currFiatAcct.value?.payeeName || '' },
  { label: t('withdrawal.paymentMethod'), value: currFiatAcct.value?.payMethod || '' },
  { label: t('withdrawal.accountNumber'), value: currFiatAcct.value?.acctNo || '' },
  { label: t('withdrawal.swiftBic'), value: currFiatAcct.value?.swiftCode || '' },
  { label: t('withdrawal.bankName'), value: currFiatAcct.value?.acctName || '' },
  { label: t('withdrawal.bankAddress'), value: currFiatAcct.value?.bankAddr || '' }
]);

// 根据当前语言环境显示不同的时间字段
const estimatedArrivalTimeString = computed(() => {
  let curI18 = getLocale()
  switch (curI18) {
    case 'zh-CN':
      return formData.value.selectFiatFee.feeItem?.estimatedArrivalTimeCN
    case 'zh-HK':
      return formData.value.selectFiatFee.feeItem?.estimatedArrivalTimeHK
    case 'en-US':
      return formData.value.selectFiatFee.feeItem?.estimatedArrivalTimeUS
    default:
      return formData.value.selectFiatFee.feeItem?.estimatedArrivalTime
  }
})

// 查询已审核法币账户信息列表
const queryAcctListApprovedResp = async () => {
  try {
    const res = await queryAcctListApproved()
    fiatAcctInfoList.value = res.data.fiatAcctInfoList || []
    if (fiatAcctInfoList.value.length > 0) {
      currFiatAcct.value = fiatAcctInfoList.value[0]
      formData.value.currFiatAcctAddrId = fiatAcctInfoList.value[0].id.toString()
    }
  } catch (error) {
    console.error(error)
  }
}

const amtValueFormatted = (value: string | number) => {
  const amount = safeNumber(value);
  return formatNumber(amount, 2);
}

// 点击最大值
const inputMaxAmt = () => {
  if (currFiatAcct.value?.avlBal) {
    formData.value.inputAmt = amtValueFormatted(currFiatAcct.value?.avlBal || 0)
    calcFeeApiResp()
  }
}

// 计算手续费
const calcFeeApiResp = async () => {
  // 校验账户和金额
  formRef.value?.validateField(['currFiatAcctAddrId', 'inputAmt'], (validObj: any) => {
    if (validObj) {
      const value = formData.value.inputAmt.replace(/,/g, '') // 移除千位分隔符
      loading.value = true
      calcFeeForFiatWithdrawApi({
        transAmt: value,
        acctInfoId: currFiatAcct.value?.id || ''
      }).then((res) => {
        fiatWithdrawFees.value = res.data.fiatWithdrawFees || []
        formData.value.selectFiatFee.channelType = res.data.channelType || ''
        if (fiatWithdrawFees.value.length > 0) {
          formData.value.selectFiatFee.feeItem = {
            ...fiatWithdrawFees.value[0]
          }
          feeAmt.value = amtValueFormatted(formData.value.selectFiatFee.feeItem.feeAmt)
        }
      }).finally(() => {
        loading.value = false
      })
    }
  })
}

const selectFiatFeeItemChange = (item: FiatWithdrawFeeItem) => {
  formData.value.selectFiatFee.feeItem = {
    ...item
  }
  feeAmt.value = amtValueFormatted(formData.value.selectFiatFee.feeItem?.feeAmt || 0)
}

const showUploadFiled = () => {
  showUploadDrawer.value = true
}
const cancelUpload = () => {
  showUploadDrawer.value = false
}

const uploadSubmit = (payload: FileInfo[]) => {
  console.log(">>>>payload<>>>>", payload)
  formData.value.fileIds = payload.map(item => item.fileSeqId ? item.fileSeqId : '').filter(Boolean)
  formData.value.fileInfos = payload
  showUploadDrawer.value = false
}

// 跳转到设置页面
const gotoBankListClick = () => {
  router.push({
    name: 'Setting',
    query: {
      activeItem: 'fiat-account'
    }
  })
}

// 切换银行账号
const selectLtWalletAddr = (item: FiatAcctInfo) => {
  currFiatAcct.value = item
  formData.value.currFiatAcctAddrId = item.id.toString()
  selectLtWalletAddrRef.value?.blur()
}

// 监听下拉选择变化
watch(() => formData.value.currFiatAcctAddrId, (newVal) => {
  if (newVal) {
    const selectedAcct = fiatAcctInfoList.value.find(item => item.id.toString() === newVal)
    if (selectedAcct) {
      currFiatAcct.value = selectedAcct
      // 重新计算手续费
      if (formData.value.inputAmt) {
        calcFeeApiResp()
      }
    }
  }
})

const validateForm = async () => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    return false
  }
}

const accNoMask = (value?: string) => {
  if (!value) return value || ''
  if (value.length < 8) {
    // 当长度小于 8 的时候0到倒数第四个为星号
    return '****' + value.slice(value.length - 4, value.length)
  }
  return value.replace(/(\d{3})\d*(\d{4})/, '$1****$2')
}

const handleInput = (val: string) => {
  let value = val.replace(/[^\d.]/g, '').replace(/(\..*)\./g, '$1').replace(/^(\d+\.?\d{0,2}).*/, '$1')
  formData.value.inputAmt = value
}

const handleInputBlur = () => {
  if (formData.value.inputAmt === '') return

  formData.value.inputAmt = amtValueFormatted(formData.value.inputAmt)
  calcFeeApiResp()
}

onMounted(() => {
  queryAcctListApprovedResp()
})

// 复制信息
const copyAllInfo = () => {
  // 将信息转成 key value
  const items = currFiatAcctItems.value.map(item => ({ label: item.label, value: item.value }))
  const text = items.map(item => `${item.label}: ${item.value}`).join('\n');
  copyBankInfo(text);
}
const copyBankInfo = (text?: string) => {
  if (!text) return
  
  copyText(text)
}

const submitForm = async () =>  {
  if (!(await validateForm())) {
    return null
  }
  if(!currFiatAcct.value?.id){
    ElMessage.warning(t('withdrawal.noAvailableAccount'))
    return null
  }
  const value = formData.value.inputAmt.replace(/,/g, '') // 移除千位分隔符

  emit('submit', {
    acctInfoId: currFiatAcct.value?.id,
    transAmt: value,
    channelType: formData.value.selectFiatFee.channelType,
    feeMethod: formData.value.selectFiatFee.feeItem.feeMethod,
    transactionPurposeLevel1: formData.value.transactionPurpose[0],
    transactionPurposeLevel2: formData.value.transactionPurpose[1],
    transactionRemarks: formData.value.inputRemark,
    fileIds: formData.value.fileIds
  })
}

// 定义子组件暴露给父组件的接口
const emit = defineEmits<{
  (e: 'submit', payload: LtWithdrawalFormData): void
  (e: 'cancel'): void
}>()
</script>

<style lang="scss" scoped>
:deep(.el-select) {
  --el-select-input-focus-border-color: #E5E6EB;
}
:deep(.el-cascader .el-input__wrapper) {
  height: 40px;
  font-size: 16px
}
.select-account {
  :deep(.el-select__wrapper) {
    height: 60px;

    &:hover {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
    
    &.is-focus {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }

    &.is-focused {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
  }
}
:deep(.el-button, .el-button.is-round) {
  padding: 0;
}
.cus-input {
  border: 1px solid #E5E6EB;
  border-radius: 6px;
  height: 40px;
  padding: 0px;
  padding-right: 12px;
  :deep(.el-input__wrapper) {
    box-shadow: none;
    background-color: #ffffff;
    
    &:hover {
      border-color: none;
      box-shadow: none;
    }
    
    &.is-focus {
      border-color: none;
      box-shadow: none;
    }

    &.is-focused {
      border-color: none;
      box-shadow: none;
    }
  }
}
.cus-select {
  height: 40px;
  :deep(.el-select__wrapper) {
    height: 40px;

    &:hover {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
    
    &.is-focus {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }

    &.is-focused {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
  }
}
.cus-textarea {
  :deep(.el-textarea__inner) {
    border-radius: 6px;
    background-color: #ffffff;
    &:hover {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
    
    &.is-focus {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }

    &.is-focused {
      border-color: #E5E6EB;
      box-shadow: 0 0 0 1px #E5E6EB !important;
    }
  }
}
.dashed-line {
  width: 1px;
  height: 95px;
  background-image: linear-gradient(to bottom, #E5E6EB 0%, #E5E6EB 80%, transparent 50%);
  background-size: 100% 10px;
  background-repeat: repeat-y
}
.icon-currency-us {
  width: 18px;
  height: 18px;
  background-image: url('/src/common/assets/icons/icon-currency-us.svg');
  background-size: cover;
}
</style>