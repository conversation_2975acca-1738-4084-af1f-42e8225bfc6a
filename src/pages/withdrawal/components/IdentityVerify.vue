<template>
  <el-drawer 
    v-model="showDrawer" 
    size="400px" 
    style="--el-drawer-padding-primary: 0px"
    header-class="custom-drawer-header"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :close-on-press-escape="false"
  >
    <template #header>
      <div class="h-60px border-b-1 mb-0 flex-row flex-items-center text-18px font-500 color-[#222527] pt-18px pb-18px pl-24px">
        <span>{{ t('withdrawal.transactionSupplementaryMaterials') }}</span>
      </div>
    </template>
    <template #default>
      <div class="p-24px">
        <el-form ref="identityVerifyFormRef" :model="formData" :rules="identityVerifyFormRules" hide-required-asterisk label-position="top" class="">
          <el-form-item :label="t('withdrawal.emil')">
            <div class="w-100%">
              <el-input
                disabled
                :value="userStore.email"
                :placeholder="t('withdrawal.emailPlaceholder')"
                type="text"
              />
            </div>
          </el-form-item>
          <el-form-item prop="verifyCode" :label="t('withdrawal.emilCode')" :error="customEmailErrorMesg">
            <div class="w-100%">
              <el-input
                v-model.trim="formData.verifyCode"
                :placeholder="t('withdrawal.verifyCodePlaceholder')"
                type="text"
                maxlength="6"
                ref="verifyCodeInputRef"
              >
                <template #suffix>
                  <el-button :loading="countdownLoading" text class="sendcode-btn pr-12px" @click.prevent="sendEmailRequest" :disabled="countdown > 0">
                    {{ sendBtnMesg }}
                  </el-button>
                </template>
              </el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div 
        style="border-top: 1px solid #E5E6EB;"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
          <el-button @click="cancelBtnClick" class="cancel-btn w-68px h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px">{{ t('withdrawal.cancelBtn') }}</el-button>
          <el-button @click="verifyEmailCodeResp" color="white" class="w-68px h-32px bg-[#030814] rounded-6px" type="primary" plain>{{ t('withdrawal.confirmBtn2') }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { t } from '@@/i18n';
import type { FormRules, FormInstance, FormItemInstance, InputInstance } from "element-plus"
import { useUserStore } from "@/pinia/stores/user"
import { sendEmailForIdentityVerifyApi, verifyEmailForIdentityVerifyApi } from '../apis'
import { FeeEnumType } from '@/pages/exchange/apis/type';

const userStore = useUserStore()

const props = withDefaults(defineProps<{
  modelValue: boolean
  businessType: FeeEnumType
}>(), {
  modelValue: true
});

const identityVerifyFormRef = ref<FormInstance | null>(null)
const verifyCodeInputRef = ref<InputInstance | null>(null)
const countdownLoading = ref(false)
const countdown = ref(0)
const customEmailErrorMesg = ref('')

const formData = reactive({
  verifyCode: "",
  sendSeqId: "",
})

const sendBtnMesg = computed(() => {
  if (countdownLoading.value) return t('withdrawal.sendingCode');

  return countdown.value > 0 ? `${t('withdrawal.resendCode')} (${countdown.value}s)` : t('withdrawal.sendCode');
});

const identityVerifyFormRules: FormRules = {
  verifyCode: [
    { required: true, message: t('withdrawal.verifyCodePlaceholder'), trigger: ["blur"] },
    { validator: (rule: any, value: any, callback: any) => {
        if (formData.sendSeqId === "") {
          return callback(new Error(t('withdrawal.verifyCodeEmptyError')))
        } 
        if (value === "") {
          // 当没有输入内容的时候检查是否有自定义的错误信息
          if (customEmailErrorMesg.value) {
            callback(new Error(customEmailErrorMesg.value))
          }else{
            callback(new Error(t('withdrawal.verifyCodeError')))
          }
        } else {
          callback()
        }
      },
      trigger: "blur"
    }
  ],
}

/** 发送验证码 */
const sendEmailRequest = () => {
  countdownLoading.value = true
  sendEmailForIdentityVerifyApi(props.businessType).then(({data}) => {
    formData.sendSeqId = data.sendSeqId
    countdown.value = 60;
    // 启动倒计时
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
        countdown.value = 0;
      }
    }, 1000)
  }).catch(error => {
    customEmailErrorMesg.value = error.message;
  }).finally(() => {
    countdownLoading.value = false
    verifyCodeInputRef.value?.focus();
  })
}

const emit = defineEmits<{
  (e: 'submit', payload: string): void
  (e: 'cancel'): void
  (e: 'update:modelValue', value: boolean): void
}>()

// 使用计算属性实现双向绑定
const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

/** 验证邮箱验证码 */
const verifyEmailCodeResp  = () => {
  identityVerifyFormRef.value?.validateField(["verifyCode"], async (valid: boolean) => {
    if (!valid) {
      return
    }
    customEmailErrorMesg.value = "";
    try {
      await verifyEmailForIdentityVerifyApi({
        sendSeqId: formData.sendSeqId,
        verifyCode: formData.verifyCode
      })
      emit('submit', formData.sendSeqId)
    } catch (error: any) {
      customEmailErrorMesg.value = error.message;
    }
  });
}

const cancelBtnClick = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.el-button {
  width: 100%;
  margin-top: 10px;
  background: #030814;
  border-radius: 6px;
  border:none;
}
.cancel-btn {
  background-color: #FFFFFF !important;
  border: 1px solid #E5E6EB !important;
  color: #222527 !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
}
:deep(.el-button.text-btn) {
  background: transparent;
  margin-left: 0;
  font-size: 14px;
  color: #222527;
  margin-top: 24px;
}
:deep(.el-button.sendcode-btn) {
  background: transparent;
  font-size: 14px;
  color: #FF0064;
  margin-top: 0;
  padding: 0;
}
:deep(.el-button.is-text:hover) {
  background: transparent;
}
:deep(.el-input) {
  --el-input-focus-border-color: #E5E6EB;
  border: #E5E6EB 1px solid;
  border-radius: 6px;
}
:deep(.el-input__inner) {
  height: 40px;
  padding: 0px 12px;
}
:deep(.el-input__wrapper) {
  border-radius: 6px;
  background-color: transparent;
}
:deep(.el-form-item.is-error .el-form-item__content .el-input .el-input__wrapper) {
  background-color: #FFF2EE;
} 

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }
  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
</style>
