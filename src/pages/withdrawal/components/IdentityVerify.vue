<template>
  <el-drawer 
    v-model="showDrawer" 
    size="400px" 
    style="--el-drawer-padding-primary: 0px"
    header-class="custom-drawer-header"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :close-on-press-escape="false"
  >
    <template #header>
      <div class="h-60px border-b-1 mb-0 flex-row flex-items-center text-18px font-500 color-[#222527] pt-18px pb-18px pl-24px">
        <span>{{ t('withdrawal.identityAuthentication') }}</span>
      </div>
    </template>
    <template #default>
      <div class="p-24px">
        <IdValidator ref="idValidatorRef" :businessType="props.businessType" />
      </div>
      <div 
        style="border-top: 1px solid #E5E6EB;"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
          <el-button @click="cancelBtnClick" class="cancel-btn w-68px h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px">{{ t('withdrawal.cancelBtn') }}</el-button>
          <el-button @click="verifyEmailCodeResp" color="white" class="w-68px h-32px bg-[#030814] rounded-6px" type="primary" plain>{{ t('withdrawal.confirmBtn2') }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { t } from '@@/i18n';
import { useUserStore } from "@/pinia/stores/user"
import { FeeEnumType } from '@/pages/exchange/apis/type';
import IdValidator from '@/common/components/IdValidator/index.vue';

const userStore = useUserStore()

const props = withDefaults(defineProps<{
  modelValue: boolean
  businessType: FeeEnumType
}>(), {
  modelValue: true
});

const idValidatorRef = ref<InstanceType<typeof IdValidator> | null>(null)

const emit = defineEmits<{
  (e: 'submit', payload: {tfaSeqId: string; sendSeqId: string}): void
  (e: 'cancel'): void
  (e: 'update:modelValue', value: boolean): void
}>()

// 使用计算属性实现双向绑定
const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

/** 验证邮箱验证码 */
const verifyEmailCodeResp  = () => {
  idValidatorRef.value?.verifyEmailCodeResp().then((res: {tfaSeqId: string; sendSeqId: string}) => {
    emit('submit', res)
  }).catch((error: any) => {
    console.error('验证邮箱验证码失败:', error);
  })
}

const cancelBtnClick = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.el-button {
  width: 100%;
  margin-top: 10px;
  background: #030814;
  border-radius: 6px;
  border:none;
}
.cancel-btn {
  background-color: #FFFFFF !important;
  border: 1px solid #E5E6EB !important;
  color: #222527 !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }
  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
</style>
