<template>
  <div class="error">
    <div class="error-svg">
      <slot />
    </div>
    <router-link to="/">
      <el-button type="primary" class="goback-btn">
        {{ t('router.gotoHome') }}
      </el-button>
    </router-link>
  </div>
</template>

<script lang="ts" setup>
import { t } from '@@/i18n'
</script>

<style lang="scss" scoped>
.error {
  background-color: white;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.goback-btn {
  background-color: #030814;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 20px;
}
</style>
