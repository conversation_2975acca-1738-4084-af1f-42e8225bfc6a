import type * as Setting from "./type"
import { request } from "@/http/axios"

/** 查询法币银行账户 */
export function queryFiatBankAccountApi(data: Setting.QueryFiatBankAccountRequestData ) {
  return request<Setting.QueryFiatBankAccountResponseData>({
    url: "/fiat/acct/query",
    method: "post",
    data
  })
}

/** 修改法币银行账户 */
export function modifyFiatBankAccountApi(data: Setting.ModifyFiatBankAccountRequestData) {
  return request<Setting.ModifyFiatBankAccountResponseData>({
    url: "/fiat/acct/modify",
    method: "post",
    data
  })
}

/** 查询法币银行账户列表 */
export function queryFiatBankAccountListApi(data: Setting.QueryFiatBankAccountListRequestData) {
  return request<Setting.QueryFiatBankAccountListResponseData>({
    url: "/fiat/acct/list",
    method: "post",
    data
  })
}

/** 删除法币银行账户 */
export function deleteFiatBankAccountApi(data?: Setting.DeleteFiatBankAccountRequestData) {
  return request<void>({
    url: "/fiat/acct/delete",
    method: "post",
    data
  })
}

/** 绑定法币银行账户 */
export function bindFiatBankAccountApi(data: Setting.BindFiatBankAccountRequestData) {
  return request<Setting.BindFiatBankAccountResponseData>({
    url: "/fiat/acct/bind",
    method: "post",
    data
  })
}


  /** 查询数币地址 */
export function queryDigitalAddressApi(data: Setting.QueryAddressRequestData) {
  return request<Setting.QueryAddressResponseData>({
    url: "/dc/addr/query",
    method: "post",
    data
  })
}

/** 修改数币地址 */
export function modifyDigitalAddressApi(data: Setting.ModifyDigitalAddressRequestData) {
  return request<Setting.ModifyDigitalAddressResponseData>({
    url: "/dc/addr/modify",
    method: "post",
    data
  })
}

/** 查询数币地址列表 */
export function queryDigitalAddressListApi(data: Setting.QueryAddressListRequestData) {
  return request<Setting.QueryAddressListResponseData>({
    url: "/dc/addr/list",
    method: "post",
    data
  })
}

/** 删除数币地址 */
export function deleteDigitalAddressApi(data: Setting.DeleteDigitalAddressRequestData) {
  return request<Setting.DeleteDigitalAddressResponseData>({
    url: "/dc/addr/delete",
    method: "post",
    data
  })
}

/** 添加数币地址 */
export function addDigitalAddressApi(data: Setting.AddDigitalAddressRequestData) {
  return request<Setting.AddDigitalAddressResponseData>({
    url: "/dc/addr/add",
    method: "post",
    data
  })
}

// ==================== 客户信息接口 ====================

/** 查询客户信息 */
export function queryCustomerApi(data: Setting.QueryCustomerRequestData) {
  return request<Setting.QueryCustomerResponseData>({
    url: "/customer/query",
    method: "post",
    data
  })
}

/** 修改昵称 */
export function modifyNicknameApi(data: Setting.ModifyNicknameRequestData) {
  return request<void>({
    url: "/customer/nickname/modify",
    method: "post",
    data
  })
}

/** 发送邮箱验证码 */
export function sendEmailCodeApi(data: Setting.SendEmailCodeRequestData) {
  return request<Setting.SendEmailCodeResponseData>({
    url: "/mail/sendToSpecifyEmail",
    method: "post",
    data
  })
}

/** 邮箱验证 */
export function emailVerifyApi(data: Setting.EmailVerifyRequestData) {
  return request<void>({
    url: "/mail/verify",
    method: "post",
    data
  })
}

/** 修改邮箱 */
export function modifyEmailApi(data: Setting.ModifyEmailRequestData) {
  return request<void>({
    url: "/settings/changeEmail",
    method: "post",
    data
  })
}

/** 修改密码 */
export function changePasswordApi(data: Setting.ChangePasswordRequestData) {
  return request<void>({
    url: "/settings/changePwd",
    method: "post",
    data
  })
}

/** 创建绑定2FA先验证密码，该接口返回绑定所需url */
export function bind2FAApi(password: string) {
  return request<Setting.Bind2FAResponseData>({
    url: "/twoFactorAuth/create",
    method: "post",
    data: { password }
  })
}

/** 绑定2FA */
export function bind2FAConfirmApi(sysSeqId: string, code: string) {
  return request<void>({
    url: "/twoFactorAuth/bind",
    method: "post",
    data: { sysSeqId, code }
  })
}

/** 解绑2FA */
export function unbind2FAApi(sysSeqId: string, password: string) {
  return request<void>({
    url: "/twoFactorAuth/unbind",
    method: "post",
    data: { sysSeqId, password }
  })
}

// ==================== 企业信息接口 ====================

/** 查询企业信息 */
export function queryCorpInfoApi(data: Setting.QueryCorpInfoRequestData) {
  return request<Setting.QueryCorpInfoResponseData>({
    url: "/corp/query",
    method: "post",
    data
  })
}

// ==================== 协议接口 ====================

/** 查询协议列表 */
export function queryAgreementListApi(data?: Setting.QueryAgreementListRequestData) {
  return request<Setting.QueryAgreementListResponseData>({
    url: "/agreement/query",
    method: "post",
    data
  })
}