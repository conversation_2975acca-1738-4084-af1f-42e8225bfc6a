export type QueryFiatBankAccountRequestData = ApiRequestData<{
  /** 系统流水号 */
  sysSeqId: string;
}>;

export type QueryFiatBankAccountResponseData = ApiResponseData<{
  merCustId: string; // 商户号
  payeeName: string; // 收款人名称
  payeeAddr: string; // 收款人地址
  payeeCtry: string; // 收款人国家
  acctNickName: string; // 账户别名
  payeeAcctType: string; // 账户类型
  acctNo: string; // 账户号
  ccy: string; // 币种
  payMethod: string; // 支付方式
  acctName: string; // 账户名称
  bankCtry: string; // 银行国家
  bankAddr: string; // 银行地址
  swiftCode: string; // 银行代码
  localClearingCode: string; // 本地清算代码
  status: string; // 状态
}>;

export type ModifyFiatBankAccountRequestData = ApiRequestData<{
  sysSeqId: string; // 系统流水号
  merCustId: string; // 商户号
  payeeName: string;
  payeeAddr: string;
  payeeCity: string;
  payeeApartment: string;
  payeeCtry: string;
  acctNickName: string;
  payeeAcctType: string;
  payeeState: string;
  payeePostalCode: string;
  acctNo: string;
  ccy: string;
  payMethod: string;
  acctName: string;
  bankCtry: string;
  bankAddr: string;
  swiftCode: string;
  localClearingCode: string;
  useQualificationInfo: string;
  sendSeqId: string;
  tfaSeqId: string;
}>;

export type ModifyFiatBankAccountResponseData = ApiResponseData<{
  /** 系统流水号 */
  sysSeqId: string;
  status: string;
}>;

export type QueryFiatBankAccountListRequestData = ApiRequestData<{
  pageNum: number;
  pageSize: number;
  ccy: string;
  bankCtry: string;
  status: string;
}>;
export type FiatAcctInfo = {
  payeeName: string;
  payeeAddr: string;
  payeeCity: string;
  payeeApartment: string;
  payeeCtry: string;
  acctNickName: string;
  payeeAcctType: string;
  payeeState: string;
  payeePostalCode: string;
  acctNo: string;
  ccy: string;
  payMethod: string;
  acctName: string;
  bankCtry: string;
  bankAddr: string;
  swiftCode: string;
  localClearingCode: string;
  useQualificationInfo: string;
  sysSeqId: string;
  merCustId: string;
  status: string;
};

export type QueryFiatBankAccountListResponseData = ApiResponseData<{
  fiatAcctInfoList: FiatAcctInfo[];
  totalCount: number;
  pages: number;
  pageNum: number;
  pageSize: number;
}>;

export type DeleteFiatBankAccountRequestData = ApiRequestData<{
  sysSeqId: string;
  sendSeqId: string;
  tfaSeqId: string;
}>;

export type DeleteFiatBankAccountResponseData = ApiResponseData<{}>;

export type BindFiatBankAccountRequestData = ApiRequestData<{
  payeeName: string;
  payeeAddr: string;
  payeeCity: string;
  payeeApartment: string;
  payeeCtry: string;
  acctNickName: string;
  payeeAcctType: string;
  payeeState: string;
  payeePostalCode: string;
  acctNo: string;
  ccy: string;
  payMethod: string;
  acctName: string;
  bankCtry: string;
  bankAddr: string;
  swiftCode: string;
  localClearingCode: string;
  useQualificationInfo: string;
  sendSeqId: string;
  tfaSeqId: string;
}>;

export type BindFiatBankAccountResponseData = ApiResponseData<{
  status: string;
  sysSeqId: string;
}>;

export type QueryAddressRequestData = ApiRequestData<{
  sysSeqId: string;
}>;

export type QueryAddressResponseData = ApiResponseData<{
  walletName: string;
  ccy: string;
  cryptoNet: string;
  cryptoAddr: string;
  status: string;
  sysSeqId: string;
}>;

export type ModifyDigitalAddressRequestData = ApiRequestData<{
  sysSeqId: string;
  walletName: string;
  ccy: string;
  cryptoNet: string;
  cryptoAddr: string;
  sendSeqId: string;
  tfaSeqId: string;
}>;

export type ModifyDigitalAddressResponseData = ApiResponseData<{
  status: string;
  sysSeqId: string;
}>;

export type QueryAddressListRequestData = ApiRequestData<{
  pageNum: number;
  pageSize: number;
  status: string;
  ccy: string;
}>;

export type DigitalAddress = {
  walletName: string;
  ccy: string;
  cryptoNet: string;
  cryptoAddr: string;
  status: string;
  sysSeqId: string;
};

export type QueryAddressListResponseData = ApiResponseData<{
  dcWalletAddrList: DigitalAddress[];
  totalCount: number;
  pages: number;
  pageNum: number;
  pageSize: number;
}>;

export type DeleteDigitalAddressRequestData = ApiRequestData<{
  sysSeqId: string;
  sendSeqId: string;
  tfaSeqId: string;
}>;

export type DeleteDigitalAddressResponseData = ApiResponseData<{}>;

export type AddDigitalAddressRequestData = ApiRequestData<{
  walletName: string;
  ccy: string;
  cryptoNet: string;
  cryptoAddr: string;
  sendSeqId: string;
  tfaSeqId: string;
}>;

export type AddDigitalAddressResponseData = ApiResponseData<{
  status: string;
  sysSeqId: string;
}>;

// ==================== 客户信息接口类型定义 ====================

/** 查询客户信息请求数据 */
export type QueryCustomerRequestData = ApiRequestData<{}>;

/** 客户信息响应数据 */
export type QueryCustomerResponseData = ApiResponseData<{
  /** 用户ID */
  userId: string;
  /** 昵称 */
  nickName: string;
  /** 用户邮箱 */
  email: string;
  /** 状态：1-正常，0-禁用 */
  status: number;
  /** 最后登录时间 */
  lastLoginTime: string;
  /** 注册IP */
  registerIp: string;
  /** 登录IP */
  loginIp: string;
  /** 2FA状态 */
  twoFactorAuthStatus?: string;
  /** 2FA流水号 */
  twoFactorAuthSysSeqId?: string;
}>;

/** 修改昵称请求数据 */
export type ModifyNicknameRequestData = ApiRequestData<{
  /** 用户昵称 */
  nickName: string;
}>;

/** 发送邮箱验证码请求数据 */
export type SendEmailCodeRequestData = ApiRequestData<{
  /** 邮箱 */
  newEmail: string;
  /** 业务类型 */
  businessType: 'CHANGE_EMAIL';
}>;

/** 发送邮箱验证码响应数据 */
export type SendEmailCodeResponseData = ApiResponseData<{
  /** 发送序列号 */
  sendSeqId: string;
}>;

/** 邮箱验证请求数据 */
export type EmailVerifyRequestData = ApiRequestData<{
  /** 发送流水号 */
  sendSeqId: string;
  /** 验证码 */
  verifyCode: string;
}>;

/** 修改邮箱请求数据 */
export type ModifyEmailRequestData = ApiRequestData<{
  /** 发送流水号 */
  sendSeqId: string;
}>;

/** 绑定2FA响应数据 */
export type Bind2FAData = {
  /** 流水号 */
  sysSeqId: string;
  /** 二维码地址 */
  url: string;
};
export type Bind2FAResponseData = ApiResponseData<Bind2FAData>;

// ==================== 密码管理接口类型定义 ====================

/** 修改密码请求数据 */
export type ChangePasswordRequestData = ApiRequestData<{
  /** 当前密码 */
  currentPwd: string;
  /** 新密码 */
  newPwd: string;
  /** 二次确认密码 */
  secondPwd: string;
}>;

// ==================== 企业信息接口类型定义 ====================

/** 查询企业信息请求数据 */
export type QueryCorpInfoRequestData = ApiRequestData<{}>;

/** 企业信息响应数据 */
export type QueryCorpInfoResponseData = ApiResponseData<{
  /** 用户ID */
  userId: string;
  /** 英文名称 */
  englishName: string;
  /** 企业类型，private-私人有限公司，listed-上市有限公司 */
  corporationType: string;
  /** 注册证书编号 */
  registCertificateNumber: string;
  /** 经营行业信息大类 */
  industry: string;
  /** 经营行业信息小类 */
  subIndustry: string;
  /** 经营网址 */
  website: string;
  /** 企业员工人数 */
  staffNumber: string;
  /** 营业地址 */
  businessAddress: string;
  /** 公司注册地址 */
  registerAddress: string;
  /** 注册省份 */
  registerProvince: string;
  /** 注册城市 */
  registerCity: string;
  /** 注册区县 */
  registerApartment: string;
  /** 注册邮政编码 */
  registerPostalCode: string;
  /** 营业省份 */
  businessProv: string;
  /** 营业城市 */
  businessCity: string;
  /** 营业区县 */
  businessApartment: string;
  /** 营业邮政编码 */
  businessPostalCode: string;
  /** 商户客户ID */
  merCustId: string;
  /** 注册国家 */
  registerCode: string;
  /** 主营地区 */
  mainBusinessAddress: string;
}>;

// ==================== 协议接口类型定义 ====================

/** 协议信息 */
export type AgreementVO = {
  /** 协议名称 */
  agreementName: string;
  /** 协议url */
  agreementUrl: string;
  /** 协议接受时间 */
  acceptanceAgreementTime: string;
};

/** 查询协议列表请求数据 */
export type QueryAgreementListRequestData = ApiRequestData<{}>;

/** 查询协议列表响应数据 */
export type QueryAgreementListResponseData = ApiResponseData<{
  /** 总数 */
  total: number;
  /** 协议信息 */
  agreementVOs: AgreementVO[];
}>;
