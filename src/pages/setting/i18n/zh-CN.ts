export default {
  setting: {
    personal: {
      modify: '修改',
      basicInfo: '基础信息',
      securityInfo: '安全信息',
      permissions: '权限',
      admin: '管理员',
      emailAddress: '邮箱地址',
      password: '密码',
      passwordDesc: '设置唯一密码以保护您的账户，非必要情况请勿更改',
      nicknameNotSet: '未设置昵称',
      emailNotSet: '未设置邮箱',
      fetchUserInfoFailed: '获取用户信息失败，请刷新页面重试',
      usernameCannotBeEmpty: '用户名不能为空',
      newUsernameSameAsCurrent: '新用户名与当前用户名相同',
      updateUsernameSuccess: '用户名修改成功',
      updateUsernameFailed: '用户名修改失败，请重试',
      twoFactorAuthentication: '双重认证',
      twoFactorAuthenticationDesc: '双重认证(2FA)增加一层安全性，需要额外的身份验证方法才能登录',
      unbind: '解绑',
      bind: '绑定',
      addAuthenticator: '添加第三方验证器',
      verifyTitle: '身份验证',
      thirdPartyAuthenticator: '第三方验证器',
      verifyPasswordDesc: '输入您的密码进行确认。设置后，我们将帮助您设置身份验证器。',
      verifyPasswordDesc2: '输入您的密码进行确认，注意解绑后您账户的安全性将下降！',
    },
    editPersonal: {
      title: '修改用户名',
      description: '您可以自由修改用户名，展示您喜欢的称呼(至多32个字符)，让账户更贴近您的风格。',
      currentUsername: '您当前的用户名',
      newUsername: '新用户名',
      placeholder: '请输入',
      cancel: '取消',
      confirm: '确认',
      newUsernameRequired: '请输入新用户名',
      newUsernameLength: '用户名长度应在1-32个字符之间',
    },
    setValidator: {
      title: '设置您的双重验证器',
      validatorDesc:
        '使用谷歌认证或微软验证等支持的验证器应用程序扫描二维码。扫描二维码后，您会收到一个六位数的确认码，请在下面输入。如果您以前设置过 2FA，请重新扫描当前页面上的二维码，因为以前的二维码已经过期。',
      step1: '1.打开手机上的身份验证器',
      step2: '2.点击添加“+”图标，扫描二维码',
      step3: '3.输入生成的确认码',
      viewSupportedApps: '查看支持的验证器应用',
      placeholder: '请输入确认码',
      passwordVerifyFailed: '密码验证失败，请重试',
      bindSuccess: '验证器绑定成功',
      unbindSuccess: '验证器解绑成功',
    },
    editEmail: {
      title: '修改邮箱地址',
      description: '您当前的邮箱是 {email} 请告诉我们您想使用的新邮箱地址并进行验证',
      currentEmail: '您当前的邮箱地址',
      newEmail: '新邮箱',
      placeholder: '请输入',
      verificationCode: '邮箱验证码',
      codePlaceholder: '请输入',
      sendCode: '发送验证码',
      cancel: '取消',
      confirm: '确认',
      newEmailRequired: '请输入新邮箱地址',
      emailFormatError: '请输入正确的邮箱格式',
      verificationCodeRequired: '请输入验证码',
      verificationCodeLength: '验证码必须为6位数字',
      sameEmailError: '新邮箱不能与当前邮箱相同',
      pleaseGetCode: '请先获取验证码',
      updateSuccess: '邮箱修改成功，即将退出登录',
      updateFailed: '邮箱修改失败，请重试',
      sendCodeSuccess: '验证码发送成功',
      sendCodeFailed: '验证码发送失败，请重试',
      newEmailSameAsCurrent: '新邮箱与当前邮箱相同',
      updateEmailSuccess: '邮箱修改成功',
      updateEmailFailed: '邮箱修改失败，请重试',
    },
    editPassword: {
      title: '修改密码',
      description: '为保障您的账户安全，请设置一个强密码',
      currentPassword: '请输入账户当前的密码',
      newPassword: '请输入您的新密码并二次确认',
      confirmPassword: '确认新密码',
      placeholder: '请输入',
      cancel: '取消',
      confirm: '确认',
      currentPasswordRequired: '请输入当前密码',
      newPasswordRequired: '请输入新密码',
      confirmPasswordRequired: '请输入确认密码',
      passwordLengthError: '密码长度应在8-32个字符之间',
      passwordFormatError: '密码必须包含大写字母和数字',
      passwordSpecialCharError: '密码必须包含特殊字符',
      passwordNotMatchError: '两次输入的密码不一致',
      samePasswordError: '新密码不能与当前密码相同',
      updatePasswordSuccess: '密码修改成功，即将退出登录',
      updatePasswordFailed: '密码修改失败，请重试',
      passwordRequirements: '密码必须包含',
      lengthRequirement: '8-32个字符',
      upperCaseAndDigitRequirement: '大写字母和数字',
      specialCharRequirement: '特殊字符',
      differentFromCurrentRequirement: '与当前密码不同',
    },
    address: {
      title: '链上地址',
      tableEmpty: '暂无数据',
      placeholder: '请选择',
      currency: '币种',
      auditStatus: '审核状态',
      reset: '重置',
      search: '查询',
      totalItems: '共 {total} 条',
      addAddress: '添加地址',
      table: {
        index: '序号',
        nickname: '昵称',
        currency: '币种',
        chain: '链',
        address: '地址',
        status: '状态',
        actions: '操作',
      },
      status: {
        approved: '审核通过',
        rejected: '审核拒绝',
        auditing: '审核中',
      },
      dialog: {
        title: '提示',
        deleteConfirm: '请确认是否删除"{displayName}"？',
      },
      message: {
        deleteSuccess: '删除成功',
        addSuccess: '添加成功',
        modifySuccess: '修改成功',
      },
      pagination: {
        total: '共 {total} 条',
        sizes: '条/页',
        prev: '上一页',
        next: '下一页',
        jumper: '跳至',
        page: '页',
      },
    },
    editAddress: {
      title: {
        create: '新增地址',
        edit: '编辑地址',
        view: '查看地址',
      },
      labels: {
        currency: '请选择币种',
        network: '区块链网络',
        address: '地址',
        nickname: '昵称',
      },
      placeholders: {
        select: '请选择',
        input: '请输入',
      },
      buttons: {
        cancel: '取消',
        next: '下一步',
      },
      rules: {
        currency: '请选择币种',
        network: '请选择区块链网络',
        address: '请输入地址',
        nickname: '请输入昵称',
      },
    },
    currency: {
      title: '银行账户',
      tableEmpty: '暂无数据',
      placeholder: '请选择',
      currency: '币种',
      bankCountry: '银行国家/地区',
      auditStatus: '审核状态',
      reset: '重置',
      search: '查询',
      totalItems: '共 {total} 条',
      bindAccount: '绑定账户',
      table: {
        index: '序号',
        accountNumber: '银行账号',
        bankName: '银行名称',
        bankCountry: '银行国家/地区',
        accountCurrency: '账户币种',
        paymentMethod: '付款方式',
        payeeName: '收款方名称',
        accountNickname: '账户昵称',
        auditStatus: '审核状态',
        creationTime: '创建时间',
        actions: '操作',
      },
      status: {
        approved: '审核通过',
        rejected: '审核拒绝',
        auditing: '审核中',
      },
      dialog: {
        title: '提示',
        deleteConfirm: '请确认是否删除"{displayName}"？',
      },
      message: {
        deleteSuccess: '删除成功',
        bindSuccess: '绑定成功',
        modifySuccess: '修改成功',
      },
      pagination: {
        total: '共 {total} 条',
        sizes: '条/页',
        prev: '上一页',
        next: '下一页',
        jumper: '跳至',
        page: '页',
      },
    },
    editCurrency: {
      title: {
        create: '绑定银行账户',
        edit: '编辑银行账户',
        view: '查看银行账户',
      },
      section: {
        payeeInfo: '收款方信息',
        payeeAccount: '收款方账户',
        payeeBank: '收款方银行信息',
      },
      labels: {
        payeeName: '收款方名称',
        payeeCountry: '国家或地区',
        payeeAddress: '收款方地址',
        nickname: '账户昵称',
        accountType: '账户类型',
        accountNumber: '银行账号',
        accountCurrency: '账户币种',
        paymentMethod: '付款方式',
        bankName: '银行名称',
        bankCountry: '银行国家/地区',
        bankAddress: '银行地址',
        swiftCode: 'Swift Code',
        addressSameAsCompany: '收款方地址是否与企业注册地址相同',
        streetAddress: '街道地址',
        apartment: '公寓或楼层',
        city: '城市',
        state: '省/州',
        postalCode: '邮编',
        optional: '(选填)',
      },
      placeholders: {
        input: '请输入',
        select: '请选择',
      },
      buttons: {
        close: '关闭',
        cancel: '取消',
        submit: '提交',
      },
      rules: {
        businessPostalCodeTooltip: '若您所在的地区没有邮编则填写0000',
        payeeName: '请输入收款方名称',
        payeeCountry: '请选择收款方国家/地区',
        payeeAddress: '请输入收款方地址',
        accountType: '请选择账户类型',
        accountNumber: '请输入银行账号',
        accountCurrency: '请选择账户币种',
        paymentMethod: '请选择付款方式',
        bankName: '请输入银行名称',
        bankCountry: '请选择银行国家/地区',
        bankAddress: '请输入银行地址',
        swiftCode: '请输入Swift Code',
        payeeCity: '请输入城市',
        payeeState: '请输入省/州',
        payeePostCode: '请输入邮编',
      },
    },
    enterprise: {
      basicInfo: '基础信息',
      corporationType: '企业类型',
      industry: '所属行业',
      legalName: '法定企业名称',
      website: '网站或社交媒体链接',
      registrationNumber: '公司注册号',
      companySize: '企业规模',
      sizeUnit: '人',
      registeredAddress: '注册地址',
      tradingAddress: '交易地址',
      address: '地址',
      enterpriseId: '企业ID',
      streetAddress: '街道地址',
      apartment: '公寓或楼层',
      city: '城市',
      state: '省/州',
      postalCode: '邮编',
      countryOrRegion: '国家或地区',
      fetchInfoFailed: '获取企业信息失败，请刷新页面重试',
      corpType: {
        private: '私人有限公司',
        listed: '上市有限公司',
      },
    },
    agreement: {
      title: '所有协议',
      description: '您可以在此处查看您在平台签署的所有协议及内容。',
      totalItems: '共 {total} 条',
      table: {
        index: '序号',
        name: '协议名称',
        time: '接受时间',
        actions: '操作',
      },
      message: {
        fetchFailed: '获取协议列表失败',
      },
      pagination: {
        total: '共 {total} 条',
        sizes: '条/页',
        prev: '上一页',
        next: '下一页',
        jumper: '跳至',
        page: '页',
      },
    },
    layout: {
      enterpriseGroup: '企业',
      enterpriseInfo: '企业信息',
      fiatAccount: '银行账户管理',
      cryptoAddress: '链上地址管理',
      personalGroup: '个人',
      accountInfo: '账号与安全信息',
      agreementGroup: '协议',
      allAgreements: '所有协议',
    },
  },
};
