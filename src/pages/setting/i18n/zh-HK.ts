export default {
  setting: {
    personal: {
      modify: '修改',
      basicInfo: '基礎資訊',
      securityInfo: '安全資訊',
      permissions: '權限',
      admin: '管理員',
      emailAddress: '郵箱地址',
      password: '密碼',
      passwordDesc: '設定唯一密碼以保護您的賬戶，非必要情況請勿更改',
      nicknameNotSet: '未設定暱稱',
      emailNotSet: '未設定郵箱',
      fetchUserInfoFailed: '獲取用戶資訊失敗，請刷新頁面重試',
      usernameCannotBeEmpty: '用戶名不能為空',
      newUsernameSameAsCurrent: '新用戶名與當前用戶名相同',
      updateUsernameSuccess: '用戶名修改成功',
      updateUsernameFailed: '用戶名修改失敗，請重試',
      twoFactorAuthentication: '雙重認證',
      twoFactorAuthenticationDesc: '雙重認證(2FA)增加一層安全性，需要額外的身份驗證方法才能登錄',
      unbind: '解綁',
      bind: '綁定',
      addAuthenticator: '添加第三方認證器',
      verifyTitle: '身份驗證',
      thirdPartyAuthenticator: '第三方認證器',
      verifyPasswordDesc: '輸入您的密碼進行確認。設定後，我們將幫助您設定身份驗證器。',
      verifyPasswordDesc2: '輸入您的密碼進行確認，注意解綁後您賬戶的安全性將下降！',
    },
    editPersonal: {
      title: '修改用戶名',
      description: '您可以自由修改用戶名，展示您喜歡的稱呼(至多32個字符)，讓賬戶更貼近您的風格。',
      currentUsername: '您當前的用戶名',
      newUsername: '新用戶名',
      placeholder: '請輸入',
      cancel: '取消',
      confirm: '確認',
      newUsernameRequired: '請輸入新用戶名',
      newUsernameLength: '用戶名長度應在1-32個字符之間',
    },
    setValidator: {
      title: '設定您的雙重認證器',
      validatorDesc:
        '使用谷歌認證或微軟認證等支持的身份驗證器應用程序掃描二維碼。掃描二維碼後，您會收到一個六位數的確認碼，請在下面輸入。如果您以前設定過 2FA，請重新掃描當前頁面上的二維碼，因為以前的二維碼已經過期。',
      step1: '1.打開手機上的身份驗證器',
      step2: '2.點擊添加“+”圖標，掃描二維碼',
      step3: '3.輸入生成的確認碼',
      viewSupportedApps: '查看支持的身份驗證器應用',
      placeholder: '請輸入確認碼',
      passwordVerifyFailed: '密碼驗證失敗，請重試',
      bindSuccess: '認證器綁定成功',
      unbindSuccess: '認證器解綁成功',
    },
    editEmail: {
      title: '修改郵箱地址',
      description: '您當前的郵箱是 {email} 請告訴我們您想使用的新郵箱地址並進行驗證',
      currentEmail: '您當前的郵箱地址',
      newEmail: '新郵箱',
      placeholder: '請輸入',
      verificationCode: '郵箱驗證碼',
      codePlaceholder: '請輸入',
      sendCode: '發送驗證碼',
      cancel: '取消',
      confirm: '確認',
      newEmailRequired: '請輸入新郵箱地址',
      emailFormatError: '請輸入正確的郵箱格式',
      verificationCodeRequired: '請輸入驗證碼',
      verificationCodeLength: '驗證碼必須為6位數字',
      sameEmailError: '新郵箱不能與當前郵箱相同',
      pleaseGetCode: '請先獲取驗證碼',
      updateSuccess: '郵箱修改成功，即將退出登錄',
      updateFailed: '郵箱修改失敗，請重試',
      sendCodeSuccess: '驗證碼發送成功',
      sendCodeFailed: '驗證碼發送失敗，請重試',
      newEmailSameAsCurrent: '新郵箱與當前郵箱相同',
      updateEmailSuccess: '郵箱修改成功',
      updateEmailFailed: '郵箱修改失敗，請重試',
    },
    editPassword: {
      title: '修改密碼',
      description: '為保障您的賬戶安全，請設定一個強密碼',
      currentPassword: '請輸入賬戶當前的密碼',
      newPassword: '請輸入您的新密碼並二次確認',
      confirmPassword: '確認新密碼',
      placeholder: '請輸入',
      cancel: '取消',
      confirm: '確認',
      currentPasswordRequired: '請輸入當前密碼',
      newPasswordRequired: '請輸入新密碼',
      confirmPasswordRequired: '請輸入確認密碼',
      passwordLengthError: '密碼長度應在8-32個字符之間',
      passwordFormatError: '密碼必須包含大寫字母和數字',
      passwordSpecialCharError: '密碼必須包含特殊字符',
      passwordNotMatchError: '兩次輸入的密碼不一致',
      samePasswordError: '新密碼不能與當前密碼相同',
      updatePasswordSuccess: '密碼修改成功，即將退出登錄',
      updatePasswordFailed: '密碼修改失敗，請重試',
      passwordRequirements: '密碼必須包含',
      lengthRequirement: '8-32個字符',
      upperCaseAndDigitRequirement: '大寫字母和數字',
      specialCharRequirement: '特殊字符',
      differentFromCurrentRequirement: '與當前密碼不同',
    },
    address: {
      title: '鏈上地址',
      tableEmpty: '暫無數據',
      placeholder: '請選擇',
      currency: '幣種',
      auditStatus: '審核狀態',
      reset: '重置',
      search: '查詢',
      totalItems: '共 {total} 條',
      addAddress: '新增地址',
      table: {
        index: '序號',
        nickname: '暱稱',
        currency: '幣種',
        chain: '鏈',
        address: '地址',
        status: '狀態',
        actions: '操作',
      },
      status: {
        approved: '審核通過',
        rejected: '審核拒絕',
        auditing: '審核中',
      },
      dialog: {
        title: '提示',
        deleteConfirm: '請確認是否刪除「{displayName}」？',
      },
      message: {
        deleteSuccess: '刪除成功',
        addSuccess: '新增成功',
        modifySuccess: '修改成功',
      },
      pagination: {
        total: '共 {total} 條',
        sizes: '條/頁',
        prev: '上一頁',
        next: '下一頁',
        jumper: '跳至',
        page: '頁',
      },
    },
    editAddress: {
      title: {
        create: '新增地址',
        edit: '編輯地址',
        view: '查看地址',
      },
      labels: {
        currency: '請選擇幣種',
        network: '區塊鏈網絡',
        address: '地址',
        nickname: '暱稱',
      },
      placeholders: {
        select: '請選擇',
        input: '請輸入',
      },
      buttons: {
        cancel: '取消',
        next: '下一步',
      },
      rules: {
        currency: '請選擇幣種',
        network: '請選擇區塊鏈網絡',
        address: '請輸入地址',
        nickname: '請輸入暱稱',
      },
    },
    currency: {
      title: '銀行賬戶',
      tableEmpty: '暫無數據',
      placeholder: '請選擇',
      currency: '幣種',
      bankCountry: '銀行國家/地區',
      auditStatus: '審核狀態',
      reset: '重置',
      search: '查詢',
      totalItems: '共 {total} 條',
      bindAccount: '綁定賬戶',
      table: {
        index: '序號',
        accountNumber: '銀行賬號',
        bankName: '銀行名稱',
        bankCountry: '銀行國家/地區',
        accountCurrency: '賬戶幣種',
        paymentMethod: '付款方式',
        payeeName: '收款方名稱',
        accountNickname: '賬戶別名',
        auditStatus: '審核狀態',
        creationTime: '創建時間',
        actions: '操作',
      },
      status: {
        approved: '審核通過',
        rejected: '審核拒絕',
        auditing: '審核中',
      },
      dialog: {
        title: '提示',
        deleteConfirm: '請確認是否刪除「{displayName}」？',
      },
      message: {
        deleteSuccess: '刪除成功',
        bindSuccess: '綁定成功',
        modifySuccess: '修改成功',
      },
      pagination: {
        total: '共 {total} 條',
        sizes: '條/頁',
        prev: '上一頁',
        next: '下一頁',
        jumper: '跳至',
        page: '頁',
      },
    },
    editCurrency: {
      title: {
        create: '綁定銀行賬戶',
        edit: '編輯銀行賬戶',
        view: '查看銀行賬戶',
      },
      section: {
        payeeInfo: '收款方資訊',
        payeeAccount: '收款方賬戶',
        payeeBank: '收款方銀行資訊',
      },
      labels: {
        payeeName: '收款方名稱',
        payeeCountry: '國家/地區',
        payeeAddress: '收款方地址',
        nickname: '暱稱',
        accountType: '賬戶類型',
        accountNumber: '銀行賬號',
        accountCurrency: '賬戶幣種',
        paymentMethod: '付款方式',
        bankName: '銀行名稱',
        bankCountry: '銀行國家/地區',
        bankAddress: '銀行地址',
        swiftCode: 'Swift Code',
        addressSameAsCompany: '收款方地址是否與企業註冊地址相同',
        streetAddress: '街道地址',
        apartment: '公寓或樓層',
        city: '城市',
        state: '省/州',
        postalCode: '郵編',
        optional: '(選填)',
      },
      placeholders: {
        input: '請輸入',
        select: '請選擇',
      },
      buttons: {
        close: '關閉',
        cancel: '取消',
        submit: '提交',
      },
      rules: {
        businessPostalCodeTooltip: '如果您所在的地區沒有郵編，請輸入0000',
        payeeName: '請輸入收款方名稱',
        payeeCountry: '請選擇收款方國家/地區',
        payeeAddress: '請輸入收款方地址',
        accountType: '請選擇賬戶類型',
        accountNumber: '請輸入銀行賬號',
        accountCurrency: '請選擇賬戶幣種',
        paymentMethod: '請選擇付款方式',
        bankName: '請輸入銀行名稱',
        bankCountry: '請選擇銀行國家/地區',
        bankAddress: '請輸入銀行地址',
        swiftCode: '請輸入Swift Code',
        payeeCity: '請輸入城市',
        payeeState: '請輸入省/州',
        payeePostCode: '請輸入郵編',
      },
    },
    enterprise: {
      basicInfo: '基礎資訊',
      corporationType: '企業類型',
      industry: '所屬行業',
      legalName: '法定企業名稱',
      website: '網站或社交媒體連結',
      registrationNumber: '公司註冊號',
      companySize: '企業規模',
      sizeUnit: '人',
      registeredAddress: '註冊地址',
      tradingAddress: '交易地址',
      address: '地址',
      enterpriseId: '企業ID',
      streetAddress: '街道地址',
      apartment: '公寓或樓層',
      city: '城市',
      state: '省/州',
      postalCode: '郵編',
      countryOrRegion: '國家或地區',
      fetchInfoFailed: '獲取企業資訊失敗，請刷新頁面重試',
      corpType: {
        private: '私人有限公司',
        listed: '上市有限公司',
      },
    },
    agreement: {
      title: '所有協議',
      description: '您可以在此處查看您在平台簽署的所有協議及內容。',
      totalItems: '共 {total} 條',
      table: {
        index: '序號',
        name: '協議名稱',
        time: '接受時間',
        actions: '操作',
      },
      message: {
        fetchFailed: '獲取協議列表失敗',
      },
      pagination: {
        total: '共 {total} 條',
        sizes: '條/頁',
        prev: '上一頁',
        next: '下一頁',
        jumper: '跳至',
        page: '頁',
      },
    },
    layout: {
      enterpriseGroup: '企業',
      enterpriseInfo: '企業資訊',
      fiatAccount: '銀行賬戶管理',
      cryptoAddress: '鏈上地址管理',
      personalGroup: '個人',
      accountInfo: '賬號與安全資訊',
      agreementGroup: '協議',
      allAgreements: '所有協議',
    },
  },
};
