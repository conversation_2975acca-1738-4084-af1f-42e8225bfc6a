export default {
  setting: {
    personal: {
      modify: 'Modify',
      basicInfo: 'Basic Info',
      securityInfo: 'Security Info',
      permissions: 'Permissions',
      admin: 'Admin',
      emailAddress: 'Email Address',
      password: 'Password',
      passwordDesc:
        'Set a unique password to protect your account, please do not change unless necessary',
      nicknameNotSet: 'Nickname not set',
      emailNotSet: 'Email not set',
      fetchUserInfoFailed:
        'Failed to fetch user information, please refresh the page and try again',
      usernameCannotBeEmpty: 'Username cannot be empty',
      newUsernameSameAsCurrent: 'The new username is the same as the current one',
      updateUsernameSuccess: 'Username updated successfully',
      updateUsernameFailed: 'Failed to update username, please try again',
      twoFactorAuthentication: 'Two-Factor Authentication',
      twoFactorAuthenticationDesc:
        'Two-Factor Authentication (2FA) adds an extra layer of security, requiring an additional verification method to log in',
      unbind: 'Unbind',
      bind: 'Bind',
      addAuthenticator: 'Add Third-Party Authenticator',
      verifyTitle: 'Verify Identity',
      thirdPartyAuthenticator: 'Third-Party Authenticator',
      verifyPasswordDesc:
        'Enter your password to confirm. After setting, we will help you set up the authenticator.',
      verifyPasswordDesc2:
        'Enter your password to confirm, note that unbinding will reduce the security of your account!',
    },
    editPersonal: {
      title: 'Modify Username',
      description:
        'You can freely modify your username to display a name you like (up to 32 characters), making the account more personalized to your style.',
      currentUsername: 'Your current username',
      newUsername: 'New username',
      placeholder: 'Please enter',
      cancel: 'Cancel',
      confirm: 'Confirm',
      newUsernameRequired: 'Please enter a new username',
      newUsernameLength: 'Username length should be between 1 and 32 characters',
    },
    setValidator: {
      title: 'Set Your Two-Factor Authenticator',
      validatorDesc:
        'Use a supported authenticator application such as Google Authenticator or Microsoft Authenticator to scan the QR code. After scanning the QR code, you will receive a six-digit confirmation code, please enter it below. If you have previously set up 2FA, please re-scan the QR code on this page as the previous QR code has expired.',
      step1: '1. Open the authenticator app on your phone',
      step2: "2. Click the '+' icon to add, then scan the QR code",
      step3: '3. Enter the generated confirmation code',
      viewSupportedApps: 'Supported Apps',
      placeholder: 'Please enter confirmation code',
      passwordVerifyFailed: 'Password verification failed, please try again',
      bindSuccess: 'Authenticator bound successfully',
      unbindSuccess: 'Authenticator unbound successfully',
      validator: "Identity Verification",
      setValidator: "Set Authenticator"
    },
    editEmail: {
      title: 'Modify Email Address',
      description:
        'Your current email is {email} Please tell us the new email address you want to use for verification',
      currentEmail: 'Your current email address',
      newEmail: 'New Email',
      placeholder: 'Please enter',
      verificationCode: 'Email Verification Code',
      codePlaceholder: 'Please enter',
      sendCode: 'Send Code',
      cancel: 'Cancel',
      confirm: 'Confirm',
      newEmailRequired: 'Please enter a new email address',
      emailFormatError: 'Please enter a valid email format',
      verificationCodeRequired: 'Please enter verification code',
      verificationCodeLength: 'Verification code must be 6 digits',
      sameEmailError: 'New email cannot be the same as current email',
      pleaseGetCode: 'Please get verification code first',
      updateSuccess: 'Email updated successfully, logging out...',
      updateFailed: 'Failed to update email, please try again',
      sendCodeSuccess: 'Verification code sent successfully',
      sendCodeFailed: 'Failed to send verification code, please try again',
      newEmailSameAsCurrent: 'New email is the same as current email',
      updateEmailSuccess: 'Email updated successfully',
      updateEmailFailed: 'Failed to update email, please try again',
    },
    editPassword: {
      title: 'Change Password',
      description: 'To secure your account, please set a strong password',
      currentPassword: 'Please enter your current account password',
      newPassword: 'Please enter your new password and confirm it',
      confirmPassword: 'Confirm new password',
      placeholder: 'Please enter',
      cancel: 'Cancel',
      confirm: 'Confirm',
      currentPasswordRequired: 'Please enter current password',
      newPasswordRequired: 'Please enter new password',
      confirmPasswordRequired: 'Please enter confirm password',
      passwordLengthError: 'Password length should be between 8 and 32 characters',
      passwordFormatError: 'Password must contain uppercase letters and numbers',
      passwordSpecialCharError: 'Password must contain special characters',
      passwordNotMatchError: 'The two passwords entered do not match',
      samePasswordError: 'New password cannot be the same as current password',
      updatePasswordSuccess: 'Password updated successfully, logging out...',
      updatePasswordFailed: 'Failed to update password, please try again',
      passwordRequirements: 'Password must contain',
      lengthRequirement: '8-32 characters',
      upperCaseAndDigitRequirement: 'uppercase letters and numbers',
      specialCharRequirement: 'special characters',
      differentFromCurrentRequirement: 'different from current password',
    },
    address: {
      title: 'Crypto Address',
      tableEmpty: 'No Data',
      placeholder: 'Please select',
      currency: 'Currency',
      auditStatus: 'Audit Status',
      reset: 'Reset',
      search: 'Search',
      totalItems: 'Total {total} items',
      addAddress: 'Add Address',
      table: {
        index: 'No.',
        nickname: 'Nickname',
        currency: 'Currency',
        chain: 'Chain',
        address: 'Address',
        status: 'Status',
        actions: 'Actions',
      },
      status: {
        approved: 'Approved',
        rejected: 'Rejected',
        auditing: 'Auditing',
      },
      dialog: {
        title: 'Prompt',
        deleteConfirm: 'Are you sure you want to delete "{displayName}"?',
      },
      message: {
        deleteSuccess: 'Deleted successfully',
        addSuccess: 'Added successfully',
        modifySuccess: 'Modified successfully',
      },
      pagination: {
        total: 'Total {total} items',
        sizes: ' / page',
        prev: 'Previous',
        next: 'Next',
        jumper: 'Go to',
        page: '',
      },
    },
    editAddress: {
      title: {
        create: 'Add Address',
        edit: 'Edit Address',
        view: 'View Address',
      },
      labels: {
        currency: 'Please select currency',
        network: 'Blockchain Network',
        address: 'Address',
        nickname: 'Nickname',
      },
      placeholders: {
        select: 'Please select',
        input: 'Please enter',
      },
      buttons: {
        cancel: 'Cancel',
        next: 'Next',
        submit: 'Submit',
      },
      steps: {
        verify: 'Identity Verification',
      },
      rules: {
        currency: 'Please select a currency',
        network: 'Please select a blockchain network',
        address: 'Please enter an address',
        nickname: 'Please enter a nickname',
      },
    },
    currency: {
      title: 'Fiat Account Management',
      tableEmpty: 'No Data',
      placeholder: 'Please select',
      currency: 'Currency',
      bankCountry: 'Bank Country/Region',
      auditStatus: 'Audit Status',
      reset: 'Reset',
      search: 'Search',
      totalItems: 'Total {total} items',
      bindAccount: 'Bind Account',
      table: {
        index: 'No.',
        accountNumber: 'Bank Account Number',
        bankName: 'Bank Name',
        bankCountry: 'Bank Country/Region',
        accountCurrency: 'Account Currency',
        paymentMethod: 'Payment Method',
        payeeName: 'Payee Name',
        accountNickname: 'Account Nickname',
        auditStatus: 'Audit Status',
        creationTime: 'Creation Time',
        actions: 'Actions',
      },
      status: {
        approved: 'Approved',
        rejected: 'Rejected',
        auditing: 'Auditing',
      },
      dialog: {
        title: 'Prompt',
        deleteConfirm: 'Are you sure you want to delete "{displayName}"?',
      },
      message: {
        deleteSuccess: 'Deleted successfully',
        bindSuccess: 'Bound successfully',
        modifySuccess: 'Modified successfully',
      },
      pagination: {
        total: 'Total {total} items',
        sizes: ' / page',
        prev: 'Previous',
        next: 'Next',
        jumper: 'Go to',
        page: '',
      },
    },
    editCurrency: {
      title: {
        create: 'Bind Fiat Account',
        edit: 'Edit Fiat Account',
        view: 'View Fiat Account',
      },
      section: {
        payeeInfo: 'Payee Information',
        payeeAccount: 'Payee Account',
        payeeBank: 'Payee Bank Information',
      },
      labels: {
        payeeName: 'Payee Name',
        payeeCountry: 'Country or Region',
        payeeAddress: 'Payee Address',
        nickname: 'Nickname',
        accountType: 'Account Type',
        accountNumber: 'Bank Account Number',
        accountCurrency: 'Account Currency',
        paymentMethod: 'Payment Method',
        bankName: 'Bank Name',
        bankCountry: 'Bank Country/Region',
        bankAddress: 'Bank Address',
        swiftCode: 'Swift Code',
        addressSameAsCompany: 'Is the payee address the same as the company registration address',
        streetAddress: 'Street Address',
        apartment: 'Apartment or Floor',
        city: 'City',
        state: 'State',
        postalCode: 'Postal Code',
        optional: '(Optional)',
      },
      placeholders: {
        input: 'Please enter',
        select: 'Please select',
      },
      buttons: {
        cancel: 'Cancel',
        close: 'Close',
        submit: 'Submit',
        next: 'Next',
        previous: 'Previous',
      },
      steps: {
        identityVerification: 'Identity Verification',
      },
      rules: {
        businessPostalCodeTooltip:
          'If you do not have a postal code for your region, please enter 0000',
        payeeName: 'Please enter payee name',
        payeeCountry: 'Please select payee country/region',
        payeeAddress: 'Please enter payee address',
        accountType: 'Please select account type',
        accountNumber: 'Please enter bank account number',
        accountCurrency: 'Please select account currency',
        paymentMethod: 'Please select payment method',
        bankName: 'Please enter bank name',
        bankCountry: 'Please select bank country/region',
        bankAddress: 'Please enter bank address',
        swiftCode: 'Please enter Swift Code',
        payeeCity: 'Please enter city',
        payeeState: 'Please enter state',
        payeePostCode: 'Please enter postal code',
      },
    },
    enterprise: {
      basicInfo: 'Basic Info',
      corporationType: 'Corporation Type',
      industry: 'Industry',
      legalName: 'Legal Company Name',
      website: 'Website or Social Media Link',
      registrationNumber: 'Company Registration Number',
      companySize: 'Company Size',
      sizeUnit: 'people',
      registeredAddress: 'Registered Address',
      tradingAddress: 'Trading Address',
      address: 'Address',
      enterpriseId: 'Enterprise ID',
      streetAddress: 'Street Address',
      apartment: 'Apartment or Floor',
      city: 'City',
      state: 'State/Province',
      postalCode: 'Postal Code',
      countryOrRegion: 'Country or Region',
      fetchInfoFailed:
        'Failed to fetch enterprise information, please refresh the page and try again',
      corpType: {
        private: 'Private Limited Company',
        listed: 'Listed Limited Company',
      },
    },
    agreement: {
      title: 'All Agreements',
      description:
        'Here you can view all the agreements and their content that you have signed on the platform.',
      totalItems: 'Total {total} items',
      table: {
        index: 'No.',
        name: 'Agreement Name',
        time: 'Acceptance Time',
        actions: 'Actions',
      },
      message: {
        fetchFailed: 'Failed to fetch agreement list',
      },
      pagination: {
        total: 'Total {total} items',
        sizes: ' / page',
        prev: 'Previous',
        next: 'Next',
        jumper: 'Go to',
        page: '',
      },
    },
    layout: {
      enterpriseGroup: 'Enterprise',
      enterpriseInfo: 'Enterprise Information',
      fiatAccount: 'Fiat Account Management',
      cryptoAddress: 'Crypto Address Management',
      personalGroup: 'Personal',
      accountInfo: 'Account & Security Information',
      agreementGroup: 'Agreement',
      allAgreements: 'All Agreements',
    },
  },
  common: {
    message:
      'To ensure the security of your funds, this operation requires binding a third-party authenticator for two-factor authentication. We recommend you bind it in advance.',
  },
};
