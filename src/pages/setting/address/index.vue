<template>
  <div class="w-full flex flex-col pb-40px">
    <span class="text-28px text-[#222527] font-600 font-family-[PingFangSC-Semibold] mb-24px">
      {{ t('setting.address.title') }}
    </span>
    <ElForm :model="searchForm" inline class="mb-30px flex flex-wrap gap-y-12px" hide-required-asterisk>
      <ElFormItem class="w-304px mr-16px mb-0">
        <ElSelect v-model="searchForm.currency" :placeholder="t('setting.address.placeholder')" clearable
          style="width: 100%;">
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('setting.address.currency') }}</span>
            <img v-if="searchForm.currency" :src="getCryptoIcon(searchForm.currency)" width="16" />
          </template>
          <ElOption v-for="item in currencyList" :key="item.enumCode" :label="item.enumDescCn" :value="item.enumCode">
            <p style="display: flex; align-items: center; margin: 0; height: 40px">
              <img style="margin-right: 8px" :src="getCryptoIcon(item.enumCode)" width="20" />
              {{ item.enumDescCn }}
            </p>
            <!-- <div class="flex items-center">
              <span>{{ item.enumDescCn }}</span>
            </div> -->
          </ElOption>
        </ElSelect>
      </ElFormItem>
      <ElFormItem class="w-304px mr-16px mb-0">
        <ElSelect v-model="searchForm.status" clearable :placeholder="t('setting.address.placeholder')"
          style="width: 100%;">
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('setting.address.auditStatus') }}</span>
          </template>
          <ElOption v-for="info in statusList" :key="info.value" :label="info.label" :value="info.value" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem class="mr-0 mb-0">
        <ElButton @click="handleReset"
          class="reset-btn min-w-76px text-14px text-[#222527] font-family-[PingFangSC-Regular] btn-hover-scale-sm">
          <SvgIcon name="icon-reset" class="mr-9px" />
          {{ t('setting.address.reset') }}
        </ElButton>
        <ElButton type="primary" @click="handleSearch"
          class="min-w-76px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm">
          <SvgIcon name="icon-query" class="mr-9px" />
          {{ t('setting.address.search') }}
        </ElButton>
      </ElFormItem>
    </ElForm>

    <div class="flex justify-between items-center mb-16px">
      <span class="text-14px text-[#222527] font-family-[PingFangSC-Regular]">{{ t('setting.address.totalItems', {
        total
      })
        }}</span>
      <ElButton @click="handleAddAddress"
        class="min-w-104px text-14px text-[#222527] font-family-[PingFangSC-Regular] bg-white border border-[#E5E6EB] rounded-6px btn-hover-scale-sm">
        <SvgIcon name="icon-add" class="mr-9px" />
        {{ t('setting.address.addAddress') }}
      </ElButton>
    </div>

    <div class="overflow-hidden rounded-12px border border-[#E5E6EB]">
      <ElTable :data="tableData" style="width: 100%" row-key="id" class="list-table"
        :empty-text="t('setting.address.tableEmpty')"
        header-cell-class-name="!bg-[#F8F9FA] font-family-[PingFangSC-Semibold] !text-12px !text-[#6B7275]"
        :header-row-style="{ height: '44px' }" :row-style="{ height: '48px' }">
        <ElTableColumn type="index" :label="t('setting.address.table.index')" width="80" />
        <ElTableColumn prop="walletName" :label="t('setting.address.table.nickname')" />
        <ElTableColumn prop="currency" :label="t('setting.address.table.currency')">
          <template #default="{ row }">
            <div class="flex items-center">
              <img :src="getCryptoIcon(row.ccy)" :alt="row.ccy" class="w-20px h-20px mr-8px rounded-full" />
              <span>{{ row.ccy }}</span>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="cryptoNet" :label="t('setting.address.table.chain')">
          <template #default="{ row }">
            <div class="flex items-center">
              <img :src="getCryptoIcon(row.cryptoNet)" :alt="row.cryptoNet" class="w-20px h-20px mr-8px rounded-full" />
              <span>{{ row.cryptoNet }}</span>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="cryptoAddr" :label="t('setting.address.table.address')" />
        <ElTableColumn prop="status" :label="t('setting.address.table.status')">
          <template #default="{ row }">
            <div
              :class="['flex items-center w-fit justify-center rounded-[12px] px-[12px] font-family-[PingFangSC-Regular] font-400 text-[12px] height-24px', getStatusClass(row.status)]">
              <span>{{ getStatusText(row.status) }}</span>
            </div>
          </template>
        </ElTableColumn>
        <!-- <ElTableColumn prop="status" :label="t('setting.address.table.status')">
          <template #default="{ row }">
            <div class="flex items-center">
              <span :class="['status-dot', getStatusClass(row.status)]" />
              <span>{{ getStatusText(row.status) }}</span>
            </div>
          </template>
        </ElTableColumn> -->
        <ElTableColumn :label="t('setting.address.table.actions')" width="150" fixed="right">
          <template #default="{ row }">
            <ElButton link type="primary" :style="{ color: '#030814' }" @click="handleView(row)"
              class="btn-hover-scale-sm">
              <SvgIcon name="icon-open" />
            </ElButton>
            <ElButton link type="primary" :disabled="row.status !== 'approved' && row.status !== 'rejected'"
              :style="{ color: (row.status === 'approved' || row.status === 'rejected') ? '#030814' : '#d2d2d2' }"
              @click="handleEdit(row)" class="btn-hover-scale-sm">
              <SvgIcon name="icon-edit" />
            </ElButton>
            <ElButton link type="primary" :disabled="row.status !== 'rejected' && row.status !== 'approved'"
              :style="{ color: (row.status === 'rejected' || row.status === 'approved') ? '#030814' : '#d2d2d2' }"
              @click="handleDelete(row)" class="btn-hover-scale-sm">
              <SvgIcon name="icon-deleted" />
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>

    <div class="flex justify-end mt-4">
      <ElPagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
        :total="total" layout="sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
        <template #jumper>{{ t('setting.address.pagination.jumper') }}</template>
      </ElPagination>
    </div>
    <EditAddress v-model="showEditAddress" :address-info="currentAddressInfo" :mode="editMode"
      :status="currentAddressInfo.status" @submit="handleSubmit" @cancel="showEditAddress = false" />
    <CommonDialog v-model:visible="showDeleteDialog" :title="t('setting.address.dialog.title')" iconClass="warning"
      :message="deleteDialogContent" @confirm="confirmDelete" />
    <template v-if="showIdentityVerify">
      <IdentityVerify v-model="showIdentityVerify" @submit="verifyIdOk" @cancel="cancelVerify"
        :businessType="businessType" />
    </template>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { t } from '@@/i18n'
import { useEnumStore } from '@/pinia/stores/enumStore'
import { BusinessEnumType } from '@/common/apis/common/type'
import { queryDigitalAddressListApi, addDigitalAddressApi, modifyDigitalAddressApi, deleteDigitalAddressApi } from '../apis'
import type { DigitalAddress } from '../apis/type'
// 组件将通过自动导入功能引入
// EditAddress, CommonDialog, IdentityVerify 组件会自动导入
import EditAddress from '../components/EditAddress.vue'
import CommonDialog from '@/common/components/Dialog/CommonDialog.vue'
import { getCryptoIcon } from '@/common/utils/imageUtils'
import { handleAuthVerification } from '@@/utils/2FAUtil'
import { FeeEnumType } from '@/pages/exchange/apis/type'
import IdentityVerify from '@/pages/withdrawal/components/IdentityVerify.vue';

const enumStore = useEnumStore()
const currencyList = enumStore.getEnumList(BusinessEnumType.CURRENCY);
const statusList = computed(() => [
  { value: 'approved', label: t('setting.address.status.approved') },
  { value: 'rejected', label: t('setting.address.status.rejected') },
  { value: 'auditing', label: t('setting.address.status.auditing') },
])

const searchForm = reactive({
  currency: '',
  status: '',
})

const tableData = ref<DigitalAddress[]>([])
const showEditAddress = ref(false)
const currentAddressInfo = ref<DigitalAddress>({} as DigitalAddress)
const editMode = ref<'view' | 'edit' | 'create'>('create')
const showDeleteDialog = ref(false)
const itemToDelete = ref<DigitalAddress | null>(null)
const showIdentityVerify = ref(false)
// 删除操作的业务类型
const businessType = ref<FeeEnumType>(FeeEnumType.CRYPTO_ADDR_DEL)

const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

async function getList() {
  const res = await queryDigitalAddressListApi({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    status: searchForm.status,
    ccy: searchForm.currency || '',
  })
  tableData.value = res.data.dcWalletAddrList || []
  total.value = res.data.totalCount || 0
}

const getStatusText = (status: DigitalAddress['status']) => {
  switch (status) {
    case "approved":
      return t('setting.address.status.approved')
    case "rejected":
      return t('setting.address.status.rejected')
    case "auditing":
      return t('setting.address.status.auditing')
    default:
      return ''
  }
}

const getStatusClass = (status: DigitalAddress['status']) => {
  switch (status) {
    case "approved":
      return 'status-approved'
    case "rejected":
      return 'status-rejected'
    case "auditing":
      return 'status-pending'
    default:
      return ''
  }
}

function handleReset() {
  searchForm.currency = ''
  searchForm.status = ''
  handleSearch()
}

function handleSearch() {
  currentPage.value = 1
  getList()
}

const handleAddAddress = () => {
  handleAuthVerification(() => {
    showEditAddress.value = true
    currentAddressInfo.value = {} as DigitalAddress
    editMode.value = 'create'
  }, {
    message: t('setting.common.message'),
  })
}

function handleView(row: DigitalAddress) {
  showEditAddress.value = true
  currentAddressInfo.value = row
  editMode.value = 'view'
}

async function handleEdit(row: DigitalAddress) {
  handleAuthVerification(() => {
    showEditAddress.value = true
    currentAddressInfo.value = row
    editMode.value = 'edit'
  }, {
    message: t('setting.common.message'),
  })

}

const deleteDialogContent = computed(() => {
  if (!itemToDelete.value)
    return ''
  const displayName = itemToDelete.value.walletName
  return t('setting.address.dialog.deleteConfirm', { displayName })
})

/**
 * 删除地址处理方法 - 集成权限验证
 * @param row 要删除的地址信息
 */
async function handleDelete(row: DigitalAddress) {
  handleAuthVerification(() => {
    itemToDelete.value = row
    showDeleteDialog.value = true
  }, {
    message: t('setting.common.message'),
  })
}

async function confirmDelete() {
  showDeleteDialog.value = false
  showIdentityVerify.value = true
}

/**
 * 二因子验证成功回调
 * @param payload 验证结果包含sendSeqId和tfaSeqId
 */
const verifyIdOk = async (payload: { sendSeqId: string; tfaSeqId: string }) => {
  showIdentityVerify.value = false
  try {
    if (!itemToDelete.value) return
    await deleteDigitalAddressApi({
      sysSeqId: itemToDelete.value.sysSeqId,
      sendSeqId: payload.sendSeqId,
      tfaSeqId: payload.tfaSeqId
    })
    ElMessage.success(t('setting.address.message.deleteSuccess'))
    getList()
  } catch (error) {
    console.error(error)
  } finally {
    itemToDelete.value = null
  }
}

/**
 * 取消二因子验证
 */
const cancelVerify = () => {
  showIdentityVerify.value = false
}

async function handleSubmit(payload: DigitalAddress, res: any) {
  try {
    if (editMode.value === 'create') {
      await addDigitalAddressApi({ ...payload, ...res })
      ElMessage.success(t('setting.address.message.addSuccess'))
    }
    else if (editMode.value === 'edit') {
      await modifyDigitalAddressApi({ ...payload, ...res })
      ElMessage.success(t('setting.address.message.modifySuccess'))
    }
    showEditAddress.value = false
    getList()
  }
  catch (error) {
    console.error(error)
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  getList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-approved {
  background-color: #EBF7EB;
  color: #289532;
}

.status-rejected {
  background-color: #FFF1EC;
  color: #DB1507;
}

.status-pending {
  background-color: #EBF6FF;
  color: #0066DB;
}

/* :deep(.list-table.el-table::before) {
  height: 0;
} */

:deep(.el-table) {
  --el-table-header-bg-color: #F8F9FA;
  --el-table-header-text-color: #6B7275;
  --el-table-header-text-font-size: 12px;
  --el-table-header-text-font-weight: 600;
  --el-table-header-text-font-family: PingFangSC-Semibold;
  --el-table-text-color: #222527;
  --el-table-text-font-size: 14px;
  --el-table-text-font-weight: 400;
  --el-table-text-font-family: PingFangSC-Regular;
}

:deep(.el-table .el-table__header-wrapper) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-table .el-table__header-wrapper tr) {
  --el-table-border: none;
}

:deep(.el-table .el-table__header-wrapper tr .el-table__cell) {
  padding: 10px 0;
}

:deep(.el-table tbody tr .el-table__cell) {
  padding: 12px 0;
  color: #222527;
}

/* 样式已直接应用到对应的组件上，不再需要deep选择器 */

.reset-btn:hover {
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
}
</style>