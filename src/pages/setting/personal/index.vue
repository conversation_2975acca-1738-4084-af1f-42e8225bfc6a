<template>
  <div class=" bg-white min-h-screen">
    <div class="flex items-center mb-32px">
      <div class="w-44px h-44px rounded-12px overflow-hidden mr-20px bg-[#ff3e6c] flex items-center justify-center">
        <SvgIcon name="icon-setting-person" class="text-white !w-24px !h-24px" />
      </div>
      <div class="font-family-[PingFangSC-Semibold] font-semibold text-28px text-[#222527] flex-grow">{{ currentUsername
      }}</div>
      <el-button text bg class="border border-1 rd-6px border-[#E5E6EB] text-[#222527] bg-[#FFFFFF] btn-hover-scale-sm"
        @click="openEditDialog">
        <SvgIcon name="icon-edit" class="mr-9px" />
        {{ t('setting.personal.modify') }}
      </el-button>
    </div>

    <div class="info-section">
      <h3 class="font-family-[PingFangSC-Semibold] font-semibold text-18px text-[#222527] mb-30px">{{
        t('setting.personal.basicInfo') }}</h3>
      <div class="flex flex-col">
        <div class="flex items-center">
          <div class="flex items-center w-200px">
            <div class="w-24px h-24px rounded-6px bg-[#FFEBF0] flex items-center justify-center mr-17.5px">
              <SvgIcon name="icon-setting-permission" class="text-[#FF0064]" />
            </div>
            <span class="font-family-[PingFangSC-Semibold] font-semibold text-14px text-[#222527]">{{
              t('setting.personal.permissions') }}</span>
          </div>
          <div class="font-family-[PingFangSC-Regular] font-weight-400 text-14px text-[#6B7275] flex-grow">{{
            t('setting.personal.admin') }}</div>
          <el-button text bg disabled
            class="border border-1 rd-6px !border-[#E5E6EB] !text-[#A7ADB0] !bg-[#FFFFFF] btn-hover-scale-sm">
            <SvgIcon name="icon-edit" class="mr-9px !text-[#E5E6EB]" />
            {{ t('setting.personal.modify') }}
          </el-button>
        </div>
        <div class="flex items-center mt-36px">
          <div class="flex items-center w-200px">
            <div class="w-24px h-24px rounded-6px bg-[#FFEBF0] flex items-center justify-center mr-17.5px">
              <SvgIcon name="icon-setting-email" class="text-[#FF0064]" />
            </div>
            <span class="font-family-[PingFangSC-Semibold] font-semibold text-14px text-[#222527]">{{
              t('setting.personal.emailAddress') }}</span>
          </div>
          <div class="font-family-[PingFangSC-Regular] font-weight-400 text-14px text-[#6B7275] flex-grow">{{ userEmail
          }}</div>
          <el-button text bg
            class="border border-1 rd-6px border-[#E5E6EB] text-[#222527] bg-[#FFFFFF] btn-hover-scale-sm"
            @click="openEditEmailDialog">
            <SvgIcon name="icon-edit" class="mr-9px" />
            {{ t('setting.personal.modify') }}
          </el-button>
        </div>
      </div>
    </div>
    <div class="w-full h-1px bg-[#EDEDEE] mt-38px"></div>
    <div class="info-section mt-30px">
      <h3 class="font-family-[PingFangSC-Semibold] font-semibold text-18px text-[#222527] mb-30px">{{
        t('setting.personal.securityInfo') }}</h3>
      <div class="flex flex-col">
        <div class="flex items-center flex-justify-between">
          <div class="flex items-center">
            <div class="flex items-center w-200px">
              <div class="w-24px h-24px rounded-6px bg-[#FFEBF0] flex items-center justify-center mr-17.5px">
                <SvgIcon name="icon-setting-password" class="text-[#FF0064]" />
              </div>
              <span class="font-family-[PingFangSC-Semibold] font-semibold text-14px text-[#222527]">{{
                t('setting.personal.password') }}</span>
            </div>
            <div class="flex-1 font-family-[PingFangSC-Regular] font-weight-400 text-14px text-[#FD3627] flex-grow">{{
              t('setting.personal.passwordDesc') }}</div>
          </div>
          <el-button text bg
            class="border border-1 rd-6px border-[#E5E6EB] text-[#222527] bg-[#FFFFFF] btn-hover-scale-sm"
            @click="openEditPasswordDialog">
            <SvgIcon name="icon-edit" class="mr-9px" />
            {{ t('setting.personal.modify') }}
          </el-button>
        </div>
      </div>
      <div class="flex flex-col mt-36px">
        <div class="flex items-start flex-justify-between">
          <div class="flex items-center w-200px">
            <div class="w-24px h-24px rounded-6px bg-[#FFEBF0] flex items-center justify-center mr-17.5px">
              <SvgIcon name="icon-setting-factor-auth" class="text-[#FF0064]" />
            </div>
            <span class="font-family-[PingFangSC-Semibold] font-semibold text-14px text-[#222527]">{{
              t('setting.personal.twoFactorAuthentication') }}</span>
          </div>
          <div class="flex items-end justify-between flex-1">
            <div class="flex flex-col flex-self-start mt-4px">
              <div class="flex-1 font-family-[PingFangSC-Regular] font-weight-400 text-14px text-[#FD3627] flex-grow">{{
                t('setting.personal.twoFactorAuthenticationDesc') }}</div>
              <div v-if="twoFactorAuthStatus === '1'" class="mt-30px color-[#6B7275] text-14px">{{
                t('setting.personal.thirdPartyAuthenticator') }}</div>

              <div v-else class="mt-30px color-[#6B7275] text-14px">
                <el-button @click="openVerifyPasswordDialog" class="min-w-104px min-h-32px bg-[#FFFFFF] add-btn">
                  <SvgIcon name="icon-add" class="w-16px h-16px color-[#030814]" />
                  <span class="text-14px font-400 color-[#030814] ml-8px">{{ t('setting.personal.addAuthenticator')
                  }}</span>
                </el-button>
              </div>
            </div>

            <el-button v-if="twoFactorAuthStatus === '1'" text bg
              class="border border-1 rd-6px border-[#E5E6EB] text-[#222527] bg-[#FFFFFF] btn-hover-scale-sm mt-45px"
              @click="openVerifyPasswordDialog">
              <SvgIcon name="icon-unbind" class="mr-9px" />
              <span class="text-#222527">{{ t('setting.personal.unbind') }}</span>
            </el-button>
          </div>

        </div>
      </div>
    </div>

    <!-- 修改用户名抽屉 -->
    <EditPersonal v-model="showEditDialog" :current-username="currentUsername" @submit="handleUsernameUpdate"
      @cancel="handleCancel" />

    <!-- 修改邮箱地址抽屉 -->
    <EditEmail v-model="showEditEmailDialog" :current-email="userEmail" @success="handleEmailUpdateSuccess"
      @cancel="handleEmailCancel" />

    <!-- 修改密码抽屉 -->
    <EditPassword v-model="showEditPasswordDialog" @cancel="handlePasswordCancel" />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { t } from '@@/i18n'
import EditPersonal from '../components/EditPersonal.vue'
import EditEmail from '../components/EditEmail.vue'
import EditPassword from '../components/EditPassword.vue'
import { queryCustomerApi, modifyNicknameApi, changePasswordApi } from '../apis'
import { Bind2FAData } from '../apis/type'
import BindTFADrawer from '@/common/components/BindTFA'

// 响应式数据
const currentUsername = ref('')
const userEmail = ref('')
// 2FA绑定信息
const twoFactorAuthStatus = ref('0') // 0 未绑定 1 已绑定
const twoFactorAuthSysSeqId = ref('') // 绑定流水号，解绑需要

const showEditDialog = ref(false)
const showEditEmailDialog = ref(false)
const showEditPasswordDialog = ref(false)
const loading = ref(false)

// 打开编辑用户名对话框
const openEditDialog = () => {
  showEditDialog.value = true
}

// 打开编辑邮箱对话框
const openEditEmailDialog = () => {
  showEditEmailDialog.value = true
}

// 打开编辑密码对话框
const openEditPasswordDialog = () => {
  showEditPasswordDialog.value = true
}

onMounted(() => {
  getUserInfo()
})

// 获取用户信息
const getUserInfo = async () => {
  try {
    loading.value = true
    const res = await queryCustomerApi({})
    currentUsername.value = res.data.nickName || t('setting.personal.nicknameNotSet')
    userEmail.value = res.data.email || t('setting.personal.emailNotSet')

    twoFactorAuthStatus.value = res.data.twoFactorAuthStatus || '0'
    twoFactorAuthSysSeqId.value = res.data.twoFactorAuthSysSeqId || ''
  } catch (error) {
    console.error('获取用户信息异常:', error)
    ElMessage.error(t('setting.personal.fetchUserInfoFailed'))
  } finally {
    loading.value = false
  }
}

// 处理用户名更新
const handleUsernameUpdate = async (payload: { newUsername: string }) => {
  try {
    // 验证新用户名
    if (!payload.newUsername || payload.newUsername.trim() === '') {
      ElMessage.error(t('setting.personal.usernameCannotBeEmpty'))
      return
    }

    if (payload.newUsername.trim() === currentUsername.value) {
      ElMessage.warning(t('setting.personal.newUsernameSameAsCurrent'))
      return
    }

    // 调用API更新用户名
    await modifyNicknameApi({
      nickName: payload.newUsername.trim()
    })

    // 更新本地状态
    currentUsername.value = payload.newUsername.trim()

    // 关闭抽屉
    showEditDialog.value = false

    // 显示成功消息
    ElMessage.success(t('setting.personal.updateUsernameSuccess'))
  } catch (error) {
    console.error('修改用户名异常:', error)
    ElMessage.error(t('setting.personal.updateUsernameFailed'))
  }
}

// 处理取消操作
const handleCancel = () => {
  showEditDialog.value = false
}

// 处理邮箱更新成功
const handleEmailUpdateSuccess = async () => {
  try {
    // 重新获取用户信息以更新邮箱显示
    await getUserInfo()

    // 关闭抽屉
    showEditEmailDialog.value = false

  } catch (error) {
    console.error('刷新用户信息异常:', error)
    // 即使刷新失败也关闭抽屉
    showEditEmailDialog.value = false
  }
}

// 处理邮箱取消操作
const handleEmailCancel = () => {
  showEditEmailDialog.value = false
}

// 绑定2FA弹出密码验证
const openVerifyPasswordDialog = () => {
  if (twoFactorAuthStatus.value === '1') {
    BindTFADrawer.showUnbind({
      sysSeqId: twoFactorAuthSysSeqId.value,
      onSuccess: () => {
        ElMessage.success(t('setting.setValidator.unbindSuccess'))
        getUserInfo()
      }
    })
  } else {
    BindTFADrawer.show({
      onSuccess: () => {
        ElMessage.success(t('setting.setValidator.bindSuccess'))
        getUserInfo()
      }
    })
  }
}
const handlePasswordCancel = () => {
  showEditPasswordDialog.value = false
}
</script>

<style lang="scss" scoped>
.add-btn {
  padding: 6px 12px;
  border: 1px dashed #030814;
  border-radius: 6px;
}
</style>