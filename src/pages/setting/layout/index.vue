<template>
  <div class="flex h-[calc(100vh-56px)] bg-[#f7f8fa]">
    <SideMenu v-model:activeItem="activeItem" :menuItems="menuItems" />
    <div class="flex-grow bg-white px-40px py-32px overflow-y-auto">
      <Personal v-if="activeItem === 'account-info'" />
      <!-- <Security v-if="activeItem === 'security-setting'" /> -->
      <Agreement v-if="activeItem === 'agreement'" />
      <Enterprise v-if="activeItem === 'enterprise'" />
      <Currency v-if="activeItem === 'fiat-account'" />
      <Address v-if="activeItem === 'crypto-address'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { t } from '@@/i18n';
import SideMenu from '../components/SideMenu.vue';
import type { MenuItem } from '../components/SideMenu.vue';
import Personal from '../personal/index.vue';
import Agreement from '../agreement/index.vue';
import Enterprise from '../enterprise/index.vue';
import Address from '../address/index.vue';
import Currency from '../currency/index.vue';
// import Security from '../security/index.vue';

const route = useRoute();
const activeItem = ref((route.query.activeItem as string) || 'enterprise');

watch(
  () => route.query.activeItem,
  (newValue) => {
    if (newValue && typeof newValue === 'string') {
      activeItem.value = newValue;
    }
  },
);

const menuItems = computed<MenuItem[]>(() => [
  {
    id: 'enterprise-group',
    name: t('setting.layout.enterpriseGroup'),
    icon: 'icon-setting-enterprise',
    subItems: [
      { id: 'enterprise', name: t('setting.layout.enterpriseInfo') },
      { id: 'fiat-account', name: t('setting.layout.fiatAccount') },
      { id: 'crypto-address', name: t('setting.layout.cryptoAddress') },
    ],
  },
  {
    id: 'personal-group',
    name: t('setting.layout.personalGroup'),
    icon: 'icon-setting-personal',
    subItems: [{ id: 'account-info', name: t('setting.layout.accountInfo') }],
  },
  {
    id: 'agreement-group',
    name: t('setting.layout.agreementGroup'),
    icon: 'icon-setting-agreement',
    subItems: [{ id: 'agreement', name: t('setting.layout.allAgreements') }],
  },
]);
</script>