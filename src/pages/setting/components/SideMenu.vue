<template>
<div class="w-176px flex-shrink-0 bg-white">
  <div class=" border-l-1 border-l-solid border-l-[#E5E6EB] mt-20px pt-2px">
    <div v-for="group in menuItems" :key="group.id" class="menu-group mx-16px ">
      <div
        class="group-header flex items-center mt-14px pl-16px"
      >
        <SvgIcon v-if="group.icon" :name="group.icon" class="h-15px w-auto mr-12px" />
        <span class="font-family-[PingFangSC-Semibold] font-semibold text-14px text-[#222527]">{{ group.name }}</span>
      </div>
      <ul class="relative m-0 list-none p-0 mt-16px">
        <li
          v-for="item in group.subItems"
          :key="item.id"
          class="menu-item group h-56px relative flex cursor-pointer items-center mt-2px justify-between py-[10px] pl-[16px] transition-all duration-300 rounded-6px hover:bg-[#f2f3f5]"
          :class="{'bg-[#FFEBF0] border-6px;': activeItem === item.id}"
          @click="handleItemClick(item)" 
        >
          <span
            class="text-14px"
            :class="{
              'font-family-[PingFangSC-Semibold] font-semibold text-[#FF0064]': activeItem === item.id,
              'font-family-[PingFangSC-Regular] font-weight-400 text-[#6B7275]': activeItem !== item.id
            }"
            >{{ item.name }}</span
          >
        </li>
      </ul>
    </div>
  </div>
</div>
</template>

<script setup lang="ts">
// 定义图标名称类型，与 SvgIcon 组件保持一致
export type IconName = "icon-setting-agreement" | "icon-setting-company" | "icon-setting-email" | "icon-setting-enterprise" | "icon-setting-permission" | "icon-setting-person" | "icon-setting-personal";

export type SubMenuItem = {
  id: string;
  name: string;
};

export type MenuItem = {
  id: string;
  name: string;
  icon: IconName;
  subItems: SubMenuItem[];
};

defineProps<{
  activeItem: string;
  menuItems: MenuItem[];
}>();

const emit = defineEmits<{
  (e: 'update:activeItem', id: string): void;
}>();

const handleItemClick = (item: SubMenuItem) => {
  emit('update:activeItem', item.id);
};
</script> 