import StepComponent from './index.vue';

export default StepComponent;
export { StepComponent };

// 导出类型定义
export interface StepInfo {
  title: string;
  description?: string;
}

export interface StepComponentProps {
  /** 当前步骤 */
  modelValue?: number;
  /** 步骤配置 */
  steps?: StepInfo[];
}

export interface StepComponentEmits {
  (e: 'update:modelValue', value: number): void;
  (e: 'step-change', step: number): void;
  (e: 'complete'): void;
}
