<template>
  <div class="step-component">
    <div class="step-container">
      <!-- 步骤1 -->
      <div class="step-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <SvgIcon v-if="currentStep > 1" name="icon-success" class="step-check" />
        <div v-else class="step-circle">
          <span class="step-number">1</span>
        </div>
        <div class="step-content">
          <div class="step-title">{{ steps[0]?.title || '' }}</div>
        </div>
      </div>

      <!-- 连接线 -->
      <div class="step-connector" :class="{ active: currentStep > 1 }"></div>

      <!-- 步骤2 -->
      <div class="step-item" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
        <SvgIcon v-if="currentStep > 2" name="icon-success" class="step-check" />
        <div v-else class="step-circle">
          <span class="step-number">2</span>
        </div>
        <div class="step-content">
          <div class="step-title">{{ steps[1]?.title || '' }}</div>
        </div>
      </div>
    </div>

    <!-- 步骤内容区域 -->
    <div class="step-content-area">
      <slot :current-step="currentStep" :next-step="nextStep" :prev-step="prevStep"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import SvgIcon from '~virtual/svg-component'

/**
 * 步骤信息接口
 */
interface StepInfo {
  title: string
  description?: string
}

/**
 * 组件属性定义
 */
interface Props {
  /** 当前步骤 */
  modelValue?: number
  /** 步骤配置 */
  steps?: StepInfo[]
  /** 是否可以进行下一步 */
  canProceed?: boolean
}

/**
 * 组件事件定义
 */
interface Emits {
  (e: 'update:modelValue', value: number): void
  (e: 'step-change', step: number): void
  (e: 'complete'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 1,
  steps: () => [
    { title: '', description: '' },
    { title: '', description: '' }
  ],
})

const emit = defineEmits<Emits>()

// 当前步骤
const currentStep = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 总步骤数
const totalSteps = computed(() => props.steps.length)

/**
 * 下一步操作
 */
const nextStep = () => {
  if (currentStep.value < totalSteps.value) {
    const newStep = currentStep.value + 1
    currentStep.value = newStep
    emit('step-change', newStep)
  }
}

/**
 * 上一步操作
 */
const prevStep = () => {
  if (currentStep.value > 1) {
    const newStep = currentStep.value - 1
    currentStep.value = newStep
    emit('step-change', newStep)
  }
}

/**
 * 完成操作
 */
const handleComplete = () => {
  emit('complete')
}

// 监听步骤变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal !== currentStep.value) {
      emit('step-change', newVal)
    }
  }
)
</script>

<style lang="scss" scoped>
.step-component {
  width: 100%;
}

.step-container {
  /* 响应式边距：最大170px，根据父容器宽度自动调整并保持居中 */
  margin-left: min(170px, max(0px, calc((100% - 344px) / 2)));
  margin-right: min(170px, max(0px, calc((100% - 344px) / 2)));
  min-width: 344px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
}

.step-item {
  display: flex;
  align-items: center;
  position: relative;

  .step-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    color: #6B7275;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    font-size: 16px;
    font-weight: 600;
    margin-right: 10px;
    transition: all 0.3s ease;

    .step-number {
      font-size: 16px;
    }
  }

  .step-content {
    .step-title {
      font-size: 16px;
      font-weight: 600;
      color: #6B7275;
      transition: color 0.3s ease;
    }
  }

  &.active {
    .step-circle {
      background-color: #ff0064;
      border-color: #ff0064;
      color: #fff;
    }

    .step-content .step-title {
      color: #222527;
    }
  }

  &.completed {
    .step-circle {
      background-color: #ff0064;
      border-color: #ff0064;
      color: #fff;
    }

    .step-content .step-title {
      color: #222527;
    }
  }
}

.step-check {
  font-size: 20px !important;
  color: #ff0064;
  margin-right: 10px;
}

.step-connector {
  flex: 1;
  height: 1px;
  background: #E5E6EB;
  border-radius: 1px;
  margin: 0 18px;
  transition: background-color 0.3s ease;

  &.active {
    background-color: #ff0064;
  }
}

.step-content-area {
  margin: 25px 0;
}
</style>