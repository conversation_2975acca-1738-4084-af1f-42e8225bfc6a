<template>
  <el-drawer v-model="showDrawer" size="400px" style="--el-drawer-padding-primary: 0px"
    header-class="custom-drawer-header" :close-on-click-modal="false" :destroy-on-close="true"
    :close-on-press-escape="false">
    <template #header>
      <div
        class="font-family-[PingFangSC-Medium] text-18px color-[#222527] h-60px border-b-1 mb-0 flex-row flex-items-center pt-18px pb-18px pl-24px">
        <span>{{ t('setting.editEmail.title') }}</span>
      </div>
    </template>
    <template #default>
      <div class="p-24px">
        <!-- 说明文字 -->
        <div class="mb-24px">
          <p class="font-family-[PingFangSC-Regular] text-14px color-[#6B7275] leading-20px m-0">
            <span v-html="t('setting.editEmail.description', {
              email: `<span class='color-[#222527]'>${currentEmail}</span>`,
            })
              "></span>
          </p>
        </div>

        <!-- 新邮箱输入 -->
        <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" hide-required-asterisk>
          <el-form-item :label="t('setting.editEmail.newEmail')" prop="newEmail"
            class="font-family-[PingFangSC-Regular] text-14px color-[#222527] mb-24px">
            <el-input v-model="formData.newEmail" :placeholder="t('setting.editEmail.placeholder')" class="w-full"
              :disabled="isProcessing" />
          </el-form-item>

          <!-- 邮箱验证码 -->
          <el-form-item :label="t('setting.editEmail.verificationCode')" prop="verificationCode"
            class="font-family-[PingFangSC-Regular] text-14px color-[#222527] mb-6px">
            <div class="verification-code-container">
              <el-input v-model="formData.verificationCode" maxlength="6"
                :placeholder="t('setting.editEmail.codePlaceholder')" class="verification-code-input"
                :disabled="isProcessing">
                <template #suffix>
                  <el-button :disabled="!isEmailValid || countdown > 0 || isSendingCode" @click="sendVerificationCode"
                    class="send-code-btn" :loading="isSendingCode">
                    {{ countdown > 0 ? `${countdown}s` : t('setting.editEmail.sendCode') }}
                  </el-button>
                </template>
              </el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div style="border-top: 1px solid #e5e6eb"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
        <el-button
          class="cancel-btn w-68px h-32px text-14px text-[#222527] font-family-[PingFangSC-Regular] mr-8px btn-hover-scale-sm"
          @click="cancelShowDrawer" :disabled="isProcessing">
          {{ t('setting.editEmail.cancel') }}
        </el-button>
        <el-button
          class="confirm-btn w-68px h-32px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm"
          @click="submit" :loading="isProcessing">
          {{ t('setting.editEmail.confirm') }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref, reactive, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { t } from '@@/i18n';
import { sendEmailCodeApi, emailVerifyApi, modifyEmailApi } from '../apis';
import { useUserStore } from '@/pinia/stores/user';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'EditEmail',
});

interface EmailInfo {
  newEmail: string;
  verificationCode: string;
}

const props = withDefaults(
  defineProps<{
    currentEmail: string;
    modelValue: boolean;
  }>(),
  {
    modelValue: true,
    currentEmail: '<EMAIL>',
  }
);

const emit = defineEmits<{
  (e: 'success'): void;
  (e: 'cancel'): void;
  (e: 'update:modelValue', value: boolean): void;
}>();

const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const userStore = useUserStore();
const router = useRouter();

const formData = ref<EmailInfo>({
  newEmail: '',
  verificationCode: '',
});

const formRef = ref<FormInstance>();
const countdown = ref(0);
const isSendingCode = ref(false);
const isProcessing = ref(false);
const sendSeqId = ref<string>('');

// 邮箱格式验证
const isEmailValid = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(formData.value.newEmail) && formData.value.newEmail !== props.currentEmail;
});

const rules = computed(() =>
  reactive<FormRules<EmailInfo>>({
    newEmail: [
      { required: true, message: t('setting.editEmail.newEmailRequired'), trigger: 'blur' },
      { type: 'email', message: t('setting.editEmail.emailFormatError'), trigger: 'blur' },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value === props.currentEmail) {
            callback(new Error(t('setting.editEmail.sameEmailError')));
          } else {
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
    verificationCode: [
      { required: true, message: t('setting.editEmail.verificationCodeRequired'), trigger: 'blur' },
      { min: 6, max: 6, message: t('setting.editEmail.verificationCodeLength'), trigger: 'blur' },
    ],
  })
);

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // 重置表单
      formData.value.newEmail = '';
      formData.value.verificationCode = '';
      countdown.value = 0;
      sendSeqId.value = '';
      isProcessing.value = false;
      isSendingCode.value = false;
      formRef.value?.clearValidate();
    }
  }
);

// 发送验证码
const sendVerificationCode = async () => {
  if (!isEmailValid.value || isSendingCode.value || countdown.value > 0) return;

  // 验证邮箱格式
  try {
    await formRef.value?.validateField('newEmail');
  } catch {
    ElMessage.error(t('setting.editEmail.emailFormatError'));
    return;
  }

  isSendingCode.value = true;

  try {
    const response = await sendEmailCodeApi({
      newEmail: formData.value.newEmail,
      businessType: 'CHANGE_EMAIL',
    });

    sendSeqId.value = response.data.sendSeqId;
    ElMessage.success(t('setting.editEmail.sendCodeSuccess'));

    // 开始倒计时
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  } catch (error: any) {
    console.error('发送验证码失败:', error);
    // ElMessage.error(error.message || t('setting.editEmail.sendCodeFailed'));
  } finally {
    isSendingCode.value = false;
  }
};

const submit = async () => {
  if (!formRef.value || isProcessing.value) return;

  try {
    // 验证表单
    await formRef.value.validate();

    if (!sendSeqId.value) {
      ElMessage.error(t('setting.editEmail.pleaseGetCode'));
      return;
    }

    isProcessing.value = true;

    // 首先验证验证码
    await emailVerifyApi({
      sendSeqId: sendSeqId.value,
      verifyCode: formData.value.verificationCode,
    });

    // 验证码正确后，修改邮箱
    await modifyEmailApi({
      sendSeqId: sendSeqId.value,
    });

    ElMessage.success(t('setting.editEmail.updateSuccess'));

    // 修改邮箱成功后退出登录
    setTimeout(() => {
      userStore.logout();
      router.push('/login');
    }, 1500); // 延迟1.5秒让用户看到成功消息
  } catch (error: any) {
    console.error('修改邮箱失败:', error);
    // ElMessage.error(error.message || t('setting.editEmail.updateFailed'));
  } finally {
    isProcessing.value = false;
  }
};

const cancelShowDrawer = () => {
  emit('cancel');
};
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

:deep(.el-input__wrapper) {
  border: 1px solid #e5e6eb;
  box-shadow: none;
  border-radius: 6px;
}

:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
  margin-bottom: 8px;
}

.cancel-btn {
  background-color: #ffffff !important;
  border: 1px solid #e5e6eb !important;
  color: #222527 !important;
  border-radius: 6px !important;
}

.cancel-btn:hover {
  background-color: #f8f9fa !important;
  border-color: #e5e6eb !important;
}

.confirm-btn {
  background-color: #030814 !important;
  border: 1px solid #030814 !important;
  color: #ffffff !important;
  border-radius: 6px !important;
  margin-left: 0px !important;
}

.confirm-btn:hover {
  background-color: #1a1f2e !important;
  border-color: #1a1f2e !important;
}

/* 验证码容器样式 */
.verification-code-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 0;
  position: relative;
}

.verification-code-input {
  flex: 1;

  :deep(.el-input__wrapper) {
    border-radius: 6px !important;
    height: 40px;
    padding: 0 12px;
  }

  :deep(.el-input__inner) {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #222527;

    &::placeholder {
      color: #a7adb0;
      font-size: 14px;
    }
  }
}

.send-code-btn {
  background: transparent !important;
  border: none !important;
  color: #ff0064 !important;
  border-radius: 6px !important;
  width: 100px;
  height: 40px;
  font-size: 14px;
  font-family: 'PingFangSC-Regular';
  flex-shrink: 0;
  margin-left: 0;
  margin-top: 0;
  padding: 0;
}

.send-code-btn:hover:not(:disabled) {
  background: transparent !important;
}

.send-code-btn:disabled {
  background: transparent !important;
  border: none !important;
  color: #a7adb0 !important;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;

  .el-drawer__header {
    border-bottom: 1px solid #e5e6eb;
  }

  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
</style>
