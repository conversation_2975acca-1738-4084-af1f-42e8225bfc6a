<template>
  <el-drawer v-model="showDrawer" size="50%" header-class="custom-drawer-header" :close-on-click-modal="false"
    :show-close="false" :destroy-on-close="true" :close-on-press-escape="false">
    <template #header>
      <div
        class="h-60px mb-0 flex justify-between flex-row flex-items-center text-18px font-600 color-[#222527] py-18px px-24px">
        <span>{{ title }}</span>
        <svgIcon name="icon-close" class="text-16px cursor-pointer color-#6B7275" @click="handleClose" />
      </div>

    </template>
    <template #default>
      <div>
        <!-- 创建和编辑模式：显示步骤组件 -->
        <StepComponent v-if="props.mode === 'create' || props.mode === 'edit'" v-model="currentStep" :steps="stepConfig"
          :show-buttons="true" @step-change="handleStepChange" @complete="handleStepComplete">
          <template #default="{ currentStep }">
            <div class="step-content-wrapper">
              <div v-if="currentStep === 1" class="step-1-content">
                <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" class="pb-50px"
                  hide-required-asterisk>
                  <span class="form-section-title">{{ t('setting.editCurrency.section.payeeInfo') }}</span>
                  <el-row :gutter="76">
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.payeeName')" prop="payeeName">
                        <el-input v-model="formData.payeeName"
                          :placeholder="t('setting.editCurrency.placeholders.input')"
                          :disabled="isFieldDisabled('payeeName')" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item>
                        <template #label>
                          <span>{{ t('setting.editCurrency.labels.nickname') }}</span>
                          <span class="text-14px text-[#6b7275]">{{
                            t('setting.editCurrency.labels.optional')
                            }}</span>
                        </template>
                        <el-input v-model="formData.acctNickName"
                          :placeholder="t('setting.editCurrency.placeholders.input')"
                          :disabled="isFieldDisabled('acctNickName')" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="76">
                    <el-col :span="20">
                      <el-form-item :label="t('setting.editCurrency.labels.addressSameAsCompany')"
                        prop="useQualificationInfo">
                        <el-switch :model-value="formData.useQualificationInfo === 'Y'"
                          :disabled="isFieldDisabled('useQualificationInfo')"
                          @update:model-value="(value) => (formData.useQualificationInfo = value ? 'Y' : 'N')" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-if="formData.useQualificationInfo !== 'Y'">
                    <el-col :span="24">
                      <el-form-item :label="t('setting.editCurrency.labels.streetAddress')" prop="payeeAddr">
                        <el-input v-model="formData.payeeAddr"
                          :placeholder="t('setting.editCurrency.placeholders.input')"
                          :disabled="isFieldDisabled('payeeAddr')" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-if="formData.useQualificationInfo !== 'Y'" :gutter="76">
                    <el-col :span="12">
                      <el-form-item prop="payeeApartment">
                        <template #label>
                          <span>{{ t('setting.editCurrency.labels.apartment') }}</span>
                          <span class="text-14px text-[#6b7275]">{{
                            t('setting.editCurrency.labels.optional')
                            }}</span>
                        </template>
                        <el-input v-model="formData.payeeApartment"
                          :placeholder="t('setting.editCurrency.placeholders.input')"
                          :disabled="isFieldDisabled('payeeApartment')" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.city')" prop="payeeCity">
                        <el-input v-model="formData.payeeCity"
                          :placeholder="t('setting.editCurrency.placeholders.input')"
                          :disabled="isFieldDisabled('payeeCity')" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-if="formData.useQualificationInfo !== 'Y'" :gutter="76">
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.state')" prop="payeeState">
                        <el-input v-model="formData.payeeState"
                          :placeholder="t('setting.editCurrency.placeholders.input')"
                          :disabled="isFieldDisabled('payeeState')" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="payeePostalCode">
                        <template #label>
                          <div class="flex items-center">
                            <div class="mr-4px">{{ t('setting.editCurrency.labels.postalCode') }}</div>
                            <el-tooltip placement="right">
                              <template #content>
                                <p style="margin: 0">
                                  {{ t('setting.editCurrency.rules.businessPostalCodeTooltip') }}
                                </p>
                              </template>
                              <svg-icon name="icon-info" class="text-13.3px color-[#A7ADB0] ml-3px" />
                            </el-tooltip>
                          </div>
                        </template>
                        <el-input v-model="formData.payeePostalCode"
                          :placeholder="t('setting.editCurrency.placeholders.input')"
                          :disabled="isFieldDisabled('payeePostalCode')"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-if="formData.useQualificationInfo !== 'Y'" :gutter="76">
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.payeeCountry')" prop="payeeCtry">
                        <el-select v-model="formData.payeeCtry" filterable
                          :placeholder="t('setting.editCurrency.placeholders.select')" style="width: 100%"
                          :disabled="isFieldDisabled('payeeCtry')">
                          <template #prefix>
                            <img v-if="formData.payeeCtry" :src="getRegionIcon(formData.payeeCtry)" width="16" />
                          </template>
                          <el-option v-for="info in regionList" :key="info.enumCode" :label="info.enumDescCn"
                            :value="info.enumCode">
                            <p style="display: flex; align-items: center; margin: 0; height: 40px">
                              <img style="margin-right: 8px" :src="getRegionIcon(info.enumCode)" width="20" />
                              {{ info.enumDescCn }}
                            </p>
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <div class="w-full h-[1px] mt-8px bg-[#EDEDEE]" />
                  <span class="form-section-title">{{ t('setting.editCurrency.section.payeeAccount') }}</span>
                  <el-row :gutter="76">
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.accountType')" prop="payeeAcctType">
                        <el-select v-model="formData.payeeAcctType"
                          :placeholder="t('setting.editCurrency.placeholders.select')" style="width: 100%"
                          :disabled="isFieldDisabled('payeeAcctType')">
                          <el-option v-for="info in accountTypeList" :key="info.enumCode" :label="info.enumDescCn"
                            :value="info.enumCode" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.accountNumber')" prop="acctNo">
                        <el-input v-model="formData.acctNo" :placeholder="t('setting.editCurrency.placeholders.input')"
                          :disabled="isFieldDisabled('acctNo')" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="76">
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.accountCurrency')" prop="ccy">
                        <el-select v-model="formData.ccy" :placeholder="t('setting.editCurrency.placeholders.select')"
                          style="width: 100%" :disabled="isFieldDisabled('ccy')">
                          <template #prefix>
                            <img v-if="formData.ccy" :src="getCurrencyIcon(formData.ccy)" width="16" />
                          </template>
                          <el-option v-for="info in currencyList" :key="info.enumCode" :label="info.enumDescCn"
                            :value="info.enumCode">
                            <p style="display: flex; align-items: center; margin: 0; height: 40px">
                              <img style="margin-right: 8px" :src="getCurrencyIcon(info.enumCode)" width="20" />
                              {{ info.enumDescCn }}
                            </p>
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.paymentMethod')" prop="payMethod">
                        <el-select v-model="formData.payMethod"
                          :placeholder="t('setting.editCurrency.placeholders.select')" style="width: 100%"
                          :disabled="isFieldDisabled('payMethod')">
                          <el-option v-for="info in transferMethodList" :key="info.enumCode" :label="info.enumDescCn"
                            :value="info.enumCode" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <div class="w-full h-[1px] mt-8px mb-24px bg-[#EDEDEE]" />
                  <span class="form-section-title">{{ t('setting.editCurrency.section.payeeBank') }}</span>
                  <el-row :gutter="76">
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.bankName')" prop="acctName">
                        <el-tooltip :content="formData.acctName" placement="top" :disabled="!formData.acctName"
                          popper-class="custom-tooltip">
                          <el-input v-model="formData.acctName"
                            :placeholder="t('setting.editCurrency.placeholders.input')"
                            :disabled="isFieldDisabled('acctName')" />
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.bankCountry')" prop="bankCtry">
                        <el-select v-model="formData.bankCtry" filterable
                          :placeholder="t('setting.editCurrency.placeholders.select')" style="width: 100%"
                          :disabled="isFieldDisabled('bankCtry')">
                          <template #prefix>
                            <img v-if="formData.bankCtry" :src="getRegionIcon(formData.bankCtry)" width="16" />
                          </template>
                          <el-option v-for="info in regionList" :key="info.enumCode" :label="info.enumDescCn"
                            :value="info.enumCode">
                            <p style="display: flex; align-items: center; margin: 0; height: 40px">
                              <img style="margin-right: 8px" :src="getRegionIcon(info.enumCode)" width="20" />
                              {{ info.enumDescCn }}
                            </p>
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="76">
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.bankAddress')" prop="bankAddr">
                        <el-tooltip :content="formData.bankAddr" placement="top" :disabled="!formData.bankAddr"
                          popper-class="custom-tooltip">
                          <el-input v-model="formData.bankAddr"
                            :placeholder="t('setting.editCurrency.placeholders.input')"
                            :disabled="isFieldDisabled('bankAddr')" />
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="t('setting.editCurrency.labels.swiftCode')" prop="swiftCode">
                        <el-input v-model="formData.swiftCode"
                          :placeholder="t('setting.editCurrency.placeholders.input')"
                          :disabled="isFieldDisabled('swiftCode')" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
              <div v-if="currentStep === 2" class="flex justify-center">
                <!--完善第二步 -->
                <div class="w-full step-two">
                  <IdValidator v-if="props.mode === 'create' || props.mode === 'edit'" ref="idValidatorRef"
                    :business-type="props.mode === 'create' ? FeeEnumType.FIAT_ACCT_BIND : FeeEnumType.FIAT_ACCT_MODIFY" />
                </div>
              </div>
            </div>
          </template>
        </StepComponent>

        <!-- 查看模式：显示原有的表单内容 -->
        <div v-if="props.mode === 'view'">
          <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" class="pb-50px"
            hide-required-asterisk>
            <span class="form-section-title">{{ t('setting.editCurrency.section.payeeInfo') }}</span>
            <el-row :gutter="76">
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.payeeName')" prop="payeeName">
                  <el-input v-model="formData.payeeName" :placeholder="t('setting.editCurrency.placeholders.input')"
                    :disabled="isFieldDisabled('payeeName')" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item>
                  <template #label>
                    <span>{{ t('setting.editCurrency.labels.nickname') }}</span>
                    <span class="text-14px text-[#6b7275]">{{ t('setting.editCurrency.labels.optional') }}</span>
                  </template>
                  <el-input v-model="formData.acctNickName" :placeholder="t('setting.editCurrency.placeholders.input')"
                    :disabled="isFieldDisabled('acctNickName')" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="76">
              <el-col :span="20">
                <el-form-item :label="t('setting.editCurrency.labels.addressSameAsCompany')"
                  prop="useQualificationInfo">
                  <el-switch :model-value="formData.useQualificationInfo === 'Y'"
                    :disabled="isFieldDisabled('useQualificationInfo')"
                    @update:model-value="(value) => (formData.useQualificationInfo = value ? 'Y' : 'N')" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="formData.useQualificationInfo !== 'Y'">
              <el-col :span="24">
                <el-form-item :label="t('setting.editCurrency.labels.streetAddress')" prop="payeeAddr">
                  <el-input v-model="formData.payeeAddr" :placeholder="t('setting.editCurrency.placeholders.input')"
                    :disabled="isFieldDisabled('payeeAddr')" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="formData.useQualificationInfo !== 'Y'" :gutter="76">
              <el-col :span="12">
                <el-form-item prop="payeeApartment">
                  <template #label>
                    <span>{{ t('setting.editCurrency.labels.apartment') }}</span>
                    <span class="text-14px text-[#6b7275]">{{ t('setting.editCurrency.labels.optional') }}</span>
                  </template>
                  <el-input v-model="formData.payeeApartment"
                    :placeholder="t('setting.editCurrency.placeholders.input')"
                    :disabled="isFieldDisabled('payeeApartment')" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.city')" prop="payeeCity">
                  <el-input v-model="formData.payeeCity" :placeholder="t('setting.editCurrency.placeholders.input')"
                    :disabled="isFieldDisabled('payeeCity')" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="formData.useQualificationInfo !== 'Y'" :gutter="76">
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.state')" prop="payeeState">
                  <el-input v-model="formData.payeeState" :placeholder="t('setting.editCurrency.placeholders.input')"
                    :disabled="isFieldDisabled('payeeState')" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="payeePostalCode">
                  <template #label>
                    <div class="flex items-center">
                      <div class="mr-4px">{{ t('setting.editCurrency.labels.postalCode') }}</div>
                      <el-tooltip placement="right">
                        <template #content>
                          <p style="margin: 0">
                            {{ t('setting.editCurrency.rules.businessPostalCodeTooltip') }}
                          </p>
                        </template>
                        <svg-icon name="icon-info" class="text-13.3px color-[#A7ADB0] ml-3px" />
                      </el-tooltip>
                    </div>
                  </template>
                  <el-input v-model="formData.payeePostalCode"
                    :placeholder="t('setting.editCurrency.placeholders.input')"
                    :disabled="isFieldDisabled('payeePostalCode')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="formData.useQualificationInfo !== 'Y'" :gutter="76">
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.payeeCountry')" prop="payeeCtry">
                  <el-select v-model="formData.payeeCtry" filterable
                    :placeholder="t('setting.editCurrency.placeholders.select')" style="width: 100%"
                    :disabled="isFieldDisabled('payeeCtry')">
                    <template #prefix>
                      <img v-if="formData.payeeCtry" :src="getRegionIcon(formData.payeeCtry)" width="16" />
                    </template>
                    <el-option v-for="info in regionList" :key="info.enumCode" :label="info.enumDescCn"
                      :value="info.enumCode">
                      <p style="display: flex; align-items: center; margin: 0; height: 40px">
                        <img style="margin-right: 8px" :src="getRegionIcon(info.enumCode)" width="20" />
                        {{ info.enumDescCn }}
                      </p>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="w-full h-[1px] mt-8px bg-[#EDEDEE]" />
            <span class="form-section-title">{{ t('setting.editCurrency.section.payeeAccount') }}</span>
            <el-row :gutter="76">
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.accountType')" prop="payeeAcctType">
                  <el-select v-model="formData.payeeAcctType"
                    :placeholder="t('setting.editCurrency.placeholders.select')" style="width: 100%"
                    :disabled="isFieldDisabled('payeeAcctType')">
                    <el-option v-for="info in accountTypeList" :key="info.enumCode" :label="info.enumDescCn"
                      :value="info.enumCode" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.accountNumber')" prop="acctNo">
                  <el-input v-model="formData.acctNo" :placeholder="t('setting.editCurrency.placeholders.input')"
                    :disabled="isFieldDisabled('acctNo')" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="76">
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.accountCurrency')" prop="ccy">
                  <el-select v-model="formData.ccy" :placeholder="t('setting.editCurrency.placeholders.select')"
                    style="width: 100%" :disabled="isFieldDisabled('ccy')">
                    <template #prefix>
                      <img v-if="formData.ccy" :src="getCurrencyIcon(formData.ccy)" width="16" />
                    </template>
                    <el-option v-for="info in currencyList" :key="info.enumCode" :label="info.enumDescCn"
                      :value="info.enumCode">
                      <p style="display: flex; align-items: center; margin: 0; height: 40px">
                        <img style="margin-right: 8px" :src="getCurrencyIcon(info.enumCode)" width="20" />
                        {{ info.enumDescCn }}
                      </p>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.paymentMethod')" prop="payMethod">
                  <el-select v-model="formData.payMethod" :placeholder="t('setting.editCurrency.placeholders.select')"
                    style="width: 100%" :disabled="isFieldDisabled('payMethod')">
                    <el-option v-for="info in transferMethodList" :key="info.enumCode" :label="info.enumDescCn"
                      :value="info.enumCode" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="w-full h-[1px] mt-8px mb-24px bg-[#EDEDEE]" />
            <span class="form-section-title">{{ t('setting.editCurrency.section.payeeBank') }}</span>
            <el-row :gutter="76">
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.bankName')" prop="acctName">
                  <el-tooltip :content="formData.acctName" placement="top" :disabled="!formData.acctName"
                    popper-class="custom-tooltip">
                    <el-input v-model="formData.acctName" :placeholder="t('setting.editCurrency.placeholders.input')"
                      :disabled="isFieldDisabled('acctName')" />
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.bankCountry')" prop="bankCtry">
                  <el-select v-model="formData.bankCtry" filterable
                    :placeholder="t('setting.editCurrency.placeholders.select')" style="width: 100%"
                    :disabled="isFieldDisabled('bankCtry')">
                    <template #prefix>
                      <img v-if="formData.bankCtry" :src="getRegionIcon(formData.bankCtry)" width="16" />
                    </template>
                    <el-option v-for="info in regionList" :key="info.enumCode" :label="info.enumDescCn"
                      :value="info.enumCode">
                      <p style="display: flex; align-items: center; margin: 0; height: 40px">
                        <img style="margin-right: 8px" :src="getRegionIcon(info.enumCode)" width="20" />
                        {{ info.enumDescCn }}
                      </p>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="76">
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.bankAddress')" prop="bankAddr">
                  <el-tooltip :content="formData.bankAddr" placement="top" :disabled="!formData.bankAddr"
                    popper-class="custom-tooltip">
                    <el-input v-model="formData.bankAddr" :placeholder="t('setting.editCurrency.placeholders.input')"
                      :disabled="isFieldDisabled('bankAddr')" />
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('setting.editCurrency.labels.swiftCode')" prop="swiftCode">
                  <el-input v-model="formData.swiftCode" :placeholder="t('setting.editCurrency.placeholders.input')"
                    :disabled="isFieldDisabled('swiftCode')" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </template>
    <template #footer>
      <!-- 底部按钮 -->
      <div style="border-top: 1px solid #e5e6eb"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end  p-24px w-full h-64px">
        <!-- 右侧：取消、上一步和提交按钮 -->
        <el-button v-if="props.mode !== 'view'" class="btn-hover-scale-sm cancel-btn mr-12px"
          @click="cancelShowDrawer">{{
            t('setting.editCurrency.buttons.cancel')
          }}</el-button>
        <el-button v-if="(props.mode === 'create' || props.mode === 'edit') && currentStep === 2" type="primary"
          class="btn-hover-scale-sm" @click="handlePrevStep">
          {{ t('setting.editCurrency.buttons.previous') }}
        </el-button>
        <el-button v-if="props.mode !== 'view'" type="primary" class="btn-hover-scale-sm" @click="submit">{{
          currentStep === 1 ? t('setting.editCurrency.buttons.next') : t('setting.editCurrency.buttons.submit')
          }}</el-button>
        <el-button v-if="props.mode === 'view'" type="primary" class="btn-hover-scale-sm" @click="handleClose">{{
          t('setting.editCurrency.buttons.close')
          }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref as vueRef, reactive, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import type { FiatAcctInfo } from '../apis/type';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { cloneDeep } from 'lodash-es';
import { BusinessEnumType } from '@/common/apis/common/type';
import { getRegionIcon, getCurrencyIcon } from '@/common/utils/imageUtils';
import { t } from '@@/i18n';
import StepComponent from './StepComponent';
import type { StepInfo } from './StepComponent';
import { FeeEnumType } from '@/pages/exchange/apis/type';
import IdValidator from '@/common/components/IdValidator/index.vue';

// 定义IdValidator组件类型
type IdValidatorInstance = InstanceType<typeof IdValidator>;

const enumStore = useEnumStore();

const regionList = enumStore.getEnumList(BusinessEnumType.NATIONALITY);
const accountTypeList = enumStore.getEnumList(BusinessEnumType.PAYEE_ACCT_TYPE);
const currencyList = enumStore.getEnumList(BusinessEnumType.FIAT_CCY);
const transferMethodList = enumStore.getEnumList(BusinessEnumType.TRANSFER_METHOD);
const props = withDefaults(
  defineProps<{
    currencyInfo: FiatAcctInfo;
    modelValue: boolean;
    mode: 'create' | 'edit' | 'view';
  }>(),
  {
    modelValue: true,
    mode: 'create',
  }
);

const title = computed(() => {
  if (props.mode === 'create') return t('setting.editCurrency.title.create');
  if (props.mode === 'edit') return t('setting.editCurrency.title.edit');
  return t('setting.editCurrency.title.view');
});

const isFieldDisabled = (field: keyof FiatAcctInfo) => {
  if (props.mode === 'view') {
    return true;
  }
  if (props.mode === 'edit') {
    if (props.currencyInfo.status === 'approved') {
      return field !== 'acctNickName';
    }
    if (props.currencyInfo.status === 'rejected') {
      return false;
    }
  }
  return false;
};

const emit = defineEmits<{
  (e: 'submit', payload: FiatAcctInfo, res?: any): void;
  (e: 'cancel'): void;
  (e: 'update:modelValue', value: boolean): void;
}>();

// 使用计算属性实现双向绑定
const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const formData = vueRef<FiatAcctInfo>(cloneDeep(props.currencyInfo));

const formRef = vueRef<FormInstance>();

// 步骤组件相关数据
const currentStep = vueRef(1);

/**
 * 动态步骤配置，第一个步骤标题会响应式地跟随页面标题变化
 */
const stepConfig = computed<StepInfo[]>(() => [
  { title: title.value },
  { title: t('setting.editCurrency.steps.identityVerification') }
]);

/**
 * 重置组件状态到初始值
 */
const resetComponentState = () => {
  // 重置表单数据
  formData.value = cloneDeep(props.currencyInfo);
  // 重置步骤
  currentStep.value = 1;
  // 清除表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 监听modelValue变化，当组件打开时重置状态
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      // 组件打开时重置所有状态
      resetComponentState();
    }
  },
  { immediate: false }
);

// 监听currencyInfo变化，确保数据同步
watch(
  () => props.currencyInfo,
  (newValue) => {
    if (props.modelValue) {
      // 只有在组件打开状态下才更新数据
      formData.value = cloneDeep(newValue);
    }
  },
  { deep: true, immediate: false }
);

/**
 * 处理步骤变化
 */
const handleStepChange = (step: number) => {
  console.log('步骤变化:', step);
};

/**
 * 处理步骤完成
 */
const handleStepComplete = () => {
  console.log('所有步骤完成');
};

const rules = computed(() =>
  reactive<FormRules<FiatAcctInfo>>({
    payeeName: [
      { required: true, message: t('setting.editCurrency.rules.payeeName'), trigger: 'blur' },
    ],
    payeeCity: [
      {
        required: formData.value.useQualificationInfo !== 'Y',
        message: t('setting.editCurrency.rules.payeeCity'),
        trigger: 'change',
      },
    ],
    payeeState: [
      {
        required: formData.value.useQualificationInfo !== 'Y',
        message: t('setting.editCurrency.rules.payeeState'),
        trigger: 'change',
      },
    ],
    payeePostalCode: [
      {
        required: formData.value.useQualificationInfo !== 'Y',
        message: t('setting.editCurrency.rules.payeePostCode'),
        trigger: 'change',
      },
    ],
    payeeAddr: [
      {
        required: formData.value.useQualificationInfo !== 'Y',
        message: t('setting.editCurrency.rules.payeeAddress'),
        trigger: 'change',
      },
    ],
    payeeCtry: [
      {
        required: formData.value.useQualificationInfo !== 'Y',
        message: t('setting.editCurrency.rules.payeeCountry'),
        trigger: 'change',
      },
    ],
    payeeAcctType: [
      { required: true, message: t('setting.editCurrency.rules.accountType'), trigger: 'change' },
    ],
    acctNo: [
      { required: true, message: t('setting.editCurrency.rules.accountNumber'), trigger: 'blur' },
    ],
    ccy: [
      {
        required: true,
        message: t('setting.editCurrency.rules.accountCurrency'),
        trigger: 'change',
      },
    ],
    payMethod: [
      { required: true, message: t('setting.editCurrency.rules.paymentMethod'), trigger: 'change' },
    ],
    acctName: [
      { required: true, message: t('setting.editCurrency.rules.bankName'), trigger: 'blur' },
    ],
    bankCtry: [
      { required: true, message: t('setting.editCurrency.rules.bankCountry'), trigger: 'change' },
    ],
    bankAddr: [
      { required: true, message: t('setting.editCurrency.rules.bankAddress'), trigger: 'blur' },
    ],
    swiftCode: [
      { required: true, message: t('setting.editCurrency.rules.swiftCode'), trigger: 'blur' },
    ],
  })
);

const setDefaultSelectValues = () => {
  if (accountTypeList.value.length === 1 && !formData.value.payeeAcctType) {
    formData.value.payeeAcctType = accountTypeList.value[0].enumCode;
  }
  if (currencyList.value.length === 1 && !formData.value.ccy) {
    formData.value.ccy = currencyList.value[0].enumCode;
  }
  if (transferMethodList.value.length === 1 && !formData.value.payMethod) {
    formData.value.payMethod = transferMethodList.value[0].enumCode;
  }
  if (regionList.value.length === 1) {
    if (!formData.value.payeeCtry) {
      formData.value.payeeCtry = regionList.value[0].enumCode;
    }
    if (!formData.value.bankCtry) {
      formData.value.bankCtry = regionList.value[0].enumCode;
    }
  }
};

// 监听父组件传入的数据变化
watch(
  () => props.currencyInfo,
  (newVal) => {
    formData.value = cloneDeep(newVal);
    setDefaultSelectValues();
  },
  { immediate: true }
);

// 监听抽屉显示状态，重新打开时重新加载数据
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      formData.value = cloneDeep(props.currencyInfo);
      setDefaultSelectValues();
    }
  }
);

// IdValidator组件引用
const idValidatorRef = vueRef<IdValidatorInstance | null>(null);

/**
 * 处理步骤切换和验证的方法
 * @param step 步骤编号 (1: 第一步验证, 2: 第二步提交)
 */
const handleStep = async (step: number) => {
  if (step === 1) {
    // 显示下一步操作界面
    if (!formRef.value) return;

    // 对输入参数进行校验
    await formRef.value.validate((valid) => {
      if (valid) {
        // 若参数校验通过，调用handleStep(2)方法
        handleStep(2);
        // 将按钮文案更新为"提交"
        currentStep.value = 2;
      }
    });
  } else if (step === 2) {
    // 第二步：身份验证
    if (idValidatorRef.value) {
      try {
        let res = await idValidatorRef.value.verifyEmailCodeResp();
        // tfaSeqId: .tfaSeqId, sendSeqId: res.sendSeqId
        emit('submit', formData.value, res);
      } catch (error) {
        console.error('身份验证失败:', error);
      }
    }
  }
};

/**
 * 处理上一步操作
 */
const handlePrevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value = currentStep.value - 1;
  }
};

/**
 * 原有的submit方法，现在调用handleStep(1)
 */
const submit = async () => {
  await handleStep(currentStep.value);
};

// 关闭
const handleClose = () => {
  showDrawer.value = false;
  emit('cancel');
};

const cancelShowDrawer = () => {
  emit('cancel');
};
</script>
<style lang="scss" scoped>
.form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #222527;
  margin-bottom: 24px;
  margin-top: 24px;
  display: block;
}

.form-section-title:first-child {
  margin-top: 0;
}


// :deep(.el-switch__core) {
//   background-color: #F5F5F5;
//   border-color: #F5F5F5;
//   border-radius: 12px;
//   height: 24px;
// }

// :deep(.el-switch.is-checked .el-switch__core) {
//   background-color: #FF0064;
//   border-color: transparent;
// }

:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
}

// :global(.el-popper.custom-tooltip) {
//   background: #ffffff;
//   border: 0.4px solid #e5e6eb;
//   box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.08);
//   border-radius: 8px;
//   line-height: 50px;
//   text-align: center;
//   font-family: PingFangSC-Regular;
//   align-items: center;
//   font-weight: 400;
//   font-size: 14px;
//   padding: 0 12px;
//   color: #222527;
// }

// :global(.el-popper.custom-tooltip .el-popper__arrow::before) {
//   background: #ffffff;
//   border-right: 0.4px solid #e5e6eb;
//   border-bottom: 0.4px solid #e5e6eb;
// }

// :global(.el-drawer__body) {
//   padding: 0 !important;
// }

// :global(.custom-drawer-header) {
//   font-weight: 500;
//   font-size: 18px;
//   padding: 0 !important;
//   margin-bottom: 0 !important;

//   .el-drawer__header {
//     border-bottom: 1px solid #e5e6eb;
//   }

//   .el-drawer__close-btn {
//     padding-right: 24px !important;
//   }
// }

.step-two {
  margin-left: min(170px, max(0px, calc((100% - 344px) / 2)));
  margin-right: min(170px, max(0px, calc((100% - 344px) / 2)));
}

.cancel-btn:hover {
  color: #222527 !important;
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
}
</style>
