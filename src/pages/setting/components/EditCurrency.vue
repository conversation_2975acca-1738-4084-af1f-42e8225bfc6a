<template>
  <el-drawer v-model="showDrawer" size="50%" header-class="custom-drawer-header" :close-on-click-modal="false"
    :show-close="false" :destroy-on-close="true" :close-on-press-escape="false">
    <template #header>
      <div
        class="h-60px mb-0 flex justify-between flex-row flex-items-center text-18px font-600 color-[#222527] py-18px px-24px">
        <span>{{ title }}</span>
        <svgIcon name="icon-close" class="text-16px cursor-pointer color-#6B7275" @click="handleClose" />
      </div>

    </template>
    <template #default>
      <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" class="pt-24px px-24px pb-50px"
        hide-required-asterisk>
        <span class="form-section-title">{{ t('setting.editCurrency.section.payeeInfo') }}</span>
        <el-row :gutter="76">
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.payeeName')" prop="payeeName">
              <el-input v-model="formData.payeeName" :placeholder="t('setting.editCurrency.placeholders.input')"
                :disabled="isFieldDisabled('payeeName')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <template #label>
                <span>{{ t('setting.editCurrency.labels.nickname') }}</span>
                <span class="text-14px text-[#6b7275]">{{
                  t('setting.editCurrency.labels.optional')
                  }}</span>
              </template>
              <el-input v-model="formData.acctNickName" :placeholder="t('setting.editCurrency.placeholders.input')"
                :disabled="isFieldDisabled('acctNickName')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="76">
          <el-col :span="20">
            <el-form-item :label="t('setting.editCurrency.labels.addressSameAsCompany')" prop="useQualificationInfo">
              <el-switch :model-value="formData.useQualificationInfo === 'Y'"
                :disabled="isFieldDisabled('useQualificationInfo')"
                @update:model-value="(value) => (formData.useQualificationInfo = value ? 'Y' : 'N')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="formData.useQualificationInfo !== 'Y'">
          <el-col :span="24">
            <el-form-item :label="t('setting.editCurrency.labels.streetAddress')" prop="payeeAddr">
              <el-input v-model="formData.payeeAddr" :placeholder="t('setting.editCurrency.placeholders.input')"
                :disabled="isFieldDisabled('payeeAddr')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="formData.useQualificationInfo !== 'Y'" :gutter="76">
          <el-col :span="12">
            <el-form-item prop="payeeApartment">
              <template #label>
                <span>{{ t('setting.editCurrency.labels.apartment') }}</span>
                <span class="text-14px text-[#6b7275]">{{
                  t('setting.editCurrency.labels.optional')
                  }}</span>
              </template>
              <el-input v-model="formData.payeeApartment" :placeholder="t('setting.editCurrency.placeholders.input')"
                :disabled="isFieldDisabled('payeeApartment')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.city')" prop="payeeCity">
              <el-input v-model="formData.payeeCity" :placeholder="t('setting.editCurrency.placeholders.input')"
                :disabled="isFieldDisabled('payeeCity')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="formData.useQualificationInfo !== 'Y'" :gutter="76">
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.state')" prop="payeeState">
              <el-input v-model="formData.payeeState" :placeholder="t('setting.editCurrency.placeholders.input')"
                :disabled="isFieldDisabled('payeeState')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="payeePostalCode">
              <template #label>
                <div class="flex items-center">
                  <div class="mr-4px">{{ t('setting.editCurrency.labels.postalCode') }}</div>
                  <el-tooltip placement="right">
                    <template #content>
                      <p style="margin: 0">
                        {{ t('setting.editCurrency.rules.businessPostalCodeTooltip') }}
                      </p>
                    </template>
                    <svg-icon name="icon-info" class="text-13.3px color-[#A7ADB0] ml-3px" />
                  </el-tooltip>
                </div>
              </template>
              <el-input v-model="formData.payeePostalCode" :placeholder="t('setting.editCurrency.placeholders.input')"
                :disabled="isFieldDisabled('payeePostalCode')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="formData.useQualificationInfo !== 'Y'" :gutter="76">
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.payeeCountry')" prop="payeeCtry">
              <el-select v-model="formData.payeeCtry" filterable
                :placeholder="t('setting.editCurrency.placeholders.select')" style="width: 100%"
                :disabled="isFieldDisabled('payeeCtry')">
                <el-option v-for="info in regionList" :key="info.enumCode" :label="info.enumDescCn"
                  :value="info.enumCode" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="w-full h-[1px] mt-8px bg-[#EDEDEE]" />
        <span class="form-section-title">{{ t('setting.editCurrency.section.payeeAccount') }}</span>
        <el-row :gutter="76">
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.accountType')" prop="payeeAcctType">
              <el-select v-model="formData.payeeAcctType" :placeholder="t('setting.editCurrency.placeholders.select')"
                style="width: 100%" :disabled="isFieldDisabled('payeeAcctType')">
                <el-option v-for="info in accountTypeList" :key="info.enumCode" :label="info.enumDescCn"
                  :value="info.enumCode" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.accountNumber')" prop="acctNo">
              <el-input v-model="formData.acctNo" :placeholder="t('setting.editCurrency.placeholders.input')"
                :disabled="isFieldDisabled('acctNo')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="76">
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.accountCurrency')" prop="ccy">
              <el-select v-model="formData.ccy" :placeholder="t('setting.editCurrency.placeholders.select')"
                style="width: 100%" :disabled="isFieldDisabled('ccy')">
                <el-option v-for="info in currencyList" :key="info.enumCode" :label="info.enumDescCn"
                  :value="info.enumCode" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.paymentMethod')" prop="payMethod">
              <el-select v-model="formData.payMethod" :placeholder="t('setting.editCurrency.placeholders.select')"
                style="width: 100%" :disabled="isFieldDisabled('payMethod')">
                <el-option v-for="info in transferMethodList" :key="info.enumCode" :label="info.enumDescCn"
                  :value="info.enumCode" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="w-full h-[1px] mt-8px mb-24px bg-[#EDEDEE]" />
        <span class="form-section-title">{{ t('setting.editCurrency.section.payeeBank') }}</span>
        <el-row :gutter="76">
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.bankName')" prop="acctName">
              <el-tooltip :content="formData.acctName" placement="top" :disabled="!formData.acctName"
                popper-class="custom-tooltip">
                <el-input v-model="formData.acctName" :placeholder="t('setting.editCurrency.placeholders.input')"
                  :disabled="isFieldDisabled('acctName')" />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.bankCountry')" prop="bankCtry">
              <el-select v-model="formData.bankCtry" filterable
                :placeholder="t('setting.editCurrency.placeholders.select')" style="width: 100%"
                :disabled="isFieldDisabled('bankCtry')">
                <el-option v-for="info in regionList" :key="info.enumCode" :label="info.enumDescCn"
                  :value="info.enumCode" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="76">
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.bankAddress')" prop="bankAddr">
              <el-tooltip :content="formData.bankAddr" placement="top" :disabled="!formData.bankAddr"
                popper-class="custom-tooltip">
                <el-input v-model="formData.bankAddr" :placeholder="t('setting.editCurrency.placeholders.input')"
                  :disabled="isFieldDisabled('bankAddr')" />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('setting.editCurrency.labels.swiftCode')" prop="swiftCode">
              <el-input v-model="formData.swiftCode" :placeholder="t('setting.editCurrency.placeholders.input')"
                :disabled="isFieldDisabled('swiftCode')" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template #footer>
      <!-- 底部按钮 -->
      <div style="border-top: 1px solid #e5e6eb"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
        <el-button v-if="props.mode !== 'view'" class="btn-hover-scale-sm cancel-btn" @click="cancelShowDrawer">{{
          t('setting.editCurrency.buttons.cancel')
          }}</el-button>
        <el-button v-if="props.mode !== 'view'" type="primary" class="btn-hover-scale-sm" @click="submit">{{
          t('setting.editCurrency.buttons.submit')
          }}</el-button>
        <el-button v-if="props.mode === 'view'" type="primary" class="btn-hover-scale-sm" @click="handleClose">{{
          t('setting.editCurrency.buttons.close')
          }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref, reactive, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import type { FiatAcctInfo } from '../apis/type';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { cloneDeep } from 'lodash-es';
import { BusinessEnumType } from '@/common/apis/common/type';
import { t } from '@@/i18n';
const enumStore = useEnumStore();

const regionList = enumStore.getEnumList(BusinessEnumType.NATIONALITY);
const accountTypeList = enumStore.getEnumList(BusinessEnumType.PAYEE_ACCT_TYPE);
const currencyList = enumStore.getEnumList(BusinessEnumType.FIAT_CCY);
const transferMethodList = enumStore.getEnumList(BusinessEnumType.TRANSFER_METHOD);

const props = withDefaults(
  defineProps<{
    currencyInfo: FiatAcctInfo;
    modelValue: boolean;
    mode: 'create' | 'edit' | 'view';
  }>(),
  {
    modelValue: true,
    mode: 'create',
  }
);

const title = computed(() => {
  if (props.mode === 'create') return t('setting.editCurrency.title.create');
  if (props.mode === 'edit') return t('setting.editCurrency.title.edit');
  return t('setting.editCurrency.title.view');
});

const isFieldDisabled = (field: keyof FiatAcctInfo) => {
  if (props.mode === 'view') {
    return true;
  }
  if (props.mode === 'edit') {
    if (props.currencyInfo.status === 'approved') {
      return field !== 'acctNickName';
    }
    if (props.currencyInfo.status === 'rejected') {
      return false;
    }
  }
  return false;
};

const emit = defineEmits<{
  (e: 'submit', payload: FiatAcctInfo): void;
  (e: 'cancel'): void;
  (e: 'update:modelValue', value: boolean): void;
}>();

// 使用计算属性实现双向绑定
const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const formData = ref<FiatAcctInfo>(cloneDeep(props.currencyInfo));

const formRef = ref<FormInstance>();

const rules = computed(() =>
  reactive<FormRules<FiatAcctInfo>>({
    payeeName: [
      { required: true, message: t('setting.editCurrency.rules.payeeName'), trigger: 'blur' },
    ],
    payeeCity: [
      {
        required: formData.value.useQualificationInfo !== 'Y',
        message: t('setting.editCurrency.rules.payeeCity'),
        trigger: 'change',
      },
    ],
    payeeState: [
      {
        required: formData.value.useQualificationInfo !== 'Y',
        message: t('setting.editCurrency.rules.payeeState'),
        trigger: 'change',
      },
    ],
    payeePostalCode: [
      {
        required: formData.value.useQualificationInfo !== 'Y',
        message: t('setting.editCurrency.rules.payeePostCode'),
        trigger: 'change',
      },
    ],
    payeeAddr: [
      {
        required: formData.value.useQualificationInfo !== 'Y',
        message: t('setting.editCurrency.rules.payeeAddress'),
        trigger: 'change',
      },
    ],
    payeeCtry: [
      {
        required: formData.value.useQualificationInfo !== 'Y',
        message: t('setting.editCurrency.rules.payeeCountry'),
        trigger: 'change',
      },
    ],
    payeeAcctType: [
      { required: true, message: t('setting.editCurrency.rules.accountType'), trigger: 'change' },
    ],
    acctNo: [
      { required: true, message: t('setting.editCurrency.rules.accountNumber'), trigger: 'blur' },
    ],
    ccy: [
      {
        required: true,
        message: t('setting.editCurrency.rules.accountCurrency'),
        trigger: 'change',
      },
    ],
    payMethod: [
      { required: true, message: t('setting.editCurrency.rules.paymentMethod'), trigger: 'change' },
    ],
    acctName: [
      { required: true, message: t('setting.editCurrency.rules.bankName'), trigger: 'blur' },
    ],
    bankCtry: [
      { required: true, message: t('setting.editCurrency.rules.bankCountry'), trigger: 'change' },
    ],
    bankAddr: [
      { required: true, message: t('setting.editCurrency.rules.bankAddress'), trigger: 'blur' },
    ],
    swiftCode: [
      { required: true, message: t('setting.editCurrency.rules.swiftCode'), trigger: 'blur' },
    ],
  })
);

const setDefaultSelectValues = () => {
  if (accountTypeList.value.length === 1 && !formData.value.payeeAcctType) {
    formData.value.payeeAcctType = accountTypeList.value[0].enumCode;
  }
  if (currencyList.value.length === 1 && !formData.value.ccy) {
    formData.value.ccy = currencyList.value[0].enumCode;
  }
  if (transferMethodList.value.length === 1 && !formData.value.payMethod) {
    formData.value.payMethod = transferMethodList.value[0].enumCode;
  }
  if (regionList.value.length === 1) {
    if (!formData.value.payeeCtry) {
      formData.value.payeeCtry = regionList.value[0].enumCode;
    }
    if (!formData.value.bankCtry) {
      formData.value.bankCtry = regionList.value[0].enumCode;
    }
  }
};

// 监听父组件传入的数据变化
watch(
  () => props.currencyInfo,
  (newVal) => {
    formData.value = cloneDeep(newVal);
    setDefaultSelectValues();
  },
  { immediate: true }
);

// 监听抽屉显示状态，重新打开时重新加载数据
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      formData.value = cloneDeep(props.currencyInfo);
      setDefaultSelectValues();
    }
  }
);

const submit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('submit', formData.value);
    }
  });
};

// 关闭
const handleClose = () => {
  showDrawer.value = false;
  emit('cancel');
};

const cancelShowDrawer = () => {
  emit('cancel');
};
</script>
<style lang="scss" scoped>
.form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #222527;
  margin-bottom: 24px;
  margin-top: 24px;
  display: block;
}

.form-section-title:first-child {
  margin-top: 0;
}


// :deep(.el-switch__core) {
//   background-color: #F5F5F5;
//   border-color: #F5F5F5;
//   border-radius: 12px;
//   height: 24px;
// }

// :deep(.el-switch.is-checked .el-switch__core) {
//   background-color: #FF0064;
//   border-color: transparent;
// }

:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
}

:global(.el-popper.custom-tooltip) {
  background: #ffffff;
  border: 0.4px solid #e5e6eb;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  line-height: 50px;
  text-align: center;
  font-family: PingFangSC-Regular;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  padding: 0 12px;
  color: #222527;
}

:global(.el-popper.custom-tooltip .el-popper__arrow::before) {
  background: #ffffff;
  border-right: 0.4px solid #e5e6eb;
  border-bottom: 0.4px solid #e5e6eb;
}

:global(.el-drawer__body) {
  padding: 0 !important;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0 !important;
  margin-bottom: 0 !important;

  .el-drawer__header {
    border-bottom: 1px solid #e5e6eb;
  }

  .el-drawer__close-btn {
    padding-right: 24px !important;
  }
}

.cancel-btn:hover {
  color: #222527 !important;
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
}
</style>
