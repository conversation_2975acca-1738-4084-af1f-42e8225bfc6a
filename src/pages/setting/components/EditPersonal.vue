<template>
  <el-drawer v-model="showDrawer" size="400px" style="--el-drawer-padding-primary: 0px"
    header-class="custom-drawer-header" :close-on-click-modal="false" :destroy-on-close="true"
    :close-on-press-escape="false">
    <template #header>
      <div
        class="font-family-[PingFangSC-Medium] text-18px color-[#222527] h-60px border-b-1 mb-0 flex-row flex-items-center pt-18px pb-18px pl-24px">
        <span>{{ t('setting.editPersonal.title') }}</span>
      </div>
    </template>
    <template #default>
      <div class="p-24px">
        <!-- 说明文字 -->
        <div class="mb-24px">
          <p class="font-family-[PingFangSC-Regular] text-14px color-[#6B7275] leading-20px m-0">
            {{ t('setting.editPersonal.description') }}
          </p>
        </div>

        <!-- 当前用户名 -->
        <div class="mb-30px">
          <div class="font-family-[PingFangSC-Regular] text-14px color-[#6B7275] font-400 mb-12px">{{
            t('setting.editPersonal.currentUsername') }}</div>
          <div class="font-family-[PingFangSC-Regular] text-14px color-[#222527] font-400">{{ currentUsername }}</div>
        </div>

        <!-- 新用户名输入 -->
        <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" hide-required-asterisk>
          <el-form-item :label="t('setting.editPersonal.newUsername')" prop="newUsername"
            class="font-family-[PingFangSC-Regular] text-14px color-[#222527] mb-6px">
            <el-input v-model="formData.newUsername" :placeholder="t('setting.editPersonal.placeholder')" maxlength="32"
              show-word-limit class="w-full" />
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div style="border-top: 1px solid #E5E6EB;"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
        <el-button
          class="cancel-btn w-68px h-32px text-14px text-[#222527] font-family-[PingFangSC-Regular] mr-8px btn-hover-scale-sm"
          @click="cancelShowDrawer">{{ t('setting.editPersonal.cancel') }}</el-button>
        <el-button
          class="confirm-btn w-68px h-32px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm"
          @click="submit">{{ t('setting.editPersonal.confirm') }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref, reactive, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import { t } from '@@/i18n'

defineOptions({
  name: 'EditPersonal'
})

interface UserInfo {
  newUsername: string;
}

const props = withDefaults(defineProps<{
  currentUsername: string;
  modelValue: boolean;
}>(), {
  modelValue: true,
  currentUsername: '<EMAIL>'
});

const emit = defineEmits<{
  (e: 'submit', payload: { newUsername: string }): void
  (e: 'cancel'): void
  (e: 'update:modelValue', value: boolean): void
}>()

const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formData = ref<UserInfo>({
  newUsername: ''
});

const formRef = ref<FormInstance>();

const rules = computed(() => reactive<FormRules<UserInfo>>({
  newUsername: [
    { required: true, message: t('setting.editPersonal.newUsernameRequired'), trigger: 'blur' },
    { min: 1, max: 32, message: t('setting.editPersonal.newUsernameLength'), trigger: 'blur' }
  ],
}))

watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // 重置表单
    formData.value.newUsername = ''
  }
})

const submit = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('submit', { newUsername: formData.value.newUsername })
    }
  })
}

const cancelShowDrawer = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

:deep(.el-input__wrapper) {
  border: 1px solid #E5E6EB;
  box-shadow: none;
  border-radius: 6px;
}

:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
  margin-bottom: 8px;
}

:deep(.el-input__count) {
  color: #6B7275;
  font-size: 12px;
}

.cancel-btn {
  background-color: #FFFFFF !important;
  border: 1px solid #E5E6EB !important;
  color: #222527 !important;
  border-radius: 6px !important;
}

.cancel-btn:hover {
  background-color: #F8F9FA !important;
  border-color: #E5E6EB !important;
}

.confirm-btn {
  background-color: #030814 !important;
  border: 1px solid #030814 !important;
  color: #FFFFFF !important;
  border-radius: 6px !important;
  margin-left: 0px !important;
}

.confirm-btn:hover {
  background-color: #1a1f2e !important;
  border-color: #1a1f2e !important;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;

  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }

  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
</style>