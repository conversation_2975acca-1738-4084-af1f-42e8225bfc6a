<template>
  <el-drawer v-model="showDrawer" size="400px" header-class="custom-drawer-header" :close-on-click-modal="false"
    :destroy-on-close="true" :close-on-press-escape="false">
    <template #header>
      <div
        class="h-60px border-b-1 mb-0 flex-row flex-items-center text-18px font-500 color-[#222527] pt-18px pb-18px pl-24px">
        <span>{{ title }}</span>
      </div>
    </template>
    <template #default>
      <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" class="p-24px"
        hide-required-asterisk>

        <el-form-item :label="t('setting.editAddress.labels.currency')" prop="ccy">
          <el-select v-model="formData.ccy" :placeholder="t('setting.editAddress.placeholders.select')"
            style="width: 100%;" :disabled="isFieldDisabled('ccy')">
            <el-option v-for="info in currencyList" :key="info.enumCode" :label="info.enumDescCn"
              :value="info.enumCode" />
          </el-select>
        </el-form-item>


        <el-form-item :label="t('setting.editAddress.labels.network')" prop="cryptoNet">
          <el-select v-model="formData.cryptoNet" :placeholder="t('setting.editAddress.placeholders.select')"
            style="width: 100%;" :disabled="isFieldDisabled('cryptoNet')">
            <el-option v-for="info in cryptoNetworkList" :key="info.enumCode" :label="info.enumDescCn"
              :value="info.enumCode" />
          </el-select>
        </el-form-item>


        <el-form-item :label="t('setting.editAddress.labels.address')" prop="cryptoAddr">
          <el-input v-model="formData.cryptoAddr" :placeholder="t('setting.editAddress.placeholders.input')"
            :disabled="isFieldDisabled('cryptoAddr')" />
        </el-form-item>


        <el-form-item :label="t('setting.editAddress.labels.nickname')" prop="walletName">
          <el-input v-model="formData.walletName" :placeholder="t('setting.editAddress.placeholders.input')"
            :disabled="isFieldDisabled('walletName')" />
        </el-form-item>

      </el-form>
    </template>
    <template #footer>
      <div v-if="props.mode !== 'view'" style="border-top: 1px solid #E5E6EB;"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
        <el-button
          class="cancel-btn w-68px text-14px text-[#222527] font-family-[PingFangSC-Regular] btn-hover-scale-sm"
          @click="cancelShowDrawer">{{ t('setting.editAddress.buttons.cancel') }}</el-button>
        <el-button type="primary"
          class="w-68px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm" @click="submit">{{
            t('setting.editAddress.buttons.next') }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref, reactive, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import type { DigitalAddress } from '../apis/type';
import { useEnumStore } from '@/pinia/stores/enumStore'
import { cloneDeep } from 'lodash-es';
import { BusinessEnumType } from '@/common/apis/common/type';
import { t } from '@@/i18n'

const enumStore = useEnumStore();

const currencyList = enumStore.getEnumList(BusinessEnumType.CURRENCY);
const cryptoNetworkList = enumStore.getEnumList(BusinessEnumType.CRYPTO_NET);

const props = withDefaults(defineProps<{
  addressInfo: DigitalAddress
  modelValue: boolean
  mode: 'create' | 'edit' | 'view'
  status?: string
}>(), {
  modelValue: true,
  mode: 'create',
  status: ''
});

const title = computed(() => {
  if (props.mode === 'create') return t('setting.editAddress.title.create')
  if (props.mode === 'edit') return t('setting.editAddress.title.edit')
  return t('setting.editAddress.title.view')
})

const isFieldDisabled = (field: keyof DigitalAddress) => {
  // 如果是查看模式，所有字段都禁用
  if (props.mode === 'view') {
    return true
  }

  // 如果状态是审核通过（approved），只有昵称字段可以编辑
  if (props.status === 'approved') {
    return field !== 'walletName'
  }

  // 其他情况下不禁用
  return false
}

const emit = defineEmits<{
  (e: 'submit', payload: DigitalAddress): void
  (e: 'cancel'): void
  (e: 'update:modelValue', value: boolean): void
}>()

const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formData = ref<DigitalAddress>(cloneDeep(props.addressInfo));

const formRef = ref<FormInstance>();

const rules = computed(() => reactive<FormRules<DigitalAddress>>({
  ccy: [{ required: true, message: t('setting.editAddress.rules.currency'), trigger: 'change' }],
  cryptoNet: [{ required: true, message: t('setting.editAddress.rules.network'), trigger: 'change' }],
  cryptoAddr: [{ required: true, message: t('setting.editAddress.rules.address'), trigger: 'blur' }],
  walletName: [{ required: true, message: t('setting.editAddress.rules.nickname'), trigger: 'blur' }],
}))

// 监听父组件传入的数据变化
watch(() => props.addressInfo, (newVal) => {
  formData.value = cloneDeep(newVal)
}, { immediate: true })

// 监听抽屉显示状态，重新打开时重新加载数据
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    formData.value = cloneDeep(props.addressInfo)
  }
})
const submit = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('submit', formData.value)
    }
  })
}

const cancelShowDrawer = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

.form-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #222527;
  margin-bottom: 20px;
  margin-top: 20px;
}

.form-section-title:first-child {
  margin-top: 0;
}

:deep(.el-input__wrapper) {
  border: 1px solid #E5E6EB;
  box-shadow: none;
}

:deep(.el-form-item) {
  width: 352px;
  margin-bottom: 24px !important;
}

:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
}

:deep(.el-drawer__body) {
  padding: 0 !important;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0 !important;
  margin-bottom: 0 !important;

  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }

  .el-drawer__close-btn {
    padding-right: 24px !important;
  }
}

.cancel-btn:hover {
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
}
</style>