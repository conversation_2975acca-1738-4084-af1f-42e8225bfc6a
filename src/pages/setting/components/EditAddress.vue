<template>
  <el-drawer v-model="showDrawer" size="400px" header-class="custom-drawer-header" :close-on-click-modal="false"
    :destroy-on-close="true" :close-on-press-escape="false">
    <template #header>
      <div
        class="h-60px border-b-1 mb-0 flex-row flex-items-center text-18px font-500 color-[#222527] pt-18px pb-18px pl-24px">
        <span>{{ title }}</span>
      </div>
    </template>
    <template #default>
      <div>
        <!-- 步骤组件 -->
        <StepComponent v-if="props.mode !== 'view'" v-model="currentStep" :steps="stepConfig"
          @step-change="handleStepChange" @complete="handleStepComplete" />

        <!-- 第一步：填写地址信息 -->
        <div v-if="currentStep === 1">
          <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" hide-required-asterisk>

            <el-form-item :label="t('setting.editAddress.labels.currency')" prop="ccy">
              <el-select v-model="formData.ccy" :placeholder="t('setting.editAddress.placeholders.select')"
                style="width: 100%;" :disabled="isFieldDisabled('ccy')">
                <template #prefix>
                  <img v-if="formData.ccy" :src="getCryptoIcon(formData.ccy)" width="16" />
                </template>
                <el-option v-for="info in currencyList" :key="info.enumCode" :label="info.enumDescCn"
                  :value="info.enumCode">
                  <p style="display: flex; align-items: center; margin: 0; height: 32px">
                    <img style="margin-right: 8px" :src="getCryptoIcon(info.enumCode)" width="16" />
                    {{ info.enumDescCn }}
                  </p>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item :label="t('setting.editAddress.labels.network')" prop="cryptoNet">
              <el-select v-model="formData.cryptoNet" :placeholder="t('setting.editAddress.placeholders.select')"
                style="width: 100%;" :disabled="isFieldDisabled('cryptoNet')">
                <template #prefix>
                  <img v-if="formData.cryptoNet" :src="getCryptoIcon(formData.cryptoNet)" width="16" />
                </template>
                <el-option v-for="info in cryptoNetworkList" :key="info.enumCode" :label="info.enumDescCn"
                  :value="info.enumCode">
                  <p style="display: flex; align-items: center; margin: 0; height: 32px">
                    <img style="margin-right: 8px" :src="getCryptoIcon(info.enumCode)" width="16" />
                    {{ info.enumDescCn }}
                  </p>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item :label="t('setting.editAddress.labels.address')" prop="cryptoAddr">
              <el-input v-model="formData.cryptoAddr" :placeholder="t('setting.editAddress.placeholders.input')"
                :disabled="isFieldDisabled('cryptoAddr')" />
            </el-form-item>

            <el-form-item :label="t('setting.editAddress.labels.nickname')" prop="walletName">
              <el-input v-model="formData.walletName" :placeholder="t('setting.editAddress.placeholders.input')"
                :disabled="isFieldDisabled('walletName')" />
            </el-form-item>

          </el-form>
        </div>

        <!-- 第二步：身份验证 -->
        <div v-else-if="currentStep === 2">
          <IdValidator ref="idValidatorRef"
            :business-type="props.mode === 'create' ? FeeEnumType.CRYPTO_ADDR_BIND : FeeEnumType.CRYPTO_ADDR_MODIFY"
            class="mt-24px" />
        </div>
      </div>
    </template>
    <template #footer>
      <div v-if="props.mode !== 'view'" style="border-top: 1px solid #E5E6EB;"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
        <el-button
          class="cancel-btn w-68px text-14px text-[#222527] font-family-[PingFangSC-Regular] btn-hover-scale-sm"
          @click="cancelShowDrawer">{{ t('setting.editAddress.buttons.cancel') }}</el-button>
        <el-button v-if="(props.mode === 'create' || props.mode === 'edit') && currentStep === 2" type="primary"
          class="btn-hover-scale-sm" @click="handlePrevStep">
          {{ t('setting.editCurrency.buttons.previous') }}
        </el-button>
        <el-button type="primary"
          class="w-68px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm" @click="submit">{{
            currentStep === 2 ? t('setting.editAddress.buttons.submit') : t('setting.editAddress.buttons.next')
          }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref, reactive, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import type { DigitalAddress } from '../apis/type';
import { useEnumStore } from '@/pinia/stores/enumStore'
import { cloneDeep } from 'lodash-es';
import { BusinessEnumType } from '@/common/apis/common/type';
import { t } from '@@/i18n'
import { getCryptoIcon } from '@/common/utils/imageUtils'
import StepComponent from './StepComponent'
import IdValidator from '@/common/components/IdValidator/index.vue'
import { FeeEnumType } from '@/pages/exchange/apis/type'

const enumStore = useEnumStore();

const currencyList = enumStore.getEnumList(BusinessEnumType.CURRENCY);
const cryptoNetworkList = enumStore.getEnumList(BusinessEnumType.CRYPTO_NET);

const props = withDefaults(defineProps<{
  addressInfo: DigitalAddress
  modelValue: boolean
  mode: 'create' | 'edit' | 'view'
  status?: string
}>(), {
  modelValue: true,
  mode: 'create',
  status: ''
});

const title = computed(() => {
  if (props.mode === 'create') return t('setting.editAddress.title.create')
  if (props.mode === 'edit') return t('setting.editAddress.title.edit')
  return t('setting.editAddress.title.view')
})

const isFieldDisabled = (field: keyof DigitalAddress) => {
  // 如果是查看模式，所有字段都禁用
  if (props.mode === 'view') {
    return true
  }

  // 如果状态是审核通过（approved），只有昵称字段可以编辑
  if (props.status === 'approved') {
    return field !== 'walletName'
  }

  // 其他情况下不禁用
  return false
}

const emit = defineEmits<{
  (e: 'submit', payload: DigitalAddress, verifyResult?: { tfaSeqId: string; sendSeqId: string }): void
  (e: 'cancel'): void
  (e: 'update:modelValue', value: boolean): void
}>()

const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formData = ref<DigitalAddress>(cloneDeep(props.addressInfo));

const formRef = ref<FormInstance>();

// 步骤管理相关数据
const currentStep = ref(1);
const stepConfig = computed(() => [
  { title: title.value },
  { title: t('setting.editAddress.steps.verify') }
]);

// IdValidator组件引用
const idValidatorRef = ref<InstanceType<typeof IdValidator> | null>(null);

const rules = computed(() => reactive<FormRules<DigitalAddress>>({
  ccy: [{ required: true, message: t('setting.editAddress.rules.currency'), trigger: 'change' }],
  cryptoNet: [{ required: true, message: t('setting.editAddress.rules.network'), trigger: 'change' }],
  cryptoAddr: [{ required: true, message: t('setting.editAddress.rules.address'), trigger: 'blur' }],
  walletName: [{ required: true, message: t('setting.editAddress.rules.nickname'), trigger: 'blur' }],
}))

// 监听父组件传入的数据变化
watch(() => props.addressInfo, (newVal) => {
  formData.value = cloneDeep(newVal)
}, { immediate: true })

// 监听抽屉显示状态，重新打开时重新加载数据
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    formData.value = cloneDeep(props.addressInfo)
    currentStep.value = 1 // 重置步骤
  }
})

/**
 * 处理步骤切换和验证的方法
 * @param step 步骤编号 (1: 第一步验证, 2: 第二步提交)
 */
const handleStep = async (step: number) => {
  if (step === 1) {
    // 显示下一步操作界面
    if (!formRef.value) return;

    // 对输入参数进行校验
    await formRef.value.validate((valid) => {
      if (valid) {
        // 若参数校验通过，调用handleStep(2)方法
        handleStep(2);
        // 将按钮文案更新为"提交"
        currentStep.value = 2;
      }
    });
  } else if (step === 2) {
    // 第二步：身份验证
    if (idValidatorRef.value) {
      try {
        let res = await idValidatorRef.value.verifyEmailCodeResp();
        // tfaSeqId: .tfaSeqId, sendSeqId: res.sendSeqId
        emit('submit', formData.value, res);
      } catch (error) {
        console.error('身份验证失败:', error);
      }
    }
  }
};

/**
 * 处理步骤变化事件
 */
const handleStepChange = (step: number) => {
  currentStep.value = step;
};

/**
 * 处理步骤完成事件
 */
const handleStepComplete = () => {
  handleStep(currentStep.value);
};
/**
 * 原有的submit方法，现在调用handleStep(1)
 */
const submit = async () => {
  await handleStep(currentStep.value);
};

/**
 * 处理上一步操作
 */
const handlePrevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value = currentStep.value - 1;
  }
};

const cancelShowDrawer = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

.form-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #222527;
  margin-bottom: 20px;
  margin-top: 20px;
}

.form-section-title:first-child {
  margin-top: 0;
}

:deep(.el-input__wrapper) {
  border: 1px solid #E5E6EB;
  box-shadow: none;
}

// :deep(.el-select) {
//   .el-select__wrapper {
//     height: 40px !important;
//     min-height: 40px;
//     border: 1px solid #E5E6EB;
//     border-radius: 8px;
//     padding: 17px 16px;
//     font-size: 16px;
//     background-color: #ffffff;
//     box-shadow: none;

//     .el-select__inner {
//       font-family: 'PingFangSC-Regular';
//       font-size: 16px;
//       color: #222527;

//       &::placeholder {
//         color: #C9CDD4;
//       }
//     }

//     .el-select__suffix {
//       .el-select__suffix-inner {
//         color: #6b7275;
//       }
//     }

//     &.is-disabled {
//       background-color: #f5f5f5;
//       border-color: #e4e7ed;

//       .el-select__inner {
//         color: #c0c4cc;
//       }
//     }
//   }

//   // &:hover:not(.is-disabled) .el-select__wrapper {
//   //   border-color: #ff0064;
//   // }
// }

:deep(.el-form-item) {
  width: 352px;
  margin-bottom: 24px !important;
}

:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
}

:deep(.el-drawer__body) {
  padding: 0 !important;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0 !important;
  margin-bottom: 0 !important;

  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }

  .el-drawer__close-btn {
    padding-right: 24px !important;
  }
}

.cancel-btn:hover {
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
}

.btn-hover-scale-sm {
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
}
</style>