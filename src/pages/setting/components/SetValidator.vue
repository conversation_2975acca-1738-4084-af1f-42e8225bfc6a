<template>
  <el-drawer 
    v-model="showDrawer" 
    size="450px" 
    style="--el-drawer-padding-primary: 0px"
    header-class="custom-drawer-header"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :close-on-press-escape="false"
  >
    <template #header>
      <div class="font-family-[PingFangSC-Medium] text-18px color-[#222527] h-60px border-b-1 mb-0 flex-row flex-items-center pt-18px pb-18px pl-24px">
        <span>{{ t('setting.personal.verifyTitle') }}</span>
      </div>
    </template>
    <template #default>
      <div class="p-24px">
        <div class="color-[#6B7275] text-14px line-height-20px">{{ t('setting.setValidator.validatorDesc') }}</div>
        <div class="mt-24px relative">
          <div class="dashed-line absolute top-18px left-3px h-30px" />
          <div class="font-family-[PingFangSC-Regular] text-14px color-[#222527] font-400 flex flex-row justify-between items-center">
            <span>{{ t('setting.setValidator.step1') }}</span>
            <el-popover placement="bottom-end" :width="247" trigger="click">
              <template #reference>
                <span class="color-[#FF0064]" style="cursor: pointer;">{{ t('setting.setValidator.viewSupportedApps') }}</span>
              </template>
              <div>
                <el-row style="display: flex; flex-direction: row; align-items: center;">
                   <div style="border: 1px solid #E5E6EB; border-radius: 4px; padding: 5px; padding-bottom: 0px;">
                    <img src="@@/assets/icons/icon-micro-auth.png" alt="" class="w-247px">
                   </div>
                   <span style="margin-left: 12px;">Microsoft Authenticator</span>
                </el-row>
                <el-row style="display: flex; flex-direction: row; align-items: center; margin-top: 24px;">
                   <div style="border: 1px solid #E5E6EB; border-radius: 4px; padding: 5px; padding-bottom: 0px;">
                    <img src="@@/assets/icons/icon-google-auth.png" alt="" class="w-247px">
                   </div>
                   <span style="margin-left: 12px;">Google Authenticator</span>
                </el-row>
              </div>
            </el-popover>
            
          </div>
        </div>
        <div class="mt-32px">
          <div class="font-family-[PingFangSC-Regular] text-14px color-[#222527] font-400">
            <span>{{ t('setting.setValidator.step2') }}</span>
          </div>
          <div class="flex flex-row relative justify-center items-center ">
            <div class="dashed-line absolute top-6px left-3px h-200px" />
            <div class="flex flex-col items-center h-176px w-176px bg-[#fff] rounded-6px justify-center mt-16px" style="border: 1px solid #E5E6EB; border-radius: 6px;">
              <qrcode-vue v-if="formData.url" :value="formData.url" :size="152" level="H" />
            </div>
          </div>
        </div>
        <div class="mt-24px">
          <div class="font-family-[PingFangSC-Regular] text-14px color-[#222527] font-400 mb-6px">
            <span>{{ t('setting.setValidator.step3') }}</span>
          </div>
          <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" hide-required-asterisk>
            <el-form-item 
              prop="confirmCode" 
              class="custom-form-item mb-6px"
            >
              <el-input 
                v-model="formData.confirmCode" 
                type="text"
                :placeholder="t('setting.setValidator.placeholder')" 
                class="custom-input"
                size="large"
                maxlength="32"
                :disabled="isProcessing"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </template>
    <template #footer>
      <div 
        style="border-top: 1px solid #E5E6EB;"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
        <el-button 
          class="cancel-btn w-68px h-32px text-14px text-[#222527] font-family-[PingFangSC-Regular] mr-12px" 
          @click="cancelShowDrawer"
          :disabled="isProcessing"
        >
          {{ t('setting.editPassword.cancel') }}
        </el-button>
        <el-button 
          class="confirm-btn w-68px h-32px text-14px text-white font-family-[PingFangSC-Regular]" 
          @click="submit"
          :loading="isProcessing"
        >
          {{ t('setting.editPassword.confirm') }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { t } from '@@/i18n'
import { ElMessage } from 'element-plus'
import QrcodeVue from "qrcode.vue"
import { bind2FAConfirmApi } from '../apis'
import type { Bind2FAData } from '../apis/type'

defineOptions({
  name: 'SetValidator'
})

const formRef = ref<FormInstance>();

const props = withDefaults(defineProps<{
  modelValue: boolean;
  bindData: Bind2FAData
}>(), {
  modelValue: true,
});

const formData = ref({
  confirmCode: '',
  sysSeqId: props.bindData?.sysSeqId,
  url: props.bindData?.url
})

const emit = defineEmits<{
  (e: 'bind-success'): void
  (e: 'cancel'): void
  (e: 'update:modelValue', value: boolean): void
}>()

const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const rules = computed(() => reactive<FormRules<typeof formData>>({
  confirmCode: [
    { required: true, message: t('setting.setValidator.placeholder'), trigger: 'blur' }
  ]
}))

watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // 重置表单
    formData.value = {
      confirmCode: '',
      sysSeqId: props.bindData?.sysSeqId,
      url: props.bindData?.url
    }
    isProcessing.value = false
    formRef.value?.clearValidate()
  }
})

const isProcessing = ref(false)

const submit = () => {
  if (!formRef.value || isProcessing.value) return
  
  // 验证表单
  formRef.value.validate().then(() => {
    isProcessing.value = true
    bind2FAConfirmApi(props.bindData.sysSeqId, formData.value.confirmCode).then(() => {
      emit('bind-success')
    }).finally(() => {
      isProcessing.value = false
    })
  })

}

const cancelShowDrawer = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

.custom-form-item {
  :deep(.el-form-item__label) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #222527;
    margin-bottom: 8px;
    line-height: 20px;
  }

  &.is-error :deep(.el-input__wrapper) {
    background: #FFF2EE;
    border: 1px solid #FD3627;
    box-shadow: none;
  }
}
.dashed-line {
  width: 1px;
  height: 87px;
  background-image: linear-gradient(to bottom, #E5E6EB 0%, #E5E6EB 80%, transparent 50%);
  background-size: 100% 10px;
  background-repeat: repeat-y
}
.custom-input {
  width: 100%;
  
  :deep(.el-input__wrapper) {
    border: 1px solid #E5E6EB;
    box-shadow: none;
    border-radius: 6px;
    height: 40px;
    padding: 0 12px;
    background-color: #ffffff;
    
    &:hover {
      border-color: #C0C4CC;
    }
    
    &.is-focus {
      border-color: #409EFF;
    }
  }
  
  :deep(.el-input__inner) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-size: 14px;
    color: #222527;
    
    &::placeholder {
      color: #A8ABB2;
    }
  }
  
  :deep(.el-input__suffix) {
    .el-input__suffix-inner {
      .el-input__icon {
        color: #A8ABB2;
        
        &:hover {
          color: #606266;
        }
      }
    }
  }
}

:deep(.el-form-item__error) {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #FD3627FF;
}

.cancel-btn {
  background-color: #FFFFFF !important;
  border: 1px solid #E5E6EB !important;
  color: #222527 !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
}

.cancel-btn:hover {
  background-color: #F8F9FA !important;
  border-color: #E5E6EB !important;
}

.confirm-btn {
  background-color: #030814 !important;
  border: 1px solid #030814 !important;
  color: #FFFFFF !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
}

.confirm-btn:hover {
  background-color: #1a1f2e !important;
  border-color: #1a1f2e !important;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }
  .el-drawer__close-btn {
    padding-right: 24px;
  }
}

</style> 