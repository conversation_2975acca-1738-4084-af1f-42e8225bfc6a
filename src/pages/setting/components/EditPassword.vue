<template>
  <el-drawer v-model="showDrawer" size="400px" style="--el-drawer-padding-primary: 0px"
    header-class="custom-drawer-header" :close-on-click-modal="false" :destroy-on-close="true"
    :close-on-press-escape="false">
    <template #header>
      <div
        class="font-family-[PingFangSC-Medium] text-18px color-[#222527] h-60px border-b-1 mb-0 flex-row flex-items-center pt-18px pb-18px pl-24px">
        <span>{{ t('setting.editPassword.title') }}</span>
      </div>
    </template>
    <template #default>
      <div class="p-24px">
        <!-- 密码修改表单 -->
        <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" hide-required-asterisk>
          <el-form-item :label="t('setting.editPassword.currentPassword')" prop="currentPassword"
            class="custom-form-item mb-20px">
            <el-input v-model="formData.currentPassword" type="password" show-password
              :placeholder="t('setting.editPassword.placeholder')" class="custom-input" size="large"
              :disabled="isProcessing" />
          </el-form-item>

          <el-form-item :label="t('setting.editPassword.newPassword')" prop="newPassword"
            class="custom-form-item mb-20px">
            <el-popover placement="bottom-start" :visible="isNewPasswordFocused" :show-arrow="false" :width="300"
              popper-class="password-strength-popper">
              <template #reference>
                <el-input v-model="formData.newPassword" type="password" show-password
                  :placeholder="t('setting.editPassword.placeholder')" class="custom-input" size="large" maxlength="32"
                  @input="handleNewPasswordInput" @focus="isNewPasswordFocused = true"
                  @blur="isNewPasswordFocused = false" @paste.prevent @copy.prevent :disabled="isProcessing" />
              </template>
              <template #default>
                <div class="password-strength-popover-content">
                  <div class="title">{{ t('setting.editPassword.passwordRequirements') }}</div>
                  <div :class="validPasswordStyle(passwordStrength.isLongEnough).className">
                    <SvgIcon :name="validPasswordStyle(passwordStrength.isLongEnough).iconName"
                      :class="validPasswordStyle(passwordStrength.isLongEnough).className" />
                    <span style="margin-left: 8px; font-size: 14px;">{{ t('setting.editPassword.lengthRequirement')
                      }}</span>
                  </div>
                  <div :class="validPasswordStyle(passwordStrength.hasUpperCaseAndDigit).className">
                    <SvgIcon :name="validPasswordStyle(passwordStrength.hasUpperCaseAndDigit).iconName"
                      :class="validPasswordStyle(passwordStrength.hasUpperCaseAndDigit).className" />
                    <span style="margin-left: 8px; font-size: 14px;">{{
                      t('setting.editPassword.upperCaseAndDigitRequirement') }}</span>
                  </div>
                  <div :class="validPasswordStyle(passwordStrength.hasSpecialChar).className">
                    <SvgIcon :name="validPasswordStyle(passwordStrength.hasSpecialChar).iconName"
                      :class="validPasswordStyle(passwordStrength.hasSpecialChar).className" />
                    <span style="margin-left: 8px; font-size: 14px;">{{ t('setting.editPassword.specialCharRequirement')
                      }}</span>
                  </div>
                  <div :class="validPasswordStyle(passwordStrength.isDifferentFromCurrent, true).className">
                    <SvgIcon :name="validPasswordStyle(passwordStrength.isDifferentFromCurrent, true).iconName"
                      :class="validPasswordStyle(passwordStrength.isDifferentFromCurrent, true).className" />
                    <span style="margin-left: 8px; font-size: 14px;">{{
                      t('setting.editPassword.differentFromCurrentRequirement') }}</span>
                  </div>
                </div>
              </template>
            </el-popover>
          </el-form-item>

          <el-form-item :label="t('setting.editPassword.confirmPassword')" prop="confirmPassword"
            class="custom-form-item mb-6px">
            <el-input v-model="formData.confirmPassword" type="password" show-password
              :placeholder="t('setting.editPassword.placeholder')" class="custom-input" size="large" maxlength="32"
              @input="handleConfirmPasswordInput" @paste.prevent @copy.prevent :disabled="isProcessing" />
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div style="border-top: 1px solid #E5E6EB;"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
        <el-button
          class="cancel-btn w-68px h-32px text-14px text-[#222527] font-family-[PingFangSC-Regular] mr-8px btn-hover-scale-sm"
          @click="cancelShowDrawer" :disabled="isProcessing">
          {{ t('setting.editPassword.cancel') }}
        </el-button>
        <el-button
          class="confirm-btn w-68px h-32px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm"
          @click="submit" :loading="isProcessing">
          {{ t('setting.editPassword.confirm') }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref, reactive, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import { t } from '@@/i18n'
import { encryptPasswordMD5 } from '@/common/utils/crypto';
import { useUserStore } from '@/pinia/stores/user'
import { useRouter } from 'vue-router'
import { changePasswordApi } from '../apis'
import { ElMessage } from 'element-plus'
import { PasswordStyle } from '@/pages/login/apis/type';

defineOptions({
  name: 'EditPassword'
})

interface PasswordInfo {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const props = withDefaults(defineProps<{
  modelValue: boolean;
}>(), {
  modelValue: true
});

const emit = defineEmits<{
  (e: 'submit', payload: { currentPwd: string; newPwd: string; secondPwd: string }): void
  (e: 'cancel'): void
  (e: 'update:modelValue', value: boolean): void
}>()

const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const userStore = useUserStore()
const router = useRouter()

const formData = ref<PasswordInfo>({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const formRef = ref<FormInstance>();
const isNewPasswordFocused = ref(false);

// 密码强度验证
const passwordStrength = reactive({
  isLongEnough: false,
  hasUpperCaseAndDigit: false,
  hasSpecialChar: false,
  isDifferentFromCurrent: false
});

const checkPasswordStrength = (password: string) => {
  passwordStrength.isLongEnough = password.length >= 8 && password.length <= 32
  passwordStrength.hasUpperCaseAndDigit = /[A-Z]/.test(password) && /[0-9]/.test(password)
  passwordStrength.hasSpecialChar = /[ ~!@#$%^&*()_+\-=\[\]{}<>;':"\\|,.\/?]/.test(password)
  passwordStrength.isDifferentFromCurrent = password !== formData.value.currentPassword && password.length > 0 && formData.value.currentPassword.length > 0
}

// 密码强度验证
const validatePassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error(t('setting.editPassword.newPasswordRequired')))
  } else {
    checkPasswordStrength(value)
    const { isLongEnough, hasUpperCaseAndDigit, hasSpecialChar, isDifferentFromCurrent } = passwordStrength
    if (!isLongEnough) {
      callback(new Error(t('setting.editPassword.passwordLengthError')))
    } else if (!hasUpperCaseAndDigit) {
      callback(new Error(t('setting.editPassword.passwordFormatError')))
    } else if (!hasSpecialChar) {
      callback(new Error(t('setting.editPassword.passwordSpecialCharError')))
    } else if (!isDifferentFromCurrent && formData.value.currentPassword.length > 0) {
      callback(new Error(t('setting.editPassword.samePasswordError')))
    } else {
      callback()
    }
  }
}

// 确认密码验证
const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error(t('setting.editPassword.confirmPasswordRequired')))
  } else if (value !== formData.value.newPassword) {
    callback(new Error(t('setting.editPassword.passwordNotMatchError')))
  } else {
    callback()
  }
}

/**
 * isDifferent 是否验证密码相同
 */
const validPasswordStyle = (valid: boolean, isDifferent: boolean = false): PasswordStyle => {
  let iconName: PasswordStyle["iconName"] = "icon-gary-check"
  let className = "notLength"
  if (formData.value.newPassword.length > 0 && !valid) {
    iconName = isDifferent ? (!valid ? "icon-red-close" : "icon-green-check") : "icon-red-close"
    className = "invalid"
  } else if (formData.value.newPassword.length > 0 && valid) {
    iconName = "icon-green-check"
    className = "valid"
  }
  return {
    'className': [className],
    'iconName': iconName,
  }
}

const rules = computed(() => reactive<FormRules<PasswordInfo>>({
  currentPassword: [
    { required: true, message: t('setting.editPassword.currentPasswordRequired'), trigger: 'blur' }
  ],
  newPassword: [
    { validator: validatePassword, trigger: ['blur', 'change'] }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}))

watch(() => formData.value.newPassword, checkPasswordStrength)
watch(() => formData.value.currentPassword, () => checkPasswordStrength(formData.value.newPassword))

watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // 重置表单
    formData.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
    isNewPasswordFocused.value = false
    isProcessing.value = false
    formRef.value?.clearValidate()
  }
})

const handleNewPasswordInput = (val: string) => {
  let value = val.replace(/[\u4e00-\u9fa5]/g, '')
  formData.value.newPassword = value
}

const handleConfirmPasswordInput = (val: string) => {
  let value = val.replace(/[\u4e00-\u9fa5]/g, '')
  formData.value.confirmPassword = value
}

const isProcessing = ref(false)

const submit = async () => {
  if (!formRef.value || isProcessing.value) return

  try {
    // 验证表单
    await formRef.value.validate()

    isProcessing.value = true

    // 调用API修改密码
    await changePasswordApi({
      currentPwd: encryptPasswordMD5(formData.value.currentPassword),
      newPwd: encryptPasswordMD5(formData.value.newPassword),
      secondPwd: encryptPasswordMD5(formData.value.confirmPassword)
    })

    ElMessage.success(t('setting.editPassword.updatePasswordSuccess'))

    // 修改密码成功后退出登录
    setTimeout(() => {
      userStore.logout()
      router.push('/login')
    }, 1500) // 延迟1.5秒让用户看到成功消息

  } catch (error: any) {
    console.error('修改密码异常:', error)
    ElMessage.error(error.message || t('setting.editPassword.updatePasswordFailed'))
  } finally {
    isProcessing.value = false
  }
}

const cancelShowDrawer = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

.custom-form-item {
  :deep(.el-form-item__label) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #222527;
    margin-bottom: 8px;
    line-height: 20px;
  }

  &.is-error :deep(.el-input__wrapper) {
    background: #FFF2EE;
    border: 1px solid #FD3627;
    box-shadow: none;
  }
}

.custom-input {
  width: 100%;

  :deep(.el-input__wrapper) {
    border: 1px solid #E5E6EB;
    box-shadow: none;
    border-radius: 6px;
    height: 40px;
    padding: 0 12px;
    background-color: #ffffff;
  }

  :deep(.el-input__inner) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-size: 14px;
    color: #222527;

    &::placeholder {
      color: #A8ABB2;
    }
  }

  :deep(.el-input__suffix) {
    .el-input__suffix-inner {
      .el-input__icon {
        color: #A8ABB2;

        &:hover {
          color: #606266;
        }
      }
    }
  }
}

:deep(.el-form-item__error) {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #FD3627FF;
}

.cancel-btn {
  background-color: #FFFFFF !important;
  border: 1px solid #E5E6EB !important;
  color: #222527 !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
}

.cancel-btn:hover {
  background-color: #F8F9FA !important;
  border-color: #E5E6EB !important;
}

.confirm-btn {
  background-color: #030814 !important;
  border: 1px solid #030814 !important;
  color: #FFFFFF !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
  margin-left: 0px !important;
}

.confirm-btn:hover {
  background-color: #1a1f2e !important;
  border-color: #1a1f2e !important;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;

  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }

  .el-drawer__close-btn {
    padding-right: 24px;
  }
}

:global(.el-popover.el-popper.password-strength-popper) {
  padding: 16px;
}

// 密码强度提示弹窗样式
.password-strength-popper {
  border-radius: 8px;
  padding: 16px;
  border: none;

  .password-strength-popover-content {
    .title {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #222527;
      margin: 0 0 12px 0;
    }

    div:not(.title) {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-size: 13px;
      color: #a9a9a9;
      /* Lighter gray for readability on black */
      transition: color 0.2s;

      .el-icon {
        margin-right: 8px;
        font-size: 16px;
      }

      &.valid {
        color: #3EB342;
      }

      &.invalid {
        color: #FD3627;
      }

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}
</style>