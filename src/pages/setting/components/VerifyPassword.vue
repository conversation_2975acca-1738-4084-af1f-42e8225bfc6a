<template>
  <el-drawer v-model="showDrawer" size="400px" style="--el-drawer-padding-primary: 0px"
    header-class="custom-drawer-header" :close-on-click-modal="false" :destroy-on-close="true"
    :close-on-press-escape="false">
    <template #header>
      <div
        class="font-family-[PingFangSC-Medium] text-18px color-[#222527] h-60px border-b-1 mb-0 flex-row flex-items-center pt-18px pb-18px pl-24px">
        <span>{{ t('setting.personal.verifyTitle') }}</span>
      </div>
    </template>
    <template #default>
      <div class="p-24px">
        <div class="color-[#6B7275] text-14px line-height-20px mb-24px">{{ props.type === 0 ?
          t('setting.personal.verifyPasswordDesc') : t('setting.personal.verifyPasswordDesc2') }}</div>
        <!-- 密码表单 -->
        <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" hide-required-asterisk>
          <el-form-item :label="t('setting.personal.password')" prop="currentPassword" class="custom-form-item mb-20px">
            <el-input v-model="formData.currentPassword" type="password" show-password
              :placeholder="t('setting.editPassword.placeholder')" class="custom-input" size="large"
              :disabled="isProcessing" />
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div style="border-top: 1px solid #E5E6EB;"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
        <el-button class="cancel-btn w-68px h-32px text-14px text-[#222527] font-family-[PingFangSC-Regular] mr-8px"
          @click="cancelShowDrawer" :disabled="isProcessing">
          {{ t('setting.editPassword.cancel') }}
        </el-button>
        <el-button class="confirm-btn w-68px h-32px text-14px text-white font-family-[PingFangSC-Regular]"
          @click="submit" :loading="isProcessing">
          {{ t('setting.editPassword.confirm') }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { t } from '@@/i18n'
import { ElMessage } from 'element-plus'
import { bind2FAApi, unbind2FAApi } from '../apis'
import { encryptPasswordMD5 } from '@@/utils/crypto'
import type { Bind2FAData } from '../apis/type'

defineOptions({
  name: 'VerifyPassword'
})

interface PasswordInfo {
  currentPassword: string;
}

const props = withDefaults(defineProps<{
  modelValue: boolean;
  type: number; // 0 绑定 1 解绑
  sysSeqId: string; // 绑定流水号，解绑需要；
}>(), {
  modelValue: true,
  type: 0,
  sysSeqId: ''
});

const emit = defineEmits<{
  (e: 'bind-success', payload: Bind2FAData): void
  (e: 'unbind-success'): void
  (e: 'cancel'): void
  (e: 'update:modelValue', value: boolean): void
}>()

const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formData = ref<PasswordInfo>({
  currentPassword: '',
});

const formRef = ref<FormInstance>();

// 密码强度验证
const validatePassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error(t('setting.editPassword.newPasswordRequired')))
  } else {
    callback()
  }
}

const rules = computed(() => reactive<FormRules<PasswordInfo>>({
  currentPassword: [
    { required: true, message: t('setting.editPassword.currentPasswordRequired'), trigger: 'blur' }
  ]
}))

watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // 重置表单
    formData.value = {
      currentPassword: '',
    }
    isProcessing.value = false
    formRef.value?.clearValidate()
  }
})

const isProcessing = ref(false)

const submit = () => {
  if (!formRef.value || isProcessing.value) return

  // 验证表单
  formRef.value.validate().then(() => {
    isProcessing.value = true

    if (props.type === 0) {
      bindResp()
    } else {
      unbindResp()
    }
  })
}

const bindResp = () => {
  // 调用API创建绑定2FA请求
  bind2FAApi(encryptPasswordMD5(formData.value.currentPassword)).then((res) => {
    emit('bind-success', res.data)
  }).finally(() => {
    isProcessing.value = false
  });
}

const unbindResp = () => {
  // 调用API解绑2FA
  unbind2FAApi(props.sysSeqId, encryptPasswordMD5(formData.value.currentPassword)).then(() => {
    emit('unbind-success')
  }).finally(() => {
    isProcessing.value = false
  });
}

const cancelShowDrawer = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

.custom-form-item {
  :deep(.el-form-item__label) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #222527;
    margin-bottom: 8px;
    line-height: 20px;
  }

  &.is-error :deep(.el-input__wrapper) {
    background: #FFF2EE;
    border: 1px solid #FD3627;
    box-shadow: none;
  }
}

.custom-input {
  width: 100%;

  :deep(.el-input__wrapper) {
    border: 1px solid #E5E6EB;
    box-shadow: none;
    border-radius: 6px;
    height: 40px;
    padding: 0 12px;
    background-color: #ffffff;

    &:hover {
      border-color: #C0C4CC;
    }

    &.is-focus {
      border-color: #409EFF;
    }
  }

  :deep(.el-input__inner) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-size: 14px;
    color: #222527;

    &::placeholder {
      color: #A8ABB2;
    }
  }

  :deep(.el-input__suffix) {
    .el-input__suffix-inner {
      .el-input__icon {
        color: #A8ABB2;

        &:hover {
          color: #606266;
        }
      }
    }
  }
}

:deep(.el-form-item__error) {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #FD3627FF;
}

.cancel-btn {
  background-color: #FFFFFF !important;
  border: 1px solid #E5E6EB !important;
  color: #222527 !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
}

.cancel-btn:hover {
  background-color: #F8F9FA !important;
  border-color: #E5E6EB !important;
}

.confirm-btn {
  background-color: #030814 !important;
  border: 1px solid #030814 !important;
  color: #FFFFFF !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
  margin-left: 0px !important;
}

.confirm-btn:hover {
  background-color: #1a1f2e !important;
  border-color: #1a1f2e !important;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;

  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }

  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
</style>