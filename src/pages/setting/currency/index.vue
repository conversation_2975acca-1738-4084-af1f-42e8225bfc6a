<script setup lang="ts">
import { onMounted, reactive, ref, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { t } from '@@/i18n'
import { bindFiatBankAccountApi, deleteFiatBankAccountApi, modifyFiatBankAccountApi, queryFiatBankAccountListApi } from '../apis'
import { BusinessEnumType } from '@/common/apis/common/type'
import { useEnumStore } from '@/pinia/stores/enumStore'
import EditCurrency from '../components/EditCurrency.vue'
import CommonDialog from '@/common/components/Dialog/CommonDialog.vue'
import type { FiatAcctInfo } from '../apis/type'
const enumStore = useEnumStore();
const currencyList = enumStore.getEnumList(BusinessEnumType.FIAT_CCY);
const regionList = enumStore.getEnumList(BusinessEnumType.NATIONALITY);

const router = useRouter()
const showEditCurrency = ref(false)
const currentCurrencyInfo = ref<FiatAcctInfo>({} as FiatAcctInfo)
const editMode = ref<'view' | 'edit' | 'create'>('create')
const showDeleteDialog = ref(false)
const itemToDelete = ref<FiatAcctInfo | null>(null)

const statusList = computed(() => [
  { value: 'approved', label: t('setting.currency.status.approved') },
  { value: 'rejected', label: t('setting.currency.status.rejected') },
  { value: 'auditing', label: t('setting.currency.status.auditing') },
])

const form = reactive({
  currency: '',
  country: '',
  status: '',
})

const tableData = ref<FiatAcctInfo[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

async function getList() {
  loading.value = true
  try {
    const res = await queryFiatBankAccountListApi({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ccy: form.currency,
      bankCtry: form.country,
      status: form.status,
    })
    tableData.value = (res.data.fiatAcctInfoList || []) as unknown as FiatAcctInfo[]
    total.value = res.data.totalCount || 0
  }
  catch (error) {
    console.error(error)
    tableData.value = []
    total.value = 0
  }
  finally {
    loading.value = false
  }
}

onMounted(() => {
  getList()
})

watch([currentPage, pageSize], getList)

const getStatusText = (status: FiatAcctInfo['status']) => {
  switch (status) {
    case 'approved':
      return t('setting.currency.status.approved')
    case 'rejected':
      return t('setting.currency.status.rejected')
    case 'auditing':
      return t('setting.currency.status.auditing')
    default:
      return ''
  }
}
const getStatusClass = (status: FiatAcctInfo['status']) => {
  switch (status) {
    case 'approved':
      return 'status-approved'
    case 'rejected':
      return 'status-rejected'
    case 'auditing':
      return 'status-pending'
    default:
      return ''
  }
}

const getCountryText = (countryCode: string) => {
  const country = regionList.value.find((item: { enumCode: string, enumDescCn: string }) => item.enumCode === countryCode)
  return country ? country.enumDescCn : countryCode
}

function handleReset() {
  form.currency = ''
  form.country = ''
  form.status = ''
  handleSearch()
}

function handleSearch() {
  currentPage.value = 1
  getList()
}

function handleBindAccount() {
  showEditCurrency.value = true
  currentCurrencyInfo.value = {} as FiatAcctInfo
  editMode.value = 'create'
}

function handleView(row: FiatAcctInfo) {
  showEditCurrency.value = true
  currentCurrencyInfo.value = row
  editMode.value = 'view'
}

function handleEdit(row: FiatAcctInfo) {
  showEditCurrency.value = true
  currentCurrencyInfo.value = row
  editMode.value = 'edit'
}

const deleteDialogContent = computed(() => {
  if (!itemToDelete.value)
    return ''
  const displayName = itemToDelete.value.acctNickName || itemToDelete.value.acctName
  return t('setting.currency.dialog.deleteConfirm', { displayName })
})

async function handleDelete(row: FiatAcctInfo) {
  itemToDelete.value = row
  showDeleteDialog.value = true
}

async function confirmDelete() {
  if (!itemToDelete.value) return
  try {
    await deleteFiatBankAccountApi({ sysSeqId: itemToDelete.value.sysSeqId })
    ElMessage.success(t('setting.currency.message.deleteSuccess'))
    getList()
  } catch (error) {
    console.error(error)
  } finally {
    showDeleteDialog.value = false
    itemToDelete.value = null
  }
}

async function handleSubmit(payload: FiatAcctInfo) {
  try {
    if (payload.useQualificationInfo === 'Y') {
      payload.payeeAddr = ''
      payload.payeeApartment = ''
      payload.payeeState = ''
      payload.payeeCity = ''
      payload.payeeCtry = ''
      payload.payeePostalCode = ''
    }
    if (!payload.acctNickName || payload.acctNickName === '') {
      payload.acctNickName = payload.acctName
    }
    if (editMode.value === 'create') {
      await bindFiatBankAccountApi(payload)
      ElMessage.success(t('setting.currency.message.bindSuccess'))
    }
    else if (editMode.value === 'edit') {
      await modifyFiatBankAccountApi(payload)
      ElMessage.success(t('setting.currency.message.modifySuccess'))
    }
    showEditCurrency.value = false
    getList()
  }
  catch (error) {
    console.error(error)
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  getList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getList()
}
</script>

<template>
  <div class="pb-40px flex flex-col  w-full">
    <span class="text-28px text-[#222527] font-600 font-family-[PingFangSC-Semibold] mb-24px">
      {{ t('setting.currency.title') }}
    </span>
    <ElForm :model="form" inline class="mb-30px flex flex-wrap gap-y-12px filter-form" hide-required-asterisk>
      <ElFormItem class="w-304px">
        <ElSelect v-model="form.currency" :placeholder="t('setting.currency.placeholder')" style="width: 100%;">
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('setting.currency.currency') }}</span>
          </template>
          <ElOption v-for="info in currencyList" :key="info.enumCode" :label="info.enumDescCn" :value="info.enumCode" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem class="w-304px" prop="country">
        <ElSelect v-model="form.country" filterable :placeholder="t('setting.currency.placeholder')"
          style="width: 100%;">
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('setting.currency.bankCountry') }}</span>
          </template>
          <ElOption v-for="info in regionList" :key="info.enumCode" :label="info.enumDescCn" :value="info.enumCode" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem class="w-304px">
        <ElSelect v-model="form.status" :placeholder="t('setting.currency.placeholder')" style="width: 100%;">
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('setting.currency.auditStatus') }}</span>
          </template>
          <ElOption v-for="info in statusList" :key="info.value" :label="info.label" :value="info.value" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem>
        <ElButton @click="handleReset"
          class="reset-btn min-w-76px text-14px text-[#222527] font-family-[PingFangSC-Regular] btn-hover-scale-sm">
          <SvgIcon name="icon-reset" class="mr-9px" />
          {{ t('setting.currency.reset') }}
        </ElButton>
        <ElButton type="primary" @click="handleSearch"
          class="min-w-76px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm">
          <SvgIcon name="icon-query" class="mr-9px" />
          {{ t('setting.currency.search') }}
        </ElButton>
      </ElFormItem>
    </ElForm>

    <div class="flex justify-between items-center mb-16px">
      <span class="text-14px text-[#222527] font-family-[PingFangSC-Regular]">{{ t('setting.currency.totalItems', {
        total
      })
        }}</span>
      <ElButton @click="handleBindAccount"
        class="min-w-104px text-14px text-[#222527] font-family-[PingFangSC-Regular] bg-white border border-[#E5E6EB] rounded-6px btn-hover-scale-sm">
        <SvgIcon name="icon-connect" class="mr-9px" />
        {{ t('setting.currency.bindAccount') }}
      </ElButton>
    </div>

    <div class="overflow-hidden rounded-12px border border-[#E5E6EB]">
      <ElTable v-loading="loading" :data="tableData" style="width: 100%" row-key="sysSeqId"
        :empty-text="t('setting.currency.tableEmpty')"
        header-cell-class-name="!bg-[#F8F9FA] font-family-[PingFangSC-Semibold] !text-12px !text-[#6B7275]"
        :header-row-style="{ height: '44px' }" :row-style="{ height: '48px' }">
        <ElTableColumn type="index" :label="t('setting.currency.table.index')" width="50" />
        <ElTableColumn prop="acctNo" :label="t('setting.currency.table.accountNumber')" />
        <ElTableColumn prop="acctName" :label="t('setting.currency.table.bankName')" />
        <ElTableColumn prop="bankCtry" :label="t('setting.currency.table.bankCountry')" width="100">
          <template #default="{ row }">
            <span>{{ getCountryText(row.bankCtry) }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="ccy" :label="t('setting.currency.table.accountCurrency')" />
        <ElTableColumn prop="payMethod" :label="t('setting.currency.table.paymentMethod')" />
        <ElTableColumn prop="payeeName" :label="t('setting.currency.table.payeeName')" />
        <ElTableColumn prop="acctNickName" :label="t('setting.currency.table.accountNickname')" />
        <ElTableColumn prop="status" :label="t('setting.currency.table.auditStatus')">
          <template #default="{ row }">
            <div
              :class="['flex items-center justify-center rounded-12px w-fit px-[12px] font-family-[PingFangSC-Regular] font-400 text-[12px] height-24px', getStatusClass(row.status)]">
              <span>{{ getStatusText(row.status) }}</span>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn :label="t('setting.currency.table.actions')" width="150" fixed="right">
          <template #default="{ row }">
            <ElButton link type="primary" :style="{ color: '#030814' }" @click="handleView(row)"
              class="btn-hover-scale-sm">
              <SvgIcon name="icon-open" />
            </ElButton>
            <ElButton link type="primary" :disabled="row.status !== 'approved' && row.status !== 'rejected'"
              :style="{ color: (row.status === 'approved' || row.status === 'rejected') ? '#030814' : '#d2d2d2' }"
              @click="handleEdit(row)" class="btn-hover-scale-sm">
              <SvgIcon name="icon-edit" />
            </ElButton>
            <ElButton link type="primary" :disabled="row.status !== 'rejected' && row.status !== 'approved'"
              :style="{ color: (row.status === 'rejected' || row.status === 'approved') ? '#030814' : '#d2d2d2' }"
              @click="handleDelete(row)" class="btn-hover-scale-sm">
              <SvgIcon name="icon-deleted" />
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>

    <div class="flex justify-end mt-4">
      <ElPagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
        :total="total" layout="sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
        <template #jumper>{{ t('setting.currency.pagination.jumper') }}</template>
      </ElPagination>
    </div>
    <EditCurrency v-model="showEditCurrency" :currency-info="currentCurrencyInfo" :mode="editMode"
      @submit="handleSubmit" @cancel="showEditCurrency = false" />
    <CommonDialog v-model:visible="showDeleteDialog" :title="t('setting.currency.dialog.title')" iconClass="warning"
      :message="deleteDialogContent" @confirm="confirmDelete" />
  </div>
</template>

<style scoped>
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-approved {
  background-color: #EBF7EB;
  color: #289532;
}

.status-rejected {
  background-color: #FFF1EC;
  color: #DB1507;
}

.status-pending {
  background-color: #EBF6FF;
  color: #0066DB;
}

:deep(.el-table) {
  --el-table-header-bg-color: #F8F9FA;
  --el-table-header-text-color: #6B7275;
  --el-table-header-text-font-size: 12px;
  --el-table-header-text-font-weight: 600;
  --el-table-header-text-font-family: PingFangSC-Semibold;
  --el-table-text-color: #222527;
  --el-table-text-font-size: 14px;
  --el-table-text-font-weight: 400;
  --el-table-text-font-family: PingFangSC-Regular;
}

:deep(.el-table .el-table__header-wrapper) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-table .el-table__header-wrapper tr) {
  --el-table-border: none;
}

:deep(.el-table .el-table__header-wrapper tr .el-table__cell) {
  padding: 10px 0;
}

:deep(.el-table tbody tr .el-table__cell) {
  padding: 12px 0;
  color: #222527;
}

.filter-form :deep(.el-form-item) {
  margin-right: 16px;
  margin-bottom: 0px;
}

.filter-form :deep(.el-form-item:last-child) {
  margin-right: 0;
}

.reset-btn:hover {
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
}
</style>