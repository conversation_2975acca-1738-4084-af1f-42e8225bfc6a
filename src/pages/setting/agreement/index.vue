<template>
  <div class=" bg-white w-full">
    <div class="mb-32px">
      <span class="font-family-[PingFangSC-Semibold] font-600 text-28px text-[#222527] mb-16px">{{
        t('setting.agreement.title') }}</span>
      <p class="font-family-[PingFangSC-Regular] font-weight-400 text-14px text-[#6B7275]">{{
        t('setting.agreement.description') }}</p>
    </div>

    <div class="bg-white">
      <div class="font-family-[PingFangSC-Regular] text-14px text-[#222527] mb-22px">
        {{ t('setting.agreement.totalItems', { total: agreementTable.length }) }}
      </div>

      <el-table v-loading="loading" :data="agreementTable" style="width: 100%"
        header-cell-class-name="!bg-[#F8F9FA] font-family-[PingFangSC-Semibold] !text-12px !text-[#6B7275]"
        :header-row-style="{ height: '44px' }" :row-style="{ height: '48px' }"
        cell-class-name="!font-family-[PingFangSC-Regular] !text-14px !text-[#222527]"
        class="rounded-lg overflow-hidden">
        <el-table-column prop="index" :label="t('setting.agreement.table.index')" width="80" align="center"
          :header-align="'center'">
          <template #default="{ $index }">
            <span>
              {{ (currentPage - 1) * pageSize + $index + 1 }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="agreementName" :label="t('setting.agreement.table.name')" align="left"
          :header-align="'left'">
          <template #default="{ row }">
            <span>
              {{ row.agreementName }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="acceptanceAgreementTime" :label="t('setting.agreement.table.time')" width="280"
          align="left" :header-align="'left'">
          <template #default="{ row }">
            <span>
              {{ row.acceptanceAgreementTime ? formatDateTime(row.acceptanceAgreementTime) : '' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column :label="t('setting.agreement.table.actions')" width="80" align="center"
          :header-align="'center'">
          <template #default="{ row }">
            <SvgIcon name="icon-open" @click="viewAgreement(row)" class="cursor-pointer" />
          </template>
        </el-table-column>
      </el-table>

      <!-- <div class="flex justify-end items-center gap-4 mt-4">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCount"
          layout="prev, pager, next, sizes, jumper"
          @size-change="handlePageSizeChange"
          @current-change="changePage"
          class="custom-pagination"
        >
          <template #jumper>{{ t('setting.agreement.pagination.jumper') }}</template>
        </el-pagination>
      </div> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { queryAgreementListApi } from '../apis'
import type { AgreementVO } from '../apis/type'
import { ElMessage } from 'element-plus'
import { formatDateTime } from '@/common/utils/datetime'

const { t, locale } = useI18n()
// 响应式数据
const currentPage = ref(1)
const pageSize = ref(50)
// const totalCount = ref(0)
const agreementList = ref<AgreementVO[]>([])
const loading = ref(false)

const agreementTable = computed(() => {
  const lang = locale.value === 'en-US' ? 'en' : locale.value
  return agreementList.value.filter((item: any) => item.languageCode === lang)
})

// 方法
const loadAgreements = async () => {
  loading.value = true
  try {
    const res = await queryAgreementListApi()

    if (res && res.data) {
      agreementList.value = res.data.agreementVOs || []
    }
  } catch (error) {
    ElMessage.error(t('setting.agreement.message.fetchFailed'))
    console.error("获取协议列表失败", error)
  } finally {
    loading.value = false
  }
}

const changePage = (page: number) => {
  currentPage.value = page
  loadAgreements()
}

const handlePageSizeChange = (newPageSize: number) => {
  pageSize.value = newPageSize
  currentPage.value = 1
  loadAgreements()
}

const viewAgreement = (agreement: AgreementVO) => {
  if (agreement.agreementUrl) {
    window.open(agreement.agreementUrl, '_blank')
  }
}

// 生命周期
onMounted(() => {
  loadAgreements()
})
</script>

<style scoped>
/* 自定义分页样式 */
:deep(.custom-pagination) {
  --el-pagination-bg-color: #FFFFFF;
  --el-pagination-text-color: #222527;
  --el-pagination-border-radius: 6px;
}

/* 分页器选中状态样式 */
:deep(.custom-pagination .el-pager li.is-active) {
  color: #030814 !important;
  background: #F5F5F5 !important;
  border-radius: 4px !important;
}

:deep(.custom-pagination .el-pagination__sizes .el-select .el-input__inner) {
  font-family: 'PingFangSC-Regular';
  font-size: 14px;
  color: #222527;
}

:deep(.custom-pagination .el-pagination__jump) {
  font-family: 'PingFangSC-Regular';
  font-size: 14px;
  color: #6B7275;
}

/* 表格样式调整 */
/* :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
} */

/* :deep(.el-table th.el-table__cell) {
  background-color: #F8F9FA !important;
  color: #6B7275 !important;
  font-family: 'PingFangSC-Semibold' !important;
  font-weight: 600 !important;
  font-size: 12px !important;
  height: 44px !important;
} */

/* :deep(.el-table td.el-table__cell) {
  font-family: 'PingFangSC-Regular' !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #222527 !important;
}

:deep(.el-table__row:hover > td) {
  background-color: #f9fafb !important;
} */

:deep(.el-table) {
  --el-table-header-bg-color: #F8F9FA;
  --el-table-header-text-color: #6B7275;
  --el-table-header-text-font-size: 12px;
  --el-table-header-text-font-weight: 600;
  --el-table-header-text-font-family: PingFangSC-Semibold;
  --el-table-text-color: #222527;
  --el-table-text-font-size: 14px;
  --el-table-text-font-weight: 400;
  --el-table-text-font-family: PingFangSC-Regular;

  .el-table__header-wrapper {
    border-radius: 12px;
    overflow: hidden;

    tr {
      --el-table-border: none;

      .el-table__cell {
        padding: 10px 0;
      }
    }
  }

  tbody tr {
    .el-table__cell {
      padding: 12px 0;
      color: #222527;
    }
  }
}
</style>
