<template>
  <div class=" bg-white min-h-screen pb-60px pr-24px">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center min-h-400px">
      <el-icon class="is-loading text-24px text-[#409eff]">
        <Loading />
      </el-icon>
    </div>

    <!-- 内容区域 -->
    <div v-else>
      <!-- Header -->
      <div class="flex items-center mb-48px">
        <div class="w-44px h-44px rounded-12px overflow-hidden mr-20px bg-[#ff3e6c] flex items-center justify-center">
          <SvgIcon name="icon-setting-company" class="text-white !w-24px !h-24px" />
        </div>
        <div class="font-family-[PingFangSC-Semibold] font-600 text-28px text-[#222527]">{{ corpInfo?.englishName ||
          '-' }}</div>
      </div>
      <!-- 基础信息 -->
      <div class="flex flex-col mb-24px">
        <span class="font-family-[PingFangSC-Semibold] font-600 text-18px text-[#222527] mb-6px">{{
          t('setting.enterprise.basicInfo') }}</span>
        <div class="grid grid-cols-3">
          <div class="info-item">
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
              t('setting.enterprise.enterpriseId') }}</span>
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{ corpInfo?.merCustId
              || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
              t('setting.enterprise.corporationType') }}</span>
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
              formatCorporationType(corpInfo?.corporationType) }}</span>
          </div>
          <div class="info-item">
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
              t('setting.enterprise.industry') }}</span>
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
              formatIndustry(corpInfo?.industry, corpInfo?.subIndustry) }}</span>
          </div>
          <div class="info-item">
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
              t('setting.enterprise.legalName') }}</span>
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
              corpInfo?.englishName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
              t('setting.enterprise.website') }}</span>
            <span
              class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527] break-all overflow-wrap-break-word">{{
                corpInfo?.website || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
              t('setting.enterprise.registrationNumber') }}</span>
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
              corpInfo?.registCertificateNumber || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
              t('setting.enterprise.companySize') }}</span>
            <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
              corpInfo?.staffNumber ? `${corpInfo?.staffNumber}${t('setting.enterprise.sizeUnit')}` : '-' }}</span>
          </div>
        </div>
      </div>
      <div class="divider" />
      <span class="font-family-[PingFangSC-Semibold] font-600 text-18px text-[#222527] mb-24px  mb-6px">{{
        t('setting.enterprise.registeredAddress') }}</span>
      <div class="info-item">
        <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
          t('setting.enterprise.streetAddress') }}</span>
        <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
          corpInfo?.registerAddress || '-' }}</span>
      </div>
      <!-- 注册地址 -->
      <div class="info-section grid grid-cols-3  mb-24px">
        <div class="info-item">
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
            t('setting.enterprise.apartment') }}</span>
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
            corpInfo?.registerApartment || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
            t('setting.enterprise.city') }}</span>
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
            corpInfo?.registerCity || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
            t('setting.enterprise.state') }}</span>
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
            corpInfo?.registerProvince || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
            t('setting.enterprise.postalCode') }}</span>
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
            corpInfo?.registerPostalCode || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
            t('setting.enterprise.countryOrRegion') }}</span>
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
            formatCountry(corpInfo?.registerCode) }}</span>
        </div>
      </div>
      <div class="divider" />
      <span class="font-family-[PingFangSC-Semibold] font-600 text-18px text-[#222527] mb-6px">{{
        t('setting.enterprise.tradingAddress') }}</span>
      <div class="info-item">
        <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
          t('setting.enterprise.streetAddress') }}</span>
        <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
          corpInfo?.businessAddress || '-' }}</span>
      </div>
      <!-- 交易地址 -->
      <div class="info-section grid grid-cols-3  mb-24px">
        <div class="info-item">
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
            t('setting.enterprise.apartment') }}</span>
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
            corpInfo?.businessApartment || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
            t('setting.enterprise.city') }}</span>
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
            corpInfo?.businessCity || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
            t('setting.enterprise.state') }}</span>
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
            corpInfo?.businessProv || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
            t('setting.enterprise.postalCode') }}</span>
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
            corpInfo?.businessPostalCode || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] mb-12px block">{{
            t('setting.enterprise.countryOrRegion') }}</span>
          <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">{{
            formatCountry(corpInfo?.mainBusinessAddress) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { t } from '@@/i18n'
import { queryCorpInfoApi } from '../apis'
import type * as Setting from '../apis/type'
import { useEnumStore } from '@/pinia/stores/enumStore'
import { BusinessEnumType } from '@/common/apis/common/type'

type CorpInfo = Setting.QueryCorpInfoResponseData['data']

const corpInfo = ref<CorpInfo>()
const { getEnumList } = useEnumStore()
const loading = ref(false)

onMounted(() => {
  getCorpInfo()
})

/**
 * 获取企业信息
 */
const getCorpInfo = async () => {
  if (loading.value) return

  loading.value = true
  try {
    const res = await queryCorpInfoApi({})
    corpInfo.value = res.data
  }
  catch (error) {
    console.error('Failed to fetch enterprise info:', error)
    ElMessage.error(t('setting.enterprise.fetchInfoFailed'))
  }
  finally {
    loading.value = false
  }
}

/**
 * 格式化行业信息
 * @param industry - 主行业代码
 * @param subIndustry - 子行业代码
 * @returns 格式化后的行业字符串
 */
const formatIndustry = (industry: string | undefined, subIndustry: string | undefined) => {
  const industryList = getEnumList(BusinessEnumType.INDUSTRY)
  const industryItem = industryList.value.find((item: { enumCode: string | undefined }) => item.enumCode === industry)

  if (!industryItem) {
    return '-'
  }

  const subIndustryList = industryItem?.children
  const subIndustryItem = subIndustryList?.find((item: { enumCode: string | undefined }) => item.enumCode === subIndustry)

  return industryItem.enumDescCn + (subIndustryItem ? `, ${subIndustryItem.enumDescCn}` : '')
}

/**
 * 格式化国家代码为国家名称
 * @param code - 国家代码字符串，多个代码用逗号分隔
 * @returns 格式化后的国家名称字符串，多个国家用逗号分隔
 */
const formatCountry = (code: string | undefined): string => {
  if (!code?.trim()) {
    return '-'
  }

  const countryCodes = code.split(',').map(item => item.trim()).filter(Boolean)
  if (countryCodes.length === 0) {
    return '-'
  }

  const countryList = getEnumList(BusinessEnumType.NATIONALITY)

  const countryNames = countryCodes
    .map(countryCode => {
      const countryItem = countryList.value.find(
        (item: { enumCode: string }) => item.enumCode === countryCode
      )
      return countryItem?.enumDescCn
    })
    .filter(Boolean) // 过滤掉 undefined 值

  return countryNames.length > 0 ? countryNames.join(', ') : '-'
}

/**
 * 格式化公司类型
 * @param type - 公司类型代码
 * @returns 格式化后的公司类型字符串
 */
const formatCorporationType = (type: string | undefined) => {
  const corporationTypeList = getEnumList(BusinessEnumType.CORPORATION_TYPE)
  const corporationTypeItem = corporationTypeList.value.find((item: { enumCode: string | undefined }) => item.enumCode === type)
  return corporationTypeItem?.enumDescCn || '-'
}

</script>

<style lang="scss" scoped>
.break-all {
  word-break: break-all;
  overflow-wrap: break-word;
}

.info-item {
  margin-top: 24px;
}

.divider {
  // width: 944px;
  margin-top: 14px;
  margin-bottom: 24px;
  height: 1px;
  background: #EDEDEE;
}
</style>