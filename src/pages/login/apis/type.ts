export enum SmsBusinessType {
  /** 用户重置密码 */
  USER_RESET_PWD = 'USER_RESET_PWD',
  /** 用户注册 */
  USER_REG = 'USER_REG',
}

export type SendEmailRequestData = ApiRequestData<{
  /** 邮箱 */
  email: string
  /** 业务类型 */
  businessType: string
}>

export type SendEmailResponseData = ApiResponseData<{
  /** 发送序列号 */
  sendSeqId: string
}>

export type RegisterFormData = ApiRequestData<{
  /** 发送序列号 */
  sendSeqId: string
  /** 密码 */
  password: string
  /** 确认密码 */
  againPassword: string
  /** 邮箱 */
  email: string
  /** 验证码 */
  verifyCode: string
}>

/** 注册请求数据 */
export type RegisterRequestData = ApiRequestData<{
  /** 发送序列号 */
  sendSeqId: string
  /** 密码 */
  password: string
}>

/** 注册响应数据 */
export type RegisterResponseData = ApiResponseData<{
  /** 用户ID */
  userId: string
}>

/** 忘记密码请求数据 */
export type ForgetPasswordRequestData = ApiRequestData<{
  /** 发送序列号 */
  sendSeqId: string
  /** 密码 */
  password: string
}>

/** 忘记密码响应数据 */
export type ForgetPasswordResponseData = ApiResponseData<{
  /** 令牌 */
  token: string
  /** 签名密钥 */
  signKey: string
  /** 初始化向量 */
  iv: string
  /** 密钥 */
  key: string
  /** 时间戳 */
  timestamp: number
}>

/** 忘记密码请求数据 */
export type ForgotPasswordRequestData = ApiRequestData<{
  /** 用户名或邮箱 */
  identifier: string
  /** 验证码 */
  code: string
}>

/** 忘记密码响应数据 */
export type ForgotPasswordResponseData = ApiResponseData<{
  /** 重置密码令牌 */
  resetToken: string
  /** 令牌过期时间（秒） */
  expiresIn: number
}>

/** 邮箱验证请求数据 */
export type EmailVerifyRequestData = ApiRequestData<{
  /** 发送序列号 */
  sendSeqId: string
  /** 验证码 */
  verifyCode: string
}>

/** 登录请求数据 */
export type LoginRequestData = ApiRequestData<{
  /** 邮箱 */
  email: string
  /** 密码 */
  password: string
}>

/** 登录响应数据 */
export type LoginResponseData = ApiResponseData<{
  /** 令牌 */
  token: string
  /** 签名密钥 */
  signKey: string
  /** 初始化向量 */
  iv: string
  /** 密钥 */
  key: string
  /** 时间戳 */
  timestamp: number
  isSuccess: boolean, 
  totalNum: number, 
  remainNum: number, 
  lockRemainTime: number
}>

/** 协议查询响应数据 */
export type AgreementQueryResponseData = ApiResponseData<{
  /** 协议列表 */
  agreementVOs: any[]
}>

export type PasswordStyle = {
  iconName: 'icon-gary-check' | 'icon-red-close' | 'icon-green-check',
  className: [string],
}