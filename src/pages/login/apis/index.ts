import type * as Auth from './type';
import { request } from '@/http/axios';

/** 发送邮箱 */
export function sendEmailApi(data: Auth.SendEmailRequestData) {
  return request<Auth.SendEmailResponseData>(
    {
      url: 'login/sendEmail',
      method: 'post',
      data,
    },
    { showError: false }
  );
}

/** 注册 */
export function registerApi(data: Auth.RegisterRequestData) {
  return request<Auth.RegisterResponseData>(
    {
      url: 'login/register',
      method: 'post',
      data,
    },
    { showError: false }
  );
}

/** 忘记密码 */
export function forgetPasswordApi(data: Auth.ForgetPasswordRequestData) {
  return request<Auth.ForgetPasswordResponseData>({
    url: 'login/forget',
    method: 'post',
    data,
  });
}

/** 邮箱验证 */
export function emailVerifyApi(data?: Auth.EmailVerifyRequestData) {
  return request<void>(
    {
      url: 'login/emailVerify',
      method: 'post',
      data,
    },
    { showError: false }
  );
}

/** 登录 */
export function loginApi(data: Auth.LoginRequestData) {
  return request<Auth.LoginResponseData>(
    {
      url: 'login/doLogin',
      method: 'post',
      data,
    },
    { showError: false }
  );
}

/** 协议 */
export function agreementQueryApi() {
  return request<Auth.AgreementQueryResponseData>(
    {
      url: 'login/queryAgreement',
      method: 'post',
    },
    { showError: false }
  );
}
