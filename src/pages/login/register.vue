<script lang="ts" setup>
import type { FormInstance, FormItemInstance, FormRules, InputInstance } from 'element-plus';
import {
  RegisterRequestData,
  SendEmailRequestData,
  EmailVerifyRequestData,
  RegisterFormData,
  SmsBusinessType,
  PasswordStyle,
} from './apis/type';
import { sendEmailApi, registerApi, emailVerifyApi } from './apis';
import { useFocus } from './composables/useFocus';
import { t, setLocale, LocaleType } from '@@/i18n';
import LoginSlot from './components/LoginSlot.vue';
import CommonDialog from '@/common/components/Dialog/CommonDialog.vue';
import { encryptPasswordMD5 } from '@/common/utils/crypto';
import { Check, Close } from '@element-plus/icons-vue';
const router = useRouter();

/** 登录表单元素的引用 */
const registerFormRef = ref<FormInstance | null>(null);
const emailInputRef = ref<FormItemInstance | null>(null);
const verifyCodeInputRef = ref<InputInstance | null>(null);
/** 登录按钮 Loading */
const loading = ref(false);
/** loading 按钮 Loading */
const countdownLoading = ref(false);
/** 倒计时 */
const countdown = ref(0);
/** 当前步骤 1 输入邮箱验证码 2 输入密码 */
const currentStep = ref(1);
// 密码输入框是否聚焦
const isPasswordFocused = ref(false);
// 自定义的邮箱认证报错信息，区别表单认证
const customEmailErrorMesg = ref('');
// 自定义注册报错信息
const customRegisterErrorMesg = ref('');

/** 注册表单数据 */
const registerFormData: RegisterFormData = reactive({
  email: '',
  password: '',
  againPassword: '',
  verifyCode: '',
  sendSeqId: '',
});

const showCancelDialog = ref(false);

const tipMesg = computed(() => {
  return currentStep.value === 1 ? t('register.tipMesgStep1') : t('register.tipMesgStep2');
});
const sendBtnMesg = computed(() => {
  if (countdownLoading.value) return {
    className: 'gray',
    text: t('register.sendingCode'),
  };

  return countdown.value > 0
    ? {
        className: 'gray',
        text: `${countdown.value}s`,
      }
    : {
        className: 'red',
        text: t('register.sendCode'),
      };
});

const passwordStrength = reactive({
  isLongEnough: false,
  hasUpperCaseAndDigit: false,
  hasSpecialChar: false,
});
const route = useRoute();

onMounted(() => {
  // 设置语言
  if (route.query.lang) {
    if (route.query.lang === 'en') {
      setLocale('en-US' as LocaleType);
    } else if (route.query.lang === 'zh-CN') {
      setLocale('zh-CN' as LocaleType);
    } else if (route.query.lang === 'zh-HK') {
      setLocale('zh-HK' as LocaleType);
    }
  }
});

const checkPasswordStrength = (password: string) => {
  passwordStrength.isLongEnough = password.length >= 8 && password.length <= 32;
  passwordStrength.hasUpperCaseAndDigit = /[A-Z]/.test(password) && /[0-9]/.test(password);
  passwordStrength.hasSpecialChar = /[^a-zA-Z0-9\u4e00-\u9fa5]/.test(password);
};
// 监听密码输入变化，校验规则
watch(() => registerFormData.password, checkPasswordStrength);

/** 发送验证码 */
const sendEmailRequest = () => {
  registerFormRef.value?.validateField('email', (valid) => {
    if (!valid) {
      return;
    }
    customEmailErrorMesg.value = '';
    countdownLoading.value = true;
    sendEmailApi({
      email: registerFormData.email,
      businessType: SmsBusinessType.USER_REG,
    })
      .then(({ data }) => {
        registerFormData.sendSeqId = data.sendSeqId;
        countdown.value = 60;
        // 启动倒计时
        const timer = setInterval(() => {
          countdown.value--;
          if (countdown.value <= 0) {
            clearInterval(timer);
            countdown.value = 0;
          }
        }, 1000);
      })
      .catch((error) => {
        customEmailErrorMesg.value = error.message;
      })
      .finally(() => {
        countdownLoading.value = false;
        verifyCodeInputRef.value?.focus();
      });
  });
};

/** 验证邮箱验证码 */
const verifyEmailCodeResp = () => {
  registerFormRef.value?.validateField(['verifyCode', 'email'], async (valid: boolean) => {
    if (!valid) {
      return;
    }
    customEmailErrorMesg.value = '';
    loading.value = true;
    emailVerifyApi({
      sendSeqId: registerFormData.sendSeqId,
      verifyCode: registerFormData.verifyCode,
    })
      .then(() => {
        currentStep.value = 2;
      })
      .catch((error) => {
        customEmailErrorMesg.value = error.message;
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

/** 返回上一步 */
const handlePrevStep = (showMsg: boolean) => {
  if (showMsg) {
    showCancelDialog.value = true;
  } else {
    router.replace({ name: 'Login' });
  }
};

/** 注册 */
const handleRegister = () => {
  registerFormRef.value?.validate((valid: boolean) => {
    if (!valid) {
      return;
    }
    customRegisterErrorMesg.value = '';
    loading.value = true;
    registerApi({
      sendSeqId: registerFormData.sendSeqId,
      password: encryptPasswordMD5(registerFormData.password),
    })
      .then(({ data }) => {
        router.replace({
          name: 'Login',
          params: {
            email: registerFormData.email,
          },
        });
      })
      .catch((error) => {
        customRegisterErrorMesg.value = error.message;
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

/** 登录表单校验规则 */
const registerFormRules: FormRules = {
  email: [
    { required: true, message: t('register.emailError'), trigger: 'blur' },
    { type: 'email', message: t('register.emailError'), trigger: ['blur'] },
  ],
  verifyCode: [
    { required: true, message: t('register.sendCodePlaceholder'), trigger: ['blur'] },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value === '') {
          // 当没有输入内容的时候检查是否有自定义的错误信息
          if (customEmailErrorMesg.value) {
            callback(new Error(customEmailErrorMesg.value));
          } else {
            callback(new Error(t('register.sendCodePlaceholder')));
          }
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
  password: [
    { required: true, message: t('register.passwordPlaceholder'), trigger: 'blur' },
    { min: 8, max: 32, message: t('register.passwordLengthError'), trigger: 'blur' },
    // 至少一位大写英文字母及数字；至少一位特殊符号，如!_@；长度至少八个字符。
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          return callback();
        }
        const { isLongEnough, hasUpperCaseAndDigit, hasSpecialChar } = passwordStrength;
        if (!isLongEnough || !hasUpperCaseAndDigit || !hasSpecialChar) {
          return callback(new Error());
        }
        callback();
      },
      trigger: ['blur', 'change'],
    },
  ],
  againPassword: [
    { required: true, message: t('register.againPasswordPlaceholder'), trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value === '') {
          if (customRegisterErrorMesg.value) {
            callback(new Error(customRegisterErrorMesg.value));
          } else {
            callback(new Error(t('register.againPasswordPlaceholder')));
          }
        } else if (value !== registerFormData.password) {
          callback(new Error(t('register.againPasswordError')));
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
};


const validPasswordStyle = (valid: boolean): PasswordStyle => {
  let iconName: PasswordStyle["iconName"] = "icon-gary-check"
  let className = "notLength"
  if (registerFormData.password.length > 0 && !valid) {
    iconName = "icon-red-close"
    className = "invalid"
  }else if (registerFormData.password.length > 0 && valid) {
    iconName = "icon-green-check"
    className = "valid"
  }
  return {
    'className': [className],
    'iconName': iconName,
  }
}

const handleAgainPasswordInput = (val: string) => {
  let value = val.replace(/[\u4e00-\u9fa5]/g, '');
  registerFormData.againPassword = value;
};

const handlePasswordInput = (val: string) => {
  let value = val.replace(/[\u4e00-\u9fa5]/g, '');
  registerFormData.password = value;
};
</script>

<template>
  <CommonDialog
    v-model:visible="showCancelDialog"
    :title="t('register.cancelRegisterTipTitle')"
    :message="t('register.cancelRegisterTipMesg')"
    iconClass="dialog-icon-warning"
    :confirmButtonText="t('register.dialogComfimBtnText')"
    :cancelButtonText="t('register.dialogCancelBtnText')"
    @confirm="handlePrevStep(false)"
    @cancel="showCancelDialog = false"
  />
  <LoginSlot>
    <template #default>
      <div class="login-card relative w-[41.75%] h-full bg-[#fff] overflow-hidden pt-[14%]">
        <div class="w-50% min-w-380px mx-auto">
          <h4
            class="font-family-[PingFangSC-Medium] font-500 text-32px text-[#222527] tracking-0 leading-44px m-0"
          >
            {{ t('register.title') }}
          </h4>
          <h6
            class="font-family-[PingFangSC-Medium] font-500 text-xl text-[#222527] tracking-normal leading-7 my-[40px] mb-4"
          >
            {{ tipMesg }}
          </h6>
        </div>
        <div class="content mx-auto w-50% min-w-380px">
          <el-form ref="registerFormRef" :model="registerFormData" :rules="registerFormRules">
            <Transition
              enter-active-class="animate__animated animate__fadeInRight"
              leave-active-class="animate__animated animate__fadeOutLeft"
              mode="out-in"
            >
              <div v-if="currentStep === 1" key="step1">
                <el-form-item prop="email" ref="emailInputRef">
                  <el-input
                    v-model.trim="registerFormData.email"
                    :placeholder="t('register.emailPlaceholder')"
                    type="text"
                  />
                </el-form-item>
                <el-form-item prop="verifyCode" :error="customEmailErrorMesg">
                  <el-input
                    v-model.trim="registerFormData.verifyCode"
                    :placeholder="t('register.sendCodePlaceholder')"
                    type="text"
                    maxlength="6"
                    ref="verifyCodeInputRef"
                  >
                    <template #suffix>
                      <el-button
                        :loading="countdownLoading"
                        text
                        class="sendcode-btn"
                        @click.prevent="sendEmailRequest"
                        :disabled="countdown > 0"
                      >
                        <span :class="sendBtnMesg.className">{{ sendBtnMesg.text }}</span>
                      </el-button>
                    </template>
                  </el-input>
                </el-form-item>

                <el-button
                  type="primary"
                  class="btn-hover-scale-sm mt-14px"
                  :loading="loading"
                  size="large"
                  @click.prevent="verifyEmailCodeResp"
                >
                  {{ t('register.nextStep') }}
                </el-button>

                <el-button class="text-btn" text @click.prevent="handlePrevStep(false)">
                  {{ t('register.prevStep') }}
                </el-button>
              </div>
              <div v-else-if="currentStep === 2" key="step2">
                <el-form-item prop="password">
                  <!-- <el-input
                      v-model.trim="registerFormData.password"
                      :placeholder="t('register.passwordPlaceholder')"
                      type="password"
                      show-password
                    /> -->
                  <el-popover
                    placement="bottom-start"
                    :visible="isPasswordFocused"
                    :show-arrow="false"
                    :width="250"
                    popper-class="password-strength-popper"
                  >
                    <template #reference>
                      <el-input
                        v-model="registerFormData.password"
                        :placeholder="t('register.passwordPlaceholder')"
                        type="password"
                        maxlength="32"
                        show-password
                        :formatter="(value: string) => value.replace(/[\u4e00-\u9fa5]/g, '')"
                        @input="handlePasswordInput"
                        @focus="isPasswordFocused = true"
                        @blur="isPasswordFocused = false"
                        @paste.prevent
                        @copy.prevent
                      />
                    </template>
                    <template #default>
                      <div class="password-strength-popover-content">
                        <div class="title">{{ t('forgotPassword.passwordTipsContainTitle') }}</div>
                        <div :class="validPasswordStyle(passwordStrength.isLongEnough).className">
                          <SvgIcon :name="validPasswordStyle(passwordStrength.isLongEnough).iconName" :class="validPasswordStyle(passwordStrength.isLongEnough).className" />
                          <span style="margin-left: 8px; font-size: 14px;">{{ t('forgotPassword.passwordTipsContainLength') }}</span>
                        </div>
                        <div :class="validPasswordStyle(passwordStrength.hasUpperCaseAndDigit).className">
                          <SvgIcon :name="validPasswordStyle(passwordStrength.hasUpperCaseAndDigit).iconName" :class="validPasswordStyle(passwordStrength.hasUpperCaseAndDigit).className" />
                          <span style="margin-left: 8px; font-size: 14px;">{{ t('forgotPassword.passwordTipsContainUpperCaseAndDigit') }}</span>
                        </div>
                        <div :class="validPasswordStyle(passwordStrength.hasSpecialChar).className">
                          <SvgIcon :name="validPasswordStyle(passwordStrength.hasSpecialChar).iconName" :class="validPasswordStyle(passwordStrength.hasSpecialChar).className" />
                          <span style="margin-left: 8px; font-size: 14px;">{{ t('forgotPassword.passwordTipsContainSpecialChar') }}! _@</span>
                        </div>
                      </div>
                    </template>
                  </el-popover>
                </el-form-item>
                <el-form-item prop="againPassword" :error="customRegisterErrorMesg">
                  <el-input
                    v-model="registerFormData.againPassword"
                    :placeholder="t('register.againPasswordPlaceholder')"
                    type="password"
                    maxlength="32"
                    show-password
                    :formatter="(value: string) => value.replace(/[\u4e00-\u9fa5]/g, '')"
                    @input="handleAgainPasswordInput"
                    @paste.prevent
                    @copy.prevent
                  />
                </el-form-item>

                <el-button
                  :loading="loading"
                  type="primary"
                  size="large"
                  @click.prevent="handleRegister"
                >
                  {{ t('register.register') }}
                </el-button>

                <el-button class="text-btn" text @click.prevent="handlePrevStep(true)">
                  {{ t('register.prevStep') }}
                </el-button>
              </div>
            </Transition>
          </el-form>
        </div>
      </div>
    </template>
  </LoginSlot>
</template>

<style lang="scss" scoped>
.el-button {
  width: 100%;
  margin-top: 10px;
  background: #030814;
  border-radius: 6px;
  border: none;
}
:deep(.el-button.text-btn) {
  background: transparent;
  margin-left: 0;
  font-size: 14px;
  color: #222527;
  margin-top: 24px;
}
:deep(.el-button.sendcode-btn) {
  background: transparent;
  font-size: 14px;
  color: #ff0064;
  margin-top: 0;
  padding: 0;

  .red {
    color: #ff0064;
  }
  .gray {
    color: #6b7275;
  }
}
:deep(.el-button.is-text:hover) {
  background: transparent;
}
:deep(.el-input) {
  --el-input-focus-border-color: #e5e6eb;
}
:deep(.el-input__inner) {
  height: 40px;
}
:deep(.el-input__wrapper) {
  border-radius: 6px;
  background-color: transparent;
}
:deep(.el-form-item.is-error .el-form-item__content .el-input .el-input__wrapper) {
  background-color: #fff2ee;
}

:global(.el-popover.el-popper.password-strength-popper) {
  padding: 16px;
}
// 密码强度提示弹窗样式
.el-popover.password-strength-popper {
  border-radius: 8px;
  color: white;
  border: none;

  .password-strength-popover-content {
    .title {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #222527;
      margin: 0 0 12px 0;
    }

    div:not(.title) {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-size: 13px;
      color: #a9a9a9; /* Lighter gray for readability on black */
      transition: color 0.2s;

      .el-icon {
        margin-right: 8px;
        font-size: 16px;
      }

      &.valid {
        color: #3eb342;
      }

      &.invalid {
        color: #fd3627;
      }

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}
</style>
