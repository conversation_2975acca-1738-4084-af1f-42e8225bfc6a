<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus';
import type { LoginRequestData } from './apis/type';
import { useUserStore } from '@/pinia/stores/user';
import { loginApi } from './apis';
import LoginSlot from './components/LoginSlot.vue';
import { onMounted } from 'vue';
import { encryptPasswordMD5 } from '@/common/utils/crypto';
import { CacheKey } from '@@/constants/cache-key';
import Cookies from 'js-cookie';
import { useI18n } from 'vue-i18n'; // 导入 i18n
import { setLocale, LocaleType } from '@@/i18n';

const { t } = useI18n(); // 使用 i18n

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
/** 登录表单元素的引用 */
const loginFormRef = ref<FormInstance | null>(null);
const showPassword = ref(false);
/** 登录按钮 Loading */
const loading = ref(false);

/** 登录表单数据 */
const loginFormData: LoginRequestData = reactive({
  email: '',
  password: '',
});
onBeforeMount(() => {});

onMounted(() => {
  // 设置语言
  if (route.query.lang) {
    if (route.query.lang === 'en') {
      setLocale('en-US' as LocaleType);
    } else if (route.query.lang === 'zh-CN') {
      setLocale('zh-CN' as LocaleType);
    } else if (route.query.lang === 'zh-HK') {
      setLocale('zh-HK' as LocaleType);
    }
  }
  // 设置默认邮箱
  if (route.params.email && typeof route.params.email === 'string') {
    loginFormData.email = route.params.email;
  } else {
    const email = localStorage.getItem(CacheKey.LOGIN_EMAIL);
    if (email) {
      loginFormData.email = email;
    }
  }
});

/** 登录表单校验规则 */
const loginFormRules = computed(() => {
  const allRules: FormRules = {
    email: [
      { required: true, message: t('login.emailRequired'), trigger: 'blur' },
      { type: 'email', message: t('login.emailInvalid'), trigger: ['blur', 'change'] },
    ],
    password: [
      { required: true, message: t('login.passwordRequired'), trigger: 'blur' },
      { min: 6, max: 32, message: t('login.passwordLength'), trigger: 'blur' },
    ],
  };
  return allRules;
});
const emailError = ref('');
const passwordError = ref('');

/** 登录 */
function handleLogin() {
  loginFormRef.value?.validate((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    emailError.value = '';
    passwordError.value = '';
    loginApi({
      email: loginFormData.email,
      password: encryptPasswordMD5(loginFormData.password),
    })
      .then(({ data }) => {
        const { token, iv, key, isSuccess, signKey, totalNum, remainNum, lockRemainTime } = data;
        if (isSuccess === false) {
          loading.value = false;
          loginFormData.password = '';
          if (remainNum === 0) {
            passwordError.value = t('login.accountLocked', {
              minutes: Math.ceil(lockRemainTime / 60),
            });
            return;
          }
          passwordError.value = t('login.passwordError', {
            count: totalNum - remainNum,
            total: totalNum,
          });
          return;
        }
        userStore.setToken(token);
        localStorage.setItem(CacheKey.LOGIN_EMAIL, loginFormData.email);
        Cookies.set(CacheKey.LOGIN_IV, iv);
        Cookies.set(CacheKey.LOGIN_KEY, key);
        Cookies.set(CacheKey.LOGIN_SIGN_KEY, signKey);
        router.push('/');
      })
      .catch((error) => {
        emailError.value = error.message;
        loading.value = false;
      });
  });
}

/** 注册 */
function handleRegister() {
  router.push('/register');
}

function handleForgotPassword() {
  router.push('/forgot-password');
}
</script>

<template>
  <LoginSlot>
    <template #default>
      <div
        class="login-card relative w-[41.75%] h-full flex-shrink-0 bg-[#fff] overflow-hidden pt-24vh"
      >
        <div class="w-50% min-w-380px mx-auto">
          <h4
            class="font-family-[PingFangSC-Medium] font-500 text-32px text-[#222527] tracking-0 leading-44px m-0"
          >
            {{ $t('login.title') }}
          </h4>
          <h6
            class="font-family-[PingFangSC-Medium] font-500 text-20px text-[#222527] tracking-normal leading-7 my-[40px] mb-4"
          >
            {{ $t('login.yourEmailAndPassword') }}
          </h6>
        </div>
        <div class="content mx-auto w-50% min-w-380px">
          <el-form
            ref="loginFormRef"
            :model="loginFormData"
            :rules="loginFormRules"
            @keyup.enter="handleLogin"
          >
            <el-form-item prop="email" :error="emailError">
              <el-input
                v-model.trim="loginFormData.email"
                :placeholder="$t('login.emailPlaceholder')"
                type="text"
                tabindex="1"
                size="large"
                class="custom-input"
              />
            </el-form-item>

            <el-form-item prop="password" :error="passwordError">
              <el-input
                v-model.trim="loginFormData.password"
                :placeholder="$t('login.passwordPlaceholder')"
                :type="showPassword ? 'text' : 'password'"
                tabindex="2"
                size="large"
                class="custom-input"
              >
                <template #append v-if="loginFormData.password">
                  <svg-icon
                    class="cursor-pointer"
                    name="icon-hide"
                    v-if="!showPassword"
                    @click="showPassword = true"
                  />
                  <svg-icon
                    class="cursor-pointer"
                    name="icon-show"
                    v-else
                    @click="showPassword = false"
                  />
                </template>
              </el-input>
            </el-form-item>

            <div class="w-100% text-right mb-22px">
              <a
                class="color-[#464B4E] cursor-pointer font-400 text-14px"
                @click.prevent="handleForgotPassword"
                >{{ $t('login.forgotPassword') }}</a
              >
            </div>

            <el-button
              :loading="loading"
              type="primary"
              size="large"
              class="btn-hover-scale-sm"
              @click.prevent="handleLogin"
            >
              {{ $t('login.login') }}
            </el-button>

            <p
              class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527] m-0 mt-12px tracking-0 leading-20px"
            >
              {{ $t('login.notRegistered') }}
              <a class="color-[#FF0064] cursor-pointer font-500" @click.prevent="handleRegister">
                {{ $t('login.joinUs') }}
              </a>
            </p>
          </el-form>
        </div>
      </div>
    </template>
  </LoginSlot>
</template>

<style lang="scss" scoped>
.login-container {
  .login-card {
    .content {
      .el-button {
        width: 100%;
        margin-top: 10px;
        background: #030814;
        border-radius: 6px;
        border: none;
      }

      :deep(.custom-input .el-input-group__append) {
        padding: 0 12px;
      }

      :deep(.el-input-group--append > .el-input__wrapper) {
        box-shadow: none;
        border: 1px solid #dcdfe6;
        border-right: none;
      }

      :deep(.el-form-item) {
        margin-bottom: 20px;
      }

      :deep(.el-form-item__error) {
        padding: 2px 4px;
      }
    }
  }
}
</style>
