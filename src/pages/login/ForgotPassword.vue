<script lang="ts" setup>
import { ref, reactive, onBeforeUnmount, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { encryptPasswordMD5 } from '@@/utils/crypto';

import { sendEmailApi, emailVerifyApi, forgetPasswordApi } from './apis';
import { PasswordStyle, SmsBusinessType } from './apis/type';
// @ts-ignore
import CommonDialog from '@/common/components/Dialog/CommonDialog.vue';
import { Check, Close } from '@element-plus/icons-vue';
import LoginSlot from './components/LoginSlot.vue';
import { t } from '@@/i18n';

// --- Core State ---
const router = useRouter();
const forgotPasswordFormRef = ref<FormInstance | null>(null);
const currentStep = ref(2); // 1: email/code, 2: new password
const sendSeqId = ref<string | null>(null);
const showCancelDialog = ref(false);
const showPassword1 = ref(false);
const showPassword2 = ref(false);
// --- Loading & UI State ---
const loading = ref(false);
const sendingCode = ref(false);
const isPasswordFocused = ref(false);

// --- Form Data ---
const formData = reactive({
  email: '',
  code: '',
  password: '',
  confirmPassword: '',
});

// --- Countdown Timer ---
const countdownActive = ref(false);
const countdownSeconds = ref(60);
let timer: ReturnType<typeof setInterval> | null = null;

const stopCountdown = () => {
  if (timer) clearInterval(timer);
  timer = null;
  countdownActive.value = false;
};

const startCountdown = () => {
  stopCountdown(); // Reset any existing timer
  countdownActive.value = true;
  countdownSeconds.value = 60;
  timer = setInterval(() => {
    if (countdownSeconds.value > 0) {
      countdownSeconds.value--;
    } else {
      stopCountdown();
    }
  }, 1000);
};

// --- Password Strength ---
const passwordStrength = reactive({
  isLongEnough: false,
  hasUpperCaseAndDigit: false,
  hasSpecialChar: false,
});

const checkPasswordStrength = (password: string) => {
  passwordStrength.isLongEnough = password.length >= 8 && password.length <= 32;
  passwordStrength.hasUpperCaseAndDigit = /[A-Z]/.test(password) && /[0-9]/.test(password);
  // passwordStrength.hasSpecialChar = /[^a-zA-Z0-9\u4e00-\u9fa5]/.test(password)
  passwordStrength.hasSpecialChar = /[ ~!@#$%^&*()_+\-=\[\]{}<>;':"\\|,.\/?]/.test(password);
};

const validPasswordStyle = (valid: boolean): PasswordStyle => {
  let iconName: PasswordStyle['iconName'] = 'icon-gary-check';
  let className = 'notLength';
  if (formData.password.length > 0 && !valid) {
    iconName = 'icon-red-close';
    className = 'invalid';
  } else if (formData.password.length > 0 && valid) {
    iconName = 'icon-green-check';
    className = 'valid';
  }
  return {
    className: [className],
    iconName: iconName,
  };
};

watch(() => formData.password, checkPasswordStrength);

// --- Form Validation ---
const formRules: FormRules = {
  email: [
    { required: true, message: t('forgotPassword.emailPlaceholder'), trigger: 'blur' },
    { type: 'email', message: t('forgotPassword.emailError'), trigger: ['blur', 'change'] },
  ],
  code: [{ required: true, message: t('forgotPassword.verifyCodePlaceholder'), trigger: 'blur' }],
  password: [
    { required: true, message: t('forgotPassword.newPasswordPlaceholder'), trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          return callback();
        }
        // The detailed checks are for the popover UI, the validator just needs to check the final state.
        const { isLongEnough, hasUpperCaseAndDigit, hasSpecialChar } = passwordStrength;
        if (!isLongEnough || !hasUpperCaseAndDigit || !hasSpecialChar) {
          // Silent error, UI is handled by popover.
          return callback(new Error());
        }
        callback();
      },
      trigger: ['blur', 'change'],
    },
  ],
  confirmPassword: [
    { required: true, message: t('forgotPassword.confirmPasswordPlaceholder'), trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value === '') {
          callback(new Error(t('forgotPassword.confirmPasswordPlaceholder')));
        } else if (value !== formData.password) {
          callback(new Error(t('forgotPassword.againPasswordError')));
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
};

const validateFormFields = async (fields: Array<keyof typeof formData>): Promise<boolean> => {
  if (!forgotPasswordFormRef.value) return false;
  try {
    await Promise.all(fields.map((field) => forgotPasswordFormRef.value!.validateField(field)));
    return true;
  } catch (error) {
    return false;
  }
};

// --- Event Handlers & API Calls ---
const handleSendCode = async () => {
  if (sendingCode.value || countdownActive.value) return;

  const isValid = await validateFormFields(['email']);
  if (!isValid) {
    ElMessage.error(t('forgotPassword.emailError'));
    return;
  }

  sendingCode.value = true;
  try {
    const res = await sendEmailApi({
      email: formData.email,
      businessType: SmsBusinessType.USER_RESET_PWD,
    });
    sendSeqId.value = res.data.sendSeqId;
    ElMessage.success(t('forgotPassword.verifyCodeSuccess'));
    startCountdown();
  } catch (error: any) {
    console.error('发送验证码失败:', error);
    ElMessage.error(error.message || t('forgotPassword.verifyCodeError'));
  } finally {
    sendingCode.value = false;
  }
};

const handleVerifyCodeAndProceed = async () => {
  const isValid = await validateFormFields(['email', 'code']);
  if (!isValid) {
    ElMessage.error(t('forgotPassword.verifyCodeError2'));
    return;
  }
  if (!sendSeqId.value) {
    ElMessage.error(t('forgotPassword.verifyCodeError3'));
    return;
  }

  loading.value = true;
  try {
    await emailVerifyApi({ sendSeqId: sendSeqId.value, verifyCode: formData.code });
    ElMessage.success(t('forgotPassword.verifyCodeSuccess2'));
    currentStep.value = 2;
  } catch (error: any) {
    console.error('验证码错误或已过期:', error);
    ElMessage.error(error.message || t('forgotPassword.verifyCodeError4'));
  } finally {
    loading.value = false;
  }
};

const resetFormAndGoToLogin = () => {
  sendSeqId.value = null;
  currentStep.value = 1;
  stopCountdown();
  forgotPasswordFormRef.value?.clearValidate();
  router.replace({
    name: 'Login',
    params: {
      email: formData.email,
    },
  });
  Object.assign(formData, { email: '', code: '', password: '', confirmPassword: '' });
};

const handleSubmitPassword = async () => {
  const isValid = await validateFormFields(['password', 'confirmPassword']);
  if (!isValid) {
    ElMessage.error(t('forgotPassword.verifyCodeError7'));
    return;
  }
  if (!sendSeqId.value) {
    ElMessage.error(t('forgotPassword.verifyCodeError5'));
    return;
  }

  loading.value = true;
  try {
    const encryptedPassword = encryptPasswordMD5(formData.password);
    await forgetPasswordApi({ sendSeqId: sendSeqId.value, password: encryptedPassword });
    ElMessage.success(t('forgotPassword.verifyCodeError6'));
    resetFormAndGoToLogin();
  } catch (error: any) {
    console.error('密码重置失败:', error);
    ElMessage.error(error.message || t('forgotPassword.verifyCodeError8'));
  } finally {
    loading.value = false;
  }
};

// --- Navigation & Dialogs ---
const backToLogin = () => {
  if (currentStep.value === 1) {
    router.push('/login');
    return;
  }
  showCancelDialog.value = true; // 显示确认对话框
};

const handleConfirmGoBack = () => {
  showCancelDialog.value = false; // 关闭对话框
  resetFormAndGoToLogin();
};

const handleCancelGoBack = () => {
  showCancelDialog.value = false; // 关闭对话框
};

const handlePasswordInput = (val: string) => {
  let value = val.replace(/[\u4e00-\u9fa5]/g, '');
  formData.password = value;
};

const handleConfirmPasswordInput = (val: string) => {
  let value = val.replace(/[\u4e00-\u9fa5]/g, '');
  formData.confirmPassword = value;
};

// --- Lifecycle Hooks ---
onBeforeUnmount(() => {
  stopCountdown();
});
</script>

<template>
  <LoginSlot>
    <template #default>
      <div class="relative w-[41.75%] h-full bg-[#fff] overflow-hidden pt-[14%]">
        <div class="w-50% min-w-380px mx-auto">
          <h4
            class="font-family-[PingFangSC-Medium] font-medium text-32px text-[#222527] tracking-0 leading-44px m-0"
          >
            {{ t('forgotPassword.title') }}
          </h4>
          <h6
            v-if="currentStep === 1"
            class="font-family-[PingFangSC-Medium] font-medium text-xl text-[#222527] tracking-normal leading-7 my-[40px] mb-4"
          >
            {{ t('forgotPassword.forgetPasswordPlaceholder') }}
          </h6>
          <h6
            v-if="currentStep === 2"
            class="font-family-[PingFangSC-Medium] font-medium text-xl text-[#222527] tracking-normal leading-7 my-[40px] mb-4"
          >
            {{ t('forgotPassword.tipMesgStep2') }}
          </h6>
        </div>
        <div class="content mx-auto w-50% min-w-380px">
          <el-form
            ref="forgotPasswordFormRef"
            :model="formData"
            :rules="formRules"
            label-width="0px"
            @submit.prevent
          >
            <!-- Step 1: Email and Code Verification -->
            <div v-if="currentStep === 1">
              <el-form-item prop="email" class="email-form-item">
                <el-input
                  v-model.trim="formData.email"
                  :placeholder="t('forgotPassword.emailPlaceholder')"
                  type="text"
                  size="large"
                  tabindex="1"
                />
              </el-form-item>
              <el-form-item prop="code" class="flex w-full">
                <el-input
                  v-model.trim="formData.code"
                  maxlength="6"
                  :placeholder="t('forgotPassword.verifyCodePlaceholder')"
                  type="text"
                  size="large"
                >
                  <template #suffix>
                    <el-button
                      class="w-full rounded-6px border-none bg-transparent text-[#FF0064] mt-0 p-0"
                      :disabled="countdownActive || sendingCode || !formData.email"
                      @click="handleSendCode"
                    >
                      {{
                        countdownActive
                          ? `${t('forgotPassword.resendCode')} (${countdownSeconds}s)`
                          : sendingCode
                            ? t('forgotPassword.sendingCode')
                            : t('forgotPassword.sendCode')
                      }}
                    </el-button>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  class="w-full mt-[10px] bg-[#030814] rounded-6px border-none btn-hover-scale-sm"
                  :loading="loading"
                  type="primary"
                  size="large"
                  @click.prevent="handleVerifyCodeAndProceed"
                >
                  {{ t('forgotPassword.verifyAndNext') }}
                </el-button>
                <el-button
                  class="mt-24px w-full bg-transparent ml-0 text-14px text-[#222527] rounded-6px border-none"
                  text
                  @click="backToLogin"
                >
                  {{ t('forgotPassword.prevStep') }}
                </el-button>
              </el-form-item>
            </div>

            <!-- Step 2: New Password -->
            <div v-if="currentStep === 2">
              <el-form-item prop="password">
                <el-popover
                  placement="bottom-start"
                  :visible="isPasswordFocused"
                  :show-arrow="false"
                  :width="250"
                  popper-class="password-strength-popper"
                >
                  <template #reference>
                    <el-input
                      v-model="formData.password"
                      :placeholder="t('forgotPassword.newPasswordPlaceholder')"
                      :type="showPassword1 ? 'text' : 'password'"
                      size="large"
                      tabindex="3"
                      maxlength="32"
                      :formatter="(value: string) => value.replace(/[\u4e00-\u9fa5]/g, '')"
                      @input="handlePasswordInput"
                      @focus="isPasswordFocused = true"
                      @blur="isPasswordFocused = false"
                      @paste.prevent
                      @copy.prevent
                    >
                      <template #append v-if="formData.password">
                        <svg-icon
                          class="cursor-pointer"
                          name="icon-hide"
                          v-if="!showPassword1"
                          @click="showPassword1 = true"
                        />
                        <svg-icon
                          class="cursor-pointer"
                          name="icon-show"
                          v-else
                          @click="showPassword1 = false"
                        />
                      </template>
                    </el-input>
                  </template>
                  <template #default>
                    <div class="password-strength-popover-content">
                      <div class="title">{{ t('forgotPassword.passwordTipsContainTitle') }}</div>
                      <div :class="validPasswordStyle(passwordStrength.isLongEnough).className">
                        <SvgIcon
                          :name="validPasswordStyle(passwordStrength.isLongEnough).iconName"
                          :class="validPasswordStyle(passwordStrength.isLongEnough).className"
                        />
                        <span style="margin-left: 8px; font-size: 14px">{{
                          t('forgotPassword.passwordTipsContainLength')
                        }}</span>
                      </div>
                      <div
                        :class="validPasswordStyle(passwordStrength.hasUpperCaseAndDigit).className"
                      >
                        <SvgIcon
                          :name="validPasswordStyle(passwordStrength.hasUpperCaseAndDigit).iconName"
                          :class="
                            validPasswordStyle(passwordStrength.hasUpperCaseAndDigit).className
                          "
                        />
                        <span style="margin-left: 8px; font-size: 14px">{{
                          t('forgotPassword.passwordTipsContainUpperCaseAndDigit')
                        }}</span>
                      </div>
                      <div :class="validPasswordStyle(passwordStrength.hasSpecialChar).className">
                        <SvgIcon
                          :name="validPasswordStyle(passwordStrength.hasSpecialChar).iconName"
                          :class="validPasswordStyle(passwordStrength.hasSpecialChar).className"
                        />
                        <span style="margin-left: 8px; font-size: 14px"
                          >{{ t('forgotPassword.passwordTipsContainSpecialChar') }}! _@</span
                        >
                      </div>
                    </div>
                  </template>
                </el-popover>
              </el-form-item>
              <el-form-item prop="confirmPassword">
                <el-input
                  v-model="formData.confirmPassword"
                  :placeholder="t('forgotPassword.confirmPasswordPlaceholder')"
                  :type="showPassword2 ? 'text' : 'password'"
                  size="large"
                  maxlength="32"
                  tabindex="4"
                  :formatter="(value: string) => value.replace(/[\u4e00-\u9fa5]/g, '')"
                  @input="handleConfirmPasswordInput"
                  @paste.prevent
                  @copy.prevent
                >
                  <template #append v-if="formData.confirmPassword">
                    <svg-icon
                      class="cursor-pointer"
                      name="icon-hide"
                      v-if="!showPassword2"
                      @click="showPassword2 = true"
                    />
                    <svg-icon
                      class="cursor-pointer"
                      name="icon-show"
                      v-else
                      @click="showPassword2 = false"
                    />
                  </template>
                </el-input>
              </el-form-item>
              <el-button
                :loading="loading"
                type="primary"
                size="large"
                @click.prevent="handleSubmitPassword"
              >
                {{ t('forgotPassword.confirmAndLogin') }}
              </el-button>
              <el-button class="text-btn" text @click="backToLogin">
                {{ t('forgotPassword.prevStep') }}
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
      <CommonDialog
        v-model:visible="showCancelDialog"
        :title="t('forgotPassword.cancelResetPassword')"
        :message="t('forgotPassword.cancelResetPasswordMesg')"
        iconClass="warning"
        :confirmButtonText="t('forgotPassword.cancelResetPasswordConfirm')"
        :cancelButtonText="t('forgotPassword.cancelResetPasswordCancel')"
        @confirm="handleConfirmGoBack"
        @cancel="handleCancelGoBack"
      />
    </template>
  </LoginSlot>
</template>

<style lang="scss" scoped>
:deep(.el-input-group__append) {
  padding: 0 12px;
}

:deep(.el-input-group--append > .el-input__wrapper) {
  box-shadow: none;
  border: 1px solid #dcdfe6;
  border-right: none;
}
.el-button {
  width: 100%;
  margin-top: 10px;
  background: #030814;
  border-radius: 6px;
  border: none;
  &.sendcode-btn {
    background: transparent;
    color: #ff0064;
    margin-top: 0;
    padding: 0;
  }
  &.text-btn {
    background: transparent;
    margin-left: 0;
    font-size: 14px;
    color: #222527;
    margin-top: 24px;
  }
}

.el-form-item__error {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #fd3627ff;
}

.email-form-item.is-error :deep(.el-input__wrapper) {
  background: #fff2ee;
  border: 1px solid #fd3627;
  box-shadow: none;
}

:global(.el-popover.el-popper.password-strength-popper) {
  padding: 16px;
}
// 密码强度提示弹窗样式
.password-strength-popper {
  border-radius: 8px;
  padding: 16px;
  border: none;

  .password-strength-popover-content {
    .title {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #222527;
      margin: 0 0 12px 0;
    }

    div:not(.title) {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-size: 13px;
      color: #a9a9a9; /* Lighter gray for readability on black */
      transition: color 0.2s;

      .el-icon {
        margin-right: 8px;
        font-size: 16px;
      }

      &.valid {
        color: #3eb342;
      }

      &.invalid {
        color: #fd3627;
      }

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}
</style>
