<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { agreementQueryApi } from '../apis';
const { t, locale } = useI18n();
import { setLocale, LocaleType } from '@@/i18n';
const userAgreement = ref<any>([]);
onMounted(() => {
  handleAgreementQuery();
});

function handleAgreementQuery() {
  agreementQueryApi().then(({ data }) => {
    userAgreement.value = data.agreementVOs;
  });
}

function openAgreement(type: string) {
  const lang = locale.value === 'en-US' ? 'en' : locale.value;
  const agreement = userAgreement.value.find(
    (item: any) => item.fileType === type && item.languageCode === lang
  );
  window.open(agreement.agreementUrl);
}

/** 切换语言 */
function changeLang(langValue: string) {
  setLocale(langValue as LocaleType);
}
</script>

<template>
  <div class="overflow-auto h-full app-wrapper">
    <div
      class="login-container min-w-1200px min-h-720px overflow-hidden flex justify-between w-full h-full relative"
    >
      <div
        class="login-left-card relative flex-1 h-full bg-[url('@@/assets/images/login/login-bg.png')] bg-cover bg-center"
      >
        <img
          src="@@/assets/images/logo/logo-white.png"
          class="w-186px mt-10vh mb-6vh object-contain max-h-10vh min-h-72px"
          alt=""
        />
        <img
          src="@@/assets/images/login/login-bg.webp"
          class="block mx-auto mb-50px w-45% max-h-40vh object-contain min-h-290px"
        />
        <!-- 星星背景元素 -->
        <div class="star-container">
          <img
            src="@@/assets/images/icon_star_white.png"
            class="star star-1"
            :style="{ animationDelay: '0s' }"
          />
          <img
            src="@@/assets/images/icon_star_two.png"
            class="star star-2"
            :style="{ animationDelay: '1.5s' }"
          />
        </div>
        <!-- <Threads :start-color="[1, 0, 0.4]" :end-color="[0, 0.6, 1]" :amplitude="1" :distance="0.3"
            :enable-mouse-interaction="false" /> -->

        <!-- </img> -->
        <img
          v-if="locale === 'zh-CN'"
          src="@@/assets/images/login/login-zh-cn-title.svg"
          class="block mx-auto mb-20px max-h-40vh object-contain"
        />
        <img
          v-if="locale === 'zh-HK'"
          src="@@/assets/images/login/login-zh-hk-title.svg"
          class="block mx-auto mb-20px max-h-40vh object-contain"
        />
        <img
          v-if="locale === 'en-US'"
          src="@@/assets/images/login/login-en-us-title.svg"
          class="block mx-auto mb-20px max-h-40vh object-contain"
        />

        <h6
          class="font-family-[PingFangSC-Medium] w-100% mx-auto font-300 text-24px text-white tracking-normal text-center leading-44px m-0 mt-3"
        >
          {{ $t('login.slogan2') }}
        </h6>
        <p
          class="bg-#2F2624 w-fit font-family-[PingFangSC-Regular] font-normal text-12px text-[#A7ADB0] tracking-0 absolute bottom-3vh ml-50% translate-x-[-50%]"
        >
          {{ $t('login.copyright') }}
        </p>
      </div>
      <slot></slot>
      <div class="absolute bottom-5vh w-[41.75%] right-[0%] flex items-center justify-center">
        <div class="flex items-center justify-center text-14px color-#464B4E">
          <a class="cursor-pointer block w-fit text-align-center" @click="openAgreement('USA')">{{
            $t('login.userAgreement1')
          }}</a>
          <span class="color-#6B7275 mx-8px">&</span>
          <a class="cursor-pointer block w-fit text-align-center" @click="openAgreement('PP')">{{
            $t('login.userAgreement2')
          }}</a>
        </div>

        <el-dropdown ref="dropdown">
          <div
            class="right-menu-item cursor-pointer group text-14px flex hover:bg-[#F8F9FA] rounded-6px justify-center items-center ml-24px p-[8px_6px] color-#464B4E"
          >
            <svgIcon name="icon-lang" class="mr-8px text-16px" />
            <span v-if="locale === 'zh-CN'">中文</span>
            <span v-else-if="locale === 'zh-HK'">繁体</span>
            <span v-else>English</span>
            <svgIcon name="icon-bottom" class="ml-12px" />
          </div>

          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="changeLang('zh-CN')">中文</el-dropdown-item>
              <el-dropdown-item @click="changeLang('zh-HK')">繁体</el-dropdown-item>
              <el-dropdown-item @click="changeLang('en-US')">English</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.login-container {
  .login-left-card {
    text-align: center;

    h4 {
      font-family: DingTalk-JinBuTi;
      font-weight: JinBuTi;
      font-size: 25px;
      color: #ffffff;
      letter-spacing: 0;
      margin: 114px 0 96px 0;
    }
  }
}

.login-card {
  .content {
    .el-button {
      width: 100%;
      margin-top: 10px;
      background: #030814;
      border-radius: 6px;
      border: none;
    }

    :deep(.custom-input .el-input__wrapper) {
      border-radius: 6px;
    }
  }
}

/* 星星容器样式 */
.star-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* 星星基础样式 - 优化性能，消除卡顿 */
.star {
  position: absolute;
  width: auto;
  height: auto;
  opacity: 0;
  animation: starTwinkle 3s ease-in-out infinite;
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translate3d(0, 0, 0);
  /* 强制硬件加速 */
  transform-style: preserve-3d;
  /* 优化渲染性能 */
  contain: layout style paint;
}

/* 第一个星星定位*/
.star-1 {
  top: 52%;
  left: 9%;
  width: 36px;
  height: 36px;
  animation-duration: 3s;
}

/* 第二个星星定位*/
.star-2 {
  bottom: 40%;
  right: 18%;
  width: 23px;
  height: 30px;
  animation-duration: 3s;
  animation-delay: 1.5s;
}

/* 优化的闪烁动画 - 保持原有路径，消除卡顿 */
@keyframes starTwinkle {
  0% {
    opacity: 0;
    transform: translate3d(0, 0, 0) scale(0.2);
  }

  50% {
    opacity: 1;
    transform: translate3d(0, -4px, 0) scale(1);
  }

  100% {
    opacity: 0;
    transform: translate3d(0, 0, 0) scale(0.1);
  }
}
</style>
