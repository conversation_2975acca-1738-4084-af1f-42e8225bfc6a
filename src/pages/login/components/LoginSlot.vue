<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { agreementQueryApi } from '../apis';
const { t, locale } = useI18n();
import { setLocale, LocaleType } from '@@/i18n';
const userAgreement = ref<any>([]);
onMounted(() => {
  handleAgreementQuery();
});

function handleAgreementQuery() {
  agreementQueryApi().then(({ data }) => {
    userAgreement.value = data.agreementVOs;
  });
}

function openAgreement(type: string) {
  const lang = locale.value === 'en-US' ? 'en' : locale.value;
  const agreement = userAgreement.value.find(
    (item: any) => item.fileType === type && item.languageCode === lang
  );
  window.open(agreement.agreementUrl);
}

/** 切换语言 */
function changeLang(langValue: string) {
  setLocale(langValue as LocaleType);
}
</script>

<template>
  <div class="overflow-auto h-full app-wrapper">
    <div
      class="login-container min-w-1200px min-h-720px overflow-hidden flex justify-between w-full h-full relative"
    >
      <div
        class="login-left-card relative flex-1 h-full bg-[url('@@/assets/images/login/login-bg.png')] bg-cover bg-center"
      >
        <img
          src="@@/assets/images/logo/logo-white.png"
          class="w-186px mt-10vh mb-6vh object-contain max-h-10vh min-h-72px"
          alt=""
        />
        <img
          src="@@/assets/images/login/login-bg.webp"
          class="block mx-auto mb-50px w-45% max-h-40vh object-contain min-h-290px"
        />
        <img
          v-if="locale === 'zh-CN'"
          src="@@/assets/images/login/login-zh-cn-title.svg"
          class="block mx-auto mb-20px max-h-40vh object-contain"
        />
        <img
          v-if="locale === 'zh-HK'"
          src="@@/assets/images/login/login-zh-hk-title.svg"
          class="block mx-auto mb-20px max-h-40vh object-contain"
        />
        <img
          v-if="locale === 'en-US'"
          src="@@/assets/images/login/login-en-us-title.svg"
          class="block mx-auto mb-20px max-h-40vh object-contain"
        />

        <h6
          class="font-family-[PingFangSC-Medium] w-100% mx-auto font-300 text-24px text-white tracking-normal text-center leading-44px m-0 mt-3"
        >
          {{ $t('login.slogan2') }}
        </h6>
        <p
          class="bg-#2F2624 w-fit font-family-[PingFangSC-Regular] font-normal text-12px text-[#A7ADB0] tracking-0 absolute bottom-3vh ml-50% translate-x-[-50%]"
        >
          {{ $t('login.copyright') }}
        </p>
      </div>
      <slot></slot>

      <div class="absolute bottom-5vh w-[41.75%] right-[0%] flex items-center justify-center">
        <div class="flex items-center justify-center text-14px color-#464B4E">
          <a class="cursor-pointer block w-fit text-align-center" @click="openAgreement('USA')">{{
            $t('login.userAgreement1')
          }}</a>
          <span class="color-#6B7275 mx-8px">&</span>
          <a class="cursor-pointer block w-fit text-align-center" @click="openAgreement('PP')">{{
            $t('login.userAgreement2')
          }}</a>
        </div>

        <el-dropdown ref="dropdown">
          <div
            class="right-menu-item cursor-pointer group text-14px flex hover:bg-[#F8F9FA] rounded-6px justify-center items-center ml-24px p-[8px_6px] color-#464B4E"
          >
            <svgIcon name="icon-lang" class="mr-8px text-16px" />
            <span v-if="locale === 'zh-CN'">中文</span>
            <span v-else-if="locale === 'zh-HK'">繁体</span>
            <span v-else>English</span>
            <svgIcon name="icon-bottom" class="ml-12px" />
          </div>

          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="changeLang('zh-CN')">中文</el-dropdown-item>
              <el-dropdown-item @click="changeLang('zh-HK')">繁体</el-dropdown-item>
              <el-dropdown-item @click="changeLang('en-US')">English</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.login-container {
  .login-left-card {
    text-align: center;
    h4 {
      font-family: DingTalk-JinBuTi;
      font-weight: JinBuTi;
      font-size: 25px;
      color: #ffffff;
      letter-spacing: 0;
      margin: 114px 0 96px 0;
    }
  }
}
.login-card {
  .content {
    .el-button {
      width: 100%;
      margin-top: 10px;
      background: #030814;
      border-radius: 6px;
      border: none;
    }

    :deep(.custom-input .el-input__wrapper) {
      border-radius: 6px;
    }
  }
}
</style>
