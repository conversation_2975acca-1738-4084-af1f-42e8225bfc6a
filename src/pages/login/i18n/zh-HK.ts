export default {
  login: {
    title: '登錄',
    email: '電子郵箱',
    password: '密碼',
    code: '驗證碼',
    login: '登 錄',
    register: '注 冊',
    forgotPassword: '忘記密碼?',
    emailPlaceholder: '請輸入電子郵箱',
    passwordPlaceholder: '請輸入賬號密碼',
    emailRequired: '請輸入電子郵箱',
    emailInvalid: '請輸入有效的電子郵箱地址',
    passwordRequired: '請輸入賬號密碼',
    passwordLength: '長度在 8 到 32 個字符',
    accountLocked: '您的賬號已被鎖定，{minutes}分鐘後解鎖',
    passwordError: '您已連續輸入{count}次錯誤的密碼，第{total}次時您的賬號將鎖定',
    joinUs: '加入我們',
    notRegistered: '還沒註冊？',
    yourEmailAndPassword: '您的賬號電子郵箱及密碼是？',
    slogan1: '歡迎回來，新的機會正等你開啟',
    slogan2: '無論身在何處，DingX都是你連接鏈上與現實世界的起點',
    copyright: '© Copyright 2025 DingX. All rights reserved.',
    userAgreement1: '用戶協議',
    userAgreement2: '隱私協議',
  },
  register: {
    title: '注 冊',
    email: '電子郵箱',
    verifyCode: '驗證碼',
    password: '密碼',
    register: '注 冊',
    sendCode: '發送驗證碼',
    resendCode: '重新發送',
    sendingCode: '發送中...',
    emailError: '請輸入正確的電子郵箱',
    emailPlaceholder: '請輸入電子郵箱',
    againPasswordPlaceholder: '請再次確認新密碼',
    againPasswordError: '兩次輸入的密碼不一致',
    passwordPlaceholder: '請輸入新密碼',
    sendCodePlaceholder: '請輸入驗證碼',
    passwordComplexityError: '密碼必須包含至少一位大寫英文字母',
    passwordLengthError: '密碼長度為 8 到 32 個字符',
    passwordDigitError: '密碼必須包含至少一位數字',
    passwordSpecialCharError: '密碼必須包含至少一位特殊符號 (例如: !_@)',
    tipMesgStep1: '請輸入您的電子郵箱來獲取註冊驗證碼',
    tipMesgStep2: '請設定您的密碼並二次確認',
    nextStep: '下一步',
    prevStep: '返回',
    cancelRegisterTipTitle: '取消註冊账户？',
    cancelRegisterTipMesg:
      '您正在註冊鼎的账户，現在退出將會丟失您目前的所有進度，請問您確認要這麼做嗎？',
    dialogComfimBtnText: '確認',
    dialogCancelBtnText: '取消',
  },
  forgotPassword: {
    title: '忘記密碼',
    verifyAndNext: '下一步',
    prevStep: '返回',
    forgetPasswordPlaceholder: '請輸入您的電子郵箱來獲取註冊驗證碼',
    emailError: '請輸入正確的電子郵箱地址',
    verifyCodePlaceholder: '請輸入驗證碼',
    emailPlaceholder: '請輸入電子郵箱',
    newPasswordPlaceholder: '請輸入新密碼',
    confirmPasswordPlaceholder: '請再次輸入新密碼',
    againPasswordError: '兩次輸入的密碼不一致',
    tipMesgStep2: '請輸入您帳戶的密碼並二次確認',
    sendCode: '發送驗證碼',
    resendCode: '重新發送',
    sendingCode: '發送中...',
    verifyCodeSuccess: '驗證碼已發送至您的電子郵箱，請注意查收',
    verifyCodeError: '驗證碼發送失敗，請稍後再試',
    verifyCodeError2: '請正確填寫電子郵箱和驗證碼',
    verifyCodeError3: '請先發送驗證碼',
    verifyCodeSuccess2: '驗證成功，請輸入新密碼',
    verifyCodeError4: '驗證碼錯誤或已過期',
    verifyCodeError5: '操作無效，請返回第一步重新發送驗證碼',
    verifyCodeError6: '密碼重置成功！正在跳轉到登錄頁...',
    verifyCodeError7: '請正確填寫新密碼和確認密碼',
    verifyCodeError8: '密碼重置失敗，請稍後再試',
    passwordTipsContainTitle: '密碼必須包含:',
    passwordTipsContainLength: '長度 8 至 32 個字符',
    passwordTipsContainUpperCaseAndDigit: '至少一位大寫英文字母及數字',
    passwordTipsContainSpecialChar: '至少一位特殊符號, 如',
    confirmAndLogin: '確認並登錄',
    cancelResetPassword: '取消重置密碼？',
    cancelResetPasswordMesg:
      '您正在進行重置密碼，假如您在這選擇返回將繼續沿用您的舊密碼。您確認要返回嗎？',
    cancelResetPasswordConfirm: '確認',
    cancelResetPasswordCancel: '取消',
  },
};
