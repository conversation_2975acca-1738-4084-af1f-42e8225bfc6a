export default {
  login: {
    title: '登录',
    email: '邮箱',
    password: '密码',
    code: '验证码',
    login: '登 录',
    register: '注 册',
    forgotPassword: '忘记密码?',
    emailPlaceholder: '请输入邮箱',
    passwordPlaceholder: '请输入账号密码',
    emailRequired: '请输入邮箱',
    emailInvalid: '请输入有效的邮箱地址',
    passwordRequired: '请输入账号密码',
    passwordLength: '长度在 8 到 32 个字符',
    accountLocked: '您的账号已被锁定，{minutes}分钟后解锁',
    passwordError: '您已连续输入{count}次错误的密码，第{total}次时您的账号将锁定',
    joinUs: '加入我们',
    notRegistered: '还没注册？',
    yourEmailAndPassword: '您的账号邮箱及密码是？',
    slogan1: '欢迎回来，新的机会正等你开启',
    slogan2: '无论身在何处，DingX都是你连接链上与现实世界的起点',
    copyright: '© Copyright 2025 DingX. All rights reserved.',
    userAgreement1: '用户协议',
    userAgreement2: '隐私协议',
  },
  register: {
    title: '注册',
    email: '邮箱',
    verifyCode: '验证码',
    password: '密码',
    register: '完成注册',
    nextStep: '下一步',
    sendCode: '发送验证码',
    resendCode: '重新发送',
    sendingCode: '发送中...',
    emailError: '请输入正确的邮箱',
    emailPlaceholder: '请输入邮箱',
    passwordPlaceholder: '请输入新密码',
    againPasswordPlaceholder: '请再次确认新密码',
    againPasswordError: '两次输入的密码不一致',
    passwordComplexityError: '密码必须包含至少一位大写英文字母',
    passwordLengthError: '密码长度为 8 到 32 个字符',
    passwordDigitError: '密码必须包含至少一位数字',
    passwordSpecialCharError: '密码必须包含至少一位特殊符号 (例如: !_@)',
    sendCodePlaceholder: '请输入验证码',
    tipMesgStep1: '请输入您的邮箱来获取注册验证码',
    tipMesgStep2: '请输入您账户的密码并二次确认',
    prevStep: '返回',
    cancelRegisterTipTitle: '取消注册账户？',
    cancelRegisterTipMesg:
      '您正在注册鼎的账户，现在退出将丢失您目前为止的所有进度，请问您确认要这么做吗？',
    dialogComfimBtnText: '确认',
    dialogCancelBtnText: '取消',
  },
  forgotPassword: {
    title: '忘记密码',
    verifyAndNext: '下一步',
    prevStep: '返回',
    forgetPasswordPlaceholder: '请输入您的邮箱来获取验证码',
    emailError: '请输入正确的邮箱地址',
    verifyCodePlaceholder: '请输入验证码',
    emailPlaceholder: '请输入邮箱',
    newPasswordPlaceholder: '请输入新密码',
    confirmPasswordPlaceholder: '请再次输入新密码',
    againPasswordError: '两次输入的密码不一致',
    tipMesgStep2: '请输入您账户的密码并二次确认',
    sendCode: '发送验证码',
    resendCode: '重新发送',
    sendingCode: '发送中...',
    verifyCodeSuccess: '验证码已发送至您的邮箱，请注意查收',
    verifyCodeError: '验证码发送失败，请稍后再试',
    verifyCodeError2: '请正确填写邮箱和验证码',
    verifyCodeError3: '请先发送验证码',
    verifyCodeSuccess2: '验证成功，请输入新密码',
    verifyCodeError4: '验证码错误或已过期',
    verifyCodeError5: '操作无效，请返回第一步重新发送验证码',
    verifyCodeError6: '密码重置成功！正在跳转到登录页...',
    verifyCodeError7: '请正确填写新密码和确认密码',
    verifyCodeError8: '密码重置失败，请稍后再试',
    passwordTipsContainTitle: '密码必须包含:',
    passwordTipsContainLength: '长度 8 至 32 个字符',
    passwordTipsContainUpperCaseAndDigit: '至少一位大写英文字母及数字',
    passwordTipsContainSpecialChar: '至少一位特殊符号, 如',
    confirmAndLogin: '确认并登录',
    cancelResetPassword: '取消重置密码？',
    cancelResetPasswordMesg:
      '您正在进行重置密码，假如您在这选择返回将继续沿用您的旧密码。您确认要返回吗？',
    cancelResetPasswordConfirm: '确认',
    cancelResetPasswordCancel: '取消',
  },
};
