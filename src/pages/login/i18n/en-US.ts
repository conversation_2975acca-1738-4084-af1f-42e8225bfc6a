export default {
  login: {
    title: 'Login',
    email: 'Email',
    password: 'Password',
    code: 'Code',
    login: 'Login',
    register: 'Register',
    forgotPassword: 'Forgot Password?',
    emailPlaceholder: 'Please enter your email',
    passwordPlaceholder: 'Please enter your password',
    emailRequired: 'Please enter your email',
    emailInvalid: 'Please enter a valid email address',
    passwordRequired: 'Please enter your password',
    passwordLength: 'Length should be between 8 and 32 characters',
    accountLocked: 'Your account has been locked. It will be unlocked after {minutes} minutes',
    passwordError:
      'You have entered an incorrect password {count} times. Your account will be locked after {total} attempts',
    joinUs: 'Join us',
    notRegistered: 'Not registered yet?',
    yourEmailAndPassword: 'What is your account email and password?',
    slogan1: 'Welcome back, new opportunities await you',
    slogan2: 'Wherever you are, DingX is your gateway to connect blockchain and the real world',
    copyright: '© Copyright 2025 DingX. All rights reserved.',
    userAgreement1: 'User Agreement',
    userAgreement2: 'Privacy Policy',
  },
  register: {
    title: 'Register',
    email: 'Email',
    verifyCode: 'Verify Code',
    password: 'Password',
    register: 'Register',
    nextStep: 'Next Step',
    sendCode: 'Send Code',
    resendCode: 'Resend Code',
    sendingCode: 'Sending...',
    emailError: 'Please enter a valid email address',
    emailPlaceholder: 'Please enter your email address',
    passwordPlaceholder: 'Please enter a new password',
    againPasswordPlaceholder: 'Please confirm your new password',
    againPasswordError: 'The two passwords do not match',
    sendCodePlaceholder: 'Please enter the verification code',
    passwordComplexityError: 'Password must contain at least one uppercase letter',
    passwordLengthError: 'Password length should be between 8 to 32 characters',
    passwordDigitError: 'Password must contain at least one digit',
    passwordSpecialCharError: 'Password must contain at least one special character (e.g., !_@)',
    tipMesgStep1: 'Please enter your email to get the registration verification code',
    tipMesgStep2: 'Please set your password and confirm it',
    prevStep: 'Back',
    cancelRegisterTipTitle: 'Cancel Register?',
    cancelRegisterTipMesg:
      'You are currently registering for an account. If you exit now, you will lose all progress made so far. Are you sure you want to proceed?',
    dialogComfimBtnText: 'Confirm',
    dialogCancelBtnText: 'Cancel',
  },
  forgotPassword: {
    title: 'Forgot Password',
    verifyAndNext: 'Next',
    prevStep: 'Back',
    forgetPasswordPlaceholder: 'Please enter your email to get the verification code',
    emailError: 'Please enter a valid email address',
    verifyCodePlaceholder: 'Please enter the verification code',
    emailPlaceholder: 'Please enter your email',
    newPasswordPlaceholder: 'Please enter a new password',
    confirmPasswordPlaceholder: 'Please enter the new password again',
    againPasswordError: 'The two passwords do not match',
    tipMesgStep2: 'Please enter your password and confirm it',
    sendCode: 'Send Code',
    resendCode: 'Resend Code',
    sendingCode: 'Sending...',
    verifyCodeSuccess: 'The verification code has been sent to your email, please check it',
    verifyCodeError: 'The verification code sending failed, please try again later',
    verifyCodeError2: 'Please fill in the email and verification code correctly',
    verifyCodeError3: 'Please send the verification code first',
    verifyCodeSuccess2: 'Verification successful, please enter a new password',
    verifyCodeError4: 'The verification code is incorrect or has expired',
    verifyCodeError5:
      'Invalid operation, please return to the first step and resend the verification code',
    verifyCodeError6: 'Password reset successfully! Redirecting to login page...',
    verifyCodeError7: 'Please fill in the new password and confirm password correctly',
    verifyCodeError8: 'Password reset failed, please try again later',
    passwordTipsContainTitle: 'Password must contain:',
    passwordTipsContainLength: 'Length 8 to 32 characters',
    passwordTipsContainUpperCaseAndDigit: 'At least one uppercase letter and digit',
    passwordTipsContainSpecialChar: 'At least one special character, such as ',
    confirmAndLogin: 'Confirm and Login',
    cancelResetPassword: 'Cancel Reset Password?',
    cancelResetPasswordMesg:
      'You are resetting your password. If you choose to return, your old password will continue to be used. Are you sure you want to return?',
    cancelResetPasswordConfirm: 'Confirm',
    cancelResetPasswordCancel: 'Cancel',
  },
};
