<template>
  <el-drawer
    v-model="showDrawer"
    size="400"
    style="--el-drawer-padding-primary: 0px"
    header-class="drawer-header"
    :destroy-on-close="true"
    @close="handleClose"
    :show-close="false"
  >
    <template #header>
      <div
        class="h-60px border-b-1 mb-0 flex justify-between flex-row flex-items-center text-18px font-600 color-[#222527] py-18px px-24px"
      >
        <span>{{ t('globalAccount.detail.title') }}</span>

        <svgIcon
          name="icon-close"
          class="text-16px cursor-pointer color-#6B7275"
          @click="handleClose"
        />
      </div>
    </template>

    <el-form
      label-width="100px"
      label-position="top"
      hide-required-asterisk
      class="transaction-form p-24px pb-70px"
    >
      <!-- Time Information -->
      <el-form-item :label="t('globalAccount.detail.status')">
        <div
          class="flex items-center leading-24px w-fit px-12px rounded-12px text-12px"
          :style="{
            backgroundColor: getStatusClass(data.accountStat).bgColor,
            color: getStatusClass(data.accountStat).color,
          }"
        >
          {{ getStatusClass(data.accountStat).name }}
        </div>
      </el-form-item>

      <el-form-item :label="t('globalAccount.detail.type')">
        <span>全球账户，Monthly Deduction</span>
      </el-form-item>

      <el-form-item :label="t('globalAccount.detail.name')">
        <span>{{ data.name || '-' }}</span>
      </el-form-item>

      <el-form-item :label="t('globalAccount.detail.account')">
        <span>{{ data.account || '-' }}</span>
      </el-form-item>

      <el-form-item :label="t('globalAccount.detail.currency')">
        <span>{{ data.currency || '-' }}</span>
      </el-form-item>

      <el-form-item label="Swift/BIC">
        <span>{{ data.bankSwiftCode || '-' }}</span>
      </el-form-item>

      <el-form-item :label="t('globalAccount.detail.bankName')">
        <span>{{ data.bankName || '-' }}</span>
      </el-form-item>

      <el-form-item :label="t('globalAccount.detail.bankAddr')">
        <span>{{ data.bankAddr || '-' }}</span>
      </el-form-item>

      <el-form-item :label="t('globalAccount.detail.bankCountry')">
        <div class="flex items-center">
          <img
            v-if="data.bankCountry"
            style="margin-right: 8px"
            :src="`https://files.dingx.tech/icons/flags/${data.bankCountry.toLowerCase()}.svg`"
            width="16"
          />
          {{ data.bankCountry || '-' }}
        </div>
      </el-form-item>

      <el-form-item label="Payment Reference">
        <span>{{ data.payRemarks || '-' }}</span>
      </el-form-item>

      <el-form-item :label="t('globalAccount.detail.nickname')">
        <div class="w-100% flex items-center justify-between">
          <span v-if="!editShow">{{ data.nickName || '-' }}</span>
          <el-input
            v-if="editShow"
            v-model="data.nickName"
            :placeholder="t('globalAccount.detail.placeholder')"
            class="w-300px"
            maxlength="32"
            show-word-limit
          />
          <el-button
            v-if="!editShow"
            class="text-[#FF0064] font-400"
            type="text"
            @click="editShow = true"
            >{{ t('globalAccount.detail.edit') }}</el-button
          >
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div
        style="border-top: 1px solid #e5e6eb"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px"
      >
        <el-button
          v-if="!editShow"
          @click="handleClose"
          text
          class="h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px btn-hover-scale-sm"
          >{{ t('globalAccount.detail.close') }}</el-button
        >

        <el-button
          v-else
          @click="handleSave"
          text
          class="h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px btn-hover-scale-sm"
          >{{ t('globalAccount.detail.saveClose') }}</el-button
        >
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { modifyNickname } from '../apis/index';

const { t } = useI18n();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  statusList: {
    type: Array,
    default: () => [],
  },
});
const editShow = ref(false);
const showDrawer = ref(props.visible);
watch(
  () => props.visible,
  (newVal) => {
    editShow.value = false;
    showDrawer.value = newVal;
  },
  { immediate: true }
);

const emit = defineEmits(['update:visible', 'getStatusClass']);
// 关闭
const handleClose = () => {
  showDrawer.value = false;
  emit('update:visible', false);
};
// 保存并关闭
const handleSave = async () => {
  await modifyNickname({
    id: props.data.id,
    nickName: props.data.nickName,
  });
  emit('update:visible', false);
};

// 获取账户状态类
const getStatusClass = (status: string) => {
  const params = props.statusList.filter((item: any) => {
    if (item.enumCode === status) {
      return item;
    }
  });
  switch (status) {
    case 'INIT':
      return {
        color: '#61555A',
        bgColor: '#F9F9F9',
        name: params.length > 0 ? (params[0] as any).enumDescCn : '',
      };
    case 'PROCESSING':
      return {
        color: '#007BFF',
        bgColor: '#EBF6FF',
        name: params.length > 0 ? (params[0] as any).enumDescCn : '',
      };
    case 'ACTIVE':
      return {
        color: '#3EB342',
        bgColor: '#EBF7EB',
        name: params.length > 0 ? (params[0] as any).enumDescCn : '',
      };
    case 'FAIL':
      return {
        color: '#FD3627',
        bgColor: '#FFF1EC',
        name: params.length > 0 ? (params[0] as any).enumDescCn : '',
      };
    default:
      return {
        color: '#D18801',
        bgColor: '#FFFCEB',
        name: params.length > 0 ? (params[0] as any).enumDescCn : '',
      };
  }
};
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

:deep(.el-input__wrapper) {
  border: 1px solid #e5e6eb;
  box-shadow: none;
  border-radius: 6px;
}

:deep(.el-form-item) {
  margin-bottom: 30px;
  .el-form-item__content {
    line-height: 20px;
    color: #222527;
  }
}

:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #464b4e !important;
  margin-bottom: 12px !important;
}

.cancel-btn {
  background-color: #ffffff !important;
  border: 1px solid #e5e6eb !important;
  color: #222527 !important;
  border-radius: 6px !important;
}

.cancel-btn:hover {
  background-color: #f8f9fa !important;
  border-color: #e5e6eb !important;
}

.confirm-btn {
  background-color: #030814 !important;
  border: 1px solid #030814 !important;
  color: #ffffff !important;
  border-radius: 6px !important;
}

.confirm-btn:hover {
  background-color: #1a1f2e !important;
  border-color: #1a1f2e !important;
}

/* 验证码容器样式 */
.verification-code-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 0;
  position: relative;
}

.verification-code-input {
  flex: 1;

  :deep(.el-input__wrapper) {
    border-radius: 6px !important;
    height: 40px;
    padding: 0 12px;
  }

  :deep(.el-input__inner) {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #222527;

    &::placeholder {
      color: #a7adb0;
      font-size: 14px;
    }
  }
}

.send-code-btn {
  background: transparent !important;
  border: none !important;
  color: #ff0064 !important;
  border-radius: 6px !important;
  width: 100px;
  height: 40px;
  font-size: 14px;
  font-family: 'PingFangSC-Regular';
  flex-shrink: 0;
  margin-left: 0;
  margin-top: 0;
  padding: 0;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #e5e6eb;
    padding: 0 !important;
    margin-bottom: 0 !important;
  }
  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
</style>
