<template>
  <el-drawer
    v-model="showDrawer"
    size="400px"
    style="--el-drawer-padding-primary: 0px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    header-class="drawer-header"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <template #header>
      <div
        class="h-60px mb-0 flex justify-between flex-row flex-items-center text-18px font-600 color-[#222527] py-18px px-24px"
      >
        <span>{{ t('globalAccount.create.title') }}</span>

        <svgIcon
          name="icon-close"
          class="text-16px cursor-pointer color-#6B7275"
          @click="handleClose"
        />
      </div>
    </template>
    <template #default>
      <div class="p-24px pb-70px">
        <!-- 说明文字 -->
        <div class="mb-24px flex items-center bg-#EBF6FF p-12px rounded-6px">
          <svg-icon name="icon-info-bg" class="text-16px color-[#007BFF] mr-10px flex-shrink-0" />
          <p class="text-14px leading-20px">
            {{ t('globalAccount.create.placeholder') }}
          </p>
        </div>

        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-position="top"
          hide-required-asterisk
        >
          <el-form-item class="font-family-[PingFangSC-Regular] text-14px color-[#222527] mb-24px">
            <p>
              {{ t('globalAccount.create.nickname') }}
              <span class="text-14px color-[#6B7275]">{{
                t('globalAccount.create.nicknamePlaceholder')
              }}</span>
            </p>

            <el-input
              v-model="formData.nickName"
              :placeholder="t('setting.editEmail.placeholder')"
              class="w-full"
              maxlength="32"
              show-word-limit
            />
          </el-form-item>

          <el-divider class="mt-32px" />

          <h6 class="text-16px font-family-[PingFangSC-Regular] text-[#222527] m-0">
            {{ t('globalAccount.create.accountConfig') }}
          </h6>

          <el-form-item
            :label="t('globalAccount.create.accountDeductionType')"
            prop="accountDeductionType"
            class="font-family-[PingFangSC-Regular] text-14px color-[#222527] m-0 mt-24px"
          >
            <el-select
              v-model="formData.accountDeductionType"
              @change="handleDeductionChange"
              :placeholder="t('globalAccount.create.accountDeductionTypePlaceholder')"
            >
              <el-option
                v-for="item in deductionOptions"
                :key="item.enumCode"
                :label="item.enumDescCn"
                :value="item.enumCode"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            :label="t('globalAccount.create.currency')"
            prop="ccy"
            class="font-family-[PingFangSC-Regular] text-14px color-[#222527] mt-24px mb-16px"
          >
            <el-select
              v-model="formData.ccy"
              @change="handleCcyChange"
              :placeholder="
                ccyOptions.length === 0 && formData.accountDeductionType !== ''
                  ? ''
                  : t('globalAccount.create.currency')
              "
            >
              <el-option
                v-for="item in ccyOptions"
                :key="item.currency"
                :label="item.currency"
                :value="item.currency"
              >
              </el-option>
              <template
                v-if="ccyOptions.length === 0 && formData.accountDeductionType !== ''"
                #prefix
              >
                <div style="color: #000">{{ t('globalAccount.create.currencyPlaceholder') }}</div>
              </template>
            </el-select>

            <p
              v-if="ccyOptions.length === 0 && formData.accountDeductionType !== ''"
              class="text-12px color-#FD3627 m-0 leading-16px mt-4px"
            >
              {{ t('globalAccount.create.currencyPlaceholderText') }}
            </p>
          </el-form-item>
        </el-form>

        <ul v-if="!(ccyOptions.length === 0 && formData.accountDeductionType !== '')">
          <li
            class="bg-[#F5F5F5] text-14px text-[#6B7275] w-fit leading-28px px-8px rounded-4px mb-8px"
          >
            {{ t('globalAccount.create.vaFee') }}：{{
              channelInfo.vaFee
                ? formData.ccy +
                  ' ' +
                  formatNumber(Number(channelInfo.vaFee)) +
                  '/' +
                  t('globalAccount.create.account')
                : '-'
            }}
          </li>
          <li
            class="bg-[#F5F5F5] text-14px text-[#6B7275] w-fit leading-28px px-8px rounded-4px mb-8px"
          >
            {{ t('globalAccount.create.vaManageFee') }}：{{
              channelInfo.vaManageFee
                ? formData.ccy +
                  ' ' +
                  formatNumber(Number(channelInfo.vaManageFee)) +
                  '/' +
                  t('globalAccount.create.monthly')
                : '-'
            }}
          </li>
          <li
            class="bg-[#F5F5F5] text-14px text-[#6B7275] w-fit leading-28px px-8px rounded-4px mb-8px"
          >
            {{ t('globalAccount.create.vaOpenTime') }}：{{
              channelInfo.vaOpenTime && channelInfo.vaOpenEndTime
                ? channelInfo.vaOpenTime +
                  t('globalAccount.create.to') +
                  channelInfo.vaOpenEndTime +
                  t('globalAccount.create.hour')
                : '-'
            }}
          </li>
        </ul>
      </div>
    </template>
    <template #footer>
      <div
        style="border-top: 1px solid #e5e6eb"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px"
      >
        <el-button
          @click="handleClose"
          class="cancel-btn w-68px h-32px text-14px text-[#222527] font-family-[PingFangSC-Regular] mr-8px btn-hover-scale-sm"
        >
          {{ t('globalAccount.create.close') }}
        </el-button>
        <el-button
          class="confirm-btn w-68px h-32px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm ml-0"
          @click="submit"
        >
          {{ t('globalAccount.create.save') }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, ref, reactive, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { t } from '@@/i18n';
import { useUserStore } from '@/pinia/stores/user';
import { formatNumber } from '@@/utils/math';
import { virtualAccountCreate, queryAvailableCurrencies, queryChannelInfos } from '../apis';
import { virtualAccountCreateRequestData } from '../apis/type';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { BusinessEnumType } from '@@/apis/common/type';
const enumStore = useEnumStore();
defineOptions({
  name: 'EditEmail',
});

interface EmailInfo {
  newEmail: string;
  verificationCode: string;
}

const props = withDefaults(
  defineProps<{
    showCreateAccount: boolean;
  }>(),
  {
    showCreateAccount: false,
  }
);
const deductionOptions = enumStore.getEnumList(BusinessEnumType.ACCOUNT_DEDUCTION_TYPE);
const ccyOptions: any = ref([]);

const emit = defineEmits<{
  (e: 'update:showCreateAccount', value: boolean): void;
  (e: 'getList'): void;
}>();

const showDrawer = computed({
  get: () => props.showCreateAccount,
  set: (value) => emit('update:showCreateAccount', value),
});

const formData = ref<virtualAccountCreateRequestData>({
  nickName: '',
  ccy: '',
  accountDeductionType: '',
  channelConfigId: '',
  channelType: '',
});

const formRef = ref<FormInstance>();
const channelInfo = ref<any>({
  vaFee: '',
  vaManageFee: '',
  vaOpenTime: '',
});
const rules = computed(() =>
  reactive<FormRules<virtualAccountCreateRequestData>>({
    accountDeductionType: [
      {
        required: true,
        message: t('globalAccount.create.accountDeductionTypePlaceholderText'),
        trigger: 'blur',
      },
    ],

    ccy: [
      {
        required: true,
        message: t('globalAccount.create.currencyPlaceholderText1'),
        trigger: 'blur',
      },
    ],
  })
);

watch(
  () => props.showCreateAccount,
  (newVal) => {
    if (newVal) {
      // 重置表单
      formData.value = {
        nickName: '',
        ccy: '',
        accountDeductionType: '',
        channelConfigId: '',
        channelType: '',
      };
      formRef.value?.clearValidate();
    }
  }
);

const handleDeductionChange = async (val: string) => {
  const res = await queryAvailableCurrencies({
    accountDeductionType: val,
  });
  formData.value.ccy = '';
  channelInfo.value = {
    vaFee: '',
    vaManageFee: '',
    vaOpenTime: '',
  };
  ccyOptions.value = res.data.pageList;
};

const handleCcyChange = async (val: string) => {
  const res = await queryChannelInfos({
    accountDeductionType: formData.value.accountDeductionType,
    ccy: val,
  });
  channelInfo.value = res.data;
};

const submit = async () => {
  try {
    // 验证表单
    await formRef.value?.validate();
    formData.value.channelType = channelInfo.value.channelType;
    formData.value.channelConfigId = channelInfo.value.channelConfigId;
    virtualAccountCreate(formData.value).then((res) => {
      ElMessage.success(t('globalAccount.create.createSuccess'));
      emit('getList');
      showDrawer.value = false;
    });
  } catch (error: any) {
    console.error('创建失败:', error);
    // ElMessage.error(error.message || t('setting.editEmail.updateFailed'));
  }
};

const handleClose = () => {
  showDrawer.value = false;
};
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

:deep(.el-input__wrapper) {
  border: 1px solid #e5e6eb;
  box-shadow: none;
  border-radius: 6px;
}

:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
  margin-bottom: 8px;
}

.cancel-btn {
  background-color: #ffffff !important;
  border: 1px solid #e5e6eb !important;
  color: #222527 !important;
  border-radius: 6px !important;
}

.cancel-btn:hover {
  background-color: #f8f9fa !important;
  border-color: #e5e6eb !important;
}

.confirm-btn {
  background-color: #030814 !important;
  border: 1px solid #030814 !important;
  color: #ffffff !important;
  border-radius: 6px !important;
}

.confirm-btn:hover {
  background-color: #1a1f2e !important;
  border-color: #1a1f2e !important;
}

/* 验证码容器样式 */
.verification-code-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 0;
  position: relative;
}

.verification-code-input {
  flex: 1;

  :deep(.el-input__wrapper) {
    border-radius: 6px !important;
    height: 40px;
    padding: 0 12px;
  }

  :deep(.el-input__inner) {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #222527;

    &::placeholder {
      color: #a7adb0;
      font-size: 14px;
    }
  }
}

.send-code-btn {
  background: transparent !important;
  border: none !important;
  color: #ff0064 !important;
  border-radius: 6px !important;
  width: 100px;
  height: 40px;
  font-size: 14px;
  font-family: 'PingFangSC-Regular';
  flex-shrink: 0;
  margin-left: 0;
  margin-top: 0;
  padding: 0;
}

.send-code-btn:hover:not(:disabled) {
  background: transparent !important;
}

.send-code-btn:disabled {
  background: transparent !important;
  border: none !important;
  color: #a7adb0 !important;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #e5e6eb;
    padding: 0 !important;
    margin-bottom: 0 !important;
  }
  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
</style>
