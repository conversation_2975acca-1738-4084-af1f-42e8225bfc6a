export default {
  globalAccount: {
    title: 'Global Account',
    search: {
      currency: 'Currency',
      accountStat: 'Account Status',
      placeholder: 'Please select',
      reset: 'Reset',
      query: 'Query',
      filter: 'Filter Item',
      inputPlaceholder: 'Please input',
      accountName: 'Account Name',
      nickName: 'Nickname',
      account: 'Account Number',
      createGlobalAccount: 'Create Global Account',
    },
    table: {
      index: 'No.',
      bankCountry: 'Bank Country',
      accountName: 'Account Name & Nickname',
      currency: 'Currency',
      account: 'Account Number',
      status: 'Status',
      operation: 'Operation',
      empty: 'No data available',
      total: 'Total {total} records',
    },
    detail: {
      title: 'Global Account - Single Account Details',
      status: 'Status',
      type: 'Account Type',
      name: 'Account Name',
      account: 'Account Number',
      currency: 'Supported Currency',
      swift: 'Swift/BIC',
      bankName: 'Bank Name',
      bankAddr: 'Bank Address',
      bankCountry: 'Bank Country/Region',
      reference: 'Payment Reference(if any)',
      nickname: 'Account Nickname',
      edit: 'Edit',
      placeholder: 'Please enter account nickname',
      close: 'Close',
      saveClose: 'Save & Close',
    },
    accountTypes: {
      global: 'Global Account, Monthly Deduction',
    },
    create: {
      title: 'Create New Global Account',
      placeholder:
        'System will apply for a dedicated virtual account (VA) for your company to manage secure and independent fund transfer and clearing, helping you operate efficiently and compliant with cross-border regulations',
      nickname: 'What is the nickname for this new account? ',
      nicknamePlaceholder: '(Optional)',
      accountConfig: 'Global Account Configuration',
      accountDeductionType: 'Account Deduction Type',
      accountDeductionTypePlaceholder: 'Please select',
      currency: 'Currency',
      currencyPlaceholder: 'Please select',
      currencyPlaceholderText:
        'Current no available currency (or has helped you open full currency account), if you need, please contact your sales or DingX customer service',
      vaFee: 'Open Fee',
      vaManageFee: 'Account Maintenance Fee',
      vaOpenTime: 'Expected Time',
      close: 'Cancel',
      save: 'Create',
      accountDeductionTypePlaceholderText: 'Please select account deduction type',
      currencyPlaceholderText1: 'Please select currency',
      account: 'Account',
      monthly: 'Monthly',
      to: 'to',
      hour: 'hour',
      createSuccess: 'Create Success',
    },
  },
};
