export type virtualAccountRequestData = ApiRequestData<{
  currency?: Array<string>;
  accountStat?: Array<string>;
  crtMethod?: string;
  checkOption?: string;
  checkText?: string;
  pageNum: number;
  pageSize: number;
}>;

export type virtualAccountResponseData = ApiResponseData<{
  totalCount: number;
  pageList: Array<virtualAccountResponseList>;
}>;

export type virtualAccountResponseList = {
  id: number;
  walletId: string;
  account: string;
  bankCountry: string;
  bankName: string;
  bankSwiftCode: string;
  payMethod: string;
  currency: string;
  name: string;
  channelType: string;
  createTime: string;
  updateTime: string;
  subWalletId: string;
  accountStat: string;
  nickName: string;
  bankAddr: string;
  paymentReference: string;
  payRemarks: string;
  crtMethod: string;
  reqAppName: string;
  expDate: string;
  unvlDate: string;
  accountDeductionType: string;
};

export type modifyNicknameRequestData = ApiRequestData<{
  id: string;
  nickName: string;
}>;

export type modifyNicknameResponseData = ApiResponseData<{
  data?: object;
}>;

export type virtualAccountCreateRequestData = ApiRequestData<{
  nickName?: string;
  ccy: string;
  accountDeductionType: string;
  channelConfigId: string;
  channelType: string;
}>;

export type virtualAccountCreateResponseData = ApiResponseData<{
  data?: object;
}>;

export type queryAvailableCurrenciesRequestData = ApiRequestData<{
  accountDeductionType: string;
}>;

export type queryAvailableCurrenciesResponseData = ApiResponseData<{
  totalCount: number;
  pageList: Array<queryAvailableCurrenciesResponseList>;
}>;

export type queryAvailableCurrenciesResponseList = {
  currency: string;
};

export type queryChannelInfosRequestData = ApiRequestData<{
  accountDeductionType: string;
  ccy: string;
}>;

export type queryChannelInfosResponseData = ApiResponseData<{
  channelConfigId: string;
  channelType: string;
  vaFee: string;
  vaManageFee: string;
  vaOpenTime: string;
}>;
