import type * as record from './type';
import { request } from '@/http/axios';

/** 虚拟账户列表 */
export function virtualAccountList(data: record.virtualAccountRequestData) {
  return request<record.virtualAccountResponseData>({
    url: '/virtualAccount/list',
    method: 'post',
    data,
  });
}

/** 修改昵称 */
export function modifyNickname(data: record.modifyNicknameRequestData) {
  return request<record.modifyNicknameResponseData>({
    url: '/virtualAccount/modifyNickname',
    method: 'post',
    data,
  });
}

/** 创建全球账户 */
export function virtualAccountCreate(data: record.virtualAccountCreateRequestData) {
  return request<record.virtualAccountCreateResponseData>({
    url: '/virtualAccount/create',
    method: 'post',
    data,
  });
}

/** 查询可用货币 */
export function queryAvailableCurrencies(data: record.queryAvailableCurrenciesRequestData) {
  return request<record.queryAvailableCurrenciesResponseData>({
    url: '/virtualAccount/queryAvailableCurrencies',
    method: 'post',
    data,
  });
}

/** 查询渠道信息 */
export function queryChannelInfos(data: record.queryChannelInfosRequestData) {
  return request<record.queryChannelInfosResponseData>({
    url: '/virtualAccount/queryChannelInfos',
    method: 'post',
    data,
  });
}
