<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { BusinessEnumType } from '@@/apis/common/type';
import { virtualAccountList } from './apis';
import { virtualAccountResponseList } from './apis/type';
import TransactionDrawer from './components/detail.vue';
import CreateAccount from './components/create.vue';

const { t } = useI18n();
const enumStore = useEnumStore();
const form = ref({
  currency: [],
  accountStat: [],
  crtMethod: '',
  checkOption: '',
  checkText: '',
  pageNum: 1,
  pageSize: 10,
});

const showCreateAccount = ref(false);
const showDrawer = ref(false);
const transactionData = ref({});
const checkOption = computed(() => [
  {
    label: t('globalAccount.search.accountName'),
    value: 'name',
  },
  {
    label: t('globalAccount.search.nickName'),
    value: 'nickName',
  },
  {
    label: t('globalAccount.search.account'),
    value: 'account',
  },
]);

const total = ref(0);
const loading = ref(false);
const tableData = ref<virtualAccountResponseList[]>([]);

const currencyList = enumStore.getEnumList(BusinessEnumType.FIAT_CCY);
const statusList = enumStore.getEnumList(BusinessEnumType.ACCOUNT_STAT);
const tradeTypeMap = ref<Map<string, string>>(new Map());

onMounted(async () => {
  getList();
});

const selectStatusList = computed(() => {
  return statusList.value.filter((item) => {
    return item.enumCode !== 'FREEZE' && item.enumCode !== 'CLOSE' && item.enumCode !== 'FAIL';
  });
});

watch(
  statusList,
  (newList) => {
    newList.forEach((item) => {
      tradeTypeMap.value.set(item.enumCode, item.enumDescCn);
    });
  },
  { immediate: true }
);
const getList = async () => {
  loading.value = true;

  try {
    const res = await virtualAccountList(form.value);
    tableData.value = res.data.pageList;
    total.value = res.data.totalCount;
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 重置
const handleReset = () => {
  form.value = {
    currency: [],
    accountStat: [],
    crtMethod: '',
    checkOption: '',
    checkText: '',
    pageNum: 1,
    pageSize: 10,
  };
};

const handleSizeChange = (val: number) => {
  form.value.pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  form.value.pageNum = val;
  getList();
};

const getStatusClass = (status: string) => {
  const params = statusList.value.filter((item) => {
    if (item.enumCode === status) {
      return item;
    }
  });
  switch (status) {
    case 'INIT':
      return { color: '#61555A', bgColor: '#F9F9F9', name: params[0].enumDescCn };
    case 'PROCESSING':
      return { color: '#007BFF', bgColor: '#EBF6FF', name: params[0].enumDescCn };
    case 'ACTIVE':
      return { color: '#3EB342', bgColor: '#EBF7EB', name: params[0].enumDescCn };
    case 'FAIL':
      return { color: '#FD3627', bgColor: '#FFF1EC', name: params[0].enumDescCn };
    default:
      return { color: '#D18801', bgColor: '#FFFCEB', name: params[0].enumDescCn };
  }
};

const detail = (data: virtualAccountResponseList) => {
  transactionData.value = data;
  showDrawer.value = true;
};
</script>

<template>
  <div class="app-container min-w-1000px pb-140px">
    <h1
      class="text-28px text-[#222527] font-600 font-family-[PingFangSC-Semibold] m-0 mb-24px leading-[40px]"
    >
      {{ t('globalAccount.title') }}
    </h1>
    <el-form :inline="true" :model="form" class="demo-form-inline" hide-required-asterisk>
      <el-form-item class="w-362px mr-16px">
        <el-select
          class="w-100%"
          multiple
          v-model="form.currency"
          clearable
          filterable
          :placeholder="t('globalAccount.search.placeholder')"
        >
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">{{
              t('globalAccount.search.currency')
            }}</span>
          </template>
          <el-option
            v-for="info in currencyList"
            :key="info.enumCode"
            :label="info.enumDescCn"
            :value="info.enumCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="w-362px mr-16px">
        <el-select
          class="w-100%"
          multiple
          v-model="form.accountStat"
          clearable
          :placeholder="t('globalAccount.search.placeholder')"
        >
          <template #prefix>
            <span class="text-[#6B7275] text-14px mr-12px font-family-[PingFangSC-Regular]">
              {{ t('globalAccount.search.accountStat') }}
            </span>
          </template>
          <el-option
            v-for="info in selectStatusList"
            :key="info.enumCode"
            :label="info.enumDescCn"
            :value="info.enumCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="w-362px mr-16px">
        <el-input
          v-model="form.checkText"
          :placeholder="t('globalAccount.search.inputPlaceholder')"
        >
          <template #prepend>
            <el-select
              class="w-100%"
              v-model="form.checkOption"
              clearable
              :placeholder="t('globalAccount.search.filter')"
            >
              <el-option
                v-for="info in checkOption"
                :key="info.value"
                :label="info.label"
                :value="info.value"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          @click="handleReset"
          class="w-76px text-14px text-[#222527] font-family-[PingFangSC-Regular] reset-btn btn-hover-scale-sm"
        >
          <SvgIcon name="icon-reset" class="mr-9px" />
          {{ t('globalAccount.search.reset') }}
        </el-button>
        <el-button
          type="primary"
          @click="getList"
          class="w-76px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm"
        >
          <SvgIcon name="icon-query" class="mr-9px" />
          {{ t('globalAccount.search.query') }}
        </el-button>
      </el-form-item>
    </el-form>

    <div class="w-100%">
      <p class="text-14px font-400 text-[#222527] mb-20px flex justify-between items-center">
        {{ t('tradeRecord.total', { total }) }}
        <el-button
          type="primary"
          @click="showCreateAccount = true"
          class="text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm"
        >
          <SvgIcon name="icon-add" class="mr-9px" />
          {{ t('globalAccount.search.createGlobalAccount') }}
        </el-button>
      </p>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        header-cell-class-name="!bg-[#F8F9FA] font-family-[PingFangSC-Semibold] !text-12px !text-[#6B7275]"
        :header-row-style="{ height: '44px' }"
        :row-style="{ height: '48px' }"
      >
        <template #empty>
          <div class="empty-table">
            <p>{{ t('globalAccount.table.empty') }}</p>
          </div>
        </template>
        <el-table-column type="index" :label="t('globalAccount.table.index')" fixed width="50" />

        <el-table-column :label="t('globalAccount.table.bankCountry')" width="180">
          <template #default="{ row }">
            <div class="flex items-center">
              <img
                v-if="row.bankCountry"
                style="margin-right: 8px"
                :src="`https://files.dingx.tech/icons/flags/${row.bankCountry.toLowerCase()}.svg`"
                width="16"
              />
              {{ row.bankCountry || '-' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column :label="t('globalAccount.table.accountName')" min-width="180">
          <template #default="{ row }">
            <p style="margin: 0">{{ row.name || '-' }}</p>
            <p style="margin: 0">{{ row.nickName || '-' }}</p>
          </template>
        </el-table-column>

        <el-table-column prop="currency" :label="t('globalAccount.table.currency')" />
        <el-table-column prop="account" :label="t('globalAccount.table.account')">
          <template #default="{ row }">
            {{ row.account || '-' }}
          </template>
        </el-table-column>
        <el-table-column :label="t('globalAccount.table.status')" width="180">
          <template #default="{ row }">
            <div
              class="flex items-center leading-24px w-fit px-12px rounded-12px text-12px"
              :style="{
                backgroundColor: getStatusClass(row.accountStat).bgColor,
                color: getStatusClass(row.accountStat).color,
              }"
            >
              {{ getStatusClass(row.accountStat).name || '-' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column :label="t('globalAccount.table.operation')" width="90" fixed="right">
          <template #default="{ row }">
            <ElButton
              link
              type="primary"
              @click="detail(row)"
              style="color: #030814"
              class="btn-hover-scale-sm"
            >
              <SvgIcon name="icon-open" />
            </ElButton>
          </template>
        </el-table-column>
      </el-table>

      <div class="flex justify-end mt-24px">
        <el-pagination
          v-model:current-page="form.pageNum"
          v-model:page-size="form.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          size="small"
          layout="prev,pager,next,sizes,jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>

    <TransactionDrawer
      v-model:visible="showDrawer"
      :data="transactionData"
      :statusList="statusList"
    />

    <CreateAccount v-model:showCreateAccount="showCreateAccount" @getList="getList" />
  </div>
</template>

<style lang="scss" scoped>
.content-open .app-container {
  width: calc(100vw - 240px);
}

.content-close .app-container {
  width: calc(100vw - 88px);
}
.date-picker-with-prefix {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding-left: 12px;
}

:deep(.el-input-group__prepend) {
  padding: 0;
  width: 104px;
}

.date-picker-with-prefix:hover {
  border-color: var(--el-border-color-hover);
}

.date-picker-with-prefix .el-date-editor {
  border: none;
  flex: 1;
}

.date-picker-with-prefix :deep(.el-input__wrapper) {
  box-shadow: none !important;
}

.date-picker-with-prefix :deep(.el-range-input) {
  text-align: left;
}

.date-picker-with-prefix :deep(.el-range__icon) {
  display: none;
}

:deep(.el-table) {
  --el-table-header-bg-color: #f8f9fa;
  --el-table-header-text-color: #6b7275;

  .el-table__header-wrapper {
    border-radius: 12px;
    overflow: hidden;
    tr {
      --el-table-border: none;
      .el-table__cell {
        padding: 10px 0;
      }
    }
  }

  tbody tr {
    .el-table__cell {
      padding: 12px 0;
    }
  }
}

.status-dot {
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}
.reset-btn:hover {
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
}
</style>
