<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage, ElPopover } from 'element-plus';
import QrcodeVue from 'qrcode.vue';
import { CopyDocument, ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { BusinessEnumType } from '@/common/apis/common/type';
import { generateSysSeqId } from '@/common/utils/generator';
import { queryCryptoRechargeAcctApi, queryFiatRechargeAcctApi, fiatRechargeOrderApi } from './apis';
import type { CryptoRechargeAcctQueryResponseData, FiatRechargeQueryResponse } from './apis/type';
import { t } from '@@/i18n';
import { defineComponent, h } from 'vue';
import CommonDialog from '@@/components/Dialog';
import DialogService from '@/common/components/Dialog/DialogService';
import { useRouter } from 'vue-router';

const router = useRouter();
const activeTab = ref('fiat');
const step = ref(1);
const dialogVisible = ref(false);
const tooltipVisible = ref(false);
const orderNo = ref('');
const isAccountInfoExpanded = ref(false);
const isSubmitting = ref(false);

const fiatForm = reactive({
  amount: '',
  currency: 'USD',
});

const formattedAmount = computed({
  get: () => fiatForm.amount,
  set: (value: string) => {
    if (value === null || value === undefined) {
      fiatForm.amount = '';
      return;
    }
    let newValue = String(value);
    // 1. Remove non-numeric characters except for the decimal point
    newValue = newValue.replace(/[^\d.]/g, '');

    // 2. Ensure there's only one decimal point
    const parts = newValue.split('.');
    if (parts.length > 2) {
      newValue = parts[0] + '.' + parts.slice(1).join('');
    }

    // 3. Limit to two decimal places
    if (parts[1] && parts[1].length > 2) {
      newValue = parts[0] + '.' + parts[1].slice(0, 2);
    }

    fiatForm.amount = newValue;
  },
});

const fiatAccountInfo = ref<FiatRechargeQueryResponse['data'] | null>(null);

const fiatAccountInfoItems = computed(() => [
  { label: t('recharge.payeeName'), value: fiatAccountInfo.value?.name || '' },
  { label: t('recharge.bankAccount'), value: fiatAccountInfo.value?.account || '' },
  { label: t('recharge.bankName'), value: fiatAccountInfo.value?.bankName || '' },
  { label: t('recharge.bankCountry'), value: fiatAccountInfo.value?.bankCountry || '' },
  { label: t('recharge.swiftCode'), value: fiatAccountInfo.value?.bankSwiftCode || '' },
]);

const form = reactive({
  coin: '',
  network: '',
});

const rechargeInfo = ref<CryptoRechargeAcctQueryResponseData['data']>();

const enumStore = useEnumStore();
const coinOptions = enumStore.getEnumList(BusinessEnumType.CURRENCY);
const networkOptions = enumStore.getEnumList(BusinessEnumType.CRYPTO_NET);

async function fetchFiatAccountInfo() {
  try {
    const res = await queryFiatRechargeAcctApi({
      sysSeqId: generateSysSeqId(),
      transCurrency: fiatForm.currency,
    });
    fiatAccountInfo.value = res.data;
  } catch (error) {
    console.error(error);
  }
}

onMounted(() => {
  const query = router.currentRoute.value.query;
  if (query.type === 'crypto') {
    activeTab.value = 'crypto';
  }
  if (activeTab.value === 'fiat') {
    fetchFiatAccountInfo();
  }
});

async function handleNext() {
  try {
    const res = await queryCryptoRechargeAcctApi({
      sysSeqId: generateSysSeqId(),
      coinSymbol: form.coin,
      network: form.network,
    });
    rechargeInfo.value = res.data;
    step.value = 2;
  } catch (error) {
    console.error(error);
  }
}

function handleBack() {
  step.value = 1;
}

async function handleCopy(text: string) {
  let success = false;
  if (navigator.clipboard && window.isSecureContext) {
    try {
      await navigator.clipboard.writeText(text);
      success = true;
    } catch (e) {
      console.error('copy error', e);
      success = false;
    }
  } else {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed'; // Prevent scrolling to bottom of page in MS Edge.
    textarea.style.top = '-9999px';
    textarea.style.left = '-9999px';
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();
    try {
      success = document.execCommand('copy');
    } catch (e) {
      console.error('copy error', e);
      success = false;
    } finally {
      document.body.removeChild(textarea);
    }
  }
  if (success) {
    ElMessage.success(t('recharge.copySuccess'));
  } else {
    ElMessage.error(t('recharge.copyFailed'));
  }
}

/** 表单验证 */
function validateFiatForm(): boolean {
  if (!fiatForm.amount || parseFloat(fiatForm.amount) <= 0) {
    ElMessage.error(t('recharge.pleaseEnterValidAmount'));
    return false;
  }

  if (parseFloat(fiatForm.amount) < 5000) {
    ElMessage.error(t('recharge.minAmountError'));
    return false;
  }

  if (!fiatAccountInfo.value) {
    ElMessage.error(t('recharge.accountInfoNotLoaded'));
    return false;
  }

  return true;
}

/** 法币充值提交 */
async function handleFiatRechargeSubmit() {
  if (!validateFiatForm()) {
    return;
  }

  if (isSubmitting.value) {
    return;
  }

  try {
    isSubmitting.value = true;

    const requestData = {
      sysSeqId: generateSysSeqId(),
      transCurrency: fiatForm.currency,
      transAmt: parseFloat(fiatForm.amount),
      depositInfoId: fiatAccountInfo.value!.depositInfoId,
      currency: fiatAccountInfo.value!.currency,
      name: fiatAccountInfo.value!.name,
      account: fiatAccountInfo.value!.account,
      bankName: fiatAccountInfo.value!.bankName,
      bankCountry: fiatAccountInfo.value!.bankCountry,
      bankSwiftCode: fiatAccountInfo.value!.bankSwiftCode,
    };

    const response = await fiatRechargeOrderApi(requestData);

    if (response.data) {
      orderNo.value = response.data.sysSeqId;
      // 清空表单
      fiatForm.amount = '';
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error('法币充值提交失败:', error);
    ElMessage.error(t('recharge.submitFailed'));
  } finally {
    isSubmitting.value = false;
  }
}

/** 跳转到充值记录页面 */
function goToRechargeRecord() {
  router.push('/recharge/record');
}

function toRechargeRecord() {
  router
    .push({
      path: '/recharge/record',
      query: { orderNo: orderNo.value },
    })
    .then(() => {
      dialogVisible.value = false;
    });
}
</script>

<template>
  <div class="w-600px mx-auto p-20px pb-100px">
    <el-tabs v-model="activeTab" class="recharge-tabs" @tab-change="fetchFiatAccountInfo">
      <el-tab-pane :label="'Fiat'" name="fiat">
        <div class="fiat-recharge-container">
          <div class="rounded-12px border-1 border-solid border-[#e5e6eb] bg-white p-24px">
            <div class="flex justify-between items-center mb-24px">
              <div
                class="font-family-[PingFangSC-Semibold] font-600 text-18px text-[#222527] flex items-center"
              >
                <div
                  class="w-24px h-24px rounded-6px bg-[#FFEBF0] flex items-center justify-center mr-12px"
                >
                  <SvgIcon name="icon-exchange-pay" class="text-[#FF0064]" />
                </div>
                <span>{{ t('recharge.recharge') }}</span>
              </div>
              <el-button
                type="primary"
                link
                class="font-family-[PingFangSC-Regular] text-[#FF0064]! text-14px font-400!"
                @click="goToRechargeRecord"
                >{{ t('recharge.rechargeRecord') }}</el-button
              >
            </div>
            <el-form :model="fiatForm" class="fiat-recharge-form">
              <el-form-item>
                <el-input
                  v-model="formattedAmount"
                  placeholder="Min 5,000.00 USD"
                  class="amount-input"
                >
                  <template #append>
                    <div class="currency-selector-wrapper">
                      <div class="icon-currency-us"></div>
                      <div>USD</div>
                      <el-icon class="el-icon--right ml-25px mr-15px"
                        ><ArrowDownBold class="text-16px text-[#6B7275]"
                      /></el-icon>
                    </div>
                  </template>
                </el-input>
              </el-form-item>
            </el-form>

            <div v-if="isAccountInfoExpanded && fiatAccountInfo" class="mt-24px">
              <div
                v-for="item in fiatAccountInfoItems"
                :key="item.label"
                class="flex justify-between items-center mb-16px text-14px font-family-[PingFangSC-Regular] font-400 text-[#6B7275]"
              >
                <span class="text-[#6B7275] font-normal">{{ item.label }}</span>
                <div
                  class="flex items-center font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]"
                >
                  <span>{{ item.value }}</span>
                  <el-button
                    :icon="CopyDocument"
                    link
                    class="ml-18px text-14px text-[#222527] btn-hover-scale-sm"
                    @click="handleCopy(item.value)"
                  />
                </div>
              </div>
            </div>

            <div class="text-center">
              <el-button
                link
                class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275]"
                @click="isAccountInfoExpanded = !isAccountInfoExpanded"
              >
                <template v-if="isAccountInfoExpanded">
                  <span>{{ t('recharge.collapse') }}</span>
                  <el-icon class="text-12px ml-4px text-[#6B7275]"><ArrowUp /></el-icon>
                </template>
                <template v-else>
                  <span>{{ t('recharge.expand') }}</span>
                  <el-icon class="text-12px ml-4px text-[#6B7275]"><ArrowDown /></el-icon>
                </template>
              </el-button>
            </div>
          </div>
          <div
            class="font-family-[PingFangSC-Regular] font-400 text-[14px] text-[#6B7275] bg-[#F5F5F5] rounded-4px py-4px px-8px w-fit mb-4px mt-16px"
          >
            {{ t('recharge.fiatRechargeHint1') }}
          </div>
          <div
            class="font-family-[PingFangSC-Regular] font-400 text-[14px] text-[#6B7275] bg-[#F5F5F5] rounded-4px py-4px px-8px mb-4px w-fit"
          >
            {{ t('recharge.fiatRechargeHint2') }}
          </div>
          <div
            class="font-family-[PingFangSC-Regular] font-400 text-[14px] text-[#6B7275] bg-[#F5F5F5] rounded-4px py-4px px-8px w-fit"
          >
            {{ t('recharge.fiatRechargeHint3') }}
          </div>
          <div class="text-center mt-32px">
            <el-button
              type="primary"
              :loading="isSubmitting"
              :disabled="!fiatForm.amount || isSubmitting || !fiatAccountInfo"
              class="mt-24px h-32px w-230px font-family-[PingFangSC-Regular] font-400 text-14px btn-hover-scale-sm"
              @click="handleFiatRechargeSubmit"
            >
              {{ isSubmitting ? t('recharge.submitting') : t('recharge.submitRecharge') }}
            </el-button>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="'Crypto'" name="crypto">
        <el-alert
          v-if="step === 1"
          :title="t('recharge.networkWarning')"
          type="warning"
          text-color="#222527"
          :closable="false"
          show-icon
          class="mb-24px"
        >
          <template #icon>
            <img src="@@/assets/icons/icon-warning.svg" alt="warning icon" />
          </template>
        </el-alert>

        <!-- Step 1: Select coin and network -->
        <div v-if="step === 1">
          <div class="rounded-12px border-1 border-solid border-[#e5e6eb] bg-white p-24px">
            <div class="mb-24px text-18px font-600 flex items-center">
              <div
                class="w-24px h-24px rounded-6px bg-[#FFEBF0] flex items-center justify-center mr-12px"
              >
                <SvgIcon name="icon-usdt" class="text-[#FF0064]" />
              </div>
              <span>{{ t('recharge.recharge') }}</span>
            </div>
            <el-form :model="form" class="recharge-form" label-position="left" label-width="auto">
              <el-form-item
                class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527] h-52px items-center"
                :label="t('recharge.selectCoin')"
              >
                <el-select
                  v-model="form.coin"
                  :placeholder="t('recharge.pleaseSelect')"
                  style="width: 100%"
                  popper-class="recharge-select-popper"
                >
                  <template #prefix>
                    <SvgIcon name="icon-usdt" class="mr-8px h-20px w-20px color-[#1BA27A]" />
                  </template>
                  <el-option
                    v-for="item in coinOptions"
                    :key="item.enumCode"
                    :label="item.enumDescCn"
                    :value="item.enumCode"
                  >
                    <div class="flex items-center">
                      <span
                        class="font-family-[PingFangSC-Semibold] font-600 text-16px text-[#222527]"
                        >{{ item.enumDescCn }}</span
                      >
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527] h-52px items-center mt-18px"
                :label="t('recharge.selectNetwork')"
              >
                <el-select
                  v-model="form.network"
                  :placeholder="t('recharge.pleaseSelect')"
                  style="width: 100%"
                  popper-class="recharge-select-popper"
                >
                  <el-option
                    v-for="item in networkOptions"
                    :key="item.enumCode"
                    :label="item.enumDescCn"
                    :value="item.enumCode"
                    class="font-family-[PingFangSC-Semibold] font-600 text-16px text-[#222527]"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="text-center">
            <el-button
              type="primary"
              class="font-family-[PingFangSC-Regular] mt-24px h-32px w-230px text-14px btn-hover-scale-sm"
              @click="handleNext"
              >{{ t('recharge.nextStep') }}</el-button
            >
          </div>
        </div>

        <!-- Step 2: Show address and info -->
        <div v-if="step === 2 && rechargeInfo">
          <div class="rounded-8px border-1 border-solid border-[#e2e8f0] bg-white p-24px">
            <div class="mb-24px text-18px font-600 flex items-center">
              <SvgIcon name="icon-usdt" class="mr-8.75px h-18px w-18px" />
              <span class="text-18px font-600 text-[#222527]">{{ t('recharge.recharge') }}</span>
            </div>
            <div class="flex flex-col gap-24px">
              <div class="flex items-center justify-between">
                <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275]">
                  {{ t('recharge.coinAndNetwork') }}
                </div>
                <div class="flex items-center text-14px font-500 text-[#1a202c]">
                  <SvgIcon
                    name="icon-usdt"
                    class="mr-6px h-12px w-12px align-middle color-[#1BA27A]"
                  />
                  <span class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]"
                    >{{ form.coin }} - {{ form.network }}</span
                  >
                  <el-button
                    type="primary"
                    link
                    class="font-family-[PingFangSC-Regular] ml-16px font-400 text-14px text-[#FF0064]"
                    @click="handleBack"
                    >{{ t('recharge.editAgain') }}</el-button
                  >
                </div>
              </div>
              <div class="flex items-center justify-between">
                <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275]">
                  {{ t('recharge.rechargeAddress') }}
                </div>
                <div class="flex items-center">
                  <span
                    class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]"
                    >{{ rechargeInfo.address }}</span
                  >
                  <el-button
                    :icon="CopyDocument"
                    link
                    class="ml-8px btn-hover-scale-sm"
                    @click="handleCopy(rechargeInfo.address)"
                  />
                </div>
              </div>

              <div
                class="flex items-center justify-center gap-24px py-24px px-50px bg-[#F5F5F5] rounded-6px"
              >
                <div
                  class="flex flex-col items-center h-176px w-176px bg-[#fff] rounded-6px justify-center"
                >
                  <qrcode-vue
                    v-if="rechargeInfo.address"
                    :value="rechargeInfo.address"
                    :size="152"
                    level="H"
                  />
                </div>
                <div class="flex flex-col ml-74px">
                  <div class="flex flex-col mb-30px">
                    <div
                      class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#464B4E] mb-12px"
                    >
                      {{ t('recharge.minRechargeAmount') }}
                    </div>
                    <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">
                      {{ rechargeInfo.minRechargeAmt }} {{ form.coin }}
                    </div>
                  </div>
                  <div class="flex flex-col">
                    <div
                      class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#464B4E] mb-12px"
                    >
                      {{ t('recharge.estimatedArrival') }}
                    </div>
                    <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">
                      {{ rechargeInfo.estimateAmt }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275]">
                  {{ t('recharge.dailyLimit') }}
                </div>
                <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">
                  {{ rechargeInfo.dailyRemainedAmt }} / {{ rechargeInfo.dailyTotalAmt }}
                  {{ form.coin }}
                </div>
              </div>
              <div class="flex items-center justify-between">
                <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275]">
                  {{ t('recharge.monthlyLimit') }}
                </div>
                <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">
                  {{ rechargeInfo.monthlyRemainedAmt }} / {{ rechargeInfo.monthlyTotalAmt }}
                  {{ form.coin }}
                </div>
              </div>
            </div>
          </div>
          <div
            class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] bg-[#F5F5F5] rounded-4px py-4px px-8px w-fit mb-4px mt-16px"
          >
            {{ t('recharge.limitWarning') }}
          </div>
          <div
            class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] bg-[#F5F5F5] rounded-4px py-4px px-8px w-fit"
          >
            {{ t('recharge.confirmInfo') }}
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <CommonDialog
      v-model:visible="dialogVisible"
      message=""
      @confirm="toRechargeRecord"
      :title="t('recharge.registrationCertificateTitle')"
      :iconClass="'warning'"
      :confirmButtonText="t('recharge.goToRegister')"
      :cancelButtonText="t('recharge.cancel')"
      :closeOnClickOutside="false"
    >
      <template #content>
        <div class="recharge-success-content">
          <div class="success-message">
            {{ t('recharge.rechargeSubmitSuccessDialog', { orderNo: orderNo }) }}
          </div>
          <div class="success-message">
            {{ t('recharge.registrationHint') }}
            <el-popover
              v-model:visible="tooltipVisible"
              :z-index="100000"
              trigger="click"
              :width="324"
              :height="160"
              placement="top"
              :show-arrow="true"
              popper-class="custom-recharge-tooltip"
            >
              <img src="@@/assets/images/icon-recharge-tips.webp" class="icon-recharge-tips-img" />
              <template #reference>
                <img src="@@/assets/icons/icon-info.svg" class="info-icon" />
              </template>
            </el-popover>
          </div>
        </div>
      </template>
    </CommonDialog>
  </div>
</template>

<style lang="scss" scoped>
.recharge-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 24px;
  }
  :deep(.el-tabs__nav-wrap) {
    display: flex;
    justify-content: center;
    &::after {
      height: 0;
    }
  }
  :deep(.el-tabs__item) {
    font-size: 24px;
    color: #6b7275;
    height: 24px;
    margin-bottom: 8px;
    &.is-active {
      color: #222527;
      font-weight: 600;
    }
  }
  :deep(.el-tabs__active-bar) {
    height: 4px;
    background: #ff0064;
    border-radius: 4px;
  }
}

.fiat-recharge-form {
  .amount-input {
    height: 52px;
    font-size: 28px;
    font-family: 'PingFang SC';
    font-weight: 400;
    :deep(.el-input__wrapper) {
      padding: 0;
      box-shadow: none;
      border: none;
      border-bottom: 1px solid #e5e6eb;
      border-radius: 0;
      .el-input__inner {
        color: #222527;
        &::placeholder {
          color: #c0c4cc;
        }
      }
    }

    :deep(.el-input-group__append) {
      padding: 0;
      background: transparent;
      box-shadow: none;
      border: none;
      border-bottom: 1px solid #e5e6eb;
      border-radius: 0;
      .currency-selector-wrapper {
        display: flex;
        align-items: center;
        color: #222527;
        font-size: 16px;
        font-weight: 600;
        .icon-currency-us {
          width: 18px;
          height: 18px;
          margin-right: 8px;
          background-image: url('@/common/assets/icons/icon-currency-us.svg');
          background-size: cover;
        }
      }
    }
  }
}

.recharge-select-popper {
  .el-select-dropdown__item {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }
}
.recharge-form {
  :deep(.el-form-item) {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e5e6eb;
    margin-bottom: 0;

    .el-form-item__label {
      width: auto;
      flex: 0 0 auto;
      font-size: 14px;
      color: #222527;
      font-weight: 400;
      font-family: 'PingFang SC';
      text-align: left;
      margin-right: 32px;
      margin-bottom: 0;
    }

    .el-form-item__content {
      flex: 1;
      .el-select {
        width: 100%;
        .el-select__wrapper {
          border: 0 !important;
          outline: 0 !important;
          box-shadow: none !important;
          background-color: transparent;
          font-family: 'PingFang SC';
          font-weight: 600;
          font-size: 16px;
          color: #222527;
          padding: 0;
        }
      }
    }
  }

  // :deep(.el-form-item:last-child) {
  //   border-bottom: none;
  // }
}

/* 充值成功弹窗内容样式 */
:deep(.recharge-success-content) {
  .success-message {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #222527;
    line-height: 1.6;
  }
  .info-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    cursor: pointer;
    color: #6b7275;
    font-size: 16px;
    transition: all 0.2s ease;
  }
}

/* 自定义气泡提示样式 */
:deep(.custom-recharge-tooltip) {
  .el-popper {
    width: 324px;
    height: 160px;
    background: #ffffff;
    border: 0.4px solid #e5e6eb;
    box-shadow: 0 2px 10px 0 #00000014;
    border-radius: 8px;
    padding: 0;
  }
  .el-popper__arrow::before {
    background: #ff0064;
    border: none;
  }

  .tooltip-content {
    color: white;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.4;
    text-align: center;
  }
}
.icon-recharge-tips-img {
  margin-top: 12px;
  width: 300px;
  height: 130px;
}
</style>
