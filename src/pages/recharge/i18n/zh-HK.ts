export default {
  recharge: {
    fiatRecharge: "法幣充值",
    cryptoRecharge: "數幣充值",
    recharge: "充值",
    rechargeRecord: "充值記錄",
    collapse: "收起",
    expand: "展開充值賬戶信息",
    submitRecharge: "提交充值",
    networkWarning: "請確保充值網絡與提幣網絡一致，否則將造成資產損失",
    selectCoin: "選擇充值幣種",
    selectNetwork: "區塊鏈網絡",
    pleaseSelect: "請選擇",
    nextStep: "下一步",
    coinAndNetwork: "充值幣種與區塊鏈網絡",
    editAgain: "重新編輯",
    rechargeAddress: "充值地址",
    minRechargeAmount: "最小充值數量",
    estimatedArrival: "預計到賬",
    dailyLimit: "單日充值剩餘額度/總額度",
    monthlyLimit: "單月充值剩餘額度/總額度",
    limitWarning: "注意充值金額/數量勿超過剩餘單日/月 充值限額，超過限額後的充值無法到賬。將由線下退回此筆超額充值，同時收取相應處理費用",
    confirmInfo: "請確認您的充值幣種和網絡與所填信息一致。",
    copySuccess: "複製成功",
    copyFailed: "複製失敗",
    fiatRechargeHint1: "請先提交充值單，並使用審核通過的銀行賬戶進行付款，最低充值金額5,000.00 USD。",
    fiatRechargeHint2: '付款完成後，請至"充值-充值交易列表"處登記匯款憑證，我們會根據匯款憑證及時確認款項。',
    fiatRechargeHint3: "建議截圖保存充值銀行賬戶信息，您可通過賬單詳情或法幣充值頁面確認充值到賬情況。",
    payeeName: "收款人名稱",
    bankAccount: "銀行賬戶",
    bankName: "銀行名稱",
    bankCountry: "銀行國家",
    swiftCode: "SWIFT銀行編碼",
    submitting: "提交中...",
    pleaseEnterValidAmount: "請輸入有效的充值金額",
    minAmountError: "最低充值金額為 5,000.00 USD",
    accountInfoNotLoaded: "充值賬戶信息未加載，請刷新頁面重試",
    submitSuccess: "充值申請提交成功",
    submitFailed: "充值申請提交失敗，請重試",
    // 彈窗相關文本
    registrationCertificateTitle: "登記憑證",
    rechargeSubmitSuccessDialog: "充值提交成功，充值單號為：{orderNo}",
    registrationHint: "請在匯款完成後到\"充值-充值記錄\"處登記匯款憑證",
    registrationDetailTip: "登記匯款憑證可以幫助我們更快地確認您的充值，請務必在匯款完成後及時登記。登記時需要上傳匯款憑證截圖或照片。",
    goToRegisterCertificate: "點擊此處去登記匯款憑證",
    cancel: "取消",
    goToRegister: "去登記",
    // 充值記錄頁面
    record: {
      tableEmpty: "暫無數據",
      title: "充值記錄",
      total: "共{total}條",
      currency: "幣種",
      chargeTime: "充值時間",
      chargeAmount: "申請金額",
      bankFlowNo: "銀行匯款流水號",
      accountAmount: "到賬金額",
      chargeStatus: "充值狀態",
      voucher: "匯款憑證",
      voucherStatus: "憑證狀態",
      operations: "操作",
      reset: "重置",
      query: "查詢",
      pleaseSelect: "請選擇",
      pleaseInput: "請輸入",
      rechargeSeqId: "充值流水",
      applyAmount: "申請金額",
      startTime: "開始時間",
      endTime: "結束時間",
      index: "序號",
      edit: "編輯",
      delete: "刪除",
      close: "關閉",
      register: "登記憑證",
      statusTypes: {
        pending: "待上傳憑證",
        submitted: "已提交",
        processing: "處理中",
        success: "充值成功",
        failed: "充值失敗",
        closed: "充值關閉"
      },
      voucherTypes: {
        notUploaded: "未上傳",
        uploaded: "已上傳",
        approved: "已通過",
        rejected: "已拒絕"
      },
      shortcuts: {
        day1: "近1天",
        day7: "近7天",
        day30: "近30天"
      },
      pagination: {
        jumper: "跳至"
      },
      registerVoucher: {
        title: "登記匯款憑證",
        description: "請上傳您的匯款憑證，包括銀行轉賬截圖、回單等，以便我們快速確認您的充值。",
        orderInfo: "訂單信息",
        bankFlowNo: "銀行匯款流水號",
        bankFlowNoPlaceholder: "請輸入銀行匯款流水號",
        voucherUpload: "上傳憑證",
        uploadTip: "支持PNG、JPG、JPEG、PDF格式，最多上傳5個文件，每個文件不超過10MB",
        remark: "備註",
        remarkPlaceholder: "請輸入備註信息（選填）",
        cancel: "取消",
        confirm: "提交",
        bankFlowNoRequired: "請輸入銀行匯款流水號",
        voucherRequired: "請上傳匯款憑證",
        submitSuccess: "匯款憑證登記成功",
        submitFailed: "匯款憑證登記失敗，請重試"
      },
      autoOpenVoucherMessage: "已為您自動打開流水號 {orderNo} 的上傳憑證頁面",
      recordNotFound: "未找到流水號 {orderNo} 對應的充值記錄",
      closeConfirmTitle: "關閉充值？",
      closeConfirmMessage: "請確認是否要關閉充值流水號 {orderNo} 嗎？關閉後將無法恢復。",
      confirmClose: "確認",
      cancel: "取消",
      closing: "關閉中...",
      closeSuccess: "充值關閉成功",
      closeFailed: "充值關閉失敗，請重試",
      bankInfo: {
        title: "打款資訊",
        payeeInfo: "收款人資訊",
        bankInfo: "銀行資訊",
        payeeName: "收款人名稱",
        account: "銀行賬戶",
        bankName: "銀行名稱",
        bankCountry: "銀行國家",
        swiftCode: "SWIFT銀行編碼",
        remark: "備註/附言",
        currency: "幣種",
        tips: "溫馨提示",
        tipsContent: "請使用您已認證的銀行賬戶進行匯款，並確保匯款資訊與上述資訊完全一致。匯款完成後，請及時登記匯款憑證以便我們快速確認到賬。",
        close: "關閉",
        loadFailed: "獲取銀行資訊失敗，請重試"
      }
    }
  }
} 