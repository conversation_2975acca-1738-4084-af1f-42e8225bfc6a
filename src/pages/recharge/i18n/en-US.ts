export default {
  recharge: {
    fiatRecharge: "Fiat Deposit",
    cryptoRecharge: "Crypto Deposit",
    recharge: "Deposit",
    rechargeRecord: "Deposit Record",
    collapse: "Collapse",
    expand: "Expand Deposit Account Information",
    submitRecharge: "Submit Deposit",
    networkWarning: "Please ensure the deposit network is consistent with the withdrawal network, otherwise it will cause asset loss",
    selectCoin: "Select Coin",
    selectNetwork: "Blockchain Network",
    pleaseSelect: "Please select",
    nextStep: "Next",
    coinAndNetwork: "Deposit Coin and Blockchain Network",
    editAgain: "Edit Again",
    rechargeAddress: "Deposit Address",
    minRechargeAmount: "Minimum Deposit Amount",
    estimatedArrival: "Estimated Arrival",
    dailyLimit: "Daily remaining/total deposit limit",
    monthlyLimit: "Monthly remaining/total deposit limit",
    limitWarning: "Please note that the deposit amount/quantity should not exceed the remaining daily/monthly deposit limit. Deposits exceeding the limit cannot be credited. This excess deposit will be returned offline, and corresponding processing fees will be charged.",
    confirmInfo: "Please confirm that your deposit coin and network are consistent with the information provided.",
    copySuccess: "Copied successfully",
    copyFailed: "Copy failed",
    fiatRechargeHint1: "Please submit the deposit order first and use the approved bank account for payment. The minimum deposit amount is 5,000.00 USD.",
    fiatRechargeHint2: 'After payment is completed, please go to "Deposit - Deposit Transaction List" to register the remittance voucher. We will confirm the payment based on the remittance voucher.',
    fiatRechargeHint3: "It is recommended to take a screenshot to save the deposit bank account information. You can check the deposit status through the bill details or the fiat deposit page.",
    payeeName: "Payee Name",
    bankAccount: "Bank Account",
    bankName: "Bank Name",
    bankCountry: "Bank Country",
    swiftCode: "SWIFT Bank Code",
    submitting: "Submitting...",
    pleaseEnterValidAmount: "Please enter a valid deposit amount",
    minAmountError: "Minimum deposit amount is 5,000.00 USD",
    accountInfoNotLoaded: "Deposit account information not loaded, please refresh and try again",
    submitSuccess: "Deposit application submitted successfully",
    submitFailed: "Failed to submit deposit application, please try again",
    // Dialog related text
    registrationCertificateTitle: "Registration Certificate",
    rechargeSubmitSuccessDialog: "Deposit submitted successfully, Order No.: {orderNo}",
    registrationHint: "Please register the remittance certificate at \"Deposit - Deposit Record\" after payment completion",
    registrationDetailTip: "Registering the remittance certificate can help us confirm your deposit faster. Please register promptly after payment completion. You need to upload a screenshot or photo of the remittance certificate when registering.",
    goToRegisterCertificate: "Click here to register remittance certificate",
    cancel: "Cancel",
    goToRegister: "Register",
    // Recharge Record Page
    record: {
      tableEmpty: "No Data",
      title: "Deposit Record",
      total: "Total {total} items",
      currency: "Currency",
      chargeTime: "Deposit Time",
      chargeAmount: "Applied Amount",
      bankFlowNo: "Bank Flow No",
      accountAmount: "Credited Amount",
      chargeStatus: "Deposit Status",
      voucher: "Remittance Voucher",
      voucherStatus: "Voucher Status",
      operations: "Operations",
      reset: "Reset",
      query: "Query",
      pleaseSelect: "Please select",
      pleaseInput: "Please input",
      rechargeSeqId: "Deposit Seq ID",
      applyAmount: "Applied Amount",
      startTime: "Start Time",
      endTime: "End Time",
      index: "No.",
      edit: "Edit",
      delete: "Delete",
      close: "Close",
      register: "Register Voucher",
      statusTypes: {
        pending: "Pending Voucher Upload",
        submitted: "Submitted",
        processing: "Processing",
        success: "Deposit Success",
        failed: "Deposit Failed",
        closed: "Deposit Closed"
      },
      voucherTypes: {
        notUploaded: "Not Uploaded",
        uploaded: "Uploaded",
        approved: "Approved",
        rejected: "Rejected"
      },
      shortcuts: {
        day1: "Last 1 Day",
        day7: "Last 7 Days",
        day30: "Last 30 Days"
      },
      pagination: {
        jumper: "Go to"
      },
      registerVoucher: {
        title: "Register Payment Voucher",
        description: "Please upload your payment voucher, including bank transfer screenshots, receipts, etc., so that we can quickly confirm your deposit.",
        orderInfo: "Order Information",
        bankFlowNo: "Bank Transfer Reference Number",
        bankFlowNoPlaceholder: "Please enter bank transfer reference number",
        voucherUpload: "Upload Voucher",
        uploadTip: "Supports PNG, JPG, JPEG, PDF formats, up to 5 files, each file no more than 10MB",
        remark: "Remark",
        remarkPlaceholder: "Please enter remark information (optional)",
        cancel: "Cancel",
        confirm: "Submit",
        bankFlowNoRequired: "Please enter bank transfer reference number",
        voucherRequired: "Please upload payment voucher",
        submitSuccess: "Payment voucher registered successfully",
        submitFailed: "Payment voucher registration failed, please try again"
      },
      autoOpenVoucherMessage: "Automatically opened voucher upload page for order No. {orderNo}",
      recordNotFound: "No deposit record found for order No. {orderNo}",
      closeConfirmTitle: "Close Deposit?",
      closeConfirmMessage: "Please confirm whether you want to close deposit order No. {orderNo}? This action cannot be undone.",
      confirmClose: "Confirm",
      cancel: "Cancel",
      closing: "Closing...",
      closeSuccess: "Deposit closed successfully",
      closeFailed: "Failed to close deposit, please try again",
      bankInfo: {
        title: "Payment Information",
        payeeInfo: "Payee Information",
        bankInfo: "Bank Information",
        payeeName: "Payee Name",
        account: "Bank Account",
        bankName: "Bank Name",
        bankCountry: "Bank Country",
        swiftCode: "SWIFT Bank Code",
        remark: "Remark",
        currency: "Currency",
        tips: "Tips",
        tipsContent: "Please use your verified bank account for remittance and ensure the remittance information is completely consistent with the above information. After remittance completion, please register the remittance voucher in time for us to quickly confirm the arrival.",
        close: "Close",
        loadFailed: "Failed to load bank information, please try again"
      }
    }
  }
} 