export default {
  recharge: {
    fiatRecharge: "法币充值",
    cryptoRecharge: "数币充值",
    recharge: "充值",
    rechargeRecord: "充值记录",
    collapse: "收起",
    expand: "展开充值账户信息",
    submitRecharge: "提交充值",
    networkWarning: "请确保充值网络与提币网络一致，否则将造成资产损失",
    selectCoin: "选择充值币种",
    selectNetwork: "区块链网络",
    pleaseSelect: "请选择",
    nextStep: "下一步",
    coinAndNetwork: "充值币种与区块链网络",
    editAgain: "重新编辑",
    rechargeAddress: "充值地址",
    minRechargeAmount: "最小充值数量",
    estimatedArrival: "预计到账",
    dailyLimit: "单日充值剩余额度/总额度",
    monthlyLimit: "单月充值剩余额度/总额度",
    limitWarning: "注意充值金额/数量勿超过剩余单日/月 充值限额，超过限额后的充值无法到账。将由线下退回此笔超额充值，同时收取相应处理费用",
    confirmInfo: "请确认您的充值币种和网络与所填信息一致。",
    copySuccess: "复制成功",
    copyFailed: "复制失败",
    fiatRechargeHint1: "请先提交充值单，并使用审核通过的银行账户进行付款，最低充值金额5,000.00 USD。",
    fiatRechargeHint2: '付款完成后，请至"充值-充值交易列表"处登记汇款凭证，我们会根据汇款凭证及时确认款项。',
    fiatRechargeHint3: "建议截图保存充值银行账户信息，您可通过账单详情或法币充值页面确认充值到账情况。",
    payeeName: "收款人名称",
    bankAccount: "银行账号",
    bankName: "银行名称",
    bankCountry: "银行国家",
    swiftCode: "SWIFT银行编码",
    submitting: "提交中...",
    pleaseEnterValidAmount: "请输入有效的充值金额",
    minAmountError: "最低充值金额为 5,000.00 USD",
    accountInfoNotLoaded: "充值账户信息未加载，请刷新页面重试",
    submitSuccess: "充值申请提交成功",
    submitFailed: "充值申请提交失败，请重试",
    // 弹窗相关文本
    registrationCertificateTitle: "登记凭证",
    rechargeSubmitSuccessDialog: "充值提交成功，充值单号为：{orderNo}",
    registrationHint: "请在汇款完成后到\"充值-充值记录\"处登记汇款凭证",
    registrationDetailTip: "登记汇款凭证可以帮助我们更快地确认您的充值，请务必在汇款完成后及时登记。登记时需要上传汇款凭证截图或照片。",
    goToRegisterCertificate: "点击此处去登记汇款凭证",
    cancel: "取消",
    goToRegister: "去登记",
    // 充值记录页面
    record: {
      tableEmpty: "暂无数据",
      title: "充值记录",
      total: "共{total}条",
      currency: "币种",
      chargeTime: "充值时间",
      chargeAmount: "申请金额",
      bankFlowNo: "银行汇款流水号",
      accountAmount: "到账金额",
      chargeStatus: "充值状态",
      voucher: "汇款凭证",
      voucherStatus: "凭证状态",
      operations: "操作",
      reset: "重置",
      query: "查询",
      pleaseSelect: "请选择",
      pleaseInput: "请输入",
      rechargeSeqId: "充值流水",
      applyAmount: "申请金额",
      startTime: "开始时间",
      endTime: "结束时间",
      index: "序号",
      edit: "编辑",
      delete: "删除",
      close: "关闭",
      register: "登记凭证",
      statusTypes: {
        pending: "待上传凭证",
        submitted: "已提交",
        processing: "处理中",
        success: "充值成功",
        failed: "充值失败",
        closed: "充值关闭"
      },
      voucherTypes: {
        notUploaded: "未上传",
        uploaded: "已上传",
        approved: "已通过",
        rejected: "已拒绝"
      },
      shortcuts: {
        day1: "近1天",
        day7: "近7天",
        day30: "近30天"
      },
      pagination: {
        jumper: "跳至"
      },
      registerVoucher: {
        title: "登记汇款凭证",
        description: "请上传您的汇款凭证，包括银行转账截图、回单等，以便我们快速确认您的充值。",
        orderInfo: "订单信息",
        bankFlowNo: "银行汇款流水号",
        bankFlowNoPlaceholder: "请输入银行汇款流水号",
        voucherUpload: "上传凭证",
        uploadTip: "支持PNG、JPG、JPEG、PDF格式，最多上传5个文件，每个文件不超过10MB",
        remark: "备注",
        remarkPlaceholder: "请输入备注信息（选填）",
        cancel: "取消",
        confirm: "提交",
        bankFlowNoRequired: "请输入银行汇款流水号",
        voucherRequired: "请上传汇款凭证",
        submitSuccess: "汇款凭证登记成功",
        submitFailed: "汇款凭证登记失败，请重试"
      },
      autoOpenVoucherMessage: "已为您自动打开流水号 {orderNo} 的上传凭证页面",
      recordNotFound: "未找到流水号 {orderNo} 对应的充值记录",
      closeConfirmTitle: "关闭充值？",
      closeConfirmMessage: "请确认是否要关闭充值流水号 {orderNo} ？关闭后将无法恢复。",
      confirmClose: "确认",
      cancel: "取消",
      closing: "关闭中...",
      closeSuccess: "充值关闭成功",
      closeFailed: "充值关闭失败，请重试",
      bankInfo: {
        title: "打款信息",
        payeeInfo: "收款人信息",
        bankInfo: "银行信息",
        payeeName: "收款人名称",
        account: "银行账号",
        bankName: "银行名称",
        bankCountry: "银行国家",
        swiftCode: "SWIFT银行编码",
        remark: "备注/附言",
        currency: "币种",
        tips: "温馨提示",
        tipsContent: "请使用您已认证的银行账户进行汇款，并确保汇款信息与上述信息完全一致。汇款完成后，请及时登记汇款凭证以便我们快速确认到账。",
        close: "关闭",
        loadFailed: "获取银行信息失败，请重试"
      }
    }
  }
} 