<script lang="ts" setup>
import { reactive, onMounted, ref, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import { useEnumStore } from '@/pinia/stores/enumStore'
import { BusinessEnumType } from '@@/apis/common/type'
import { queryRechargeRecordApi, rechargeConfirmApi, rechargeCloseApi, queryFiatRechargeAcctApi } from './apis'
import type { RechargeRecordQueryRequest, RechargeRecordItem } from './apis/type'
import moment from 'moment'
import UploadFiles from '@/common/components/UploadFiles/index.vue'
import DialogService from '@/common/components/Dialog/DialogService'

const { t } = useI18n()
const route = useRoute()
const enumStore = useEnumStore()

interface FormData {
  transCurrency: string
  transStat: string
  sysSeqId: string
  transAmt: string
  extension3: string
  pageNum: number
  pageSize: number
  dateRange: any[]
}

const form = ref<FormData>({
  transCurrency: '',
  transStat: '',
  sysSeqId: '',
  transAmt: '',
  extension3: '',
  pageNum: 1,
  pageSize: 10,
  dateRange: []
})

const total = ref(0)
const loading = ref(false)
const tableData = ref<RechargeRecordItem[]>([])

// 登记凭证相关
const showRegisterDrawer = ref(false)
const selectedRecord = ref<RechargeRecordItem | null>(null)
const voucherFormRef = ref()
const isSubmitting = ref(false)
const voucherForm = ref({
  bankTransferSeqId: '',
  vouchers: [],
})

// 银行信息抽屉相关
const showBankInfoDrawer = ref(false)
const bankInfoLoading = ref(false)
const bankInfo = ref<any>(null)
const voucherRules = {
  bankTransferSeqId: [
    { required: true, message: t('recharge.record.registerVoucher.bankFlowNoRequired'), trigger: 'blur' }
  ],
  vouchers: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (!value || value.length === 0) {
          callback(new Error(t('recharge.record.registerVoucher.voucherRequired')))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 获取枚举
interface EnumItem {
  enumCode: string
  enumDescCn: string
  extendField?: string
}

const shortcuts = computed(() => [
  {
    text: t('recharge.record.shortcuts.day1'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate())
      start.setHours(0, 0, 0, 0)
      return [start, end]
    },
  },
  {
    text: t('recharge.record.shortcuts.day7'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 6)
      start.setHours(0, 0, 0, 0)
      return [start, end]
    },
  },
  {
    text: t('recharge.record.shortcuts.day30'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 29)
      start.setHours(0, 0, 0, 0)
      return [start, end]
    },
  },
])

const currencyList = enumStore.getEnumList(BusinessEnumType.FIAT_CCY)
const statusList = enumStore.getEnumList(BusinessEnumType.RECHARGE_STAT)
watch(currencyList, (newList) => {
  if (newList.length > 0) {
    form.value.transCurrency = newList[0].enumCode
  }
}, { deep: true })

onMounted(async () => {
  // 获取法币币种枚举
  // const fiatCcyList = await enumStore.getEnumList(BusinessEnumType.FIAT_CCY)
  // currencyList.value = fiatCcyList as unknown as EnumItem[]
  
  // 默认选中第一个法币币种
  // if (currencyList.value.length > 0) {
  //   form.value.transCurrency = currencyList.value[0].enumCode
  // }
  
  // 获取交易状态枚举
  // const rechargeStatList = await enumStore.getEnumList(BusinessEnumType.RECHARGE_STAT)
  // statusList.value = rechargeStatList as unknown as EnumItem[]
  
  // 检查URL参数中是否有流水号
  const orderNo = route.query.orderNo as string
  if (orderNo) {
    // 如果有流水号，设置到搜索条件中
    form.value.sysSeqId = orderNo
  }
  
  await getList()
  
  // 如果有流水号参数，自动查找对应记录并打开上传凭证
  if (orderNo) {
    await nextTick()
    autoOpenVoucherDrawer(orderNo)
  }
})

const getList = async () => {
  loading.value = true
  // 将日期转换为时间戳
  const startTime = form.value.dateRange && form.value.dateRange[0] 
    ? moment(form.value.dateRange[0]).format('YYYY-MM-DD HH:mm:ss') 
    : ''
  const endTime = form.value.dateRange && form.value.dateRange[1] 
    ? moment(form.value.dateRange[1]).format('YYYY-MM-DD HH:mm:ss') 
    : ''

  try {
    const res = await queryRechargeRecordApi({
      transCurrency: form.value.transCurrency ? form.value.transCurrency : undefined,
      transStat: form.value.transStat ? form.value.transStat : undefined,
      sysSeqId: form.value.sysSeqId || undefined,
      transAmt: form.value.transAmt ? Number(form.value.transAmt) : undefined,
      extension3: form.value.extension3 || undefined,
      startTime,
      endTime,
      pageNum: form.value.pageNum,
      pageSize: form.value.pageSize,
    })
    // 为每条记录添加凭证状态
    tableData.value = res.data.transLogResponseList
    total.value = res.data.total
  }
  catch (error) {
    console.error(error)
  }
  finally {
    loading.value = false
  }
}
  
// 重置
const handleReset = () => {
  form.value = {
    transCurrency: '',
    transStat: '',
    sysSeqId: '',
    transAmt: '',
    extension3: '',
    pageNum: 1,
    pageSize: 10,
    dateRange: []
  }
  
  // 重置后，如果有法币币种数据，默认选中第一个
  if (currencyList.value.length > 0) {
    form.value.transCurrency = currencyList.value[0].enumCode
  }
}

// 查询
const handleSearch = () => {
  form.value.pageNum = 1
  getList()
}

const handleSizeChange = (val: number) => {
  form.value.pageSize = val
  getList()
}

const handleCurrentChange = (val: number) => {
  form.value.pageNum = val
  getList()
}

const getStatusClass = (status: string) => {
  // 从枚举数据中查找对应的状态名称，与筛选框保持一致
  const statusItem = statusList.value.find(item => item.enumCode === status)
  const statusName = statusItem?.enumDescCn || ''
  
  switch (status) {
    case 'I':
      return { color: '#FBB004', name: statusName }
    case 'P':
      return { color: '#007BFF', name: statusName }
    case 'S':
      return { color: '#3EB342', name: statusName }
    case 'F':
      return { color: '#FD3627', name: statusName }
    case 'C':
      return { color: '#ACAEB3', name: statusName }
    default:
      return { color: '', name: statusName }
  }
}

const handleDelete = async (row: RechargeRecordItem) => {
  DialogService.confirm(t('recharge.record.closeConfirmMessage', { orderNo: row.sysSeqId }), t('recharge.record.closeConfirmTitle'), {
    confirmButtonText: t('recharge.record.confirmClose'),
    cancelButtonText: t('recharge.record.cancel'),
    iconClass: 'warning',
  }).then(async (result) => {
    if (result) {
      await rechargeCloseApi({
        sysSeqId: row.sysSeqId
      })
      ElMessage.success(t('recharge.record.closeSuccess'))
      getList()
    }
  })
}

// 登记凭证
const handleRegisterVoucher = (row: RechargeRecordItem) => {
  selectedRecord.value = row
  // 重置表单并预填充银行流水号
  voucherForm.value = {
    bankTransferSeqId: row.extension3 || '',
    vouchers: [],
  }
  showRegisterDrawer.value = true
}

// 自动打开上传凭证抽屉
const autoOpenVoucherDrawer = (orderNo: string) => {
  // 在表格数据中查找对应的记录, 并且状态为 I 或 F
  const targetRecord = tableData.value.find(item => item.sysSeqId === orderNo)
  if (targetRecord && (targetRecord.transStat === 'I' || targetRecord.transStat === 'F')) {
    // 如果找到记录，自动打开上传凭证
    handleRegisterVoucher(targetRecord)
    ElMessage.success(t('recharge.record.autoOpenVoucherMessage', { orderNo }))
  } else {
    ElMessage.warning(t('recharge.record.recordNotFound', { orderNo }))
  }
}

// 登记凭证成功回调
const handleRegisterSuccess = () => {
  showRegisterDrawer.value = false
  selectedRecord.value = null
  // 刷新列表
  getList()
}

// 登记凭证取消回调
const handleRegisterCancel = () => {
  showRegisterDrawer.value = false
  selectedRecord.value = null
}

// 提交凭证
const submitVoucher = async () => {
  if (!voucherFormRef.value) return
  
  try {
    const valid = await voucherFormRef.value.validate()
    if (!valid) return
    
    isSubmitting.value = true
    // 调用充值确认接口
    const res = await rechargeConfirmApi({
      sysSeqId: selectedRecord.value!.sysSeqId,
      bankTransferSeqId: voucherForm.value.bankTransferSeqId,
      fileIds: voucherForm.value.vouchers.map((item: any) => item.fileSeqId),
    })
    
    ElMessage.success(t('recharge.record.registerVoucher.submitSuccess'))
    handleRegisterSuccess()
    
  } catch (error: any) {
    console.error('Submit failed:', error)
    // ElMessage.error(error.message || t('recharge.record.registerVoucher.submitFailed'))
  } finally {
    isSubmitting.value = false
  }
}

const handleOpenVoucherBankInfo = (row: RechargeRecordItem) => {
  showBankInfoDrawer.value = true
  bankInfo.value = row.depositInfoDetail
}

const handleOpenVoucher = (url: string) => {
  window.open(url, '_blank')
}

// 处理申请金额输入，限制2位小数
const handleAmountInput = (value: string) => {
  // 移除所有非数字和小数点的字符
  let cleanValue = value.replace(/[^\d.]/g, '')
  
  // 确保只有一个小数点
  const parts = cleanValue.split('.')
  if (parts.length > 2) {
    cleanValue = parts[0] + '.' + parts.slice(1).join('')
  }
  
  // 限制小数点后最多2位
  if (parts.length === 2 && parts[1].length > 2) {
    cleanValue = parts[0] + '.' + parts[1].slice(0, 2)
  }
  
  // 更新表单值
  form.value.transAmt = cleanValue
}

// 金额千分位格式化
const formatAmount = (amount: number) => {
  return amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 计算文本显示宽度（估算）
const getTextWidth = (text: string) => {
  // 去除左侧空格
  const trimmedText = text.trimStart()
  
  // 英文字符和数字约8px，中文字符约14px，符号约6px
  let width = 0
  for (let i = 0; i < trimmedText.length; i++) {
    const char = trimmedText.charAt(i)
    if (/[\u4e00-\u9fa5]/.test(char)) {
      // 中文字符
      width += 14
    } else if (/[0-9.,]/.test(char)) {
      // 数字和符号
      width += 8
    } else {
      // 英文字符
      width += 8
    }
  }
  return width + 16 // 减少padding，去除左侧空白
}

// 计算申请金额列的最大宽度
const transAmtColumnWidth = computed(() => {
  if (!tableData.value||tableData.value.length === 0) return 120
  
  const maxWidth = Math.max(
    ...tableData.value.map(item => {
      const text = `${formatAmount(item.transAmt)} ${item.transCurrency}`
      return getTextWidth(text)
    }),
    100 // 最小宽度
  )
  
  return Math.min(maxWidth, 200) // 最大宽度限制
})

// 计算到账金额列的最大宽度
const realAmtColumnWidth = computed(() => {

  if (!tableData.value||tableData.value.length === 0) return 120
  
  const maxWidth = Math.max(
    ...tableData.value.map(item => {
      const text = `${formatAmount(item.realAmt)} ${item.realCurrency}`
      return getTextWidth(text)
    }),
    100 // 最小宽度
  )
  
  return Math.min(maxWidth, 200) // 最大宽度限制
})
</script>

<template>
  <div class="px-40px py-32px w-100% min-w-800px pb-140px">
    <div class="font-family-[PingFangSC-Semibold] font-600 text-28px text-[#222527] mb-24px">{{ t('recharge.record.title') }}</div>
    
    <el-form :inline="true" :model="form" class="mb-30px" hide-required-asterisk>
      <!-- 第一行：币种、充值时间 -->
      <el-row class="w-100% mb-12px" :gutter="12">
        <el-col :span="8">
          <el-form-item class="w-100%">
            <el-select v-model="form.transCurrency" clearable :placeholder="t('recharge.record.pleaseSelect')" style="width: 100%;">
              <template #prefix>
                <span class="text-[#6B7275] text-14px font-400 mr-12px font-family-[PingFangSC-Regular]">{{ t('recharge.record.currency') }}</span>
              </template>
              <el-option
                v-for="info in currencyList" 
                :key="info.enumCode" 
                :label="info.enumDescCn"
                :value="info.enumCode"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="16">
          <el-form-item class="w-100%">
            <div class="date-picker-with-prefix">
              <span class="date-prefix text-[#6B7275] text-14px font-400 mr-8px font-family-[PingFangSC-Regular]">{{ t('recharge.record.chargeTime') }}</span>
              <el-date-picker
                v-model="form.dateRange"
                type="datetimerange"
                :shortcuts="shortcuts"
                range-separator=""
                clearable
                :start-placeholder="t('recharge.record.startTime')"
                :end-placeholder="t('recharge.record.endTime')"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：充值流水、申请金额、银行汇款流水号 -->
      <el-row class="w-100%" :gutter="12">
        <el-col :span="8" class="mb-12px">
          <el-form-item class="w-100%">
            <el-input v-model="form.sysSeqId" clearable :placeholder="t('recharge.record.pleaseInput')" style="width: 100%;">
              <template #prefix>
                <span class="text-[#6B7275] text-14px font-400 mr-12px font-family-[PingFangSC-Regular]">{{ t('recharge.record.rechargeSeqId') }}</span>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item class="w-100%">
            <el-input v-model="form.transAmt" clearable :placeholder="t('recharge.record.pleaseInput')" style="width: 100%;" @input="handleAmountInput">
              <template #prefix>
                <span class="text-[#6B7275] text-14px font-400 mr-12px font-family-[PingFangSC-Regular]">{{ t('recharge.record.applyAmount') }}</span>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item class="w-100%">
            <el-input v-model="form.extension3" clearable :placeholder="t('recharge.record.pleaseInput')" style="width: 100%;">
              <template #prefix>
                <span class="text-[#6B7275] text-14px font-400 mr-12px font-family-[PingFangSC-Regular]">{{ t('recharge.record.bankFlowNo') }}</span>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：状态筛选和按钮 -->
      <el-row class="w-100%" :gutter="12">
        <el-col :span="8" class="mb-12px">
          <el-form-item class="w-100%">
            <el-select v-model="form.transStat" clearable :placeholder="t('recharge.record.pleaseSelect')" style="width: 100%;">
              <template #prefix>
                <span class="text-[#6B7275] text-14px font-400 mr-12px font-family-[PingFangSC-Regular]">{{ t('recharge.record.chargeStatus') }}</span>
              </template>
              <el-option
                v-for="info in statusList" 
                :key="info.enumCode" 
                :label="info.enumDescCn"
                :value="info.enumCode"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item class="w-100%">
            <div class="flex gap-8px">
              <el-button @click="handleReset" class="reset-btn min-w-76px text-14px text-[#222527] font-family-[PingFangSC-Regular] mr-0px btn-hover-scale-sm">
                <SvgIcon name="icon-reset" class="mr-9px" />
                {{ t('recharge.record.reset') }}
              </el-button>
              <el-button type="primary" @click="handleSearch" class="min-w-76px text-14px text-white font-family-[PingFangSC-Regular] ml-0px btn-hover-scale-sm">
                <SvgIcon name="icon-query" class="mr-9px" />
                {{ t('recharge.record.query') }}
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="w-100%">
      <p class="text-14px font-400 text-[#222527] mb-20px">{{ t('recharge.record.total', { total }) }}</p>
      <el-table
        v-loading="loading" 
        :data="tableData" 
        :empty-text="t('recharge.record.tableEmpty')"
        style="width: 100%" 
        row-key="sysSeqId"
        header-cell-class-name="!bg-[#F8F9FA] font-family-[PingFangSC-Semibold] !text-12px !text-[#6B7275]"
        :header-row-style="{ height: '44px' }" 
        :row-style="{ height: '48px' }"
      >
        <el-table-column type="index" :label="t('recharge.record.index')" fixed min-width="50" />
        
        <el-table-column prop="sysSeqId" :label="t('recharge.record.rechargeSeqId')" min-width="180" />
        
        <el-table-column :label="t('recharge.record.chargeTime')" width="180">
          <template #default="{ row }">
            {{ moment(row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        
        <el-table-column prop="transAmt" :label="t('recharge.record.chargeAmount')" :width="transAmtColumnWidth" header-align="left">
          <template #default="{ row }">
            <div class="text-right whitespace-nowrap">{{ formatAmount(row.transAmt) }} {{ row.transCurrency }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="extension3" :label="t('recharge.record.bankFlowNo')" min-width="120" />
        
        <el-table-column prop="realAmt" :label="t('recharge.record.accountAmount')" :width="realAmtColumnWidth" header-align="left">
          <template #default="{ row }">
            <div class="text-right whitespace-nowrap">{{ formatAmount(row.realAmt) }} {{ row.realCurrency }}</div>
          </template>
        </el-table-column>
        
        <el-table-column :label="t('recharge.record.chargeStatus')" min-width="100">
          <template #default="{ row }">
            <div class="flex items-center">
              <div :class="['status-dot']" :style="{ backgroundColor: getStatusClass(row.transStat).color }" />
              <div>{{ getStatusClass(row.transStat).name }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column :label="t('recharge.record.voucher')" min-width="100">
          <template #default="{ row }">
            <div class="flex items-center justify-left">
              <img 
                v-if="row.fileUrl" 
                @click="handleOpenVoucher(row.fileUrl)"
                src="@/common/assets/icons/files/icon-voucher-default.png" 
                class="!w-36px !h-36px rounded-6px" 
              />
              <span v-else class="text-[#222527]">-</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column :label="t('recharge.record.operations')" min-width="120" fixed="right">
          <template #default="{ row }">
            <div class="flex gap-8px justify-left">
              <el-button 
                type="primary" 
                link 
                size="small"
                class="text-[#222527] !p-0 !w-20px !h-20px flex justify-center items-center btn-hover-scale-sm"
                @click="handleOpenVoucherBankInfo(row)"
              >
                <SvgIcon name="icon-open" class="!w-16px !h-16px" />
              </el-button>
              <!-- 只有状态为 I 时才显示登记凭证按钮 -->
              <el-button 
                v-if="row.transStat === 'I'"
                type="primary" 
                link 
                size="small"
                class="text-[#222527] !p-0 !w-20px !h-20px flex justify-center items-center btn-hover-scale-sm"
                @click="handleRegisterVoucher(row)"
                :title="t('recharge.record.register')"
              >
                <SvgIcon name="icon-recharge-edit" class="!w-16px !h-16px" />
              </el-button>
              <!-- 只有状态为 I 时才显示删除按钮 -->
              <el-button 
                v-if="row.transStat === 'I'"
                type="danger" 
                link 
                size="small"
                class="text-[#222527] !p-0 !w-20px !h-20px flex justify-center items-center btn-hover-scale-sm"
                @click="handleDelete(row)"
                :title="t('recharge.record.close')"
              >
                <SvgIcon name="icon-recharge-close" class="!w-16px !h-16px" />
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="flex justify-end mt-24px">
        <el-pagination
          v-model:current-page="form.pageNum"
          v-model:page-size="form.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          size="small"
          layout="prev,pager,next,sizes,jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <template #jumper>{{ t('recharge.record.pagination.jumper') }}</template>
        </el-pagination>
      </div>
    </div>

    <!-- 登记凭证抽屉 -->
    <el-drawer 
      v-model="showRegisterDrawer" 
      size="400px" 
      style="--el-drawer-padding-primary: 0px"
      header-class="custom-drawer-header"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <template #header>
        <div class="font-family-[PingFangSC-Medium] text-18px color-[#222527] h-60px border-b-1 mb-0 flex-row flex-items-center pt-18px pb-18px pl-24px">
          <span>{{ t('recharge.record.registerVoucher.title') }}</span>
        </div>
      </template>
      <template #default>
        <div class="p-24px">
          <!-- 说明文字 -->
          <!-- <div class="mb-24px">
            <p class="font-family-[PingFangSC-Regular] text-14px color-[#6B7275] leading-20px m-0">
              {{ t('recharge.record.registerVoucher.description') }}
            </p>
          </div> -->

          <!-- 订单信息 -->
          <!-- <div class="mb-24px p-16px bg-[#F8F9FA] rounded-6px">
            <div class="flex justify-between items-center mb-8px">
              <span class="text-14px color-[#6B7275]">{{ t('recharge.record.registerVoucher.orderInfo') }}</span>
            </div>
            <div class="text-14px color-[#222527] mb-4px">
              {{ t('recharge.record.rechargeSeqId') }}: {{ selectedRecord?.sysSeqId }}
            </div>
            <div class="text-14px color-[#222527]">
              {{ t('recharge.record.chargeAmount') }}: {{ formatAmount(selectedRecord?.transAmt || 0) }} {{ selectedRecord?.transCurrency }}
            </div>
          </div> -->

          <!-- 表单 -->
          <el-form ref="voucherFormRef" :model="voucherForm" :rules="voucherRules" label-position="top" hide-required-asterisk>
            <el-form-item 
              :label="t('recharge.record.registerVoucher.bankFlowNo')"
              prop="bankTransferSeqId" 
              class="custom-form-item mb-20px"
            >
              <el-input 
                v-model="voucherForm.bankTransferSeqId" 
                :placeholder="t('recharge.record.registerVoucher.bankFlowNoPlaceholder')" 
                class="custom-input"
                size="large"
                :clearable="true"
                :disabled="isSubmitting"
              />
            </el-form-item>

            <el-form-item 
              :label="t('recharge.record.registerVoucher.voucherUpload')"
              prop="vouchers" 
              class="custom-form-item mb-20px"
            >
              <div class="w-100%">
                <UploadFiles 
                  fileType="FRV" 
                  :limit="1" 
                  v-model:fileList="voucherForm.vouchers"
                  :accept="'image/png,image/jpeg,image/jpg,application/pdf'"
                  class="w-full"
                />
              </div>
            </el-form-item>

            <!-- <el-form-item 
              :label="t('recharge.record.registerVoucher.remark')"
              prop="remark" 
              class="custom-form-item"
            >
              <el-input 
                v-model="voucherForm.remark" 
                :placeholder="t('recharge.record.registerVoucher.remarkPlaceholder')" 
                type="textarea"
                :rows="3"
                class="custom-input"
                :disabled="isSubmitting"
                maxlength="200"
                show-word-limit
              />
            </el-form-item> -->
          </el-form>
        </div>

        <div 
          style="border-top: 1px solid #E5E6EB;"
          class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
          <el-button 
            class="cancel-btn w-68px h-32px text-14px text-[#222527] font-family-[PingFangSC-Regular] mr-12px btn-hover-scale-sm" 
            @click="handleRegisterCancel"
            :disabled="isSubmitting"
          >
            {{ t('recharge.record.registerVoucher.cancel') }}
          </el-button>
          <el-button 
            class="confirm-btn w-68px h-32px text-14px text-white font-family-[PingFangSC-Regular] btn-hover-scale-sm" 
            @click="submitVoucher"
            :loading="isSubmitting"
          >
            {{ t('recharge.record.registerVoucher.confirm') }}
          </el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 银行信息抽屉 -->
    <el-drawer 
      v-model="showBankInfoDrawer" 
      size="400px" 
      style="--el-drawer-padding-primary: 0px"
      header-class="custom-drawer-header"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
    >
      <template #header>
        <div class="font-family-[PingFangSC-Medium] font-500 text-18px color-[#222527] h-60px border-b-1px border-[#E5E6EB] mb-0 flex-row flex-items-center pt-18px pb-18px pl-24px">
          <span>{{ t('recharge.record.bankInfo.title') }}</span>
        </div>
      </template>
      <template #default>
        <div class="p-24px">
          <div v-loading="bankInfoLoading" class="min-h-200px">
            <div v-if="bankInfo" class="w-full">
              <!-- 银行信息表格 -->
              <div class="w-full overflow-hidden space-y-30px">
                <div class="flex flex-col items-start">
                  <span class="w-full text-14px font-400 text-[#464B4E] font-family-[PingFangSC-Regular]">{{ t('recharge.record.bankInfo.payeeName') }}</span>
                  <span class="w-full mt-12px break-all text-left font-400 text-14px text-[#222527] font-family-[PingFangSC-Regular]">{{ bankInfo.name }}</span>
                </div>
                <div class="flex flex-col items-start">
                  <span class="w-full text-14px font-400 text-[#464B4E] font-family-[PingFangSC-Regular]">{{ t('recharge.record.bankInfo.account') }}</span>
                  <span class="w-full mt-12px break-all text-left font-400 text-14px text-[#222527] font-family-[PingFangSC-Regular]">{{ bankInfo.account }}</span>
                </div>
                <div class="flex flex-col items-start">
                  <span class="w-full text-14px font-400 text-[#464B4E] font-family-[PingFangSC-Regular]">{{ t('recharge.record.bankInfo.bankName') }}</span>
                  <span class="w-full mt-12px break-all text-left font-400 text-14px text-[#222527] font-family-[PingFangSC-Regular]">{{ bankInfo.bankName }}</span>
                </div>
                <div class="flex flex-col items-start">
                  <span class="w-full text-14px font-400 text-[#464B4E] font-family-[PingFangSC-Regular]">{{ t('recharge.record.bankInfo.bankCountry') }}</span>
                  <span class="w-full mt-12px break-all text-left font-400 text-14px text-[#222527] font-family-[PingFangSC-Regular]">{{ bankInfo.bankCountry }}</span>
                </div>
                <div class="flex flex-col items-start">
                  <span class="w-full text-14px font-400 text-[#464B4E] font-family-[PingFangSC-Regular]">{{ t('recharge.record.bankInfo.swiftCode') }}</span>
                  <span class="w-full mt-12px break-all text-left font-400 text-14px text-[#222527] font-family-[PingFangSC-Regular]">{{ bankInfo.bankSwiftCode }}</span>
                </div>
                <div class="flex flex-col items-start">
                  <span class="w-full text-14px font-400 text-[#464B4E] font-family-[PingFangSC-Regular]">{{ t('recharge.record.bankInfo.remark') }}</span>
                  <span class="w-full mt-12px break-all text-left font-400 text-14px text-[#222527] font-family-[PingFangSC-Regular]">{{ bankInfo.remark }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- <div 
          style="border-top: 1px solid #E5E6EB;"
          class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
          <el-button 
            class="cancel-btn w-68px h-32px text-14px text-[#222527] font-family-[PingFangSC-Regular]" 
            @click="showBankInfoDrawer = false"
          >
            {{ t('recharge.record.bankInfo.close') }}
          </el-button>
        </div> -->
      </template>
    </el-drawer>
  </div>
</template>

<style lang="scss" scoped>
.date-picker-with-prefix {
  display: flex;
  align-items: center;
  width: 100%;
  height: 32px;
  background: #FFFFFF;
  border: 1px solid #E5E6EB;
  border-radius: 6px;
  padding-left: 12px;

  &:hover {
    border-color: #C9CDD4;
  }

  .el-date-editor {
    border: none;
    flex: 1;
    height: 30px;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none !important;
    height: 30px;
  }

  :deep(.el-range-input) {
    text-align: left;
    height: 30px;
    line-height: 30px;
  }

  :deep(.el-range__icon) {
    display: none;
  }
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-table) {
  --el-table-header-bg-color: #F8F9FA;
  --el-table-header-text-color: #6B7275;
  --el-table-header-text-font-size: 12px;
  --el-table-header-text-font-weight: 600;
  --el-table-header-text-font-family: PingFangSC-Semibold;
  --el-table-text-color: #222527;
  --el-table-text-font-size: 14px;
  --el-table-text-font-weight: 400;
  --el-table-text-font-family: PingFangSC-Regular;

  .el-table__header-wrapper{
    border-radius: 12px;
    overflow: hidden;
    tr {
      --el-table-border: none;
      .el-table__cell{
        padding:10px 0;
      }
    }
  } 

  tbody tr {
    .el-table__cell{
      padding:12px 0;
      color: #222527;
    }
  }
}

.status-dot {
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

// 抽屉样式
.custom-form-item {
  margin-bottom: 20px;
}

.custom-input {
  width: 100%;
  
  :deep(.el-input__wrapper) {
    border: 1px solid #E5E6EB;
    box-shadow: none;
    border-radius: 6px;
    height: 40px;
    padding: 0 12px;
    background-color: #ffffff;
    
    &:hover {
      border-color: #C0C4CC;
    }
    
    &.is-focus {
      border-color: #409EFF;
    }
  }
  
  :deep(.el-input__inner) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-size: 14px;
    color: #222527;
    
    &::placeholder {
      color: #A8ABB2;
    }
  }
  
  :deep(.el-textarea__inner) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-size: 14px;
    color: #222527;
    border: 1px solid #E5E6EB;
    border-radius: 6px;
    
    &::placeholder {
      color: #A8ABB2;
    }
    
    &:hover {
      border-color: #C0C4CC;
    }
    
    &:focus {
      border-color: #409EFF;
    }
  }
}

.cancel-btn {
  background-color: #FFFFFF !important;
  border: 1px solid #E5E6EB !important;
  color: #222527 !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
}

.cancel-btn:hover {
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
}

.confirm-btn {
  background-color: #030814 !important;
  border: 1px solid #030814 !important;
  color: #FFFFFF !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
}

.confirm-btn:hover {
  background-color: #1a1f2e !important;
  border-color: #1a1f2e !important;
}

.reset-btn:hover {
  background-color: #fff !important;
  border-color: #dcdfe6 !important;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }
  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
</style> 