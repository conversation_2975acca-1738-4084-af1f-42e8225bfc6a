export type CryptoRechargeAcctQueryRequestData = ApiRequestData<{
  /** 代币符号 */
  coinSymbol: string;
  /** 区块链网络 */
  network: string;
  /** 系统流水号 */
  sysSeqId: string;
}>;

export type CryptoRechargeAcctQueryResponseData = ApiResponseData<{
  /** 用户ID */
  userId: string;
  /** 充值地址 */
  address: string;
  /** 最小充值数量 */
  minRechargeAmt: string;
  /** 预计到账 */
  estimateAmt: string;
  /** 单日剩余额度 */
  dailyRemainedAmt: string;
  /** 单日总额度 */
  dailyTotalAmt: string;
  /** 单月剩余额度 */
  monthlyRemainedAmt: string;
  /** 单月总额度 */
  monthlyTotalAmt: string;
}>;

/** 法币充值账户信息查询请求参数 */
export type FiatRechargeQueryRequest = ApiRequestData<{
  /**
   * 请求流水号
   */
  sysSeqId?: string;
  /**
   * 交易币种：USD
   */
  transCurrency?: string;
}>;

export type FiatInfo = {
  depositInfoId: string;
  currency: string;
  name: string;
  account: string;
  bankName: string;
  bankCountry: string;
  bankAddr: string;
  payMethod: string;
  bankSwiftCode: string;
  payRemak: string;
  nickName: string;
  needPreOrderFlag: boolean;
  channelType: string;
};

/** 法币充值账户信息查询响应参数 */
export type FiatRechargeQueryResponse = ApiResponseData<{
  depositInfoDetails: FiatInfo[]; // 法币充值账户信息列表
  userId: string;
}>;

/** 法币充值下单请求参数 */
export type FiatRechargeOrderRequest = ApiRequestData<{
  /** 请求流水号 */
  sysSeqId: string;
  /** 交易币种：USD */
  transCurrency: string;
  /** 交易金额 */
  transAmt: number;
  /** 账户号 */
  account: string;
}>;

/** 法币充值下单响应参数 */
export type FiatRechargeOrderResponse = ApiResponseData<{
  id: number;
  sysSeqId: string;
  sysDate: string;
  merCustId: string;
  userCustId: string;
  userId: string;
  transType: string;
  transAmt: number;
  transCurrency: string;
  feeAmt: number;
  feeCurrency: string;
  feeFlag: string;
  calcMode: string;
  realAmt: number;
  realCurrency: string;
  transStat: string;
}>;

/** 充值记录查询请求参数 */
export type RechargeRecordQueryRequest = ApiRequestData<{
  /** 交易类型 */
  transType?: string;
  /** 交易币种 */
  transCurrency?: string;
  /** 交易状态 */
  transStat?: string;
  /** 交易时间-开始日期 */
  startTime?: string;
  /** 交易时间-结束日期 */
  endTime?: string;
  /** 页码 */
  pageNum: number;
  /** 每页条数 */
  pageSize: number;
  /** 充值流水 */
  sysSeqId?: string;
  /** 银行汇款流水号 */
  extension3?: string;
  /** 交易金额 */
  transAmt?: number;
}>;

export type DepositInfoDetail = {
  depositInfoId: string;
  currency: string;
  name: string;
  account: string;
  bankName: string;
  bankCountry: string;
  bankSwiftCode: string;
};

/** 充值记录列表项 */
export interface RechargeRecordItem {
  /** ID */
  id: number;
  /** 系统流水号 */
  sysSeqId: string;
  /** 系统日期 */
  sysDate: string;
  /** 商户编号 */
  merCustId: string;
  /** 用户编号 */
  userCustId: string;
  /** 用户ID */
  userId: string;
  /** 交易类型 */
  transType: string;
  /** 交易金额 */
  transAmt: number;
  /** 交易币种 */
  transCurrency: string;
  /** 手续费 */
  feeAmt: number;
  /** 手续费币种 */
  feeCurrency: string;
  /** 手续费标志,I-内扣，O-外扣 */
  feeFlag: string;
  /** 手续费公式 */
  calcMode: string;
  /** 实际金额 */
  realAmt: number;
  /** 实际币种 */
  realCurrency: string;
  /** 交易状态IPSF */
  transStat: string;
  /** 交易流水号 */
  traceId: string;
  /** 响应码 */
  respCode: string;
  /** 响应码描述 */
  respDesc: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 充值账户信息 */
  depositInfoDetail: DepositInfoDetail;
  /** 扩展字段1 */
  extension1?: string;
  /** 扩展字段2 */
  extension2?: string;
  /** 扩展字段3 - 银行汇款流水号 */
  extension3?: string;
  /** 凭证文件URL */
  fileUrl?: string;
}

/** 充值记录查询响应参数 */
export type RechargeRecordQueryResponse = ApiResponseData<{
  /** 总数 */
  total: number;
  /** 交易流水响应列表 */
  transLogResponseList: RechargeRecordItem[];
}>;

/** 充值确认请求参数 */
export type RechargeConfirmRequest = ApiRequestData<{
  /** 系统流水号 */
  sysSeqId: string;
  /** 银行流水号 */
  bankTransferSeqId: string;
  /** 凭证文件列表 */
  fileIds: any[];
}>;

/** 充值确认响应参数 */
export type RechargeConfirmResponse = ApiResponseData<{
  /** 是否成功 */
  success: boolean;
  /** 消息 */
  message?: string;
}>;

/** 充值关闭请求参数 */
export type RechargeCloseRequest = ApiRequestData<{
  /** 系统流水号 */
  sysSeqId: string;
}>;

/** 充值关闭响应参数 */
export type RechargeCloseResponse = ApiResponseData<{
  sysSeqId: boolean;
  transStat?: string;
}>;
