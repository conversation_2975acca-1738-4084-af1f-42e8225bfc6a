import type * as Recharge from './type';
import { request } from '@/http/axios';

/** 获取数币充值账户信息 */
export function queryCryptoRechargeAcctApi(data: Recharge.CryptoRechargeAcctQueryRequestData) {
  return request<Recharge.CryptoRechargeAcctQueryResponseData>(
    {
      url: '/crypto/recharge/acct/query',
      method: 'post',
      data,
    },
    {
      showLoading: false,
    }
  );
}

/** 获取法币充值账户信息 */
export function queryFiatRechargeAcctApi(data: Recharge.FiatRechargeQueryRequest) {
  return request<Recharge.FiatRechargeQueryResponse>(
    {
      url: '/fiat/recharge/acct/query',
      method: 'post',
      data,
    },
    {
      showLoading: false,
    }
  );
}

/** 法币充值下单 */
export function fiatRechargeOrderApi(data: Recharge.FiatRechargeOrderRequest) {
  return request<Recharge.FiatRechargeOrderResponse>({
    url: '/fiat/recharge/order',
    method: 'post',
    data,
  });
}

/** 查询充值记录 */
export function queryRechargeRecordApi(data: Recharge.RechargeRecordQueryRequest) {
  return request<Recharge.RechargeRecordQueryResponse>({
    url: '/fiat/recharge/list',
    method: 'post',
    data,
  });
}

/** 充值确认 */
export function rechargeConfirmApi(data: Recharge.RechargeConfirmRequest) {
  return request<Recharge.RechargeConfirmResponse>({
    url: '/fiat/recharge/confirm',
    method: 'post',
    data,
  });
}

/** 充值关闭 */
export function rechargeCloseApi(data: Recharge.RechargeCloseRequest) {
  return request<Recharge.RechargeCloseResponse>({
    url: '/fiat/recharge/close',
    method: 'post',
    data,
  });
}
