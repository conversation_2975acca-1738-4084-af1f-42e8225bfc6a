<template>
    <div class="p-0 flex flex-row w-full">
        <!-- Left Steps Section -->
        <div class="flex-1 flex-shrink-0">
            <div class="relative">
                <div class="flex items-start font-semibold text-18px text-#222527 mb-16px">
                    <div
                        class="flex items-center justify-center w-24px h-24px bg-#FFEBF0 rounded-6px mr-12px text-#ff0064 flex-shrink-0">
                        <svg-icon name="deposit/icon-crypto" font-size="16px" />
                    </div>
                    <span class="min-w-330px">{{ t('deposit.cryptoTab.selectCoin') }}</span>
                </div>
                <div class="ml-36px relative min-w-330px">
                    <el-select ref="selectCryptoRef" v-model="selectedCrypto"
                        :placeholder="t('deposit.cryptoTab.selectPlaceholder')"
                        class="crypto-select min-w-330px h-40px">
                        <template #prefix>
                            <img v-if="selectedCrypto" :src="getCryptoIcon(selectedCrypto)" :alt="selectedCrypto"
                                class="mr-8px w-20px h-20px" />
                        </template>
                        <el-option value="1" hidden></el-option>
                        <div @click="handleCryptoChange(item.enumCode)" v-for="item in cryptoOptions"
                            :key="item.enumCode" :style="{
                                width: '100%',
                                cursor: 'pointer',
                                color: item.enumCode == selectedCrypto ? '#ff0064' : '#222527',
                                fontSize: '14px',
                                padding: '16px',
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                fontWeight: item.enumCode == selectedCrypto ? '600' : '400',
                                background: item.enumCode == selectedCrypto ? '#F8F9FA' : 'white',
                            }">
                            <img :src="getCryptoIcon(item.enumCode)" :alt="selectedCrypto" width="20" height="20"
                                style="margin-right: 14px" />
                            <span
                                class="ml-8px font-family-[PingFangSC-Regular] font-400 text-16px text-[#222527] leading-20px">{{
                                    item.enumDescCn }}</span>

                        </div>
                    </el-select>
                </div>
                <!-- 第一条连接线 -->
                <div class="absolute w-1px left-12px z-1 top-[calc(100%-40px)] h-88px"
                    style="background: repeating-linear-gradient(to bottom, #E5E6EB 0px, #E5E6EB 4px, transparent 4px, transparent 8px);">
                </div>
            </div>

            <div class="flex items-start font-semibold text-18px text-#222527 mt-64px relative">
                <div
                    class="flex items-center justify-center w-24px h-24px bg-#FFEBF0 rounded-6px mr-12px text-#ff0064 flex-shrink-0">
                    <svg-icon name="deposit/icon-eth-net" font-size="16px" />
                </div>
                <span class="min-w-330px">{{ t('deposit.cryptoTab.selectNetwork') }}</span>
                <!-- 第二条连接线 -->
                <!-- <div class="absolute w-1px bg-gradient-to-b from-#E5E6EB via-#E5E6EB to-transparent bg-repeat-y left-12px z-1 top-40px h-80px"
                    style="background-size: 100% 8px;"></div> -->
            </div>
            <div class="ml-36px relative min-w-330px mt-16px">
                <el-select v-model="selectedNetwork" :placeholder="t('deposit.cryptoTab.selectPlaceholder')"
                    ref="selectNetworkRef" class="network-select min-w-330px h-40px" :disabled="!selectedCrypto"
                    @change="handleNetworkChange">
                    <template #prefix>
                        <img v-if="selectedNetwork" :src="getCryptoIcon(selectedNetwork)" :alt="selectedCrypto"
                            class="mr-8px w-20px h-20px" />
                    </template>
                    <el-option value="1" hidden></el-option>
                    <div @click="handleNetworkChange(item.enumCode)" v-for="item in networkOptions" :key="item.enumCode"
                        :style="{
                            width: '100%',
                            cursor: 'pointer',
                            color: item.enumCode == selectedNetwork ? '#ff0064' : '#222527',
                            fontSize: '14px',
                            padding: '16px',
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            fontWeight: item.enumCode == selectedNetwork ? '600' : '400',
                            background: item.enumCode == selectedNetwork ? '#F8F9FA' : 'white',
                        }">
                        <img :src="getCryptoIcon(item.enumCode)" :alt="selectedNetwork" width="20" height="20"
                            style="margin-right: 14px" />
                        <span
                            class="ml-8px font-family-[PingFangSC-Regular] font-400 text-16px text-[#222527] leading-20px">{{
                                item.enumDescCn }}</span>

                    </div>
                </el-select>
            </div>
        </div>

        <div class="flex-shrink-0 mx-24px w-1px h-532px bg-#E5E6EB"></div>

        <!-- Right Content Section -->
        <div class="flex-1 flex-shrink-0 flex flex-col mt-20px min-w-367px">
            <!-- Loading State -->
            <div v-if="isLoading" class="flex flex-col items-center justify-center mt-60px">
                <el-icon class="is-loading text-32px text-#ff0064 mb-16px">
                    <Loading />
                </el-icon>
            </div>
            <!-- Data Content -->
            <div v-else-if="selectedCrypto && selectedNetwork && rechargeInfo && rechargeInfo.address" class="w-full">
                <!-- Crypto Address Info -->

                <transition-group name="fade-slide" tag="div" class="w-full flex flex-col gap-24px flex-shrink-0">
                    <!-- Address Section -->
                    <div :key="`address-${selectedCrypto}-${selectedNetwork}-${rechargeInfo?.address || 'empty'}`"
                        class="flex justify-between items-start info-item-animated"
                        :style="{ '--animation-delay': '0ms' }">
                        <div
                            class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] flex-shrink-0 pt-2px">
                            {{ t('deposit.cryptoTab.depositAddress') }}
                        </div>
                        <div class="flex items-start ml-16px">
                            <span
                                class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527] break-all leading-20px text-right min-w-208px">
                                {{ rechargeInfo?.address }}
                            </span>
                            <el-button link class="ml-8px btn-hover-scale-sm flex-shrink-0 p-0"
                                @click="handleCopy(rechargeInfo?.address || '')">
                                <SvgIcon name="icon-copy-btn" class="text-16px color-[#222527]" />
                            </el-button>
                        </div>
                    </div>

                    <!-- QR Code Section -->
                    <div :key="`qr-${selectedCrypto}-${selectedNetwork}-${rechargeInfo?.address || 'empty'}`"
                        class="bg-[#F8F9FA] rounded-6px p-24px flex items-center flex items-start gap-24px info-item-animated"
                        :style="{ '--animation-delay': '100ms' }">
                        <!-- Left Text -->
                        <div
                            class="font-family-[PingFangSC-Regular] text-left flex-1  h-full font-400 text-14px text-[#6B7275]">
                            {{ t('deposit.cryptoTab.qrCodeHint') }}
                        </div>
                        <!-- QR Code -->
                        <div
                            class="flex items-center justify-center w-176px h-176px bg-white rounded-6px flex-shrink-0">
                            <qrcode-vue v-if="rechargeInfo?.address" :value="rechargeInfo?.address" :size="152"
                                level="H" />
                        </div>
                    </div>

                    <!-- Info Section -->
                    <div :key="`info-${selectedCrypto}-${selectedNetwork}-${rechargeInfo?.address || 'empty'}`"
                        class="flex justify-between items-center info-item-animated"
                        :style="{ '--animation-delay': '200ms' }">
                        <div class="flex-1">
                            <div class="flex justify-between items-center mb-24px">
                                <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275]">
                                    {{ t('deposit.cryptoTab.minDeposit') }}
                                </div>
                                <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">
                                    {{ moneyFormatInput(rechargeInfo?.minRechargeAmt) }} {{ selectedCrypto }}
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275]">
                                    {{ t('deposit.cryptoTab.estimatedArrival') }}
                                </div>
                                <div class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">
                                    {{ rechargeInfo?.estimateAmt }} {{ t('deposit.cryptoTab.networkConfirmations') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </transition-group>
            </div>

            <!-- Empty State -->
            <div v-else class="flex flex-col items-center justify-center mt-60px">
                <img src="@/common/assets/images/crypto-deposit-empty.webp" :alt="getEmptyStateText"
                    class="w-144px h-144px mb-8px" />
                <div class="text-14px text-#6B7275 font-family-[PingFangSC-Regular] font-400">
                    {{ getEmptyStateText }}
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { CopyDocument, Loading } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import QrcodeVue from 'qrcode.vue';
import { useI18n } from 'vue-i18n';
import { CryptoRechargeAcctQueryResponseData } from '../apis/type';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { BusinessEnumType } from '@/common/apis/common/type';
import { queryCryptoRechargeAcctApi } from '../apis';
import { generateSysSeqId } from '@/common/utils/generator';
import { moneyFormatInput } from '@/common/utils/format';
import { getCryptoIcon } from '@/common/utils/imageUtils';

const { t } = useI18n();
// For UI only, no logic implementation
const selectCryptoRef = ref<HTMLSelectElement>();
const selectNetworkRef = ref<HTMLSelectElement>();
const selectedCrypto = ref('');
const selectedNetwork = ref('');
const form = ref({
    coin: 'USDT'
});
const enumStore = useEnumStore();
const cryptoOptions = enumStore.getEnumList(BusinessEnumType.CURRENCY);
const networkOptions = enumStore.getEnumList(BusinessEnumType.CRYPTO_NET);

// Mock recharge info data - initially undefined
const rechargeInfo = ref<CryptoRechargeAcctQueryResponseData['data'] | null>(null);
// Loading state for better UX
const isLoading = ref(false);

// Computed property for empty state text
const getEmptyStateText = computed(() => {
    if (!selectedCrypto.value || !selectedNetwork.value) {
        return t('deposit.cryptoTab.selectCoinAndNetwork');
    }
    if (rechargeInfo.value && !rechargeInfo.value.address) {
        return t('deposit.cryptoTab.emptyTip');
    }
    return t('deposit.cryptoTab.selectCoinAndNetwork');
});

// Handle crypto selection
const handleCryptoChange = (value: string) => {
    form.value.coin = value;
    selectCryptoRef.value?.blur();
    selectedCrypto.value = value;
    selectedNetwork.value = '';
    // Clear previous data and reset loading state
    rechargeInfo.value = null;
    isLoading.value = false;
    // Update mock data based on selected crypto
    if (value && selectedNetwork.value) {
        updateRechargeInfo(value, selectedNetwork.value);
    }
};

// Handle network selection
const handleNetworkChange = (value: string) => {
    selectedNetwork.value = value;
    selectNetworkRef.value?.blur();
    if (selectedCrypto.value && value) {
        // Clear previous data and set loading state
        rechargeInfo.value = null;
        isLoading.value = true;
        updateRechargeInfo(selectedCrypto.value, value);
    }
};

// Update recharge info based on selections
const updateRechargeInfo = async (crypto: string, network: string) => {
    try {
        isLoading.value = true;
        const res = await queryCryptoRechargeAcctApi({
            sysSeqId: generateSysSeqId(),
            coinSymbol: crypto,
            network: network,
        });
        rechargeInfo.value = res.data;
    } catch (error) {
        console.error(error);
        // Clear data on error to show empty state
        rechargeInfo.value = null;
    } finally {
        isLoading.value = false;
    }
};



// Copy function
const handleCopy = async (text: string) => {
    let success = false;
    if (navigator.clipboard && window.isSecureContext) {
        try {
            await navigator.clipboard.writeText(text);
            success = true;
        } catch (e) {
            console.error('copy error', e);
            success = false;
        }
    } else {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        textarea.style.top = '-9999px';
        textarea.style.left = '-9999px';
        document.body.appendChild(textarea);
        textarea.focus();
        textarea.select();
        try {
            success = document.execCommand('copy');
        } catch (e) {
            console.error('copy error', e);
            success = false;
        } finally {
            document.body.removeChild(textarea);
        }
    }
    if (success) {
        ElMessage.success(t('deposit.copySuccess'));
    } else {
        ElMessage.error(t('deposit.copyFailed'));
    }
};
</script>

<script lang="ts">
export default {
    name: 'CryptoDeposit'
};
</script>

<style lang="scss" scoped>
:deep(.crypto-select .network-select) {
    .el-select__wrapper {
        height: 40px !important;
        min-height: 40px;
        border: 1px solid #E5E6EB;
        border-radius: 8px;
        padding: 17px 16px;
        font-size: 16px;
        background-color: #ffffff;
        box-shadow: none;

        .el-select__inner {
            font-family: 'PingFangSC-Regular';
            font-size: 16px;
            color: #222527;

            &::placeholder {
                color: #C9CDD4;
            }
        }

        .el-select__suffix {
            .el-select__suffix-inner {
                color: #6b7275;
            }
        }
    }

    &:hover .el-input__wrapper {
        border-color: #ff0064;
    }
}

.btn-hover-scale-sm {
    transition: transform 0.2s ease;

    &:hover {
        transform: scale(1.1);
    }
}

/* 信息栏动画效果 */
.info-item-animated {
    animation: fadeSlideIn 400ms ease-out forwards;
    animation-delay: var(--animation-delay, 0ms);
    opacity: 0;
    transform: translateY(20px);
}

/* 渐入滑动动画效果 */
@keyframes fadeSlideIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>