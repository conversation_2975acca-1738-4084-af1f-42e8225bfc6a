<template>
    <div class="p-0 flex flex-row w-786px pb-32px">
        <!-- Left Steps Section -->
        <div>
            <div class="relative">
                <div class="flex items-start font-semibold text-18px text-#222527 mb-16px">
                    <div
                        class="flex items-center justify-center w-24px h-24px bg-#FFEBF0 rounded-6px mr-12px text-#ff0064 flex-shrink-0">
                        <svg-icon name="deposit/icon-crypto" font-size="16px" />
                    </div>
                    <span class="w-330px">{{ t('deposit.cryptoTab.selectCoin') }}</span>
                </div>
                <div class="ml-36px relative w-330px">
                    <el-select v-model="selectedCrypto" :placeholder="t('deposit.cryptoTab.selectPlaceholder')"
                        class="crypto-select w-330px h-40px" @change="handleCryptoChange">
                        <template #prefix>
                            <SvgIcon name="icon-usdt" class="mr-8px color-[#1BA27A]! text-20px!" />
                        </template>
                        <el-option v-for="item in cryptoOptions" :key="item.enumCode" :label="item.enumDescCn"
                            :value="item.enumCode">
                            <div class="flex items-center">
                                <span class="font-family-[PingFangSC-Regular] font-400 text-16px text-[#222527]">{{
                                    item.enumDescCn }}</span>
                            </div>
                        </el-option>
                    </el-select>
                </div>
                <!-- 第一条连接线 -->
                <div class="absolute w-1px left-12px z-1 top-[calc(100%-40px)] h-88px"
                    style="background: repeating-linear-gradient(to bottom, #E5E6EB 0px, #E5E6EB 4px, transparent 4px, transparent 8px);">
                </div>
            </div>

            <div class="flex items-start font-semibold text-18px text-#222527 mt-64px relative">
                <div
                    class="flex items-center justify-center w-24px h-24px bg-#FFEBF0 rounded-6px mr-12px text-#ff0064 flex-shrink-0">
                    <svg-icon name="deposit/icon-eth-net" font-size="16px" />
                </div>
                <span class="w-330px">{{ t('deposit.cryptoTab.selectNetwork') }}</span>
                <!-- 第二条连接线 -->
                <!-- <div class="absolute w-1px bg-gradient-to-b from-#E5E6EB via-#E5E6EB to-transparent bg-repeat-y left-12px z-1 top-40px h-80px"
                    style="background-size: 100% 8px;"></div> -->
            </div>
            <div class="ml-36px relative w-330px mt-16px">
                <el-select v-model="selectedNetwork" :placeholder="t('deposit.cryptoTab.selectPlaceholder')"
                    class="network-select w-330px h-40px" :disabled="!selectedCrypto" @change="handleNetworkChange">
                    <el-option v-for="network in networkOptions" :key="network.enumCode" :label="network.enumDescCn"
                        :value="network.enumCode">
                        <div class="flex items-center">
                            <span class="font-family-[PingFangSC-Regular] font-400 text-16px text-[#222527]">{{
                                network.enumDescCn }}</span>
                        </div>
                    </el-option>
                </el-select>
            </div>
        </div>

        <div class="mx-24px w-1px h-532px bg-#E5E6EB"></div>

        <!-- Right Content Section -->
        <div class="flex-shrink-0 flex flex-col mt-20px w-367px">
            <div v-if="selectedCrypto && selectedNetwork && rechargeInfo && rechargeInfo.address" class="w-full">
                <!-- Crypto Address Info -->
                <div class="w-full">
                    <div class="flex flex-col gap-24px">
                        <!-- Address Section -->
                        <div class="flex justify-between items-start">
                            <div
                                class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] flex-shrink-0 pt-2px">
                                {{ t('deposit.cryptoTab.depositAddress') }}
                            </div>
                            <div class="flex items-start ml-16px">
                                <span
                                    class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527] break-all leading-20px text-right max-w-208px">
                                    {{ rechargeInfo?.address }}
                                </span>
                                <el-button link class="ml-8px btn-hover-scale-sm flex-shrink-0 p-0"
                                    @click="handleCopy(rechargeInfo?.address || '')">
                                    <SvgIcon name="icon-copy-btn" class="text-16px color-[#222527]" />
                                </el-button>
                            </div>
                        </div>

                        <!-- QR Code and Info Section -->
                        <div class="flex gap-24px bg-[#F8F9FA] rounded-6px p-24px">
                            <!-- QR Code -->
                            <div
                                class="flex items-center justify-center w-176px h-176px bg-white rounded-6px flex-shrink-0">
                                <qrcode-vue v-if="rechargeInfo?.address" :value="rechargeInfo?.address" :size="152"
                                    level="H" />
                            </div>

                            <!-- Info Section -->
                            <div class="flex flex-col justify-center flex-1 min-w-0">
                                <div class="mb-30px">
                                    <div
                                        class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#464B4E] mb-12px">
                                        {{ t('deposit.cryptoTab.minDeposit') }}
                                    </div>
                                    <div
                                        class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527] font-medium">
                                        {{ moneyFormatInput(rechargeInfo?.minRechargeAmt) }} {{ form.coin }}
                                    </div>
                                </div>
                                <div>
                                    <div
                                        class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#464B4E] mb-12px">
                                        {{ t('deposit.cryptoTab.estimatedArrival') }}
                                    </div>
                                    <div
                                        class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527] font-medium">
                                        {{ rechargeInfo?.estimateAmt }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Limits Section -->
                        <!-- <div class="flex flex-col gap-16px">
                            <div class="flex justify-between items-center">
                                <div
                                    class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] min-w-109px">
                                    {{ t('deposit.cryptoTab.dailyLimit') }}/<br />
                                    {{ t('deposit.cryptoTab.allLimit') }}
                                </div>
                                <div
                                    class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527] text-left max-w-150px">
                                    {{ moneyFormatInput(rechargeInfo?.dailyRemainedAmt) }} /<br /> {{
                                        moneyFormatInput(rechargeInfo?.dailyTotalAmt) }} {{
                                        form.coin
                                    }}
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <div
                                    class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#6B7275] min-w-109px">
                                    {{ t('deposit.cryptoTab.monthlyLimit') }}/<br />
                                    {{ t('deposit.cryptoTab.allLimit') }}
                                </div>
                                <div
                                    class="font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527] text-left max-w-150px">
                                    {{ moneyFormatInput(rechargeInfo?.monthlyRemainedAmt) }} /<br /> {{
                                        moneyFormatInput(rechargeInfo?.monthlyTotalAmt) }}
                                    {{ form.coin }}
                                </div>
                            </div>
                        </div> -->
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div v-else class="flex flex-col items-center justify-center mt-60px">
                <img src="@/common/assets/images/crypto-deposit-empty.webp" alt="暂无数据" class="w-144px h-144px mb-8px" />
                <div class="text-14px text-#6B7275 font-family-[PingFangSC-Regular] font-400">
                    {{ rechargeInfo && !rechargeInfo.address ?
                        t('deposit.cryptoTab.emptyTip') : t('deposit.cryptoTab.selectCoinAndNetwork')
                    }}
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { CopyDocument } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import QrcodeVue from 'qrcode.vue';
import { useI18n } from 'vue-i18n';
import { CryptoRechargeAcctQueryResponseData } from '../apis/type';
import { useEnumStore } from '@/pinia/stores/enumStore';
import { BusinessEnumType } from '@/common/apis/common/type';
import { queryCryptoRechargeAcctApi } from '../apis';
import { generateSysSeqId } from '@/common/utils/generator';
import { moneyFormatInput } from '@/common/utils/format';

const { t } = useI18n();
// For UI only, no logic implementation
const selectedCrypto = ref('');
const selectedNetwork = ref('');
const form = ref({
    coin: 'USDT'
});
const enumStore = useEnumStore();
const cryptoOptions = enumStore.getEnumList(BusinessEnumType.CURRENCY);
const networkOptions = enumStore.getEnumList(BusinessEnumType.CRYPTO_NET);

// Mock recharge info data - initially undefined
const rechargeInfo = ref<CryptoRechargeAcctQueryResponseData['data'] | null>(null);

// Handle crypto selection
const handleCryptoChange = (value: string) => {
    selectedNetwork.value = '';
    form.value.coin = value;

    // Update mock data based on selected crypto
    if (value && selectedNetwork.value) {
        updateRechargeInfo(value, selectedNetwork.value);
    }
};

// Handle network selection
const handleNetworkChange = (value: string) => {
    if (selectedCrypto.value && value) {
        updateRechargeInfo(selectedCrypto.value, value);
    }
};

// Update recharge info based on selections
const updateRechargeInfo = async (crypto: string, network: string) => {
    try {
        const res = await queryCryptoRechargeAcctApi({
            sysSeqId: generateSysSeqId(),
            coinSymbol: crypto,
            network: network,
        });
        rechargeInfo.value = res.data;
    } catch (error) {
        console.error(error);
    }
};

// Copy function
const handleCopy = async (text: string) => {
    let success = false;
    if (navigator.clipboard && window.isSecureContext) {
        try {
            await navigator.clipboard.writeText(text);
            success = true;
        } catch (e) {
            console.error('copy error', e);
            success = false;
        }
    } else {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        textarea.style.top = '-9999px';
        textarea.style.left = '-9999px';
        document.body.appendChild(textarea);
        textarea.focus();
        textarea.select();
        try {
            success = document.execCommand('copy');
        } catch (e) {
            console.error('copy error', e);
            success = false;
        } finally {
            document.body.removeChild(textarea);
        }
    }
    if (success) {
        ElMessage.success(t('deposit.copySuccess'));
    } else {
        ElMessage.error(t('deposit.copyFailed'));
    }
};
</script>

<script lang="ts">
export default {
    name: 'CryptoDeposit'
};
</script>

<style lang="scss" scoped>
:deep(.crypto-select) {
    .el-select__wrapper {
        height: 40px !important;
        min-height: 40px;
        border: 1px solid #E5E6EB;
        border-radius: 8px;
        padding: 17px 16px;
        font-size: 16px;
        background-color: #ffffff;
        box-shadow: none;

        .el-select__inner {
            font-family: 'PingFangSC-Regular';
            font-size: 16px;
            color: #222527;

            &::placeholder {
                color: #C9CDD4;
            }
        }

        .el-select__suffix {
            .el-select__suffix-inner {
                color: #6b7275;
            }
        }
    }

    &:hover .el-input__wrapper {
        border-color: #ff0064;
    }
}

:deep(.network-select) {
    .el-select__wrapper {
        height: 40px !important;
        min-height: 40px;
        border: 1px solid #E5E6EB;
        border-radius: 8px;
        padding: 17px 16px;
        background-color: #ffffff;
        box-shadow: none;

        .el-select__inner {
            font-family: 'PingFangSC-Regular';
            font-size: 16px;
            color: #222527;

            &::placeholder {
                color: #C9CDD4;
            }
        }

        .el-select__suffix {
            .el-select__suffix-inner {
                color: #6b7275;
            }
        }

        &.is-disabled {
            background-color: #f5f5f5;
            border-color: #e4e7ed;

            .el-select__inner {
                color: #c0c4cc;
            }
        }
    }

    &:hover:not(.is-disabled) .el-input__wrapper {
        border-color: #ff0064;
    }
}

.btn-hover-scale-sm {
    transition: transform 0.2s ease;

    &:hover {
        transform: scale(1.1);
    }
}
</style>