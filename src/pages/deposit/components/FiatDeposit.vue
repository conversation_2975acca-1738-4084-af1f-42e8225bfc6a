<template>
    <div class="p-0 flex flex-row w-786px">
        <!-- Account Selection -->
        <div>
            <div class="relative">
                <div class="flex items-start font-semibold text-18px text-#222527 mb-16px">
                    <div
                        class="flex items-center justify-center w-24px h-24px bg-#FFEBF0 rounded-6px mr-12px text-#ff0064 flex-shrink-0">
                        <svg-icon name="deposit/icon-bank-illustration" font-size="16px" />
                    </div>
                    <span class="w-330px">{{ t('deposit.fiatTab.selectAccount') }}</span>
                </div>
                <div class="ml-36px relative w-330px">
                    <el-select v-model="fiatAccountInfo" ref="selectFiatRef"
                        :placeholder="t('deposit.fiatTab.selectAccountPlaceholder')"
                        class="account-select w-330px h-60px">
                        <template #prefix>
                            <svg-icon v-if="!fiatAccountInfo" name="deposit/icon-deposit-account" font-size="26px" />
                        </template>
                        <template #label>
                            <div v-if="fiatAccountInfo" class="flex items-start flex-col">
                                <span class="font-family-[PingFangSC-Regular] font-600 text-16px text-[#222527]">{{
                                    fiatAccountInfo.name }}</span>
                                <span
                                    class="font-family-[PingFangSC-Regular] font-400 text-16px text-[#222527] mt-3px">{{
                                        fiatAccountInfo?.bankCountry }}-{{ fiatAccountInfo?.currency }}-{{
                                        fiatAccountInfo?.payMethod }}-{{ fiatAccountInfo?.account }}</span>

                            </div>
                        </template>
                        <el-option value="1" hidden></el-option>
                        <div class="w-452px">
                            <div @click="selectLtWalletAddr(item)" v-for="item in fiatAccountList" :key="item.account"
                                :style="{
                                    width: '100%',
                                    cursor: 'pointer',
                                    color: '#222527',
                                    fontSize: '16px',
                                    paddingLeft: '16px',
                                    paddingRight: '16px',
                                    paddingTop: '8px',
                                    paddingBottom: '8px',
                                    background: item.account == fiatAccountInfo?.account ? '#F8F9FA' : 'white',
                                }">
                                <div style="font-weight: 600;">{{ item.name }}</div>
                                <div style="margin-top: 3px;">
                                    {{ item?.bankCountry }}-{{ item?.currency }}-{{ item?.payMethod }}-{{ item?.account
                                    }}
                                </div>
                            </div>
                        </div>
                    </el-select>
                </div>
                <!-- 第一条连接线 -->
                <div class="absolute w-1px left-12px z-1 top-[calc(100%-60px)] h-108px"
                    style="background: repeating-linear-gradient(to bottom, #E5E6EB 0px, #E5E6EB 4px, transparent 4px, transparent 8px);">
                </div>
            </div>
            <div v-if="!fiatAccountInfo">
                <div class="flex items-start font-semibold text-18px text-#222527 mt-64px relative">
                    <div
                        class="flex items-center justify-center w-24px h-24px bg-#FFEBF0 rounded-6px mr-12px text-#ff0064 flex-shrink-0">
                        <svg-icon name="icon-bank" font-size="16px" />
                    </div>
                    <span class="w-330px line-height-20px">{{ t('deposit.fiatTab.accountInfoHint') }}</span>
                    <!-- 第二条连接线 -->
                    <div :class="connectionLineClass"
                        style="background: repeating-linear-gradient(to bottom, #E5E6EB 0px, #E5E6EB 4px, transparent 4px, transparent 8px);">
                    </div>
                </div>
                <div class="flex items-start font-semibold text-18px text-#222527 mt-64px">
                    <div
                        class="flex items-center justify-center w-24px h-24px bg-#FFEBF0 rounded-6px mr-12px text-#ff0064 flex-shrink-0">
                        <svg-icon name="deposit/icon-fiat-voucher" font-size="16px" />
                    </div>
                    <span class="w-330px line-height-20px">{{ t('deposit.fiatTab.voucherHint') }}</span>
                </div>
            </div>
            <div v-else>
                <div>
                    <div class="flex items-start font-semibold text-18px text-#222527 mt-64px relative">
                        <div class="flex items-center justify-center w-24px h-24px bg-#FFEBF0 rounded-6px mr-12px text-#ff0064 flex-shrink-0"
                            ref="bankIconRef">
                            <svg-icon name="icon-bank" font-size="16px" />
                        </div>
                        <span class="w-330px">{{ t('deposit.fiatTab.transferHint') }}</span>

                        <!-- 第二条连接线 -->
                        <div v-if="isPreOrder" class="absolute w-1px left-12px z-1 top-40px"
                            :style="secondConnectionLineStyle">
                        </div>
                    </div>
                    <div class="ml-36px mt-16px bg-#F8F9FA rounded-6px p-16px flex flex-col">
                        <span class="text-14px text-#6B7275 font-family-[PingFangSC-Semibold] font-600">{{
                            t('deposit.fiatTab.aboutVA') }}</span>
                        <span
                            class="text-14px text-#6B7275 font-family-[PingFangSC-Regular] font-400 mt-8px line-height-20px">{{
                                t('deposit.fiatTab.vaDescription') }}</span>
                    </div>
                </div>
                <div v-if="isPreOrder" class="flex items-start font-semibold text-18px text-#222527 mt-20px">
                    <div class="flex items-center justify-center w-24px h-24px bg-#FFEBF0 rounded-6px mr-12px text-#ff0064 flex-shrink-0"
                        ref="fiatPayIconRef">
                        <svg-icon name="deposit/icon-fiat-pay" font-size="16px" />
                    </div>
                    <span class="w-330px">{{ t('deposit.fiatTab.enterAmount') }}</span>
                </div>
                <div v-if="isPreOrder" class="ml-36px w-330px flex flex-col items-end">
                    <el-input v-model="depositAmount"
                        :placeholder="`Min ${(formatNumber(props.feeQuota?.recmRechargeQuota || 5000))}`" type="text"
                        @input="handleAmountInput"
                        class="text-16px h-40px mt-16px border-1px border-#E5E6EB rounded-6px">
                        <template #suffix>
                            <div class="flex items-center">
                                <el-image
                                    :src="`https://files.dingx.tech/icons/currency/${fiatAccountInfo?.currency}.png`"
                                    class="w-20px h-20px mr-8px" />
                                <span class="text-16px text-#222527 font-family-[PingFangSC-Regular] font-400">{{
                                    fiatAccountInfo?.currency }}</span>
                            </div>
                        </template>
                    </el-input>
                    <el-button :disabled="isSubmitDisabled"
                        :class="{ 'bg-#ff0064 text-#ffffff': !isSubmitDisabled, 'bg-#D2D2D2 text-#ffffff': isSubmitDisabled }"
                        class="w-108px h-32px rounded-6px mt-48px" @click="handleRecharge">{{
                            t('deposit.fiatTab.rechargeBtn')
                        }}</el-button>
                </div>
            </div>
        </div>
        <div class="mx-24px w-1px h-532px bg-#E5E6EB"></div>
        <!-- Right Bank Card -->
        <div class="flex-shrink-0 flex flex-col items-center mt-20px w-367px">
            <div v-if="fiatAccountInfo" class="mt-24px">
                <div v-for="item in fiatAccountInfoItems" :key="item.label"
                    class="flex justify-between items-center mb-24px text-14px font-family-[PingFangSC-Regular] font-400 text-[#6B7275]">
                    <span class="min-w-42px max-w-84px text-#6B7275">{{ item.label }}</span>
                    <div class="flex items-center font-family-[PingFangSC-Regular] font-400 text-14px text-[#222527]">
                        <span class="w-208px text-right">{{ item.value || '-' }}</span>
                        <el-button link class="ml-8px btn-hover-scale-sm" @click="handleCopy(item.value)">
                            <SvgIcon name="icon-copy-btn" class="text-16px color-#222527" />
                        </el-button>
                    </div>
                </div>
                <div @click="copyAllInfo(fiatAccountInfoItems)"
                    class="font-family-[PingFangSC-Regular] font-400 text-14px color-[#FF0064]! text-right cursor-pointer">
                    {{ t('deposit.copyAllInfo') }}</div>
            </div>
            <!-- 空状态提示 -->
            <div v-else class="flex flex-col items-center justify-center mt-60px">
                <img src="@/common/assets/images/fiat-deposit-empty.webp" alt="暂无数据" class="w-144px h-144px mb-8px" />
                <div class="text-14px text-#6B7275 font-family-[PingFangSC-Regular] font-400">{{
                    t('deposit.fiatTab.selectDepositAccount') }}</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { CopyDocument } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { queryFiatRechargeAcctApi, fiatRechargeOrderApi } from '../apis';
import { generateSysSeqId } from '@/common/utils/generator';
import { FiatInfo } from '../apis/type';
import { useRouter } from 'vue-router';
import DialogService from '@/common/components/Dialog/DialogService';
import { copyText } from '@/common/utils/clipboard';
import { FeeQuotaResponseData } from '@/common/apis/common/type';
import { formatNumber } from '@/common/utils/math';

const router = useRouter();
const { t, locale } = useI18n();

// 图标元素的引用
const bankIconRef = ref<HTMLElement>();
const fiatPayIconRef = ref<HTMLElement>();
const dynamicLineHeight = ref(140); // 默认高度

/**
 * @description: 处理金额输入，限制为数字且最多两位小数
 * @param {string} value
 */
const handleAmountInput = (value: string) => {
    // 移除非数字和非小数点的字符
    let newValue = value.replace(/[^0-9.]/g, '');
    // 只保留第一个小数点
    const dotIndex = newValue.indexOf('.');
    if (dotIndex !== -1) {
        newValue = newValue.slice(0, dotIndex + 1) + newValue.slice(dotIndex + 1).replace(/\./g, '');
    }
    // 限制小数部分只能有两位
    newValue = newValue.replace(/(\.\d{2})\d*/, '$1');
    depositAmount.value = newValue;
};

/**
 * 计算两个图标之间的距离
 */
const calculateIconDistance = () => {
    try {
        // 检查元素是否存在
        if (!bankIconRef.value || !fiatPayIconRef.value) {
            console.warn('图标元素未找到:', {
                bankIcon: !!bankIconRef.value,
                fiatPayIcon: !!fiatPayIconRef.value
            });
            return;
        }

        // 检查元素是否有 getBoundingClientRect 方法
        if (typeof bankIconRef.value.getBoundingClientRect !== 'function' ||
            typeof fiatPayIconRef.value.getBoundingClientRect !== 'function') {
            console.error('元素不是有效的 DOM 元素');
            return;
        }

        const bankRect = bankIconRef.value.getBoundingClientRect();
        const fiatPayRect = fiatPayIconRef.value.getBoundingClientRect();

        // 计算两个图标中心点之间的垂直距离
        const bankCenter = bankRect.top + bankRect.height / 2;
        const fiatPayCenter = fiatPayRect.top + fiatPayRect.height / 2;
        const distance = Math.abs(fiatPayCenter - bankCenter);

        // 更新动态连接线高度，减去一些偏移量以确保连接线不会超出图标
        const newHeight = Math.max(distance - 50, 100); // 最小高度100px
        dynamicLineHeight.value = newHeight;

        console.log('图标距离计算结果:', {
            bankRect: { top: bankRect.top, height: bankRect.height },
            fiatPayRect: { top: fiatPayRect.top, height: fiatPayRect.height },
            distance,
            newHeight
        });
    } catch (error) {
        console.error('计算图标距离时发生错误:', error);
    }
};

/**
 * 检查当前语言是否为中文
 * @returns {boolean} 是否为中文语言
 */
const isChinese = computed(() => {
    const currentLocale = locale.value;
    return currentLocale === 'zh-CN' || currentLocale === 'zh-HK';
});

/**
 * 动态计算连接线的样式
 * @returns {string} 连接线的CSS类名
 */
const connectionLineClass = computed(() => {
    const baseClass = "absolute w-1px left-12px z-1";
    const baseStyle = "background: repeating-linear-gradient(to bottom, #E5E6EB 0px, #E5E6EB 4px, transparent 4px, transparent 8px);";

    if (isChinese.value) {
        // 中文状态下的样式
        return `${baseClass} top-[calc(100%-40px)] h-90px`;
    } else {
        // 英文状态下的样式
        return `${baseClass} top-[calc(100%-55px)] h-110px`;
    }
});

/**
 * 动态计算第二个连接线的样式（根据图标间距离）
 * @returns {object} 连接线的内联样式对象
 */
const secondConnectionLineStyle = computed(() => {
    return {
        background: 'repeating-linear-gradient(to bottom, #E5E6EB 0px, #E5E6EB 4px, transparent 4px, transparent 8px)',
        height: `${dynamicLineHeight.value}px`
    };
});

// Props定义
interface Props {
    feeQuota?: FeeQuotaResponseData["data"] | null;
}

const props = withDefaults(defineProps<Props>(), {
    feeQuota: null,
});

const fiatAccountInfo = ref<FiatInfo>();
const fiatAccountList = ref<FiatInfo[]>([]);
const isPreOrder = ref(false);
const depositAmount = ref('');
const orderNo = ref('');
const selectFiatRef = ref<HTMLSelectElement>();

/**
 * @description: 计算提交按钮是否禁用状态
 * 当输入金额小于推荐充值限额时禁用
 */
const isSubmitDisabled = computed(() => {
    const minAmount = props.feeQuota?.recmRechargeQuota || 5000;
    return Number(depositAmount.value) < minAmount;
});

const fiatAccountInfoItems = computed(() => [
    { label: t('deposit.fiatTab.accountName'), value: fiatAccountInfo.value?.name || '' },
    { label: t('deposit.fiatTab.paymentMethod'), value: fiatAccountInfo.value?.payMethod || '' },
    { label: t('deposit.fiatTab.accountNumber'), value: fiatAccountInfo.value?.account || '' },
    { label: t('deposit.fiatTab.swiftBic'), value: fiatAccountInfo.value?.bankSwiftCode || '' },
    { label: t('deposit.fiatTab.bankName'), value: fiatAccountInfo.value?.bankName || '' },
    { label: t('deposit.fiatTab.bankAddress'), value: fiatAccountInfo.value?.bankAddr || '' },
    { label: t('deposit.fiatTab.bankCountry'), value: fiatAccountInfo.value?.bankCountry || '' },
    { label: t('deposit.fiatTab.paymentRemark'), value: fiatAccountInfo.value?.payRemak || '' },
]);

async function fetchFiatAccountInfo() {
    try {
        const res = await queryFiatRechargeAcctApi({
            sysSeqId: generateSysSeqId(),
            transCurrency: '',
        });
        fiatAccountList.value = res.data.depositInfoDetails;
    } catch (error) {
        console.error(error);
    }
}

onMounted(() => {
    fetchFiatAccountInfo();
    // 初始计算图标间距离
    nextTick(() => {
        calculateIconDistance();
    });
});

// 监听 isPreOrder 变化，重新计算图标间距离
watch(isPreOrder, (newValue) => {
    if (newValue) {
        nextTick(() => {
            calculateIconDistance();
        });
    }
});

// 监听语言变化，重新计算图标间距离
watch(locale, () => {
    nextTick(() => {
        calculateIconDistance();
    });
});

const selectLtWalletAddr = (item: FiatInfo) => {
    fiatAccountInfo.value = item;
    selectFiatRef.value?.blur()
    isPreOrder.value = fiatAccountInfo.value?.needPreOrderFlag || false;

    // 重新计算图标间距离
    nextTick(() => {
        calculateIconDistance();
    });
}

const copyAllInfo = async (items: { label: string; value: string }[]) => {
    const text = items.map(item => `${item.label}: ${item.value}`).join('\n');
    handleCopy(text);
}

// Copy function
const handleCopy = async (text: string) => {
    copyText(text)
};

/**
 * @description: 处理充值提交
 * 验证账户信息、金额输入和最小充值限额
 */
async function handleRecharge() {
    if (!fiatAccountInfo.value || !depositAmount.value) {
        ElMessage.warning(t('deposit.fiatTab.inputAmountHint'));
        return;
    }

    // 验证充值金额是否满足最小限额要求
    const minAmount = props.feeQuota?.recmRechargeQuota || 5000;
    const inputAmount = Number(depositAmount.value);
    if (inputAmount < minAmount) {
        ElMessage.warning(t('deposit.fiatTab.minAmountHint', { amount: minAmount.toLocaleString() }));
        return;
    }

    try {
        const params = {
            sysSeqId: generateSysSeqId(),
            transCurrency: fiatAccountInfo.value.currency,
            transAmt: Number(depositAmount.value),
            account: fiatAccountInfo.value.account,
        };
        const res = await fiatRechargeOrderApi(params);
        if (res.data) {
            orderNo.value = res.data.sysSeqId;
            // 清空表单
            depositAmount.value = '';
        }
        DialogService.show({
            title: t('deposit.fiatTab.submitSuccessTitle'),
            message: t('deposit.fiatTab.submitSuccessMsg', { orderNo: orderNo.value }),
            iconClass: 'success',
            confirmButtonText: t('deposit.fiatTab.viewRecord'),
            cancelButtonText: t('deposit.fiatTab.cancel'),
            onConfirm: goToRechargeRecord,
        });
    } catch (error) {
        console.error('recharge error', error);
    }
}
/** 跳转到充值记录页面 */
function goToRechargeRecord() {
    router.push('/deposit/record');
}
</script>

<style lang="scss" scoped>
.fiat-deposit-container {
    padding: 24px;
    background-color: #ffffff;
    border-radius: 16px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.deposit-title {
    font-size: 32px;
    font-weight: 600;
    color: #222527;
    margin-bottom: 32px;
    font-family: 'PingFangSC-Semibold';
}

.deposit-content {
    display: flex;
    width: 100%;
}

.steps-section {
    flex: 1;
    max-width: 380px;
}

.step-item {
    position: relative;
    margin-bottom: 64px;
}

.step-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
}

.step-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: #FFEBF0;
    border-radius: 6px;
    margin-right: 12px;
    color: #ff0064;
    flex-shrink: 0;
}

.step-title {
    font-size: 18px;
    font-weight: 600;
    color: #222527;
    max-width: 330px;
}

.step-content {
    margin-left: 36px;
    position: relative;
    width: 330px;
}

.step-connector {
    position: absolute;
    width: 1px;
    background: linear-gradient(to bottom, #E5E6EB 50%, transparent 50%);
    background-size: 100% 8px;
    background-repeat: repeat-y;
    left: 12px;
    z-index: 1;
    top: calc(100% - 60px);
    height: 108px;
}

.va-info-section {
    margin-left: 36px;
    margin-top: 24px;
    padding: 20px;
    background-color: #F7F8FA;
    border-radius: 8px;
    width: 330px;
}

.va-title {
    font-size: 16px;
    font-weight: 600;
    color: #222527;
    margin-bottom: 12px;
}

.va-description {
    font-size: 14px;
    color: #6B7275;
    line-height: 1.6;
    font-family: 'PingFangSC-Regular';
}

.vertical-divider {
    width: 1px;
    height: 532px;
    background-color: #E5E6EB;
    margin: 0 24px;
}

.bank-info-section {
    flex-shrink: 0;
    width: 367px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
}

.bank-details {
    width: 100%;
    margin-top: 24px;
}

.bank-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-size: 14px;
}

.detail-label {
    color: #6B7275;
    font-weight: normal;
    font-family: 'PingFangSC-Regular';
}

.detail-value {
    display: flex;
    align-items: center;
    font-family: 'PingFangSC-Regular';
    font-size: 14px;
    color: #222527;
}

.copy-btn {
    margin-left: 18px;
    font-size: 14px;
    color: #222527;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 60px;
}

.empty-image {
    width: 144px;
    height: 144px;
    margin-bottom: 8px;
}

.empty-text {
    font-size: 14px;
    color: #6B7275;
    font-family: 'PingFangSC-Regular';
}

:deep(.account-select) {
    .el-select__wrapper {
        height: 60px !important;
        min-height: 60px;
        border: 1px solid #E5E6EB;
        border-radius: 8px;
        padding: 17px 16px;
        background-color: #ffffff;
        box-shadow: none;

        .el-select__inner {
            font-family: 'PingFangSC-Regular';
            font-size: 16px;
            color: #222527;
            white-space: pre-line;

            &::placeholder {
                color: #C9CDD4;
            }
        }

        .el-select__suffix {
            .el-select__suffix-inner {
                color: #6b7275;
            }
        }
    }

    &:hover .el-input__wrapper {
        border-color: #ff0064;
    }
}
</style>