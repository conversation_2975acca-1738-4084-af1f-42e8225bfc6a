<script lang="ts" setup>
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
import FiatDeposit from './components/FiatDeposit.vue';
import CryptoDeposit from './components/CryptoDeposit.vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { feeQuotaApi } from '@/common/apis/common';
import { FeeQuotaResponseData } from '@/common/apis/common/type';
import { formatNumber } from '@/common/utils/math';

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const feeQuota = ref<FeeQuotaResponseData['data'] | null>(null);

// Active tab state with smooth animation support
const activeTab = ref<'fiat' | 'crypto'>('fiat');
// Tab container and slider refs for animation
const tabsRef = ref<HTMLElement>();
const sliderRef = ref<HTMLElement>();

/**
 * 更新滑动指示器位置
 * 根据当前激活的tab计算并设置滑动指示器的位置和宽度
 */
const updateSliderPosition = async () => {
  await nextTick();
  if (!tabsRef.value || !sliderRef.value) return;

  const activeButton = tabsRef.value.querySelector('.tab-button.active') as HTMLElement;
  if (!activeButton) return;

  const tabsRect = tabsRef.value.getBoundingClientRect();
  const buttonRect = activeButton.getBoundingClientRect();

  // 计算相对于tabs容器的位置
  const left = buttonRect.left - tabsRect.left;
  const width = buttonRect.width;

  // 应用平滑过渡动画
  sliderRef.value.style.transform = `translateX(${left}px)`;
  sliderRef.value.style.width = `${width}px`;
};

/**
 * 切换tab标签
 * @param tab - 要切换到的tab类型
 */
const switchTab = async (tab: 'fiat' | 'crypto') => {
  if (activeTab.value === tab) return;

  activeTab.value = tab;
  await updateSliderPosition();
};

// Watch for route changes to update active tab
watch(
  () => route.query.type,
  async (newType) => {
    if (newType === 'crypto') {
      activeTab.value = 'crypto';
    } else {
      activeTab.value = 'fiat';
    }
    await updateSliderPosition();
  },
  { immediate: true }
);

// Watch for active tab changes to update slider position
watch(activeTab, updateSliderPosition);

/** 跳转到充值记录页面 */
function goToRechargeRecord() {
  router.push('/deposit/record');
}

onMounted(async () => {
  await getFeeQuota();
  // 初始化滑动指示器位置
  await updateSliderPosition();

  // 监听窗口大小变化，重新计算滑动指示器位置
  window.addEventListener('resize', updateSliderPosition);
});

// 组件卸载时清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', updateSliderPosition);
});
async function getFeeQuota() {
  try {
    const res = await feeQuotaApi();
    feeQuota.value = res.data;
  } catch (error) {
    console.error(error);
  }
}
</script>

<template>
  <div class="deposit-container">
    <div class="deposit-content">
      <!-- Main Content Area -->
      <div class="deposit-main">
        <!-- Header with title and tabs aligned -->
        <div class="deposit-header">
          <h1 class="deposit-title">{{ t('deposit.title') }}</h1>
          <!-- Tab Navigation with smooth slider -->
          <div ref="tabsRef" class="deposit-tabs">
            <!-- 滑动指示器背景 -->
            <div ref="sliderRef" class="tab-slider"></div>
            <button
              class="tab-button"
              :class="{ active: activeTab === 'fiat' }"
              @click="switchTab('fiat')"
            >
              {{ t('deposit.fiat') }}
            </button>
            <button
              class="tab-button"
              :class="{ active: activeTab === 'crypto' }"
              @click="switchTab('crypto')"
            >
              {{ t('deposit.crypto') }}
            </button>
          </div>
        </div>

        <!-- Deposit Form Content with smooth transitions -->
        <div class="deposit-form-content">
          <Transition name="fade" mode="out-in">
            <FiatDeposit
              v-if="activeTab === 'fiat'"
              :fee-quota="feeQuota ?? undefined"
              key="fiat"
            />
            <CryptoDeposit v-else key="crypto" />
          </Transition>
        </div>
      </div>

      <!-- Sidebar with smooth transitions -->
      <div class="deposit-sidebar-container">
        <Transition name="slide-fade" mode="out-in">
          <div v-if="activeTab === 'fiat'" class="deposit-sidebar" key="fiat-sidebar">
            <span class="sidebar-card-title">{{ t('deposit.sidebar.fiat.title1') }}</span>
            <div class="sidebar-card-content">
              <p>
                {{ t('deposit.sidebar.fiat.content1_1') }}
                <span class="amount"
                  >{{ formatNumber(feeQuota?.recmRechargeQuota || 5000) }} USD</span
                >
                {{ t('deposit.sidebar.fiat.content1_2') }}
              </p>
              <p class="description">{{ t('deposit.sidebar.fiat.content1_3') }}</p>
            </div>
            <span class="sidebar-card-title">{{ t('deposit.sidebar.fiat.title2') }}</span>
            <div class="sidebar-card-content">
              <p class="description">{{ t('deposit.sidebar.fiat.content2') }}</p>
            </div>
            <span class="sidebar-card-title">{{ t('deposit.sidebar.fiat.title3') }}</span>
            <div class="sidebar-card-content">
              <p class="description">{{ t('deposit.sidebar.fiat.content3') }}</p>
            </div>
            <span class="sidebar-card-title">{{ t('tips.compliance.title') }}</span>
            <div class="sidebar-card-content mb-0px">
              <p class="description">{{ t('tips.compliance.content') }}</p>
            </div>
          </div>
          <div v-else class="deposit-sidebar" key="crypto-sidebar">
            <span class="sidebar-card-title">{{ t('deposit.sidebar.crypto.title1') }}</span>
            <div class="sidebar-card-content">
              <p class="description">
                {{ t('deposit.sidebar.crypto.content1_1') }}
              </p>
              <p class="description">{{ t('deposit.sidebar.crypto.content1_2') }}</p>
            </div>
            <span class="sidebar-card-title">{{ t('tips.compliance.title') }}</span>
            <div class="sidebar-card-content mb-0px">
              <p class="description">{{ t('tips.compliance.content') }}</p>
            </div>
          </div>
        </Transition>
        <!-- Deposit Records Button with smooth transition -->
        <Transition name="fade-scale">
          <button
            v-if="activeTab === 'fiat'"
            class="deposit-records-btn"
            @click="goToRechargeRecord"
          >
            <svg-icon name="icon-withdrawal-record" class="text-15px" />
            {{ t('deposit.recordBtn') }}
          </button>
        </Transition>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.deposit-container {
  background-color: #ffffff;
  padding: 32px 40px;
  min-width: 1120px;
  // display: flex;
  justify-content: center;
  // overflow: auto;
}

.deposit-content {
  display: flex;
  gap: 24px;
  padding-right: 24px;
  justify-content: center;
  height: fit-content;
  margin-bottom: 32px;
}

/* Main Content Area */
.deposit-main {
  flex: 1;
  padding: 24px;
  min-width: 834px;
  // max-height: 644px;
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 12px;
  height: fit-content;
}

/* Header with title and tabs aligned */
.deposit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.deposit-title {
  font-family: 'PingFangSC-Semibold';
  font-weight: 600;
  font-size: 28px;
  color: #222527;
  margin: 0;
}

/* Tab Navigation with smooth animation */
.deposit-tabs {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 6px;
  padding: 4px;
  width: fit-content;
  position: relative;
  overflow: hidden;
}

/* 滑动指示器 - 提供平滑的背景滑动效果 */
.tab-slider {
  position: absolute;
  top: 4px;
  left: 4px;
  height: 32px;
  background: linear-gradient(135deg, #030814 0%, #1a1d29 100%);
  border-radius: 4px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1;
  /* 确保在移动设备上也有硬件加速 */
  will-change: transform, width;
  transform: translateX(0px);
  box-shadow:
    0 2px 8px rgba(3, 8, 20, 0.15),
    0 1px 3px rgba(3, 8, 20, 0.1);

  /* 动画期间增强阴影效果 */
  transition:
    all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 0.3s ease;

  /* 添加微妙的光泽效果 */
  &::before {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    height: 50%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    border-radius: 3px 3px 0 0;
    pointer-events: none;
  }
}

.tab-button {
  border: none;
  height: 32px;
  width: 144px;
  background: transparent;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  font-family: 'PingFangSC-Regular';
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #030814;
  position: relative;
  z-index: 2;
  /* 确保文字在滑动指示器之上 */
  transform: scale(1);
  will-change: transform, color;

  /* 激活状态 */
  &.active {
    color: #ffffff;
    font-weight: 500;
    transform: scale(1.02);
  }

  /* 悬停效果 */
  &:hover:not(.active) {
    color: #666;
    transform: scale(1.01);
  }

  /* 点击效果 */
  &:active {
    transform: scale(0.98);
    transition-duration: 0.1s;
  }

  /* 焦点状态 */
  &:focus-visible {
    outline: 2px solid #030814;
    outline-offset: 2px;
  }
}

.deposit-form-content {
  width: 100%;
}

/* Sidebar */
.deposit-sidebar-container {
  flex-shrink: 0;
  width: 264px;
}

.deposit-sidebar {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.sidebar-card-title {
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 16px;
  color: #222527;
  display: block;
  margin-bottom: 8px;
}

.sidebar-card-content {
  margin-bottom: 24px;

  p {
    font-family: PingFangSC;
    font-weight: 400;
    text-align: left;
    font-size: 14px;
    line-height: 1.5;
    margin-top: 0;
    margin-bottom: 0;
    color: #6b7275;

    &:last-child {
      margin-bottom: 0;
    }

    .amount {
      font-weight: 600;
      color: #030814;
    }
  }

  .description {
    color: #6b7275;
  }
}

.deposit-records-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 32px;
  background-color: #030814;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  transition: all 0.2s ease;
  gap: 8px;

  &:hover {
    background-color: #1a1d29;
  }
}

/* Vue Transition Animations */

/* 淡入淡出过渡效果 - 用于主要内容区域 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 滑动淡入过渡效果 - 用于侧边栏内容，从下往上的过渡方式 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(15px) scale(0.98);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.98);
}

/* 缩放淡入过渡效果 - 用于按钮，从下往上的过渡方式，延迟启动确保在侧边栏动画完成后触发 */
.fade-scale-enter-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  transition-delay: 0.35s;
  /* 延迟350ms，等待侧边栏动画完成 */
}

.fade-scale-leave-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  /* 离开动画不需要延迟，立即开始 */
}

.fade-scale-enter-from {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
}

.fade-scale-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-8px);
}

/* 优化过渡期间的性能 */
.fade-enter-active *,
.fade-leave-active *,
.slide-fade-enter-active *,
.slide-fade-leave-active *,
.fade-scale-enter-active *,
.fade-scale-leave-active * {
  will-change: transform, opacity;
}

/* 确保过渡期间元素层级正确 */
.fade-enter-active,
.slide-fade-enter-active,
.fade-scale-enter-active {
  z-index: 10;
}

.fade-leave-active,
.slide-fade-leave-active,
.fade-scale-leave-active {
  z-index: 9;
}
</style>
