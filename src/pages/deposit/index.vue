<script lang="ts" setup>
import { ref, watch } from 'vue';
import FiatDeposit from './components/FiatDeposit.vue';
import CryptoDeposit from './components/CryptoDeposit.vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { feeQuotaApi } from '@/common/apis/common';
import { FeeQuotaResponseData } from '@/common/apis/common/type';
import { formatNumber } from '@/common/utils/math';

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const feeQuota = ref<FeeQuotaResponseData["data"] | null>(null);

// Active tab state - for UI only, no logic implementation
const activeTab = ref<'fiat' | 'crypto'>('fiat');

// Watch for route changes to update active tab
watch(
  () => route.query.type,
  (newType) => {
    if (newType === 'crypto') {
      activeTab.value = 'crypto';
    } else {
      activeTab.value = 'fiat';
    }
  },
  { immediate: true }
);

/** 跳转到充值记录页面 */
function goToRechargeRecord() {
  router.push('/deposit/record');
}

onMounted(async () => {
  await getFeeQuota();
});
async function getFeeQuota() {
  try {
    const res = await feeQuotaApi();
    feeQuota.value = res.data;
  } catch (error) {
    console.error(error);
  }
}
</script>

<template>
  <div class="deposit-container">
    <div class="deposit-content">
      <!-- Main Content Area -->
      <div class="deposit-main">
        <!-- Header with title and tabs aligned -->
        <div class="deposit-header">
          <h1 class="deposit-title">{{ t('deposit.title') }}</h1>
          <!-- Tab Navigation -->
          <div class="deposit-tabs">
            <button class="tab-button" :class="{ active: activeTab === 'fiat' }" @click="activeTab = 'fiat'">
              {{ t('deposit.fiat') }}
            </button>
            <button class="tab-button" :class="{ active: activeTab === 'crypto' }" @click="activeTab = 'crypto'">
              {{ t('deposit.crypto') }}
            </button>
          </div>
        </div>

        <!-- Deposit Form Content -->
        <div class="deposit-form-content">
          <FiatDeposit v-if="activeTab === 'fiat'" :fee-quota="feeQuota ?? undefined" />
          <CryptoDeposit v-if="activeTab === 'crypto'" />
        </div>
      </div>

      <!-- Sidebar -->
      <div class="deposit-sidebar-container">
        <div v-if="activeTab === 'fiat'" class="deposit-sidebar">
          <span class="sidebar-card-title">{{ t('deposit.sidebar.fiat.title1') }}</span>
          <div class="sidebar-card-content">
            <p>
              {{ t('deposit.sidebar.fiat.content1_1') }}
              <span class="amount">{{ formatNumber(feeQuota?.recmRechargeQuota || 5000) }} USD</span>
              {{ t('deposit.sidebar.fiat.content1_2') }}
            </p>
            <p class="description">{{ t('deposit.sidebar.fiat.content1_3') }}</p>
          </div>
          <span class="sidebar-card-title">{{ t('deposit.sidebar.fiat.title2') }}</span>
          <div class="sidebar-card-content">
            <p class="description">{{ t('deposit.sidebar.fiat.content2') }}</p>
          </div>
          <span class="sidebar-card-title">{{ t('deposit.sidebar.fiat.title3') }}</span>
          <div class="sidebar-card-content">
            <p class="description">{{ t('deposit.sidebar.fiat.content3') }}</p>
          </div>
          <span class="sidebar-card-title">{{ t('tips.compliance.title') }}</span>
          <div class="sidebar-card-content mb-0px">
            <p class="description">{{ t('tips.compliance.content') }}</p>
          </div>
        </div>
        <div v-else-if="activeTab === 'crypto'" class="deposit-sidebar">
          <span class="sidebar-card-title">{{ t('deposit.sidebar.crypto.title1') }}</span>
          <div class="sidebar-card-content">
            <p class="description">
              {{ t('deposit.sidebar.crypto.content1_1') }}
            </p>
            <p class="description">{{ t('deposit.sidebar.crypto.content1_2') }}</p>
          </div>
          <span class="sidebar-card-title">{{ t('tips.compliance.title') }}</span>
          <div class="sidebar-card-content mb-0px">
            <p class="description">{{ t('tips.compliance.content') }}</p>
          </div>
        </div>
        <!-- Deposit Records Button -->
        <button v-if="activeTab === 'fiat'" class="deposit-records-btn" @click="goToRechargeRecord">
          <svg-icon name="icon-withdrawal-record" class="text-15px" />
          {{ t('deposit.recordBtn') }}
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.deposit-container {
  background-color: #ffffff;
  padding: 32px 40px;
  min-width: 1120px;
  display: flex;
  justify-content: center;
  // overflow: auto;
}

.deposit-content {
  display: flex;
  gap: 24px;
  padding-right: 24px;
  justify-content: center;
  height: fit-content;
  margin-bottom: 32px;
}

/* Main Content Area */
.deposit-main {
  flex: 1;
  padding: 24px;
  max-width: 834px;
  // max-height: 644px;
  background: #FFFFFF;
  border: 1px solid #E5E6EB;
  border-radius: 12px;
  height: fit-content;
}

/* Header with title and tabs aligned */
.deposit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.deposit-title {
  font-family: 'PingFangSC-Semibold';
  font-weight: 600;
  font-size: 28px;
  color: #222527;
  margin: 0;
}

/* Tab Navigation */
.deposit-tabs {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 6px;
  padding: 4px;
  width: fit-content;
}

.tab-button {
  border: none;
  height: 32px;
  width: 144px;
  background: transparent;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  font-family: 'PingFangSC-Regular';
  transition: all 0.2s ease;
  color: #030814;

  &.active {
    background-color: #030814;
    color: #ffffff;
  }

  &:hover:not(.active) {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.deposit-form-content {
  width: 100%;
}

/* Sidebar */
.deposit-sidebar-container {
  flex-shrink: 0;
  width: 264px;
}

.deposit-sidebar {
  background: #F8F9FA;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.sidebar-card-title {
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 16px;
  color: #222527;
  display: block;
  margin-bottom: 8px;
}

.sidebar-card-content {
  margin-bottom: 24px;

  p {
    font-family: PingFangSC;
    font-weight: 400;
    text-align: left;
    font-size: 14px;
    line-height: 1.5;
    margin-top: 0;
    margin-bottom: 0;
    color: #6b7275;

    &:last-child {
      margin-bottom: 0;
    }

    .amount {
      font-weight: 600;
      color: #030814;
    }
  }

  .description {
    color: #6b7275;
  }
}

.deposit-records-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 32px;
  background-color: #030814;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  transition: all 0.2s ease;
  gap: 8px;

  &:hover {
    background-color: #1a1d29;
  }
}
</style>