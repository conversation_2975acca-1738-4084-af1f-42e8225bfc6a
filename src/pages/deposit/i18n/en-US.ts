export default {
  deposit: {
    title: 'Deposit',
    fiat: 'Fiat',
    crypto: 'Crypto',
    recordBtn: 'View Deposit Records',
    copySuccess: 'Copied successfully',
    copyFailed: 'Copy failed',
    copyAllInfo: 'Copy All Information',

    // Fiat Deposit
    fiatTab: {
      selectAccount: 'Please select the global account you wish to deposit into:',
      selectAccountPlaceholder: 'Please select an account',
      accountInfoHint:
        'After selecting an account, DingX will display the bank information on the right; you can verify the details and initiate a transfer from your external bank in the corresponding currency.',
      voucherHint:
        'Depending on the global account, DingX may require you to enter the deposit amount and upload the corresponding bank transfer voucher.',
      transferHint: 'Please make the transfer according to the bank information on the right.',
      aboutVA: 'About VA Account Deposit',
      vaDescription:
        'The account you are depositing into is an exclusive virtual bank account under the same name, for use by this enterprise only. After the funds are transferred, DingX will automatically recognize the transaction information and notify you via the system or email at the earliest opportunity, displaying the actual credited amount in the console. Please wait patiently.',
      enterAmount: 'Please enter the amount you wish to deposit:',
      rechargeBtn: 'Deposit',
      accountName: 'Account Name',
      paymentMethod: 'Payment Method',
      accountNumber: 'Account Number',
      swiftBic: 'Swift/BIC',
      bankName: 'Bank Name',
      bankAddress: 'Bank Address',
      bankCountry: 'Bank Country/Region',
      paymentRemark: 'Payment Remark',
      selectDepositAccount: 'Please select an account to deposit into',
      inputAmountHint: 'Please select an account and enter the deposit amount',
      minAmountHint: 'Deposit amount cannot be less than {amount} USD',
      submitSuccessTitle: 'Submission Successful',
      submitSuccessMsg:
        'Deposit submission successful, the order number is: {orderNo}; please check the "Deposit - Deposit Record" after the remittance is completed.',
      viewRecord: 'View Record',
      cancel: 'Cancel',
    },

    // Crypto Deposit
    cryptoTab: {
      emptyTip: 'No data',
      selectCoin: 'Please select the currency you wish to deposit',
      selectPlaceholder: 'Please select',
      selectNetwork: 'Please select the blockchain network for the deposit currency',
      depositAddress: 'Deposit Address',
      minDeposit: 'Minimum Deposit Amount',
      estimatedArrival: 'Estimated Arrival',
      dailyLimit: 'Daily Deposit Remaining',
      allLimit: 'Total Limit',
      monthlyLimit: 'Monthly Deposit Remaining',
      selectCoinAndNetwork: 'Please select a deposit currency and network',
    },

    // Sidebar
    sidebar: {
      fiat: {
        title1: 'About Deposit Limits',
        content1_1: 'The minimum deposit amount is',
        content1_2: '.',
        content1_3:
          'The current account has a deposit limit. If you need to increase the limit, please contact your dedicated sales or customer service personnel to apply.',
        title2: 'About Arrival Time',
        content2:
          'The arrival time is subject to the clearing agreement between the remitting bank and the receiving bank, and may vary due to differences in region, currency, and channel. Please be aware.',
        title3: 'Friendly Reminder',
        content3:
          'It is recommended to save a screenshot of the deposit bank account information. You can check the deposit status through the bill details or the fiat deposit page.',
      },
      crypto: {
        title1: 'Please Note',
        content1_1:
          'The deposit address displayed on the page is your deposit address on the blockchain network. Please ensure it is consistent with your withdrawal platform network, otherwise it may cause asset loss.',
        content1_2:
          'You can use a cryptocurrency wallet to make deposit transactions, including copying the deposit address to your wallet or using the wallet to scan the QR code displayed on the page.',
      },
    },

    // Recharge Record Page
    record: {
      tableEmpty: 'No Data',
      title: 'Deposit Record',
      total: 'Total {total} items',
      currency: 'Currency',
      chargeTime: 'Deposit Time',
      chargeAmount: 'Applied Amount',
      bankFlowNo: 'Bank Flow No',
      accountAmount: 'Credited Amount',
      chargeStatus: 'Deposit Status',
      voucher: 'Remittance Voucher',
      voucherStatus: 'Voucher Status',
      operations: 'Operations',
      reset: 'Reset',
      query: 'Query',
      pleaseSelect: 'Please select',
      pleaseInput: 'Please input',
      rechargeSeqId: 'Deposit Seq ID',
      applyAmount: 'Applied Amount',
      startTime: 'Start Time',
      endTime: 'End Time',
      index: 'No.',
      edit: 'Edit',
      delete: 'Delete',
      close: 'Close',
      register: 'Register Voucher',
      statusTypes: {
        pending: 'Pending Voucher Upload',
        submitted: 'Submitted',
        processing: 'Processing',
        success: 'Deposit Success',
        failed: 'Deposit Failed',
        closed: 'Deposit Closed',
      },
      voucherTypes: {
        notUploaded: 'Not Uploaded',
        uploaded: 'Uploaded',
        approved: 'Approved',
        rejected: 'Rejected',
      },
      shortcuts: {
        day1: 'Last 1 Day',
        day7: 'Last 7 Days',
        day30: 'Last 30 Days',
      },
      pagination: {
        jumper: 'Go to',
      },
      registerVoucher: {
        title: 'Register Payment Voucher',
        description:
          'Please upload your payment voucher, including bank transfer screenshots, receipts, etc., so that we can quickly confirm your deposit.',
        orderInfo: 'Order Information',
        bankFlowNo: 'Bank Transfer Reference Number',
        bankFlowNoPlaceholder: 'Please enter bank transfer reference number',
        voucherUpload: 'Upload Voucher',
        uploadTip:
          'Supports PNG, JPG, JPEG, PDF formats, up to 5 files, each file no more than 10MB',
        remark: 'Remark',
        remarkPlaceholder: 'Please enter remark information (optional)',
        cancel: 'Cancel',
        confirm: 'Submit',
        bankFlowNoRequired: 'Please enter bank transfer reference number',
        voucherRequired: 'Please upload payment voucher',
        submitSuccess: 'Payment voucher registered successfully',
        submitFailed: 'Payment voucher registration failed, please try again',
      },
      autoOpenVoucherMessage: 'Automatically opened voucher upload page for order No. {orderNo}',
      recordNotFound: 'No deposit record found for order No. {orderNo}',
      closeConfirmTitle: 'Close Deposit?',
      closeConfirmMessage:
        'Please confirm whether you want to close deposit order No. {orderNo}? This action cannot be undone.',
      confirmClose: 'Confirm',
      cancel: 'Cancel',
      closing: 'Closing...',
      closeSuccess: 'Deposit closed successfully',
      closeFailed: 'Failed to close deposit, please try again',
      bankInfo: {
        title: 'Payment Information',
        payeeInfo: 'Payee Information',
        bankInfo: 'Bank Information',
        payeeName: 'Payee Name',
        account: 'Bank Account',
        bankName: 'Bank Name',
        bankCountry: 'Bank Country',
        swiftCode: 'SWIFT Bank Code',
        remark: 'Remark',
        currency: 'Currency',
        tips: 'Tips',
        tipsContent:
          'Please use your verified bank account for remittance and ensure the remittance information is completely consistent with the above information. After remittance completion, please register the remittance voucher in time for us to quickly confirm the arrival.',
        close: 'Close',
        loadFailed: 'Failed to load bank information, please try again',
      },
    },
  },
};
