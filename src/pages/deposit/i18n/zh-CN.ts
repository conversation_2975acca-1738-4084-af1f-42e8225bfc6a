export default {
  deposit: {
    title: '充值',
    fiat: 'Fiat',
    crypto: 'Crypto',
    recordBtn: '查看充值记录',
    copySuccess: '复制成功',
    copyFailed: '复制失败',
    copyAllInfo: '复制所有信息',

    // Fiat 充值
    fiatTab: {
      selectAccount: '请选择您希望汇入的全球账户：',
      selectAccountPlaceholder: '请下拉选择账户',
      accountInfoHint:
        '当您选择好账户后，DingX会在右侧显示您账户的银行信息；您可以核对详细的信息并在您的外部银行发起对应币种的转账。',
      voucherHint: '根据全球账户的不同，DingX可能会要求您填写充值金额并上传对应的银行转账凭证。',
      transferHint: '请按右侧银行信息进行转账：',
      aboutVA: '关于VA账户充值',
      vaDescription:
        '您正在充值的账户为专属同名银行虚拟账户，仅限本企业使用。资金转入后，DingX将自动识别到账信息且在第一时间通过系统或邮件通知您，并在控台展示实际到账金额，请您耐心等待。',
      enterAmount: '请输入您想充值的金额：',
      rechargeBtn: '充值',
      accountName: '账户名',
      paymentMethod: '支付方式',
      accountNumber: '账户号',
      swiftBic: 'Swift/BIC',
      bankName: '银行名称',
      bankAddress: '银行地址',
      bankCountry: '银行所在国家或地区',
      paymentRemark: '付款备注',
      selectDepositAccount: '请选择汇入账户',
      inputAmountHint: '请选择账户并输入充值金额',
      minAmountHint: '充值金额不能少于 {amount} USD',
      submitSuccessTitle: '提交成功',
      submitSuccessMsg:
        '充值提交成功，充值单号为：{orderNo}；请在汇款完成后到“充值-充值记录”处查看',
      viewRecord: '去查看',
      cancel: '取消',
    },

    // Crypto 充值
    cryptoTab: {
      emptyTip: '暂无数据',
      selectCoin: '请选择您希望充值的币种',
      selectPlaceholder: '请选择',
      selectNetwork: '请选择充值币种的区块链网络',
      depositAddress: '充值地址',
      minDeposit: '最小充值数量',
      estimatedArrival: '预计到账',
      networkConfirmations: '次网络确认',
      dailyLimit: '单日充值剩余额',
      allLimit: '总额度',
      monthlyLimit: '单月充值剩余额度',
      selectCoinAndNetwork: '请选择充值币种及网络',
      qrCodeHint: '请使用加密货币钱包进行扫码充值',
    },

    // Sidebar
    sidebar: {
      fiat: {
        title1: '关于充值额度',
        content1_1: '最低充值金额为',
        content1_2: '。',
        content1_3:
          '当前账户充值金额设有额度限制，若需提升额度，请联系您的专属销售或客服人员协助申请。',
        title2: '关于到账时间',
        content2:
          '到账时间依据汇出银行与收款银行之间的清算协定为准，可能因地区、币种及通道差异而有所变动，敬请留意。',
        title3: '友情提示',
        content3: '建议截图保存充值银行账户信息，您可通过账单详情或法币充值页面查询充值到账情况。',
      },
      crypto: {
        title1: '操作提示',
        content1_1:
          '页面展示的充值地址为您在区块链网络上的充值地址，请确保其与您的提币平台网络保持一致，否则可能造成资产损失。',
        content1_2:
          '您可使用加密货币钱包进行充值交易，包括复制充值地址至您的钱包或使用钱包扫描页面展示的二维码。',
      },
    },

    // 充值记录页面
    record: {
      tableEmpty: '暂无数据',
      title: '充值记录',
      total: '共{total}条',
      currency: '币种',
      chargeTime: '充值时间',
      chargeAmount: '申请金额',
      bankFlowNo: '银行汇款流水号',
      accountAmount: '到账金额',
      chargeStatus: '充值状态',
      voucher: '汇款凭证',
      voucherStatus: '凭证状态',
      operations: '操作',
      reset: '重置',
      query: '查询',
      pleaseSelect: '请选择',
      pleaseInput: '请输入',
      rechargeSeqId: '充值流水',
      applyAmount: '申请金额',
      startTime: '开始时间',
      endTime: '结束时间',
      index: '序号',
      edit: '编辑',
      delete: '删除',
      close: '关闭',
      register: '登记凭证',
      statusTypes: {
        pending: '待上传凭证',
        submitted: '已提交',
        processing: '处理中',
        success: '充值成功',
        failed: '充值失败',
        closed: '充值关闭',
      },
      voucherTypes: {
        notUploaded: '未上传',
        uploaded: '已上传',
        approved: '已通过',
        rejected: '已拒绝',
      },
      shortcuts: {
        day1: '近1天',
        day7: '近7天',
        day30: '近30天',
      },
      pagination: {
        jumper: '跳至',
      },
      registerVoucher: {
        title: '登记汇款凭证',
        description: '请上传您的汇款凭证，包括银行转账截图、回单等，以便我们快速确认您的充值。',
        orderInfo: '订单信息',
        bankFlowNo: '银行汇款流水号',
        bankFlowNoPlaceholder: '请输入银行汇款流水号',
        voucherUpload: '上传凭证',
        uploadTip: '支持PNG、JPG、JPEG、PDF格式，最多上传5个文件，每个文件不超过10MB',
        remark: '备注',
        remarkPlaceholder: '请输入备注信息（选填）',
        cancel: '取消',
        confirm: '提交',
        bankFlowNoRequired: '请输入银行汇款流水号',
        voucherRequired: '请上传汇款凭证',
        submitSuccess: '汇款凭证登记成功',
        submitFailed: '汇款凭证登记失败，请重试',
      },
      autoOpenVoucherMessage: '已为您自动打开流水号 {orderNo} 的上传凭证页面',
      recordNotFound: '未找到流水号 {orderNo} 对应的充值记录',
      closeConfirmTitle: '关闭充值？',
      closeConfirmMessage: '请确认是否要关闭充值流水号 {orderNo} ？关闭后将无法恢复。',
      confirmClose: '确认',
      cancel: '取消',
      closing: '关闭中...',
      closeSuccess: '充值关闭成功',
      closeFailed: '充值关闭失败，请重试',
      bankInfo: {
        title: '打款信息',
        payeeInfo: '收款人信息',
        bankInfo: '银行信息',
        payeeName: '收款人名称',
        account: '银行账号',
        bankName: '银行名称',
        bankCountry: '银行国家',
        swiftCode: 'SWIFT银行编码',
        remark: '备注/附言',
        currency: '币种',
        tips: '温馨提示',
        tipsContent:
          '请使用您已认证的银行账户进行汇款，并确保汇款信息与上述信息完全一致。汇款完成后，请及时登记汇款凭证以便我们快速确认到账。',
        close: '关闭',
        loadFailed: '获取银行信息失败，请重试',
      },
    },
  },
};
