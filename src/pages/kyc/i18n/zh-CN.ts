export default {
  nav: {
    companyInfo: '企业资质',
    businessInfo: '经营信息',
    shareholderInfo: '股东信息',
    directorInfo: '董事信息',
    managerInfo: '管理者信息',
    additionalInfo: '附加信息',
  },
  alert: {
    error: '您的企业认证信息有误，请检查并修改，或',
    suggestion: '点击查阅修改建议',
  },
  button: {
    giveUp: '放弃认证',
    saveDraft: '保存草稿',
    submit: '提交审核',
    completed: '已完成修改',
  },
  dialog: {
    giveUpTitle: '放弃认证？',
    giveUpMessage: '您确认要放弃认证吗？请注意，放弃认证账号将会删除您所有已填写的信息。',
    confirm: '确认',
    cancel: '取消',
    saveSuccess: '保存成功',
    submitTitle: '确认完成并提交？',
    submitMessage: '请注意，请确认您所提交的材料的真实性与完整性。提交后不可修改，并耐心等待',
    submit: '提交',
    thinkAgain: '再想想',
    thanksTitle: '感谢配合，请耐心等待我们的通知',
    thanksMessage:
      '您所补充的信息及材料已提交审核，我们会加急处理您的流程！如遇特殊情况请联系我们的运营人员。',
    gotIt: '知道了',
    congratsTitle: '恭喜您，距离Web3之旅更进一步！',
    congratsMessage: '您的材料已提交审核，大概需要1-3个工作日！如遇特殊情况请联系我们的运营人员。',
  },
  certificationNav: {
    title: '认证目录',
  },
  messages: {
    saveSuccess: '保存成功',
  },
  opinions: {
    title: '审核意见及修改建议',
    suggestion: '请按照以下建议修改 {moduleName} 的内容',
    companyQualification: '企业资质',
    businessInfo: '经营信息',
    shareholderInfo: '股东信息',
    directorInfo: '董事信息',
    managerInfo: '管理者信息',
    additionalInfo: '附加信息',
  },
  shareholderInfo: {
    title: '股东信息',
    description:
      '持股/收益所有权达10%或以上的自然人股东/最终受益人均需提供，若多于1位，请添加自然人股东/最终受益人继续提供对应信息。您可以在这个页面添加或去修改您所在企业的自然人股东或最终受益人，最多10位。',
    index: '序号',
    shareholderType: '股东',
    shareholderTypeDesc: '自然人股东/最终受益人',
    progress: '进度',
    edit: '编辑',
    delete: '删除',
    preview: '查看',
    tableEmpty: '暂无数据',
    addShareholder: '添加股东',
    editShareholder: '编辑股东',
    deleteShareholder: '删除股东',
    action: '操作',
    notRequired: '选填',
    shareholder: '股东',
    finalBeneficiary: '最终受益人',
    shareholdingRatio: '持股比例',
    nationality: '国籍',
    idType: '证件类型',
    enFirstName: '英文名',
    enLastName: '英文姓',
    cnFirstName: '中文姓',
    cnLastName: '中文名',
    firstName: '姓',
    lastName: '名',
    expirationStart: '证件有效期开始时间',
    expirationStartAbb: '开始时间',
    expirationEnd: '证件有效期结束时间',
    expirationLongTerm: '证件长期有效',
    expirationEndAbb: '结束时间',
    expiration: '证件有效期',
    gender: '性别',
    idNumber: '证件号码',
    residenceCountry: '居住国家',
    residenceAddress: '居住地址',
    registerCity: '城市',
    registerProv: '省/州',
    registerPostalCode: '邮编',
    birthDate: '出生日期',
    identityAuthentication: '身份认证',
    formValidateFile: '请上传身份认证文件',
    identityAuthenticationDescription:
      '当您在鼎进行交易或操作时，为了确保交易双方身份的真实有效及交易本身的安全，需要您完成特定级别的身份验证：',
    identityAuthenticationDescription1: '1.请对上方所选择的「证件类型」进行认证；',
    identityAuthenticationDescription2:
      '2.身份证件需提交与公司注册证书上身份信息一致的文件；身份证件需在有效期内。',
    idTypeEnumPASSPORT: '护照',
    idTypeEnumSPR: '新加坡永久居民',
    idTypeEnumSCC: '新加坡市民卡',
    idTypeEnumHK2003: '中国香港身份证2003版',
    idTypeEnumHK2018: '中国香港身份证2018版',
    idTypeEnumID: '身份证',
    genderEnumMale: '男',
    genderEnumFemale: '女',
    formValidateRatio: '请输入持股比例',
    formValidateRatioRange: '请输入0-100的持股比例',
    formValidateRatioRange2: '持股比例需大于0',
    formValidateNationality: '请选择国籍',
    formValidateIdType: '请选择证件类型',
    formValidateEnFirstName: '请输入英文名',
    formValidateEnLastName: '请输入英文姓',
    formValidateEnName: '请输入英文姓名',
    formValidateCnFirstName: '请输入中文姓',
    formValidateCnLastName: '请输入中文名',
    formValidateCnName: '请输入中文姓名',
    formValidateExpirationStart: '请选择证件有效期开始时间',
    formValidateExpirationEnd: '请选择证件有效期结束时间',
    formValidateExpirationError: '请选择正确的证件有效期',
    formValidateGender: '请选择性别',
    formValidateIdNumber: '请输入证件号码',
    formValidateResidenceCountry: '请选择居住国家',
    formValidateResidenceAddress: '请输入居住地址',
    formValidateBirthdayDate: '请选择出生日期',
    formValidateRegisterCity: '请输入城市',
    formValidateRegisterProv: '请输入省/州',
    formValidateRegisterPostalCode: '请输入邮编',
    statusWaiting: '待填写',
    statusCompleted: '已完成',
    statusModified: '待修改',
    statusApproved: '已认证',
    selectPlaceholder: '请选择',
    formValidateIdNumberExist: '已存在相同的证件的股东',
    deleteConfirmContent:
      '删除后您将丢失这名股东已填写的所有信息以及资料。注意，若您已在董事及管理者信息处同步了这名股东，则对应信息也会丢失，您将需要重新填写（如有需要）。',
    deleteConfirmTitle: '您确定删除该股东信息吗？',
    managerSyncWarningContent:
      '    您修改的股东股份低于10%。由于该股东信息此前已同步为管理者信息，当前操作将导致其同步的管理者信息被自动删除。\n    您需要重新选择一位股份大于等于10%的股东/董事来作为您的新管理者。请确认是否继续操作？',
    managerSyncWarningTitle: '股份份额变更将影响已同步的管理者',
    ensureDesc: '本人证明已经提供所有关于持有该公司至少10%股份的人员的准确信息',
    ensure: '请勾选此项',
    btnAddConfirm: '添加',
    btnCancel: '取消',
  },
  directorInfo: {
    title: '董事信息',
    description:
      '您在这个页面可以添加或删除您所在企业的董事、关键控制人，至多10位。若该成员同时也是您企业的股东并且您已完成对应信息的填写，您可以进行资料的快速导入。',
    directorType: '董事',
    directorTypeDesc: '董事、关键控制人',
    dispatchSyncStatusDesc: '已同步股东',
    addDirector: '添加董事',
    syncShareholderInfo: '同步股东信息',
    syncShareholderDesc:
      '请注意，同步股东信息后，您仅可查看此股东的信息。若您希望修改请前往对应股东信息页进行修改。',
    selectShareholder: '选择同步信息的股东',
    formValidateIdNumberExist: '已存在相同的证件的董事',
    coiTitle: '证明材料',
    coiDesc: '董事在职证明COI或同等证明材料(非香港地区)',
    coiUploadTip: '请上传董事在职证明COI或同等证明材料',
  },
  managerInfo: {
    title: '管理者信息',
    description:
      '管理人经公司授权，有权登录并操作鼎账户、签署所有文件并处理与公司相关的一切事物。您将在下方选项中挑选任意一项进行身份确认。',
    description2: '请注意，您将挑选当前账户作为管理者的身份，请选择您的身份类型。',
    managerType: '管理者类型',
    managerTypeDesc: '股东/董事/实际控制人(10%以上)',
    managerTypeDesc2: '授权管理者',
    shareholderName: '我是',
    authorizationLetter: '授权管理人授权书',
    downloadTemplate: '下载模版',
    identityAuthentication: '身份认证',
    identityAuthenticationDescription:
      '当您在鼎进行交易或操作时，为了确保交易双方身份的真实有效及交易本身的安全，需要您完成特定级别的身份验证：',
    identityAuthenticationDescription1: '1.请对上方所选择的「证件类型」进行认证；',
    identityAuthenticationDescription2:
      '2.身份证件需提交与公司注册证书上身份信息一致的文件；身份证件需在有效期内。',
    formValidateAuthorizationLetter: '请上传授权管理人授权书',
    formValidateManagerType: '请选择管理者类型',
    formValidateManagerName: '请选择管理者名称',
  },
  additionalInfo: {
    title: '附加信息',
    supplementaryMaterials: '补充材料(选填)',
    supplementaryMaterialsDesc: '此项选填，若有额外需要请联系您的销售或客服；',
    agreement: {
      text: '我已知悉并同意：本平台在部分地区可合规提供相关金融服务，在其他地区仅提供咨询及渠道推荐服务，所有资金与资产操作均由当地合规第三方机构独立完成。如需详细了解，请',
      consultationService: '查看协议',
      andOther: '。',
    },
    rules: {
      maaoaFileList: '请上传组织大纲及章程',
      bamFileList: '请上传业务真实性材料',
      financialAgreementFlag: '请勾选同意协议',
    },
    downloadMaaoaUrl: 'https://files.dingx.tech/kyc/cn/%E7%AB%A0%E7%A8%8B.png',
    downloadTemplateUrl:
      'https://files.dingx.tech/ding_zonetop_prod/20250717/a85bc25d142740e99116e4d5db4715b4.pdf',
  },
  companyInfo: {
    title: '企业资质',
    companyInfo: '企业信息',
    submitRelatedData: '提交相关资料',
    registerLocation: {
      title: '注册地址',
      description: '请查阅您公司所注册的资料与信息，并在下方选择证书上登记的注册所在地。',
      label: '公司注册地',
    },
    qualification: {
      title: '企业资质',
      ci: {
        title: '企业注册证书(CI)',
        downloadTemplate: '下载模板',
      },
      registrationNumber: '注册证书编号',
      establishDate: '成立时间',
      englishName: '英文名称',
      chineseName: '中文名称',
      optional: '(选填)',
      br: {
        title: '商业登记证书(BR)',
        downloadTemplate: '下载模板',
        number: '商业登记证编号',
        address: '商业登记地址',
        endTime: '商业登记证截止时间',
      },
      nar1nnc1: {
        title: 'NAR1或NNC1',
        tooltip1: '公司成立超过一年，提供《周年申报表》NAR1',
        tooltip2: '公司成立未满一年，提供《法团成立表格》NNC1',
        downloadNAR1: '下载NAR1模板',
        downloadNNC1: '下载NNC1模板',
      },
      dco: {
        title: '董事在职证明COI或同等证明材料',
      },
      phone: '手机号',
      registerAddress: '街道地址',
      nameChange: '过去5年是否变更名称',
      formerName: '曾用名',
      corporationType: '企业类型',
      registerCapital: '注册资金',
      registerCapitalUnit: '百万',
      maaoa: {
        title: '组织大纲及章程',
        downloadTemplate: '下载模板',
      },
      bam: {
        title: '业务真实性材料',
        tooltip: '请提供能证明贵司真实业务的信息，如办公室照片/一段业务描述或交易订单等。',
      },
      registerApartment: '公寓或楼层(选填)',
      registerCity: '城市',
      registerProv: '省/州',
      registerPostalCode: '邮编',
      businessAddrEqualRegisterAddr: '商业登记地址是否与企业注册地址相同',
      businessApartment: '公寓或楼层(选填)',
      businessCity: '城市',
      businessProv: '省/州',
      businessPostalCode: '邮编',
      businessAddress: '街道地址',
    },
    placeholder: {
      select: '请选择',
      input: '请输入',
    },
    validation: {
      uploadCi: '请上传企业注册证书',
      uploadBr: '请上传商业登记证书',
      uploadNar1: '请上传NAR1或NNC1',
      uploadDco: '请上传董事在职证明COI或同等证明材料',
      uploadMaaoa: '请上传组织大纲及章程',
      uploadBam: '请上传业务真实性材料',
      registCertificateNumber: '请输入注册证书编号',
      registerCode: '请选择公司注册地',
      establishDate: '请选择成立时间',
      englishName: '请输入英文名称',
      businessCertificateNumber: '请输入商业登记证号码',
      businessAddress: '请输入商业登记地址',
      businessCertificateEndTime: '请选择商业登记证时间',
      selectAreaCode: '请选择区号',
      inputPhone: '请输入电话',
      pastFiveYearsChangeAme: '请选择过去五年变更名称',
      formerName: '请输入曾用名',
      corporationType: '请选择企业类型',
      registerCapital: '请输入注册资本',
      registerAddress: '请输入注册地址',
      registerProv: '请输入注册省/州',
      registerCity: '请输入注册城市',
      registerApartment: '请输入注册公寓或楼层',
      registerPostalCode: '请输入注册邮编',
      businessProv: '请输入商业登记地所在省/州',
      businessCity: '请输入商业登记地所在城市',
      businessApartment: '请输入商业登记地所在公寓或楼层',
      businessPostalCode: '请输入商业登记地邮编',
      registerCapitalUnit: '请选择注册资金币种',
      businessPostalCodeTooltip: '若您所在的地区没有邮编则填写0000',
    },
  },
  businessInfo: {
    title: '经营信息',
    businessAddrEqualRegisterAddr: '营业地址是否与企业注册地址相同',
    website: '营业网址(选填)',
    businessAddress: '营业地址',
    mainBusinessAddress: '主营业务所在地',
    industryInfo: '经营行业信息',
    firstFundingSource: '原始资金来源',
    wealthSource: '财富来源',
    continuousFundingSource: '持续资金来源',
    salesLastYear: '去年销售额(选填)',
    staffNumber: '企业员工人数(选填)',
    settlePurpose: '开户目的',
    million: '百万',
    placeholder: {
      select: '请选择',
      input: '请输入',
      website: '请输入，如：https://xxx.xxx.xxx',
    },
    rules: {
      businessAddress: '请输入营业地址',
      mainBusinessAddress: '请选择主营业务所在地',
      industryList: '请选择经营行业信息',
      firstFundingSource: '请选择原始资金来源',
      wealthSource: '请选择财富来源',
      continuousFundingSource: '请选择持续资金来源',
      settlePurpose: '请选择开户目的',
      businessApartment: '请输入公寓或楼层',
      businessCity: '请输入城市',
      businessProv: '请输入省/州',
      businessPostalCode: '请输入邮编',
      website: '请输入，如：https://xxx.xxx.xxx',
    },
  },
  rejected: {
    greeting: '尊敬的{email}您好，',
    message1:
      '感谢您使用我们的服务，并提交企业认证申请。经过我们系统和合规团队的审核，很遗憾地通知您，本次提交的企业信息未能通过认证。由于所提供的企业主体与实际业务不符或存在信息不一致，该企业账户已被系统标记为不可再申请，无法继续进行认证或使用相关服务。',
    message2:
      '我们非常理解这可能给您带来一定的不便，也感谢您对平台合规流程的理解与支持。如果您拥有其他合法合规的企业主体，并希望继续使用我们的服务，欢迎您重新提交认证申请，我们将尽快为您审核。',
    message3: '如有疑问，您也可随时通过官网内客户服务或邮件与我们联系，我们将竭诚协助您解决问题。',
    thanks1: '感谢您的理解与配合！',
    regards1: '顺颂商祺，',
    team: '鼎DingX运营团队',
    contact: '<EMAIL> ｜ www.dingexchange.com',
    backToLogin: '返回登录',
  },
  initial: {
    title: '认证您的鼎账户即可解锁服务',
    desc: '您在鼎的旅程才刚刚开始。只需完成我们对于企业信息的认证，即可解锁我们为您打造的所有精彩功能。',
    wait: '您好！请等待我们通知您审核结果',
    button: '立刻启程',
  },
};
