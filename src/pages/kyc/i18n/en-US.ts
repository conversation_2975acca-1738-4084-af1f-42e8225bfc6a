export default {
  nav: {
    companyInfo: 'Company Qualification',
    businessInfo: 'Business Information',
    shareholderInfo: 'Shareholder Information',
    directorInfo: 'Director Information',
    managerInfo: 'Manager Information',
    additionalInfo: 'Additional Information',
  },
  alert: {
    error: 'Your company authentication information is incorrect, please check and modify, or',
    suggestion: 'Click to view modification suggestions',
  },
  button: {
    giveUp: 'Give Up Authentication',
    saveDraft: 'Save Draft',
    submit: 'Submit for Review',
    completed: 'Modification Completed',
  },
  dialog: {
    giveUpTitle: 'Give Up Authentication?',
    giveUpMessage:
      'Are you sure you want to give up authentication? Please note that giving up will delete all information you have filled in.',
    confirm: 'Confirm',
    cancel: 'Cancel',
    saveSuccess: 'Saved Successfully',
    submitTitle: 'Confirm Completion and Submit?',
    submitMessage:
      'Please confirm the authenticity and completeness of your submitted materials. Once submitted, they cannot be modified. Please wait patiently.',
    submit: 'Submit',
    thinkAgain: 'Think Again',
    thanksTitle: 'Thank you for your cooperation, please wait for our notification',
    thanksMessage:
      'The information and materials you have supplemented have been submitted for review. We will expedite your process! If you have any special circumstances, please contact our operations staff.',
    gotIt: 'Got it',
    congratsTitle: 'Congratulations, you are one step closer to the Web3 journey!',
    congratsMessage:
      'Your materials have been submitted for review, which will take about 1-3 working days! If you have any special circumstances, please contact our operations staff.',
  },
  certificationNav: {
    title: 'Certification Directory',
  },
  messages: {
    saveSuccess: 'Saved successfully',
  },
  opinions: {
    title: 'Review Opinions and Modification Suggestions',
    suggestion: 'Please modify the content of {moduleName} according to the following suggestions',
    companyQualification: 'Company Qualification',
    businessInfo: 'Business Information',
    shareholderInfo: 'Shareholder Information',
    directorInfo: 'Director Information',
    managerInfo: 'Manager Information',
    additionalInfo: 'Additional Information',
  },
  shareholderInfo: {
    title: 'Shareholder Information',
    description:
      'Shareholders with a holding or income ownership of 10% or more natural persons shareholders/final beneficiaries are required to provide. If there are more than one natural persons shareholders/final beneficiaries, please add natural persons shareholders/final beneficiaries to continue to provide corresponding information. You can add or modify natural persons shareholders or final beneficiaries in this page, up to 10.',
    index: 'index',
    shareholderType: 'Shareholder',
    shareholderTypeDesc: 'Natural Person Shareholder/Final Beneficiary',
    progress: 'Progress',
    edit: 'Edit',
    delete: 'Delete',
    preview: 'preview',
    tableEmpty: 'No Data',
    addShareholder: 'Add Shareholder',
    editShareholder: 'Edit Shareholder',
    deleteShareholder: 'Delete Shareholder',
    action: 'Action',
    notRequired: 'Not Required',
    shareholder: 'Shareholder',
    finalBeneficiary: 'Final Beneficiary',
    shareholdingRatio: 'Shareholding Ratio',
    nationality: 'Nationality',
    idType: 'ID Type',
    enFirstName: 'English First Name',
    enLastName: 'English Last Name',
    cnFirstName: 'Chinese First Name',
    cnLastName: 'Chinese Last Name',
    firstName: 'First Name',
    lastName: 'Last Name',
    expirationStart: 'Expiration Start',
    expirationStartAbb: 'Start',
    expirationEnd: 'Expiration End',
    expirationLongTerm: 'Long Term',
    expirationEndAbb: 'End',
    expiration: 'Expiration',
    gender: 'Gender',
    idNumber: 'ID Number',
    residenceCountry: 'Residence Country',
    residenceAddress: 'Residence Address',
    identityAuthentication: 'Identity Authentication',
    formValidateFile: 'Please upload identity authentication file',
    identityAuthenticationDescription:
      'When you are trading or operating on our platform, to ensure the authenticity and security of the transaction, you need to complete a specific level of identity verification:',
    identityAuthenticationDescription1:
      '1. Please authenticate the ID type you have selected above;',
    identityAuthenticationDescription2:
      '2. The identity documents need to be submitted with the identity information consistent with the company registration certificate; the identity documents need to be within the validity period.',
    idTypeEnumPASSPORT: 'Passport',
    idTypeEnumSPR: 'Singapore Permanent Resident',
    idTypeEnumSCC: 'Singapore Citizen Card',
    idTypeEnumHK2003: 'Hong Kong ID Card 2003',
    idTypeEnumHK2018: 'Hong Kong ID Card 2018',
    idTypeEnumID: 'ID Card',
    genderEnumMale: 'Male',
    genderEnumFemale: 'Female',
    formValidateRatio: 'Please enter shareholding ratio',
    formValidateRatioRange: 'Please enter a shareholding ratio between 0 and 100',
    formValidateRatioRange2: 'Shareholding ratio must be greater than 0',
    formValidateNationality: 'Please select nationality',
    formValidateIdType: 'Please select ID type',
    formValidateEnFirstName: 'Please enter English first name',
    formValidateEnLastName: 'Please enter English last name',
    formValidateEnName: 'Please enter English name',
    formValidateCnFirstName: 'Please enter Chinese first name',
    formValidateCnLastName: 'Please enter Chinese last name',
    formValidateCnName: 'Please enter Chinese name',
    formValidateExpirationStart: 'Please select expiration start',
    formValidateExpirationEnd: 'Please select expiration end',
    formValidateExpirationError: 'Please select a correct expiration',
    statusWaiting: 'Waiting',
    statusCompleted: 'Completed',
    statusModified: 'Modified',
    statusApproved: 'Approved',
    formValidateGender: 'Please select gender',
    formValidateIdNumber: 'Please enter ID number',
    formValidateResidenceCountry: 'Please select residence country',
    formValidateResidenceAddress: 'Please enter residence address',
    selectPlaceholder: 'Please select',
    formValidateIdNumberExist: 'Already exists shareholder with the same ID number',
    deleteConfirmContent:
      'After deletion, you will lose all information and materials filled in by this shareholder. Note: If you have synchronized this shareholder in the director and manager information, the corresponding information will also be lost, and you will need to fill it in again if necessary.',
    deleteConfirmTitle: 'Are you sure you want to delete this shareholder information?',
    managerSyncWarningContent:
      '    The shareholding of this shareholder has been modified to less than 10%. Since this shareholder was previously synchronized as manager information, this operation will automatically delete the synchronized manager information.\n    You need to reselect a shareholder/director with a shareholding of 10% or more as your new manager. Are you sure you want to continue?',
    managerSyncWarningTitle: 'Shareholding change will affect the synchronized manager',
    ensureDesc:
      'I certify that I have provided all accurate information about natural persons shareholders/final beneficiaries who hold at least 10% of the shares',
    ensure: 'Please check this box',
  },
  directorInfo: {
    title: 'Director Information',
    description:
      'You can add or delete directors and key controllers in this page, up to 10. If the member is also a shareholder of your company and you have completed the corresponding information, you can import the data quickly.',
    directorType: 'Director',
    directorTypeDesc: 'Director/Key Controller',
    dispatchSyncStatusDesc: 'Synced from shareholder',
    addDirector: 'Add Director',
    syncShareholderInfo: 'Sync Shareholder Info',
    syncShareholderDesc:
      'Please note that after synchronizing shareholder information, you can only view the information of this shareholder. If you want to modify, please go to the corresponding shareholder information page to modify.',
    selectShareholder: 'Select Shareholder',
    formValidateIdNumberExist: 'Already exists director with the same ID number',
    coiTitle: 'Certificate of Incorporation (CI)',
    coiDesc:
      'Certificate of Incorporation (CI) or equivalent proof of directorship (non-Hong Kong region)',
    coiUploadTip:
      'Please upload certificate of incorporation (CI) or equivalent proof of directorship (non-Hong Kong region)',
  },
  managerInfo: {
    title: 'Manager Information',
    description:
      'The manager is authorized by the company to log in and operate the account, sign all documents and handle all matters related to the company. You will pick one of the options below to confirm your identity.',
    description2:
      "Please note that you are picking the current account as the manager's identity, please select your identity type.",
    managerType: 'Manager Type',
    managerTypeDesc: 'Shareholder/Director/Actual Controller (10% above)',
    managerTypeDesc2: 'Authorized Manager',
    addManager: 'Add Manager',
    shareholderName: 'I am',
    authorizationLetter: 'Authorization Letter',
    downloadTemplate: 'Download Template',
    identityAuthentication: 'Identity Authentication',
    identityAuthenticationDescription:
      'When you are trading or operating on our platform, to ensure the authenticity and security of the transaction, you need to complete a specific level of identity verification:',
    identityAuthenticationDescription1:
      '1. Please authenticate the ID type you have selected above;',
    identityAuthenticationDescription2:
      '2. The identity documents need to be submitted with the identity information consistent with the company registration certificate; the identity documents need to be within the validity period.',
    formValidateAuthorizationLetter: 'Please upload authorization letter',
    formValidateManagerType: 'Please select manager type',
    formValidateManagerName: 'Please select manager name',
  },
  companyInfo: {
    title: 'Company Qualification',
    companyInfo: 'Company Information',
    submitRelatedData: 'Submit Related Data',
    registerLocation: {
      title: 'Registration Address',
      description:
        "Please review your company's registered information and select the registration location listed on the certificate below.",
      label: 'Company Registration Location',
    },
    qualification: {
      title: 'Company Qualification',
      ci: {
        title: 'Certificate of Incorporation (CI)',
        downloadTemplate: 'Download Template',
      },
      registrationNumber: 'Registration Certificate Number',
      establishDate: 'Establishment Date',
      englishName: 'English Name',
      chineseName: 'Chinese Name',
      optional: '(Optional)',
      br: {
        title: 'Business Registration Certificate (BR)',
        downloadTemplate: 'Download Template',
        number: 'Business Registration Number',
        address: 'Business Registration Address',
        endTime: 'Expiration Date of Business Registration',
      },
      nar1nnc1: {
        title: 'NAR1 or NNC1',
        tooltip1: 'For companies established for more than one year, provide Annual Return (NAR1)',
        tooltip2:
          'For companies established for less than one year, provide Incorporation Form (NNC1)',
        downloadNAR1: 'Download NAR1 Template',
        downloadNNC1: 'Download NNC1 Template',
      },
      dco: {
        title: "Director's Certificate of Incumbency or Equivalent Supporting Materials",
      },
      phone: 'Phone Number',
      registerAddress: 'Street Address',
      nameChange: 'Has the name changed in the past 5 years',
      formerName: 'Former Name',
      corporationType: 'Company Type',
      registerCapital: 'Registered Capital',
      registerCapitalUnit: 'Million',
      maaoa: {
        title: 'Memorandum and Articles of Association',
        downloadTemplate: 'Download Template',
      },
      bam: {
        title: 'Business Authenticity Materials',
        tooltip:
          "Please provide information that proves your company's authentic business, such as office photos, business descriptions, or transaction orders.",
      },
      registerApartment: 'Apartment or Floor (Optional)',
      registerCity: 'City',
      registerProv: 'Province/State',
      registerPostalCode: 'Postal Code',
      businessAddrEqualRegisterAddr:
        'Is the business registration address the same as the company registration address?',
      businessApartment: 'Apartment or Floor (Optional)',
      businessCity: 'City',
      businessProv: 'Province/State',
      businessPostalCode: 'Postal Code',
      businessAddress: 'Street Address',
    },
    placeholder: {
      select: 'Please select',
      input: 'Please enter',
    },
    validation: {
      uploadCi: 'Please upload Certificate of Incorporation',
      uploadBr: 'Please upload Business Registration Certificate',
      uploadNar1: 'Please upload NAR1 or NNC1',
      uploadDco:
        "Please upload Director's Certificate of Incumbency or equivalent supporting materials",
      uploadMaaoa: 'Please upload Memorandum and Articles of Association',
      uploadBam: 'Please upload Business Authenticity Materials',
      registCertificateNumber: 'Please enter registration certificate number',
      registerCode: 'Please select company registration location',
      establishDate: 'Please select establishment date',
      englishName: 'Please enter English name',
      businessCertificateNumber: 'Please enter business registration certificate number',
      businessAddress: 'Please enter business registration address',
      businessCertificateEndTime: 'Please select business registration certificate expiry date',
      selectAreaCode: 'Please select area code',
      inputPhone: 'Please enter phone number',
      pastFiveYearsChangeAme: 'Please select whether the name has changed in the past 5 years',
      formerName: 'Please enter former name',
      corporationType: 'Please select company type',
      registerCapital: 'Please enter registered capital',
      registerAddress: 'Please enter registration address',
      registerProv: 'Please enter registration province/state',
      registerCity: 'Please enter registration city',
      registerApartment: 'Please enter registration apartment or floor',
      registerPostalCode: 'Please enter registration postal code',
      businessProv: 'Please enter business registration province/state',
      businessCity: 'Please enter business registration city',
      businessApartment: 'Please enter business registration apartment or floor',
      businessPostalCode: 'Please enter business registration postal code',
      registerCapitalUnit: 'Please select registered capital unit',
      businessPostalCodeTooltip: 'If you do not have a postal code, please enter 0000',
    },
  },
  businessInfo: {
    title: 'Business Information',
    businessAddrEqualRegisterAddr:
      'Is the business address the same as the company registration address?',
    website: 'Business Website (Optional)',
    businessAddress: 'Business Address',
    mainBusinessAddress: 'Main Business Location',
    industryInfo: 'Industry Information',
    firstFundingSource: 'Original Source of Funds',
    wealthSource: 'Source of Wealth',
    continuousFundingSource: 'Continuous Source of Funds',
    salesLastYear: "Last Year's Sales (Optional)",
    staffNumber: 'Number of Employees (Optional)',
    settlePurpose: 'Account Opening Purpose',
    million: 'Million',
    placeholder: {
      select: 'Please select',
      input: 'Please enter',
    },
    rules: {
      businessAddress: 'Please enter business address',
      mainBusinessAddress: 'Please select main business location',
      industryList: 'Please select industry information',
      firstFundingSource: 'Please select original source of funds',
      wealthSource: 'Please select source of wealth',
      continuousFundingSource: 'Please select continuous source of funds',
      settlePurpose: 'Please select account opening purpose',
      businessApartment: 'Please enter apartment or floor',
      businessCity: 'Please enter city',
      businessProv: 'Please enter province/state',
      businessPostalCode: 'Please enter postal code',
    },
  },
  additionalInfo: {
    title: 'Additional Information',
    supplementaryMaterials: 'Supplementary Materials (Optional)',
    supplementaryMaterialsDesc:
      'This item is optional. If you have additional needs, please contact your sales or customer service.',
    agreement: {
      text: 'I have read and agree: This platform can provide compliant financial services in certain regions, and only provide consultation and channel recommendation services in other regions. All fund and asset operations are independently completed by local compliant third-party institutions. For details, please',
      consultationService: 'view agreement',
      andOther: '.',
    },
    rules: {
      maaoaFileList: 'Please upload Memorandum and Articles of Association',
      bamFileList: 'Please upload Business Authenticity Materials',
      financialAgreementFlag: 'Please agree to the agreement',
    },
    downloadMaaoaUrl: 'https://files.dingx.tech/kyc/en/%E7%AB%A0%E7%A8%8B.png',
    downloadTemplateUrl:
      'https://files.dingx.tech/ding_zonetop_prod/********/a6b5dc502dc34ac8a0567e33e6cecaf9.pdf',
  },
  rejected: {
    greeting: 'Dear {email},',
    message1:
      'Thank you for using our service and submitting your company certification application. After review by our system and compliance team, we regret to inform you that the submitted company information did not pass the certification. Due to inconsistencies between the provided company entity and actual business or other information discrepancies, this company account has been marked as ineligible for further applications and cannot continue to be certified or use related services.',
    message2:
      "We understand this may cause you some inconvenience, and we appreciate your understanding and support of our platform's compliance process. If you have another legitimate and compliant company entity and wish to continue using our services, you are welcome to submit a new certification application, and we will review it as soon as possible.",
    message3:
      'If you have any questions, you can always contact us through customer service on our official website or by email, and we will do our best to assist you.',
    thanks1: 'Thank you for your understanding and cooperation!',
    regards1: 'Best regards,',
    team: 'DingX Operations Team',
    contact: '<EMAIL> ｜ www.dingexchange.com',
    backToLogin: 'Back to Login',
  },
  initial: {
    title: 'Unlock services by verifying your Ding account',
    desc: 'Your journey with Ding has just begun. Complete our business information verification to unlock all the exciting features we have prepared for you.',
    wait: 'Hello! Please wait for our notification of the review result.',
    button: 'Start Now',
  },
};
