import {
  CorpShareholderVO,
  CorpDirectorInfoVO,
  EditShareholderInfo,
  EditDirectorInfo,
  EditManagerInfo,
  corpQualificationInfoVO,
  corpBusinessInfoVO,
  CorpSupplementInfoVO,
  ManagerTypeEnum,
} from '../apis/type';
import { t } from '@@/i18n';

export const useFormData = () => {
  // 验证股东信息是否合法，数据是否都完整
  const validateShareholderInfo = (corpShareholderVO: CorpShareholderVO) => {
    const shareholderInfos: EditShareholderInfo[] = corpShareholderVO.corpShareholderInfoVOs || [];
    // 如果对象为空或未定义，返回 code 0
    if (!shareholderInfos || shareholderInfos?.length === 0) {
      return { errMsg: '', code: 0 };
    }
    for (const shareholderInfo of shareholderInfos) {
      if (!shareholderInfo || Object.keys(shareholderInfo).length === 0) {
        return { errMsg: '', code: 0 };
      }

      if (!shareholderInfo.shareholdingRatio) {
        return { errMsg: t('shareholderInfo.formValidateRatio'), code: 2 };
      }
      if (!shareholderInfo.shareholdingRatio) {
        return { errMsg: t('shareholderInfo.formValidateRatioRange'), code: 2 };
      }
      if (!shareholderInfo.nationality) {
        return { errMsg: t('shareholderInfo.formValidateNationality'), code: 2 };
      }
      if (!shareholderInfo.idType) {
        return { errMsg: t('shareholderInfo.formValidateIdType'), code: 2 };
      }
      if (!shareholderInfo.enFirstName) {
        return { errMsg: t('shareholderInfo.formValidateEnFirstName'), code: 2 };
      }
      if (!shareholderInfo.enLastName) {
        return { errMsg: t('shareholderInfo.formValidateEnLastName'), code: 2 };
      }
      // if (!shareholderInfo.cnFirstName) {
      //   return {errMsg: t('shareholderInfo.formValidateCnFirstName'), code: 2}
      // }
      // if (!shareholderInfo.cnLastName) {
      //   return {errMsg: t('shareholderInfo.formValidateCnLastName'), code: 2}
      // }
      if (!shareholderInfo.expirationStart) {
        return { errMsg: t('shareholderInfo.formValidateExpirationError'), code: 2 };
      }
      if (!shareholderInfo.expirationEnd) {
        return { errMsg: t('shareholderInfo.formValidateExpirationError'), code: 2 };
      }
      if (!shareholderInfo.gender) {
        return { errMsg: t('shareholderInfo.formValidateGender'), code: 2 };
      }
      if (!shareholderInfo.idNumber) {
        return { errMsg: t('shareholderInfo.formValidateIdNumber'), code: 2 };
      }
      if (!shareholderInfo.residenceCountry) {
        return { errMsg: t('shareholderInfo.formValidateResidenceCountry'), code: 2 };
      }
      if (!shareholderInfo.residenceAddress) {
        return { errMsg: t('shareholderInfo.formValidateResidenceAddress'), code: 2 };
      }
    }

    if (corpShareholderVO.shareholderEnsure != 'Y') {
      return { errMsg: t('shareholderInfo.ensure'), code: 2 };
    }
    // 所有验证通过，返回 code 1
    return { errMsg: '', code: 1 };
  };

  // 验证董事信息是否合法，数据是否都完整
  const validateDirectorInfo = (corpDirectorInfoVO: CorpDirectorInfoVO, registerCode: string) => {
    const directorInfos: EditDirectorInfo[] = corpDirectorInfoVO.corpDirectorInfoVOs || [];
    const coiFileids = corpDirectorInfoVO.fileSeqIds || [];
    if (!directorInfos || directorInfos?.length === 0) {
      return { errMsg: '', code: 0 };
    }
    if (registerCode !== 'HK' && coiFileids.length === 0) {
      return { errMsg: t('directorInfo.coiUploadTip'), code: 2 };
    }

    for (const directorInfo of directorInfos) {
      // 如果对象为空或未定义，返回 code 0
      if (!directorInfo || Object.keys(directorInfo).length === 0) {
        return { errMsg: '', code: 0 };
      }
      if (directorInfo.shareholdingSynFlag === 'Y' && directorInfo.shareholdingSeqId === '') {
        return { errMsg: t('shareholderInfo.selectShareholder'), code: 2 };
      }
      if (!directorInfo.nationality) {
        return { errMsg: t('shareholderInfo.formValidateNationality'), code: 2 };
      }
      if (!directorInfo.idType) {
        return { errMsg: t('shareholderInfo.formValidateIdType'), code: 2 };
      }
      if (!directorInfo.enFirstName) {
        return { errMsg: t('shareholderInfo.formValidateEnFirstName'), code: 2 };
      }
      if (!directorInfo.enLastName) {
        return { errMsg: t('shareholderInfo.formValidateEnLastName'), code: 2 };
      }
      // if (!directorInfo.cnFirstName) {
      //   return {errMsg: t('shareholderInfo.formValidateCnFirstName'), code: 2}
      // }
      // if (!directorInfo.cnLastName) {
      //   return {errMsg: t('shareholderInfo.formValidateCnLastName'), code: 2}
      // }
      if (!directorInfo.expirationStart) {
        return { errMsg: t('shareholderInfo.formValidateExpirationError'), code: 2 };
      }
      if (!directorInfo.expirationEnd) {
        return { errMsg: t('shareholderInfo.formValidateExpirationError'), code: 2 };
      }
      if (!directorInfo.gender) {
        return { errMsg: t('shareholderInfo.formValidateGender'), code: 2 };
      }
      if (!directorInfo.idNumber) {
        return { errMsg: t('shareholderInfo.formValidateIdNumber'), code: 2 };
      }
      if (!directorInfo.residenceCountry) {
        return { errMsg: t('shareholderInfo.formValidateResidenceCountry'), code: 2 };
      }
      if (!directorInfo.residenceAddress) {
        return { errMsg: t('shareholderInfo.formValidateResidenceAddress'), code: 2 };
      }
    }
    // 所有验证通过，返回 code 1
    return { errMsg: '', code: 1 };
  };

  // 验证管理者是否合法
  const validateManagerInfo = (managerInfo: EditManagerInfo) => {
    // 如果对象为空或未定义，返回 code 0
    if (!managerInfo || Object.keys(managerInfo).length === 0) {
      return { errMsg: '', code: 0 };
    }

    if (!managerInfo.managerType) {
      return { errMsg: t('managerInfo.formValidateManagerType'), code: 2 };
    }
    if (managerInfo.managerType === ManagerTypeEnum.S) {
      if (!managerInfo.shareholdingSeqId) {
        return { errMsg: t('managerInfo.formValidateManagerName'), code: 2 };
      }
    }
    if (managerInfo.managerType === ManagerTypeEnum.M) {
      if (!managerInfo.fileInfos?.filter((item) => item?.fileType === 'ALFAM').length) {
        return { errMsg: t('managerInfo.formValidateAuthorizationLetter'), code: 2 };
      }
      if (!managerInfo.fileInfos?.filter((item) => item?.fileType === 'MIAD').length) {
        return { errMsg: t('shareholderInfo.formValidateFile'), code: 2 };
      }
      if (!managerInfo.nationality) {
        return { errMsg: t('shareholderInfo.formValidateNationality'), code: 2 };
      }
      if (!managerInfo.idType) {
        return { errMsg: t('shareholderInfo.formValidateIdType'), code: 2 };
      }
      if (!managerInfo.enFirstName) {
        return { errMsg: t('shareholderInfo.formValidateEnFirstName'), code: 2 };
      }
      if (!managerInfo.enLastName) {
        return { errMsg: t('shareholderInfo.formValidateEnLastName'), code: 2 };
      }
      if (!managerInfo.expirationStart) {
        return { errMsg: t('shareholderInfo.formValidateExpirationError'), code: 2 };
      }
      if (!managerInfo.expirationEnd) {
        return { errMsg: t('shareholderInfo.formValidateExpirationError'), code: 2 };
      }
      if (!managerInfo.gender) {
        return { errMsg: t('shareholderInfo.formValidateGender'), code: 2 };
      }
      if (!managerInfo.idNumber) {
        return { errMsg: t('shareholderInfo.formValidateIdNumber'), code: 2 };
      }
      if (!managerInfo.residenceCountry) {
        return { errMsg: t('shareholderInfo.formValidateResidenceCountry'), code: 2 };
      }
      if (!managerInfo.residenceAddress) {
        return { errMsg: t('shareholderInfo.formValidateResidenceAddress'), code: 2 };
      }
    }
    // 所有验证通过，返回 code 1
    return { errMsg: '', code: 1 };
  };

  // 验证企业信息是否合法，数据是否都完整
  const validateCompanyInfo = (companyInfo: corpQualificationInfoVO) => {
    if (Object.keys(companyInfo).length === 0) {
      return { errMsg: '', code: 0 };
    }
    if (!companyInfo.fileInfos) {
      return { errMsg: '请上传文件', code: 2 };
    }
    if (companyInfo.fileInfos?.filter((item) => item.fileType === 'ERC').length === 0) {
      return { errMsg: '请上传企业注册证书', code: 2 };
    }
    if (
      companyInfo.registerCode === 'HK' &&
      companyInfo.fileInfos?.filter((item) => item.fileType === 'BRC').length === 0
    ) {
      return { errMsg: '请上传商业登记证书', code: 2 };
    }
    if (
      companyInfo.registerCode === 'HK' &&
      companyInfo.fileInfos?.filter((item) => item.fileType === 'NAR1NNC1').length === 0
    ) {
      return { errMsg: '请上传NAR1或NNC1', code: 2 };
    }
    if (companyInfo.registerCode === 'HK' && !companyInfo.businessCertificateNumber) {
      return { errMsg: '请输入商业登记证编号', code: 2 };
    }
    if (companyInfo.registerCode === 'HK' && !companyInfo.businessCertificateEndTime) {
      return { errMsg: '请选择商业登记证截止时间', code: 2 };
    }
    if (!companyInfo.englishName) {
      return { errMsg: '请输入英文名称', code: 2 };
    }
    if (!companyInfo.establishDate) {
      return { errMsg: '请选择成立时间', code: 2 };
    }
    if (!companyInfo.registCertificateNumber) {
      return { errMsg: '请输入注册证书编号', code: 2 };
    }
    if (!companyInfo.registerCode) {
      return { errMsg: '请选择公司注册地', code: 2 };
    }
    if (companyInfo.pastFiveYearsChangeAme === 'Y' && !companyInfo.formerName) {
      return { errMsg: '请输入曾用名', code: 2 };
    }
    if (!companyInfo.phone) {
      return { errMsg: '请输入电话', code: 2 };
    }
    if (!companyInfo.phoneAreaCode) {
      return { errMsg: '请选择电话区号', code: 2 };
    }
    if (!companyInfo.registerAddress) {
      return { errMsg: '请输入注册地址', code: 2 };
    }
    if (!companyInfo.registerCapital) {
      return { errMsg: '请输入注册资本', code: 2 };
    }
    if (!companyInfo.fiatCcy) {
      return { errMsg: '请选择注册币种', code: 2 };
    }
    if (!companyInfo.registerPostalCode) {
      return { errMsg: '请输入注册地址邮政编码', code: 2 };
    }
    if (!companyInfo.fiatCcy) {
      return { errMsg: '请选择注册资本', code: 2 };
    }
    if (!companyInfo.corporationType) {
      return { errMsg: '请选择企业类型', code: 2 };
    }
    if (companyInfo.registerCode === 'HK' && !companyInfo.businessAddress) {
      return { errMsg: '请输入商业登记地址', code: 2 };
    }
    if (companyInfo.registerCode === 'HK' && !companyInfo.businessApartment) {
      return { errMsg: '请输入公寓或楼层', code: 2 };
    }
    if (companyInfo.registerCode === 'HK' && !companyInfo.businessCity) {
      return { errMsg: '请输入城市', code: 2 };
    }
    if (companyInfo.registerCode === 'HK' && !companyInfo.businessProv) {
      return { errMsg: '请输入省/州', code: 2 };
    }
    if (companyInfo.registerCode === 'HK' && !companyInfo.businessPostalCode) {
      return { errMsg: '请输入邮编', code: 2 };
    }
    return { errMsg: '', code: 1 };
  };

  // 验证经营信息是否合法，数据是否都完整
  const validateBusinessInfo = (businessInfo: corpBusinessInfoVO) => {
    if (Object.keys(businessInfo).length === 0) {
      return { errMsg: '', code: 0 };
    }
    if (!businessInfo.mainBusinessAddress) {
      return { errMsg: '请选择主营业务所在地', code: 2 };
    }
    if (!businessInfo.industry || !businessInfo.subIndustry) {
      return { errMsg: '请选择经营行业信息', code: 2 };
    }
    if (!businessInfo.firstFundingSource) {
      return { errMsg: '请选择经原始资金来源', code: 2 };
    }
    if (!businessInfo.wealthSource) {
      return { errMsg: '请选择财富来源', code: 2 };
    }
    if (!businessInfo.continuousFundingSource) {
      return { errMsg: '请选择持续资金来源', code: 2 };
    }
    if (!businessInfo.settlePurpose) {
      return { errMsg: '请选择开户目的', code: 2 };
    }
    if (!businessInfo.businessAddress) {
      return { errMsg: '请输入营业地址', code: 2 };
    }
    if (!businessInfo.businessApartment) {
      return { errMsg: '请输入公寓或楼层', code: 2 };
    }
    if (!businessInfo.businessCity) {
      return { errMsg: '请输入城市', code: 2 };
    }
    if (!businessInfo.businessProv) {
      return { errMsg: '请输入省/州', code: 2 };
    }
    if (!businessInfo.businessPostalCode) {
      return { errMsg: '请输入邮编', code: 2 };
    }
    return { errMsg: '', code: 1 };
  };

  const validateAdditionalInfo = (additionalInfo: CorpSupplementInfoVO) => {
    if (Object.keys(additionalInfo).length === 0) {
      return { errMsg: '', code: 0 };
    }
    if (additionalInfo.financialAgreementFlag !== 'Y') {
      return { errMsg: '请同意协议', code: 2 };
    }
    if (!additionalInfo.fileInfos) {
      return { errMsg: '请上传文件', code: 2 };
    }
    if (additionalInfo.fileInfos?.filter((item) => item.fileType === 'MAAOA').length === 0) {
      return { errMsg: '请上传组织大纲及章程', code: 2 };
    }
    if (additionalInfo.fileInfos?.filter((item) => item.fileType === 'BAM').length === 0) {
      return { errMsg: '请上传业务真实性材料', code: 2 };
    }
    return { errMsg: '', code: 1 };
  };

  return {
    validateShareholderInfo,
    validateDirectorInfo,
    validateManagerInfo,
    validateCompanyInfo,
    validateBusinessInfo,
    validateAdditionalInfo,
  };
};
