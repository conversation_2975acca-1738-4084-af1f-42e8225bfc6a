import { GenderEnum, StatusEnum, ManagerTypeEnum } from '../apis/type';
import { t } from '@@/i18n';
import { IdTypeEnum } from '../apis/type';
import { useEnumStore } from '@/pinia/stores/enumStore'
import { BusinessEnumType } from '@@/apis/common/type';

const enumStore = useEnumStore();
// 在UI组件内可能用到一些功能方法
export const useComponent = () => {
  
  const getStatusClass = (status: StatusEnum) => {
    switch (status) {
      case StatusEnum.I:
        return '#ACAEB3'
      case StatusEnum.D:
        return '#3EB342'
      case StatusEnum.E:
        return '#FD3627'
      case StatusEnum.S:
        return '#3EB342'
      default:
        return ''
    }
  }

  const getStatusText = (status: StatusEnum) => {
    switch (status) {
      case StatusEnum.I:
        return t('shareholderInfo.statusWaiting')
      case StatusEnum.D:
        return t('shareholderInfo.statusCompleted')
      case StatusEnum.E:
        return t('shareholderInfo.statusModified')
      case StatusEnum.S:
        return t('shareholderInfo.statusApproved')
      default:
        return ''
    }
  }

  // 性别选项
  const getGenderOptions = () => [
    { label: t('shareholderInfo.genderEnumMale'), value: GenderEnum.M },
    { label: t('shareholderInfo.genderEnumFemale'), value: GenderEnum.F },
  ]
  // 管理者类型枚举
  const getManagerTypeOptions = () => [
    { label: t('managerInfo.managerTypeDesc'), value: ManagerTypeEnum.S },
    { label: t('managerInfo.managerTypeDesc2'), value: ManagerTypeEnum.M },
  ]


  // // 香港证件类型枚举
  // const idTypeHKOptions = () => [
  //   { label: t('shareholderInfo.idTypeEnumPASSPORT'), value: IdTypeEnum.PASSPORT },
  //   { label: t('shareholderInfo.idTypeEnumHK2003'), value: IdTypeEnum.HK2003 },
  //   { label: t('shareholderInfo.idTypeEnumHK2018'), value: IdTypeEnum.HK2018 },
  // ]
  // // 新加坡类型枚举
  // const idTypeSGPOptions = () => [
  //   { label: t('shareholderInfo.idTypeEnumPASSPORT'), value: IdTypeEnum.PASSPORT },
  //   { label: t('shareholderInfo.idTypeEnumSPR'), value: IdTypeEnum.SPR },
  //   { label: t('shareholderInfo.idTypeEnumSCC'), value: IdTypeEnum.SCC },
  // ]
  // // 中国大陆类型枚举
  // const idTypePRCOptions = () => [
  //   { label: t('shareholderInfo.idTypeEnumID'), value: IdTypeEnum.ID },
  // ]

  // // 其他地区类型枚举
  // const idTypeOtherOptions = () => [
  //   { label: t('shareholderInfo.idTypeEnumPASSPORT'), value: IdTypeEnum.PASSPORT },
  // ]
  // // 根据国籍获取证件类型枚举
  // const getIdTypeOptions = (nationality: string) => {
  //   if (nationality === 'HK') {
  //     return idTypeHKOptions()
  //   } else if (nationality === 'SG') {
  //     return idTypeSGPOptions()
  //   }  else if (nationality === 'PRC') {
  //     return idTypePRCOptions()
  //   }else {
  //     return idTypeOtherOptions()
  //   }
  // }

  // 香港证件类型枚举
  const idTypeHK = [IdTypeEnum.PASSPORT, IdTypeEnum.HK2003, IdTypeEnum.HK2018]
  // 新加坡类型枚举
  const idTypeSGP = [IdTypeEnum.PASSPORT, IdTypeEnum.SPR, IdTypeEnum.SCC]
  // 中国大陆类型枚举
  const idTypePRC = [IdTypeEnum.PASSPORT, IdTypeEnum.ID]
  // 其他地区类型枚举
  const idTypeOther = [IdTypeEnum.PASSPORT]
  // 根据国籍获取证件类型枚举
  const getIdTypeOptions = (nationality: string) => {
    // 证件类型枚举全量数据
    const idTypesOptions = enumStore.getEnumList(BusinessEnumType.ID_TYPE)
    if (nationality === 'HK') {
      const idTypeHKOptions = idTypesOptions.value.map(item => ({
        label: item.enumDescCn,
        value: item.enumCode as IdTypeEnum
      })).filter(item => idTypeHK.includes(item.value))
      return idTypeHKOptions
    } else if (nationality === 'SG') {
      const idTypeSGPOptions = idTypesOptions.value.map(item => ({
        label: item.enumDescCn,
        value: item.enumCode as IdTypeEnum
      })).filter(item => idTypeSGP.includes(item.value))
      return idTypeSGPOptions
    }  else if (nationality === 'PRC') {
      const idTypePRCOptions = idTypesOptions.value.map(item => ({
        label: item.enumDescCn,
        value: item.enumCode as IdTypeEnum
      })).filter(item => idTypePRC.includes(item.value))
      return idTypePRCOptions
    }else {
      const idTypeOtherOptions = idTypesOptions.value.map(item => ({
        label: item.enumDescCn,
        value: item.enumCode as IdTypeEnum
      })).filter(item => idTypeOther.includes(item.value))
      return idTypeOtherOptions
    }
  }

  
  return {
    getStatusClass,
    getStatusText,
    getIdTypeOptions,
    getGenderOptions,
    getManagerTypeOptions,
  }
}
