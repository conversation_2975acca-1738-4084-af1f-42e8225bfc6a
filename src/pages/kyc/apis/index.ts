import type * as Kyc from "./type"
import { request } from "@/http/axios"

/** 查询企业认证信息 */
export function corpAuthApi(data: {}) {
  return request<Kyc.CorpAuthResponseData>({
    url: "corp/auth/query",
    method: "post",
    data
  })
}

/** 修改企业认证信息 */
export function modifyCorpAuthApi(data: Kyc.ModifyCorpAuthRequestData) {
  return request<Kyc.ModifyCorpAuthResponseData>({
    url: "corp/auth/modify",
    method: "post",
    data
  })
}

/** 企业认证审核 */
export function corpAuthApproveApi(data?: Kyc.CorpAuthApproveRequestData) {
  return request<string>({
    url: "corp/auth/approve",
    method: "post",
    data
  })
}
