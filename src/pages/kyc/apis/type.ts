export enum CorpModuleEnumType {
  /** 企业资质模块 */
  QUALIFICATION = 'QUALIFICATION',
  /** 企业经营模块 */
  BUSINESS = 'BUSINESS',
  /** 企业股东模块 */
  SHAREHOLDER = 'SHAREHOLDER',
  /** 企业董事模块 */
  DIRECTOR = 'DIRECTOR',
  /** 企业管理者模块 */
  MANAGER = 'MANAGER',
  /** 企业银行信息以及补充材料模块 */
  SUPPLEMENT = 'SUPPLEMENT',
}

/** 证件类型 */
export enum IdTypeEnum {
  /** 护照 */
  PASSPORT = 'PASSPORT',
  /** 新加坡永久居民 */
  SPR = 'SPR',
  /** 新加坡市民卡 */
  SCC = 'SCC',
  /** 中国香港身份证2003版 */
  HK2003 = 'HK2003',
  /** 中国香港身份证2018版 */
  HK2018 = 'HK2018',
  /** 大陆身份证 */
  ID = 'ID',
}

/** 性别枚举 */
export enum GenderEnum {
  /** 男 */
  M = 'M',
  /** 女 */
  F = 'F',
}

// 状态 I-待填写/D-已完成/E-待修改/S-已认证
export enum StatusEnum {
  /** 待填写 */
  I = 'I',
  /** 已完成 */
  D = 'D',
  /** 待修改 */
  E = 'E',
  /** 已认证 */
  S = 'S',
}

// 审核状态 I-默认/P-待审核/E-待修改/A-待三方认证/S-已认证
export enum ApproveStatusEnum {
  /** 默认 */
  I = 'I',
  /** 待审核 */
  P = 'P',
  /** 待修改 */
  E = 'E',
  /** 待三方认证 */
  A = 'A',
  /** 已认证 */
  S = 'S',
}

// 管理者类型，S-股东/董事/实际控制人(10%以上)，M-授权管理者
export enum ManagerTypeEnum {
  /** 股东/董事/实际控制人(10%以上) */
  S = 'S',
  /** 授权管理者 */
  M = 'M',
}

export interface FileInfo {
  /** 文件名称 */
  fileName?: string;
  /** 文件URL */
  fileUrl?: string;
  /** 文件序列ID */
  fileSeqId?: string;
  /** 文件原始名称 */
  fileOriginName?: string;
  /** 文件类型 */
  fileType?: string;
  /** 业务序列ID */
  businessSeqId?: string;
  /** 文件大小 */
  fileSize?: string;
}

export interface CorpShareholderVO {
  /** 本人证明已经提供所有关于持有该公司至少10%股份的人员的准确信息，Y-是，N-否 */
  shareholderEnsure?: string;
  /** 业务序列ID */
  businessSeqId?: string;
  /** 接口返回详情才存在 意见 */
  opinions?: string;
  /** 接口返回详情才存在 状态 */
  status?: StatusEnum | '';
  /** 接口返回详情才存在 审核状态 */
  approveStatus?: ApproveStatusEnum | '';
  /** 股东信息 */
  corpShareholderInfoVOs?: Array<EditShareholderInfo>;
}

export interface EditShareholderInfo {
  /** 接口返回详情才存在 文件信息 */
  fileInfos?: Array<FileInfo>;
  /** 提交编辑前端塞入 文件序列ID */
  fileSeqIds?: Array<string>;
  /** 业务序列ID */
  businessSeqId?: string;
  /** 持股比例 */
  shareholdingRatio?: string;
  /** 国籍 */
  nationality?: string;
  /** 证件类型，PASSPORT-护照/SPR-新加坡永久居民/SCC-新加坡市民卡/HK2003-中国香港身份证2003版/HK2018-中国香港身份证2018版 */
  idType?: IdTypeEnum | '';
  /** 英文名 */
  enFirstName?: string;
  /** 英文姓 */
  enLastName?: string;
  /** 中文名 */
  cnFirstName?: string;
  /** 中文姓 */
  cnLastName?: string;
  /** 开始日期 */
  expirationStart?: string;
  /** 结束日期 */
  expirationEnd?: string;
  /** 性别 */
  gender?: GenderEnum | '';
  /** 证件号码 */
  idNumber?: string;
  /** 居住国家 */
  residenceCountry?: string;
  /** 居住地址 */
  residenceAddress?: string;
}

export interface CorpDirectorInfoVO {
  /** 接口返回详情才存在 文件信息 */
  fileInfos?: Array<FileInfo>;
  /** 提交编辑前端塞入 文件序列ID */
  fileSeqIds?: Array<string>;
  /** 业务序列ID */
  businessSeqId?: string;
  /** 接口返回详情才存在 意见 */
  opinions?: string;
  /** 接口返回详情才存在 状态 */
  status?: StatusEnum | '';
  /** 接口返回详情才存在 审核状态 */
  approveStatus?: ApproveStatusEnum | '';
  /** 董事信息 */
  corpDirectorInfoVOs?: Array<EditDirectorInfo>;
}

export interface EditDirectorInfo {
  /** 接口返回详情才存在 文件信息 */
  fileInfos?: Array<FileInfo>;
  /** 提交编辑前端塞入 文件序列ID */
  fileSeqIds?: Array<string>;
  /** 业务序列ID */
  businessSeqId?: string;
  /** 国籍 */
  nationality?: string;
  /** 证件类型 */
  idType?: IdTypeEnum | '';
  /** 英文名 */
  enFirstName?: string;
  /** 英文姓 */
  enLastName?: string;
  /** 中文名 */
  cnFirstName?: string;
  /** 中文姓 */
  cnLastName?: string;
  /** 开始日期 */
  expirationStart?: string;
  /** 结束日期 */
  expirationEnd?: string;
  /** 性别 */
  gender?: GenderEnum | '';
  /** 证件号码 */
  idNumber?: string;
  /** 居住国家 */
  residenceCountry?: string;
  /** 居住地址 */
  residenceAddress?: string;
  /** 股东表唯一id */
  shareholdingSeqId?: string;
  /** 是否同步股东信息 Y-同步/N-不同步 */
  shareholdingSynFlag?: string;
  /** 接口返回详情才存在 状态 */
  status?: StatusEnum | '';
  /** 接口返回详情才存在 审核状态 */
  approveStatus?: ApproveStatusEnum | '';
  /** 接口返回详情才存在 意见 */
  opinions?: string;
}

export interface EditManagerInfo {
  /** 文件信息 */
  fileInfos?: Array<FileInfo>;
  /** 提交编辑前端塞入 文件序列ID */
  fileSeqIds?: Array<string>;
  /** 业务序列ID */
  businessSeqId?: string;
  /** 经理类型 */
  managerType?: ManagerTypeEnum | '';
  /** 经理名称 */
  managerName?: string;
  /** 国籍 */
  nationality?: string;
  /** 证件类型 */
  idType?: IdTypeEnum | '';
  /** 英文名 */
  enFirstName?: string;
  /** 英文姓 */
  enLastName?: string;
  /** 中文名 */
  cnFirstName?: string;
  /** 中文姓 */
  cnLastName?: string;
  /** 开始日期 */
  expirationStart?: string;
  /** 结束日期 */
  expirationEnd?: string;
  /** 性别 */
  gender?: GenderEnum | '';
  /** 证件号码 */
  idNumber?: string;
  /** 居住国家 */
  residenceCountry?: string;
  /** 居住地址 */
  residenceAddress?: string;
  /** 状态 */
  status?: StatusEnum | '';
  /** 审核状态 */
  approveStatus?: ApproveStatusEnum | '';
  /** 意见 */
  opinions?: string;
  /** 股东 */
  shareholdingSeqId?: string;
}

/** 企业资质信息 */
export interface corpQualificationInfoVO {
  /** 文件信息 */
  fileInfos?: Array<{
    /** 文件名称 */
    fileName?: string;
    /** 文件URL */
    fileUrl?: string;
    /** 文件序列ID */
    fileSeqId?: string;
    /** 文件原始名称 */
    fileOriginName?: string;
    /** 文件类型 */
    fileType?: string;
    /** 业务序列ID */
    businessSeqId?: string;
    /** 文件大小 */
    fileSize?: string;
  }>;
  /** 业务序列ID */
  businessSeqId?: string;
  /** 注册代码 */
  registerCode?: string;
  /** 注册地址 */
  registerAddress?: string;
  /** 注册证书号码 */
  registCertificateNumber?: string;
  /** 成立日期 */
  establishDate?: string;
  /** 英文名称 */
  englishName?: string;
  /** 中文名称 */
  chineseName?: string;
  /** 营业执照号码 */
  businessCertificateNumber?: string;
  /** 营业执照地址 */
  businessAddress?: string;
  /** 营业执照结束时间 */
  businessCertificateEndTime?: string;
  /** 电话 */
  phone?: string;
  /** 电话区号 */
  phoneAreaCode?: string;
  /** 过去五年变更名称 */
  pastFiveYearsChangeAme?: string;
  /** 曾用名 */
  formerName?: string;
  /** 企业类型 */
  corporationType?: string;
  /** 注册资本 */
  registerCapital?: string;
  /** 状态 */
  status?: string;
  /** 审核状态 */
  approveStatus?: string;
  /** 意见 */
  opinions?: string;
  /** 注册省份 */
  registerProv?: string;
  /** 注册城市 */
  registerCity?: string;
  /** 注册地所在公寓或楼层 */
  registerApartment?: string;
  /** 注册邮编 */
  registerPostalCode?: string;
  /** 商业登记地址是否与企业注册地址相同 */
  businessAddrEqualRegisterAddr?: string;
  /** 商业登记地所在省/州 */
  businessProv?: string;
  /** 商业登记地所在城市 */
  businessCity?: string;
  /** 商业登记地所在公寓或楼层 */
  businessApartment?: string;
  /** 商业登记地邮编 */
  businessPostalCode?: string;
  /** 注册资金币种 */
  fiatCcy?: string;
}

export interface corpBusinessInfoVO {
  /** 网站 */
  website?: string;
  /** 业务地址 */
  businessAddress?: string;
  /** 主营业务地址 */
  mainBusinessAddress?: string;
  /** 行业 */
  industry?: string;
  /** 二级行业 */
  subIndustry?: string;
  /** 原始资金 */
  firstFundingSource?: string;
  /** 财富来源 */
  wealthSource?: string;
  /** 持续资金 */
  continuousFundingSource?: string;
  /** 销售去年 */
  salesLastYear?: string;
  /** 员工人数 */
  staffNumber?: string;
  /** 开户目的 */
  settlePurpose?: string;
  /** 状态 */
  status?: string;
  /** 审核状态 */
  approveStatus?: string;
  /** 意见 */
  opinions?: string;
  /** 商业登记地址是否与企业注册地址相同 */
  businessAddrEqualRegisterAddr?: string;
  /** 商业登记地所在省/州 */
  businessProv?: string;
  /** 商业登记地所在城市 */
  businessCity?: string;
  /** 商业登记地所在公寓或楼层 */
  businessApartment?: string;
  /** 商业登记地邮编 */
  businessPostalCode?: string;
  /** 注册资金币种 */
  fiatCcy?: string;
}

export type CorpSupplementInfoVO = {
  /** 文件信息 */
  fileInfos?: Array<FileInfo>;
  /** 业务序列ID */
  businessSeqId?: string;
  /** 状态 */
  status?: string;
  /** 审核状态 */
  approveStatus?: string;
  /** 意见 */
  opinions?: string;
  /** 是否同意协议 Y-是，N-否*/
  financialAgreementFlag?: string;
};

export type CorpAuthData = {
  /** 用户ID */
  userId?: string;
  /** 申请ID */
  applyId?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 最后审核时间 */
  lastApproveTime?: string;
  /** 最后审核ID */
  lastApproveId?: string;
  /** 状态 */
  status?: string;
  /** 审核状态 */
  approveStatus?: string;
  /** 意见 */
  opinions?: string;
  /** 企业资质信息 */
  corpQualificationInfoVO?: corpQualificationInfoVO;
  /** 企业业务信息 */
  corpBusinessInfoVO?: corpBusinessInfoVO;
  /** 股东信息 */
  corpShareholderVO?: CorpShareholderVO;
  /** 董事信息 */
  corpDirectorInfoVO?: CorpDirectorInfoVO;
  /** 经理信息 */
  corpManagerInfoVO?: EditManagerInfo;
  /** 补充信息 */
  corpSupplementInfoVO?: CorpSupplementInfoVO;
};

/** 企业认证信息 */
export type CorpAuthResponseData = ApiResponseData<CorpAuthData>;

export type ModifyCorpAuthRequestData = ApiRequestData<{
  /** 申请ID */
  applyId?: string;
  /** 模块类型 */
  moduleType?: string;
  /** 企业资质信息 */
  modifyCorpQualificationInfoVO?: {
    /** 文件序列ID */
    fileSeqIds?: Array<string>;
    /** 业务序列ID */
    businessSeqId?: string;
    /** 注册代码 */
    registerCode?: string;
    /** 注册地址 */
    registerAddress?: string;
    /** 注册证书号码 */
    registCertificateNumber?: string;
    /** 成立日期 */
    establishDate?: string;
    /** 英文名称 */
    englishName?: string;
    /** 中文名称 */
    chineseName?: string;
    /** 营业执照号码 */
    businessCertificateNumber?: string;
    /** 营业执照地址 */
    businessAddress?: string;
    /** 营业执照结束时间 */
    businessCertificateEndTime?: string;
    /** 电话 */
    phone?: string;
    /** 过去五年变更名称 */
    pastFiveYearsChangeAme?: string;
    /** 曾用名 */
    formerName?: string;
    /** 企业类型 */
    corporationType?: string;
    /** 注册资本 */
    registerCapital?: string;
    /** 状态 */
    status?: string;
    /** 审核状态 */
    approveStatus?: string;
    /** 意见 */
    opinions?: string;
    /** 原始资金 */
    firstFundingSource?: string;
    /** 财富来源 */
    wealthSource?: string;
    /** 持续资金 */
    continuousFundingSource?: string;
    /** 销售去年 */
    salesLastYear?: string;
    /** 员工人数 */
    staffNumber?: string;
    /** 开户目的 */
    settlePurpose?: string;
  };
  /** 股东信息 */
  modifyCorpShareholderInfoVOs?: Array<{
    /** 文件序列ID */
    fileSeqIds: Array<string>;
    /** 业务序列ID */
    businessSeqId: string;
    /** 持股比例 */
    shareholdingRatio: string;
    /** 国籍 */
    nationality: string;
    /** 证件类型 */
    idType: string;
    /** 英文名 */
    enFirstName: string;
    /** 英文姓 */
    enLastName: string;
    /** 中文名 */
    cnFirstName: string;
    /** 中文姓 */
    cnLastName: string;
    /** 开始日期 */
    expirationStart: string;
    /** 结束日期 */
    expirationEnd: string;
    /** 性别 */
    gender: string;
    /** 证件号码 */
    idNumber: string;
    /** 居住国家 */
    residenceCountry: string;
    /** 居住地址 */
    residenceAddress: string;
  }>;
  /** 董事信息 */
  modifyCorpDirectorInfoVOs?: Array<{
    /** 文件序列ID */
    fileSeqIds: Array<string>;
    /** 业务序列ID */
    businessSeqId: string;
    /** 国籍 */
    nationality: string;
    /** 证件类型 */
    idType: string;
    /** 英文名 */
    enFirstName: string;
    /** 英文姓 */
    enLastName: string;
    /** 中文名 */
    cnFirstName: string;
    /** 中文姓 */
    cnLastName: string;
    /** 开始日期 */
    expirationStart: string;
    /** 结束日期 */
    expirationEnd: string;
    /** 性别 */
    gender: string;
    /** 证件号码 */
    idNumber: string;
    /** 居住国家 */
    residenceCountry: string;
    /** 居住地址 */
    residenceAddress: string;
    /** 业务序列ID */
    shareholdingSeqId: string;
    /** 业务序列ID */
    shareholdingSynFlag: string;
  }>;
  /** 经理信息 */
  modifyCorpManagerInfoVO?: {
    /** 文件序列ID */
    fileSeqIds: Array<string>;
    /** 经理类型 */
    managerType: string;
    /** 经理名称 */
    managerName: string;
    /** 业务序列ID */
    businessSeqId: string;
    /** 国籍 */
    nationality: string;
    /** 证件类型 */
    idType: string;
    /** 英文名 */
    enFirstName: string;
    /** 英文姓 */
    enLastName: string;
    /** 中文名 */
    cnFirstName: string;
    /** 中文姓 */
    cnLastName: string;
    /** 开始日期 */
    expirationStart: string;
    /** 结束日期 */
    expirationEnd: string;
    /** 性别 */
    gender: string;
    /** 证件号码 */
    idNumber: string;
    /** 居住国家 */
    residenceCountry: string;
    /** 居住地址 */
    residenceAddress: string;
    /** 业务序列ID */
    shareholdingSeqId: string;
  };
  /** 补充信息 */
  modifyCorpSupplementInfoVO?: {
    /** 文件序列ID */
    fileSeqIds: Array<string>;
    /** 业务序列ID */
    businessSeqId: string;
    /** 是否同意协议 Y-是，N-否*/
    financialAgreementFlag: string;
  };
}>;

export type ModifyCorpAuthResponseData = ApiResponseData<{
  /** 申请ID */
  applyId: string;
}>;

export type CorpAuthApproveRequestData = ApiRequestData<{
  /** 申请ID */
  applyId: string;
}>;

// 定义子组件暴露给父组件的接口
export interface ComponentExposed {
  submitForm: (validate: boolean) => Promise<any>;
}

export enum EnumMenuItemStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  ERROR = 'error',
}
