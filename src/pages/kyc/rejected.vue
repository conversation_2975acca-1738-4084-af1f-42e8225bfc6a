<script setup>
import { useUserStore } from '@/pinia/stores/user';
import { useRouter } from 'vue-router';
const router = useRouter();
const userStore = useUserStore();
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
/** 登出 */
function logout() {
  userStore.logout();
  router.push('/login');
}
const email = ref(localStorage.getItem('ding-login-email') || '');
</script>

<template>
  <div class="bg-[#F5F5F5] app-wrapper">
    <div class="w-100% h-200px bg-[#030814]">
      <div
        class="w-960px h-100% mx-auto bg-[url(@@/assets/images/rejected-bg.webp)] bg-no-repeat bg-550px bg-right-top"
      ></div>
    </div>

    <div class="w-800px h-568px mx-auto bg-white rounded-12px mt-[-84px] p-32px">
      <p>{{ t('rejected.greeting', { email: email }) }}</p>
      <p>{{ t('rejected.message1') }}</p>
      <p>{{ t('rejected.message2') }}</p>
      <p>{{ t('rejected.message3') }}</p>
      <p>{{ t('rejected.thanks1') }}</p>
      <p>{{ t('rejected.regards1') }}</p>
      <p>{{ t('rejected.team') }}</p>
      <p><EMAIL> ｜ https://www.dingx.tech</p>
      <el-button type="primary" @click="logout" class="w-230px block mx-auto mt-100px">{{
        t('rejected.backToLogin')
      }}</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
p {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
  letter-spacing: 0;
  line-height: 20px;
  margin: 0;
  margin-bottom: 20px;
}
</style>
