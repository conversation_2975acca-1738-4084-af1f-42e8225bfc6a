<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import CompanyInfo from './components/CompanyInfo.vue';
import BusinessInfo from './components/BusinessInfo.vue';
import { modifyCorpAuthApi, corpAuth<PERSON>pi, corpAuthApproveApi } from './apis';
import type { ComponentExposed, CorpAuthData } from './apis/type';
import { StatusEnum, ApproveStatusEnum, EnumMenuItemStatus } from './apis/type';
import CertificationNav, { type MenuItem } from './components/CertificationNav.vue';
import AdditionalInfo from './components/AdditionalInfo.vue';
import OpinionDrawer from './components/OpinionDrawer.vue';
import { DialogService } from '@/common/components/Dialog/DialogService';
import ShareholderInfo from './components/ShareholderInfo.vue';
import DirectorInfo from './components/DirectorInfo.vue';
import ManagerInfo from './components/ManagerInfo.vue';
import { useFormData } from './composables/useFormData';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const activeItem = ref('company-info');
const companyInfoRef = ref<ComponentExposed | null>(null);
const businessInfoRef = ref<ComponentExposed | null>(null);
const additionalInfoRef = ref<ComponentExposed | null>(null);
const shareholderInfoRef = ref<ComponentExposed | null>(null);
const directorInfoRef = ref<ComponentExposed | null>(null);
const managerInfoRef = ref<ComponentExposed | null>(null);
const mainContainerRef = ref<HTMLDivElement | null>(null);

const componentRefs = {
  'company-info': companyInfoRef,
  'business-info': businessInfoRef,
  'shareholder-info': shareholderInfoRef,
  'director-info': directorInfoRef,
  'manager-info': managerInfoRef,
  'additional-info': additionalInfoRef,
};

const menuItemsData = ref([
  {
    id: 'company-info',
    nameKey: 'nav.companyInfo',
    iconBase: 'company',
    status: EnumMenuItemStatus.PENDING,
    approvalStatus: ApproveStatusEnum.I,
    serverStatus: '',
  },
  {
    id: 'business-info',
    nameKey: 'nav.businessInfo',
    iconBase: 'business',
    status: EnumMenuItemStatus.PENDING,
    approvalStatus: ApproveStatusEnum.I,
    serverStatus: '',
  },
  {
    id: 'shareholder-info',
    nameKey: 'nav.shareholderInfo',
    iconBase: 'shareholder',
    status: EnumMenuItemStatus.PENDING,
    approvalStatus: ApproveStatusEnum.I,
    serverStatus: '',
  },
  {
    id: 'director-info',
    nameKey: 'nav.directorInfo',
    iconBase: 'director',
    status: EnumMenuItemStatus.PENDING,
    approvalStatus: ApproveStatusEnum.I,
    serverStatus: '',
  },
  {
    id: 'manager-info',
    nameKey: 'nav.managerInfo',
    iconBase: 'manager',
    status: EnumMenuItemStatus.PENDING,
    approvalStatus: ApproveStatusEnum.I,
    serverStatus: '',
  },
  {
    id: 'additional-info',
    nameKey: 'nav.additionalInfo',
    iconBase: 'additional',
    status: EnumMenuItemStatus.PENDING,
    approvalStatus: ApproveStatusEnum.I,
    serverStatus: '',
  },
]);

const menuItems = computed<MenuItem[]>(() =>
  menuItemsData.value.map((item) => ({
    ...item,
    name: t(item.nameKey),
  }))
);

const isAllCompleted = computed(() => {
  return menuItems.value.every((item) => item.status === EnumMenuItemStatus.COMPLETED);
});

const opinions = computed(() => {
  let opinions: any[] = [];
  if (!kycInfo.value) return [];
  if (kycInfo.value.corpQualificationInfoVO?.opinions) {
    opinions.push({
      moduleName: t('opinions.companyQualification'),
      opinion: kycInfo.value.corpQualificationInfoVO?.opinions,
    });
  }
  if (kycInfo.value.corpBusinessInfoVO?.opinions) {
    opinions.push({
      moduleName: t('opinions.businessInfo'),
      opinion: kycInfo.value.corpBusinessInfoVO?.opinions,
    });
  }
  if (kycInfo.value.corpShareholderVO?.opinions) {
    const shareholderOpinions = kycInfo.value.corpShareholderVO?.opinions;
    opinions.push({
      moduleName: t('opinions.shareholderInfo'),
      opinion: shareholderOpinions,
    });
  }
  if (kycInfo.value.corpDirectorInfoVO?.opinions) {
    const directorOpinions = kycInfo.value.corpDirectorInfoVO?.opinions;
    opinions.push({
      moduleName: t('opinions.directorInfo'),
      opinion: directorOpinions,
    });
  }
  if (kycInfo.value.corpManagerInfoVO?.opinions) {
    opinions.push({
      moduleName: t('opinions.managerInfo'),
      opinion: kycInfo.value.corpManagerInfoVO?.opinions,
    });
  }
  if (kycInfo.value.corpSupplementInfoVO?.opinions) {
    opinions.push({
      moduleName: t('opinions.additionalInfo'),
      opinion: kycInfo.value.corpSupplementInfoVO?.opinions,
    });
  }
  return opinions;
});

const router = useRouter();

const alertStatus = computed<boolean>(() => {
  // return true
  return kycInfo.value.status === StatusEnum.E;
});

const kycInfo = ref<CorpAuthData>({
  applyId: '',
});

const isLoading = ref(false);
const opinionDrawerVisible = ref(false);
const navTopOffset = ref('32px');

// const updateNavTopOffset = () => {
//   nextTick(() => {
//     if (mainContainerRef.value) {
//       console.log('mainContainerRef.value', mainContainerRef.value.offsetHeight);
//       navTopOffset.value = `${mainContainerRef.value.offsetTop + 32}px`;
//     } else {
//       navTopOffset.value = '32px';
//     }
//   });
// };

onMounted(() => {
  getkycInfo();
});

// watch(alertStatus, updateNavTopOffset, { flush: 'post' });

// watch(activeItem, (newValue, oldValue) => {
//   if (oldValue && oldValue !== newValue) {
//     save(false, oldValue)
//   }
// })

// 查询kyc信息
const getkycInfo = async () => {
  isLoading.value = true;
  const { data } = await corpAuthApi({});
  kycInfo.value = data;
  isLoading.value = false;

  // 右侧侧边状态
  const validateForm = useFormData();
  const validateCompanyInfo = validateForm.validateCompanyInfo(data.corpQualificationInfoVO || {});
  menuItemsData.value[0].status = getStatus(
    validateCompanyInfo.code,
    data.corpQualificationInfoVO?.status
  );
  menuItemsData.value[0].approvalStatus = getApprovalStatus(
    data.corpQualificationInfoVO?.approveStatus
  );
  menuItemsData.value[0].serverStatus = data.corpQualificationInfoVO?.status || '';
  // menuItems.value[0].info = createOrderSignature(data.corpQualificationInfoVO);

  const validateBusinessInfo = validateForm.validateBusinessInfo(data.corpBusinessInfoVO || {});
  menuItemsData.value[1].status = getStatus(
    validateBusinessInfo.code,
    data.corpBusinessInfoVO?.status
  );
  menuItemsData.value[1].approvalStatus = getApprovalStatus(data.corpBusinessInfoVO?.approveStatus);
  menuItemsData.value[1].serverStatus = data.corpBusinessInfoVO?.status || '';
  // menuItems.value[1].info = createOrderSignature(data.corpBusinessInfoVO);

  // 股东信息字段验证
  const validateShareholderInfo = validateForm.validateShareholderInfo(
    data.corpShareholderVO || {}
  );
  menuItemsData.value[2].status = getStatus(
    validateShareholderInfo.code,
    data.corpShareholderVO?.status
  );
  menuItemsData.value[2].approvalStatus = getApprovalStatus(data.corpShareholderVO?.approveStatus);
  menuItemsData.value[2].serverStatus = data.corpShareholderVO?.status || '';

  // 董事信息字段验证
  const validateDirectorInfo = validateForm.validateDirectorInfo(
    data.corpDirectorInfoVO || {},
    data.corpQualificationInfoVO?.registerCode || ''
  );
  menuItemsData.value[3].status = getStatus(
    validateDirectorInfo.code,
    data.corpDirectorInfoVO?.status
  );
  menuItemsData.value[3].approvalStatus = getApprovalStatus(data.corpDirectorInfoVO?.approveStatus);
  menuItemsData.value[3].serverStatus = data.corpDirectorInfoVO?.status || '';

  const validateManagerInfo = validateForm.validateManagerInfo(data.corpManagerInfoVO || {});
  menuItemsData.value[4].status = getStatus(
    validateManagerInfo.code,
    data.corpManagerInfoVO?.status
  );
  menuItemsData.value[4].approvalStatus = getApprovalStatus(data.corpManagerInfoVO?.approveStatus);
  menuItemsData.value[4].serverStatus = data.corpManagerInfoVO?.status || '';
  // menuItems.value[4].info = createOrderSignature(data.corpManagerInfoVO);

  const validateAdditionalInfo = validateForm.validateAdditionalInfo(
    data.corpSupplementInfoVO || {}
  );
  menuItemsData.value[5].status = getStatus(
    validateAdditionalInfo.code,
    data.corpSupplementInfoVO?.status
  );
  menuItemsData.value[5].approvalStatus = getApprovalStatus(
    data.corpSupplementInfoVO?.approveStatus
  );
  menuItemsData.value[5].serverStatus = data.corpSupplementInfoVO?.status || '';
  // menuItems.value[5].info = createOrderSignature(data.corpSupplementInfoVO);
};

/**
 * @description: 获取菜单项状态
 * @param {number} code - 表单校验状态码 0-待完善 1-已完善
 * @param {string} status - 后端返回的状态 E-认证失败 D-已认证 S-已认证
 * @returns {string} 'error' | 'completed' | 'pending'
 */
const getStatus = (code: number, status?: string) => {
  if (status && status.length > 0) {
    const allMatch =
      status?.split('').every((char) => char === StatusEnum.D || char === StatusEnum.S) || false;
    if (allMatch) {
      return EnumMenuItemStatus.COMPLETED;
    }

    const hasMatch = status?.split('').some((char) => char === StatusEnum.E) || false;
    if (hasMatch) {
      return EnumMenuItemStatus.ERROR;
    }
  }
  if (status === StatusEnum.E || code === 2) {
    return EnumMenuItemStatus.ERROR;
  } else if (status === StatusEnum.D || status === StatusEnum.S || code === 1) {
    return EnumMenuItemStatus.COMPLETED;
  } else {
    return EnumMenuItemStatus.PENDING;
  }
};

const getApprovalStatus = (status?: string) => {
  if (status && status.length > 0) {
    const hasMatch = status?.split('').some((char) => char == ApproveStatusEnum.E) || false;
    if (hasMatch) {
      return ApproveStatusEnum.E;
    }
  }
  if (status === ApproveStatusEnum.E) {
    return ApproveStatusEnum.E;
  } else {
    return ApproveStatusEnum.I;
  }
};

const navItemClick = async (curItem: MenuItem) => {
  try {
    await save(false);
  } catch (e) {
    console.log(e);
  }
  activeItem.value = curItem.id;
};

const save = async (validate: boolean, itemIdToSave?: string, refresh = true) => {
  const activeComponentId = itemIdToSave || activeItem.value;
  const componentRef = componentRefs[activeComponentId as keyof typeof componentRefs];
  if (!componentRef?.value) return false;

  const params = await componentRef.value.submitForm(validate);
  if (params) {
    const paramsToTest = { ...params };
    delete paramsToTest.moduleType;

    const hasDataToSave = Object.values(paramsToTest).some((value) => {
      if (value === null || value === undefined) {
        return false;
      }
      if (Array.isArray(value) && value.length === 0) {
        return false;
      }
      return true;
    });

    if (!hasDataToSave) {
      return false;
    }

    try {
      await modifyCorpAuthApi({
        ...params,
        applyId: kycInfo.value.applyId || '',
      });
      if (refresh) {
        getkycInfo();
      }

      return true;
    } catch (error) {
      console.error(error);
    }
  }

  return false;
};

const showSubmitDialog = () => {
  DialogService.show({
    title: t('dialog.submitTitle'),
    message: t('dialog.submitMessage'),
    iconClass: 'warning',
    confirmButtonText: t('dialog.submit'),
    cancelButtonText: t('dialog.thinkAgain'),
    onConfirm: async () => {
      // 先保存逻辑成功再提交
      try {
        const saveRes = await save(true, undefined, false);
        if (!saveRes) {
          return;
        }

        handleSubmit();
      } catch (error) {
        console.error(error);
      }
    },
  });
};
const handleSubmit = async () => {
  await corpAuthApproveApi({
    applyId: kycInfo.value.applyId || '',
  }).then((res) => {
    // 如果状态为待修改，则弹出感谢配合，请耐心等待我们的通知
    if (kycInfo.value.status === StatusEnum.E) {
      DialogService.show({
        title: t('dialog.thanksTitle'),
        message: t('dialog.thanksMessage'),
        iconClass: 'info',
        hideCancelButton: true,
        confirmButtonText: t('dialog.gotIt'),
        onConfirm: () => {
          router.push('/');
        },
      });
      return;
    }
    DialogService.show({
      title: t('dialog.congratsTitle'),
      message: t('dialog.congratsMessage'),
      iconClass: 'success',
      hideCancelButton: true,
      confirmButtonText: t('dialog.gotIt'),
      onConfirm: () => {
        router.push('/');
      },
    });
  });
};
const handleCancel = () => {
  DialogService.show({
    title: t('dialog.giveUpTitle'),
    message: t('dialog.giveUpMessage'),
    iconClass: 'warning',
    confirmButtonText: t('dialog.confirm'),
    cancelButtonText: t('dialog.cancel'),
    onConfirm: () => {
      router.back();
    },
  });
};
</script>

<template>
  <div class="kyc-container">
    <div class="dashboard-container" :style="{ height: 'calc(100% - 30px)' }">
      <div class="content-wrapper">
        <div class="main-content" ref="mainContainerRef">
          <div v-if="alertStatus" class="sticky-alert pt-32px pb-24px">
            <el-alert v-if="alertStatus" :closable="false" class="kyc-alert !flex">
              <template #title>
                <span>{{ t('alert.error') }}</span>
                <span class="suggestion-link" @click="opinionDrawerVisible = true">{{
                  t('alert.suggestion')
                }}</span>
              </template>
              <template #icon>
                <img src="@@/assets/icons/icon-warning.svg" />
              </template>
            </el-alert>
          </div>
          <div :style="alertStatus ? 'margin-top: 0px;' : 'margin-top: 32px;'">
            <CompanyInfo
              class="main-content-box"
              ref="companyInfoRef"
              :kycInfo="kycInfo"
              v-if="activeItem === 'company-info' && !isLoading"
            />
            <BusinessInfo
              ref="businessInfoRef"
              class="main-content-box"
              :kycInfo="kycInfo"
              v-if="activeItem === 'business-info'"
            />
            <AdditionalInfo
              ref="additionalInfoRef"
              class="main-content-box"
              :additionalInfo="kycInfo.corpSupplementInfoVO"
              v-if="activeItem === 'additional-info'"
            />
            <ShareholderInfo
              ref="shareholderInfoRef"
              class="main-content-box"
              :shareholderInfo="kycInfo.corpShareholderVO"
              :directorList="kycInfo.corpDirectorInfoVO?.corpDirectorInfoVOs"
              :managerInfo="kycInfo.corpManagerInfoVO"
              v-if="activeItem === 'shareholder-info'"
            />
            <DirectorInfo
              ref="directorInfoRef"
              class="main-content-box"
              :directorInfo="kycInfo.corpDirectorInfoVO"
              :shareholderList="kycInfo.corpShareholderVO?.corpShareholderInfoVOs"
              :registerCode="kycInfo.corpQualificationInfoVO?.registerCode || ''"
              v-if="activeItem === 'director-info'"
            />
            <ManagerInfo
              ref="managerInfoRef"
              class="main-content-box"
              :managerInfo="kycInfo.corpManagerInfoVO"
              :shareholderList="kycInfo.corpShareholderVO?.corpShareholderInfoVOs"
              v-if="activeItem === 'manager-info'"
            />
          </div>
        </div>
        <CertificationNav
          v-model:activeItem="activeItem"
          :menuItems="menuItems"
          :itemClick="navItemClick"
          class="sticky-nav"
        />
      </div>
      <div
        class="z-9 bg-#fff fixed bottom-0 w-100% h-80px border-t-1 border-t-[#EDEDEE] border-t-style-dotted flex justify-center items-center"
      >
        <el-button
          class="btn-hover-scale-sm"
          v-if="kycInfo.status !== StatusEnum.E"
          type="info"
          @click="handleCancel"
          >{{ t('button.giveUp') }}</el-button
        >
        <el-button class="btn-hover-scale-sm" type="primary" @click="save(false)">{{
          t('button.saveDraft')
        }}</el-button>
        <el-button
          class="min-w-136px btn-hover-scale-sm"
          :class="isAllCompleted ? 'bg-#FF0064 border-#FF0064' : 'bg-[#D2D2D2] border-[#D2D2D2]'"
          type="primary"
          :disabled="!isAllCompleted"
          @click="showSubmitDialog"
          >{{
            kycInfo.status === StatusEnum.E ? t('button.completed') : t('button.submit')
          }}</el-button
        >
      </div>
    </div>
    <OpinionDrawer v-model="opinionDrawerVisible" :opinions="opinions" />
  </div>
</template>

<style lang="scss" scoped>
.kyc-container {
  padding-bottom: 32px;
  min-width: 1000px;
  position: relative;
  margin-bottom: 80px;
  overflow: hidden;

  .kyc-alert {
    background-color: #fff8eb;
    width: 690px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 400;
    font-family: PingFangSC;

    :deep(.el-alert__title) {
      font-size: 14px;
      line-height: 22px;
      color: #1d2129;
    }

    .suggestion-link {
      color: #ff0064;
      cursor: pointer;
      margin-left: 5px;
    }
  }
}

.dashboard-container {
  background-color: #fff;
  width: 100%;
  overflow-y: auto;
}

.content-wrapper {
  display: flex;
  justify-content: space-between;
  max-width: 1040px;
  margin: 0 auto;
  padding: 0 40px;
}

.sticky-alert {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #fff;
}

.main-content {
  .main-content-box {
    overflow-x: hidden;
    padding-bottom: 80px;
    width: 690px !important;
  }
}

.sticky-nav {
  position: sticky;
  align-self: flex-start;
  top: 32px;
  z-index: 999;
}

// @media (max-width: 1439px) {
//   .main-content {
//     gap: 60px;
//   }
// }

@media (min-width: 1440px) {
  .sticky-nav {
    position: fixed;
    right: 40px;
    top: 92px;
    z-index: 999;
  }
}
</style>
