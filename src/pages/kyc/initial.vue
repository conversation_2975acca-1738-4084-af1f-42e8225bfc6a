<script setup>
import { useUserStore } from '@/pinia/stores/user';
import { t } from '@/common/i18n';
const router = useRouter();
const userStore = useUserStore();

const goKyc = () => {
  router.push('/kyc');
};
</script>

<template>
  <div class="bg-[#fff] app-wrapper text-center">
    <img class="w-400px h-240px mt-108px" src="@@/assets/images/kyc-initial.webp" />
    <h4 class="text-28px font-600 text-[#222527] m-0 mt-32px leading-40px">
      {{ t('initial.title') }}
    </h4>
    <p
      v-if="userStore.kycStatus === 'I' || userStore.kycStatus === 'E'"
      class="text-14px font-400 text-[#6B7275] mx-auto m-0 mt-16px leading-20px w-600px"
    >
      {{ t('initial.desc') }}
    </p>
    <p
      v-if="userStore.kycStatus === 'D'"
      class="text-14px font-400 text-[#222527] mx-auto m-0 mt-16px leading-20px w-600px"
    >
      {{ t('initial.wait') }}
    </p>
    <el-button
      v-if="userStore.kycStatus === 'I' || userStore.kycStatus === 'E'"
      type="primary"
      @click="goKyc"
      class="w-230px block mx-auto mt-32px btn-hover-scale-sm"
      >{{ t('initial.button') }}</el-button
    >
  </div>
</template>

<style lang="scss" scoped>
p {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
  letter-spacing: 0;
  line-height: 20px;
  margin: 0;
  margin-bottom: 20px;
}
</style>
