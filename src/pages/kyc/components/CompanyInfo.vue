<template>
  <div class="bg-white h-[100%] pb-10px">
    <div class="text-28px font-semibold text-[#222527]">{{ t('companyInfo.title') }}</div>

    <el-form
      ref="companyForm"
      :hide-required-asterisk="true"
      :model="form"
      :rules="campanyRules"
      label-position="top"
      class="bank-form"
    >
      <!-- 公司注册地-->
      <section class="mb-32px last:mb-0">
        <h3 class="mt-32px mb-0 text-18px leading-24px font-semibold text-[#222527]">
          {{ t('companyInfo.registerLocation.title') }}
        </h3>
        <p class="mt-8px mb-24px text-14px text-[#6B7275]">
          {{ t('companyInfo.registerLocation.description') }}
        </p>

        <el-form-item
          :label="t('companyInfo.registerLocation.label')"
          class="w-260px mb-32px"
          prop="registerCode"
        >
          <el-select
            filterable
            v-model="form.registerCode"
            :placeholder="t('companyInfo.placeholder.select')"
            style="width: 100%"
          >
            <template #prefix>
              <img
                v-if="form.registerCode"
                style="margin-right: 8px"
                :src="`https://files.dingx.tech/icons/flags/${form.registerCode.toLowerCase()}.svg`"
                width="16"
              />
            </template>
            <el-option
              v-for="info in regions"
              :key="info.enumCode"
              :label="info.enumDescCn"
              :value="info.enumCode"
              style="height: 40px"
            >
              <p style="display: flex; align-items: center; margin: 0; height: 40px">
                <img
                  style="margin-right: 8px"
                  :src="`https://files.dingx.tech/icons/flags/${info.enumCode.toLowerCase()}.svg`"
                  width="20"
                />

                {{ info.enumDescCn }}
              </p>
            </el-option>
          </el-select>
        </el-form-item>

        <el-divider />
      </section>

      <!-- 企业信息 -->
      <section class="mb-24px last:mb-0">
        <h3 class="mb-8px text-18px font-semibold text-[#1a1a1a]">
          {{ t('companyInfo.companyInfo') }}
        </h3>

        <el-row :gutter="170" class="mt-24px">
          <el-col :span="12">
            <el-form-item :label="t('companyInfo.qualification.englishName')" prop="englishName">
              <el-input
                v-model="form.englishName"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <template #label>
                {{ t('companyInfo.qualification.chineseName') }}
                <span class="text-[#6B7275]">{{ t('companyInfo.qualification.optional') }}</span>
              </template>
              <el-input
                v-model="form.chineseName"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="t('companyInfo.qualification.phone')" prop="phone">
              <el-input
                v-model="form.phone"
                :placeholder="t('companyInfo.placeholder.input')"
                :formatter="phoneFormatInputExpose"
                :parser="phoneParseInputExpose"
              >
                <template #prepend>
                  <el-select
                    filterable
                    :filter-method="filterPhoneCode"
                    v-model="form.phoneAreaCode"
                    :placeholder="t('companyInfo.placeholder.select')"
                    style="width: 88px"
                  >
                    <el-option
                      v-for="info in filteredOptions"
                      :key="info.enumCode"
                      :label="info.extendField"
                      :value="info.enumCode"
                    >
                      <p
                        style="
                          display: flex;
                          align-items: center;
                          margin: 0;
                          justify-content: space-between;
                          width: 260px;
                        "
                      >
                        <span>{{ info.enumDescCn }}</span>
                        <span>{{ info.extendField }}</span>
                      </p>
                    </el-option>
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="t('companyInfo.qualification.nameChange')">
              <el-switch
                v-model="form.pastFiveYearsChangeAme"
                active-value="Y"
                inactive-value="N"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.pastFiveYearsChangeAme === 'Y'">
            <el-form-item :label="t('companyInfo.qualification.formerName')" prop="formerName">
              <el-input
                v-model="form.formerName"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="t('companyInfo.qualification.corporationType')"
              prop="corporationType"
            >
              <el-select
                v-model="form.corporationType"
                :placeholder="t('companyInfo.placeholder.select')"
              >
                <el-option
                  v-for="info in companyTypeOptions"
                  :key="info.enumCode"
                  :label="info.enumDescCn"
                  :value="info.enumCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="t('companyInfo.qualification.registerCapital')"
              prop="registerCapital"
            >
              <el-input
                v-model="form.registerCapital"
                :placeholder="t('companyInfo.placeholder.input')"
                :formatter="moneyFormatInputExpose"
                :parser="moneyParseInputExpose"
              >
                <template #prepend>
                  <el-select
                    filterable
                    v-model="form.fiatCcy"
                    :placeholder="t('companyInfo.placeholder.select')"
                    style="width: 88px"
                  >
                    <el-option
                      v-for="info in fiatCcyOptions"
                      :key="info.enumCode"
                      :label="info.extendField"
                      :value="info.enumCode"
                    >
                      <p style="display: flex; margin: 0">
                        <span>{{ info.enumDescCn }}</span>
                      </p>
                    </el-option>
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider />
      </section>

      <!-- 注册地址 -->
      <section class="mb-24px last:mb-0">
        <h3 class="mb-8px text-18px font-semibold text-[#1a1a1a]">
          {{ t('companyInfo.registerLocation.title') }}
        </h3>

        <el-row :gutter="170" class="mt-24px">
          <!-- 注册地址 -->
          <el-col :span="24">
            <el-form-item
              :label="t('companyInfo.qualification.registerAddress')"
              prop="registerAddress"
            >
              <el-input
                v-model="form.registerAddress"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              :label="t('companyInfo.qualification.registerApartment')"
              prop="registerApartment"
            >
              <el-input
                v-model="form.registerApartment"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="t('companyInfo.qualification.registerCity')" prop="registerCity">
              <el-input
                v-model="form.registerCity"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="t('companyInfo.qualification.registerProv')" prop="registerProv">
              <el-input
                v-model="form.registerProv"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item prop="registerPostalCode">
              <div class="flex items-center">
                <span class="mr-4px">{{ t('companyInfo.qualification.businessPostalCode') }}</span>
                <el-tooltip placement="right">
                  <template #content>
                    <p style="margin: 0">
                      {{ t('companyInfo.validation.businessPostalCodeTooltip') }}
                    </p>
                  </template>
                  <svg-icon name="icon-info" class="text-13.3px color-[#A7ADB0] ml-3px" />
                </el-tooltip>
              </div>
              <el-input
                v-model="form.registerPostalCode"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider />
      </section>

      <!-- 提交相关资料 -->
      <section class="mb-24px last:mb-0">
        <h3 class="mb-8px text-18px font-semibold text-[#1a1a1a]">
          {{ t('companyInfo.submitRelatedData') }}
        </h3>

        <el-row :gutter="170" class="mt-24px">
          <!-- 企业注册证书 -->
          <el-col :span="24">
            <el-form-item prop="ciFileList">
              <div class="flex items-center">
                <span>{{ t('companyInfo.qualification.ci.title') }}</span>
                <el-button
                  @click="downloadTemplate('/CI.png')"
                  link
                  class="ml-8px p-0 text-14px text-[#FF0064] font-400"
                  size="small"
                  >{{ t('companyInfo.qualification.ci.downloadTemplate') }}</el-button
                >
              </div>
              <UploadFiles v-model:fileList="form.ciFileList" :fileType="'ERC'" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              :label="t('companyInfo.qualification.registrationNumber')"
              prop="registCertificateNumber"
            >
              <el-input
                v-model="form.registCertificateNumber"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="t('companyInfo.qualification.establishDate')"
              prop="establishDate"
            >
              <el-date-picker
                v-model="form.establishDate"
                type="date"
                value-format="YYYYMMDD"
                :placeholder="t('companyInfo.placeholder.select')"
                class="w-100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 商业登记证书 -->
          <el-col :span="24" v-if="form.registerCode === 'HK'">
            <el-form-item prop="brFileList">
              <div class="flex items-center">
                <span>{{ t('companyInfo.qualification.br.title') }}</span>
                <el-button
                  link
                  @click="downloadTemplate('/BR.png')"
                  class="ml-8px p-0 text-14px text-[#FF0064] font-400"
                  size="small"
                  >{{ t('companyInfo.qualification.br.downloadTemplate') }}</el-button
                >
              </div>
              <UploadFiles v-model:fileList="form.brFileList" :fileType="'BRC'" />
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="form.registerCode === 'HK'">
            <el-form-item
              :label="t('companyInfo.qualification.br.number')"
              prop="businessCertificateNumber"
            >
              <el-input
                v-model="form.businessCertificateNumber"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="form.registerCode === 'HK'">
            <el-form-item
              :label="t('companyInfo.qualification.br.endTime')"
              prop="businessCertificateEndTime"
            >
              <el-date-picker
                v-model="form.businessCertificateEndTime"
                type="date"
                value-format="YYYYMMDD"
                :placeholder="t('companyInfo.placeholder.select')"
                class="w-100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="24" v-if="form.registerCode === 'HK'">
            <el-form-item :label="t('companyInfo.qualification.businessAddrEqualRegisterAddr')">
              <el-switch
                v-model="form.businessAddrEqualRegisterAddr"
                active-value="Y"
                inactive-value="N"
              />
            </el-form-item>
          </el-col>

          <el-col
            :span="24"
            v-if="form.registerCode === 'HK' && form.businessAddrEqualRegisterAddr !== 'Y'"
          >
            <el-form-item
              :label="t('companyInfo.qualification.businessAddress')"
              prop="businessAddress"
            >
              <el-input
                v-model="form.businessAddress"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col
            :span="12"
            v-if="form.registerCode === 'HK' && form.businessAddrEqualRegisterAddr !== 'Y'"
          >
            <el-form-item
              :label="t('companyInfo.qualification.businessApartment')"
              prop="businessApartment"
            >
              <el-input
                v-model="form.businessApartment"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col
            :span="12"
            v-if="form.registerCode === 'HK' && form.businessAddrEqualRegisterAddr !== 'Y'"
          >
            <el-form-item :label="t('companyInfo.qualification.businessCity')" prop="businessCity">
              <el-input
                v-model="form.businessCity"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col
            :span="12"
            v-if="form.registerCode === 'HK' && form.businessAddrEqualRegisterAddr !== 'Y'"
          >
            <el-form-item :label="t('companyInfo.qualification.businessProv')" prop="businessProv">
              <el-input
                v-model="form.businessProv"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col
            :span="12"
            v-if="form.registerCode === 'HK' && form.businessAddrEqualRegisterAddr !== 'Y'"
          >
            <el-form-item prop="businessPostalCode">
              <div class="flex items-center">
                <span class="mr-4px">{{ t('companyInfo.qualification.businessPostalCode') }}</span>
                <el-tooltip placement="right">
                  <template #content>
                    <p style="margin: 0">
                      {{ t('companyInfo.validation.businessPostalCodeTooltip') }}
                    </p>
                  </template>
                  <svg-icon name="icon-info" class="text-13.3px color-[#A7ADB0] ml-3px" />
                </el-tooltip>
              </div>

              <el-input
                v-model="form.businessPostalCode"
                :placeholder="t('companyInfo.placeholder.input')"
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- NAR1或NNC1 -->
          <el-col :span="24" v-if="form.registerCode === 'HK'">
            <el-form-item prop="nar1FileList">
              <div class="flex items-center">
                <span class="mr-4px">{{ t('companyInfo.qualification.nar1nnc1.title') }}</span>
                <el-tooltip placement="right">
                  <template #content>
                    <p style="margin: 0">{{ t('companyInfo.qualification.nar1nnc1.tooltip1') }}</p>
                    <p style="margin: 0">{{ t('companyInfo.qualification.nar1nnc1.tooltip2') }}</p>
                  </template>
                  <svg-icon name="icon-info" class="text-13.3px color-[#A7ADB0] ml-3px" />
                </el-tooltip>
                <el-button
                  link
                  @click="downloadTemplate('/NAR1.png')"
                  class="ml-8px p-0 text-14px text-[#FF0064] font-400"
                  size="small"
                  >{{ t('companyInfo.qualification.nar1nnc1.downloadNAR1') }}</el-button
                >
                <el-button
                  link
                  @click="downloadTemplate('/NNC1.png')"
                  class="ml-8px p-0 text-14px text-[#FF0064] font-400"
                  size="small"
                  >{{ t('companyInfo.qualification.nar1nnc1.downloadNNC1') }}</el-button
                >
              </div>
              <UploadFiles v-model:fileList="form.nar1FileList" :fileType="'NAR1NNC1'" />
            </el-form-item>
          </el-col>
        </el-row>
      </section>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { watch, onMounted, ref, computed } from 'vue';
import UploadFiles from '@@/components/UploadFiles/index.vue';
import type { FormInstance, FormRules } from 'element-plus';
import { CorpAuthData, ComponentExposed } from '../apis/type';
import { useI18n } from 'vue-i18n'; // 添加 i18n 引用
import {
  phoneFormatInput,
  phoneParseInput,
  moneyFormatInput,
  moneyParseInput,
} from '@@/utils/format';

const phoneFormatInputExpose = phoneFormatInput;
const phoneParseInputExpose = phoneParseInput;
const moneyFormatInputExpose = moneyFormatInput;
const moneyParseInputExpose = moneyParseInput;
// 使用 i18n
const { t } = useI18n();

interface CompanyInfoForm {
  fileList?: any[];
  fileInfos?: any[];
  chineseName?: string;
  englishName?: string;
  establishDate?: string;
  registCertificateNumber?: string;
  registerCode?: string;
  ciFileList?: any[];
  brFileList?: any[];
  nar1FileList?: any[];
  businessCertificateNumber?: string;
  businessAddress?: string;
  businessCertificateEndTime?: string;
  phone?: string;
  pastFiveYearsChangeAme?: string;
  formerName?: string;
  corporationType?: string;
  registerCapital?: string;
  businessSeqId?: string;
  registerAddress?: string;
  phoneAreaCode?: string;
  businessAddrEqualRegisterAddr?: string;
  businessProv?: string;
  businessCity?: string;
  businessApartment?: string;
  businessPostalCode?: string;
  registerPostalCode?: string;
  registerApartment?: string;
  registerProv?: string;
  registerCity?: string;
  fiatCcy?: string;
}
const form = ref<CompanyInfoForm>({
  chineseName: '',
  englishName: '',
  establishDate: '',
  registCertificateNumber: '',
  registerCode: '',
  ciFileList: [],
  brFileList: [],
  nar1FileList: [],
  businessCertificateNumber: '',
  businessAddress: '',
  businessCertificateEndTime: '',
  phone: '',
  pastFiveYearsChangeAme: 'N',
  formerName: '',
  corporationType: '',
  registerCapital: '',
  businessSeqId: '',
  registerAddress: '',
  phoneAreaCode: '',
  businessAddrEqualRegisterAddr: 'N',
  businessProv: '',
  businessCity: '',
  businessApartment: '',
  businessPostalCode: '',
  registerPostalCode: '',
  registerApartment: '',
  registerProv: '',
  registerCity: '',
  fiatCcy: '',
});
// 定义 props
const props = withDefaults(
  defineProps<{
    kycInfo: CorpAuthData;
  }>(),
  {}
);

// 获取枚举
interface EnumItem {
  enumCode: string;
  enumDescCn: string;
  extendField?: string;
}
import { BusinessEnumType } from '@@/apis/common/type';
import { useEnumStore } from '@/pinia/stores/enumStore';
const enumStore = useEnumStore();
const regions = enumStore.getEnumList(BusinessEnumType.NATIONALITY);
const phoneCodes = enumStore.getEnumList(BusinessEnumType.QUALIFICATION_PHONE_AREA_CODE);
const companyTypeOptions = enumStore.getEnumList(BusinessEnumType.CORPORATION_TYPE);
const fiatCcyOptions = enumStore.getEnumList(BusinessEnumType.ALL_FIAT_CCY);
const filteredOptions = ref<EnumItem[]>([]);

onMounted(async () => {
  // 初始化 filteredOptions 为完整的 phoneCodes 数据
  filteredOptions.value = phoneCodes.value;

  const companyInfo = props.kycInfo?.corpQualificationInfoVO || {};
  if (Object.keys(companyInfo).length !== 0) {
    form.value = companyInfo;
    form.value.ciFileList = companyInfo.fileInfos?.filter((item) => item.fileType === 'ERC') || [];
    form.value.brFileList = companyInfo.fileInfos?.filter((item) => item.fileType === 'BRC') || [];
    form.value.nar1FileList =
      companyInfo.fileInfos?.filter((item) => item.fileType === 'NAR1NNC1') || [];
    form.value.pastFiveYearsChangeAme = companyInfo.pastFiveYearsChangeAme || 'N';

    setTimeout(() => {
      companyForm.value?.validate().catch(() => {
        // 此处调用 validate 是为了在加载数据后立即在界面上显示校验状态，
        // Promise 校验失败是预期行为，因此只需捕获异常防止报错，无需处理。
      });
    }, 0);
  }
});

// 判断注册地址是否有值
const isRegisterAddressValid = computed(() => {
  return (
    form.value.registerAddress &&
    form.value.registerProv &&
    form.value.registerCity &&
    form.value.registerPostalCode
  );
});

// 自定义验证函数，检查 fileList 是否为空
const validateFileList = (rule: any, value: any[], callback: (error?: Error) => void) => {
  if (value.length === 0 && rule.field === 'ciFileList') {
    callback(new Error(t('companyInfo.validation.uploadCi')));
  } else if (value.length === 0 && rule.field === 'brFileList') {
    callback(new Error(t('companyInfo.validation.uploadBr')));
  } else if (value.length === 0 && rule.field === 'nar1FileList') {
    callback(new Error(t('companyInfo.validation.uploadNar1')));
  } else {
    companyForm.value?.clearValidate(rule.field);
    callback();
  }
};

// 自定义过滤方法，同时匹配国家名称和区号
const filterPhoneCode = (query: string) => {
  if (query === '') {
    filteredOptions.value = phoneCodes.value;
    return;
  }
  // 转为小写进行不区分大小写的匹配
  const queryLower = query.toLowerCase();

  filteredOptions.value = phoneCodes.value.filter((item) => {
    return (
      item.enumDescCn.toLowerCase().includes(queryLower) ||
      (item.extendField && item.extendField.toLowerCase().includes(queryLower))
    );
  });
  console.log(filteredOptions);
};

const campanyRules = computed(() => {
  const allRules: FormRules = {
    registCertificateNumber: [
      {
        required: true,
        message: t('companyInfo.validation.registCertificateNumber'),
        trigger: 'blur',
      },
    ],
    registerCode: [
      { required: true, message: t('companyInfo.validation.registerCode'), trigger: 'change' },
    ],
    establishDate: [
      { required: true, message: t('companyInfo.validation.establishDate'), trigger: 'change' },
    ],
    englishName: [
      { required: true, message: t('companyInfo.validation.englishName'), trigger: 'blur' },
    ],
    ciFileList: [{ validator: validateFileList, trigger: 'change' }],
    businessCertificateNumber: [
      {
        required: true,
        message: t('companyInfo.validation.businessCertificateNumber'),
        trigger: 'blur',
      },
    ],
    businessAddress: [
      { required: true, message: t('companyInfo.validation.businessAddress'), trigger: 'blur' },
    ],
    businessCertificateEndTime: [
      {
        required: true,
        message: t('companyInfo.validation.businessCertificateEndTime'),
        trigger: 'blur',
      },
    ],
    phone: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!form.value.phoneAreaCode) {
            callback(new Error(t('companyInfo.validation.selectAreaCode')));
          } else if (!value) {
            callback(new Error(t('companyInfo.validation.inputPhone')));
          } else {
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
    pastFiveYearsChangeAme: [
      {
        required: true,
        message: t('companyInfo.validation.pastFiveYearsChangeAme'),
        trigger: 'blur',
      },
    ],
    formerName: [
      { required: true, message: t('companyInfo.validation.formerName'), trigger: 'blur' },
    ],
    corporationType: [
      { required: true, message: t('companyInfo.validation.corporationType'), trigger: 'change' },
    ],
    registerCapital: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!form.value.fiatCcy) {
            callback(new Error(t('companyInfo.validation.registerCapitalUnit')));
          } else if (!value) {
            callback(new Error(t('companyInfo.validation.registerCapital')));
          } else {
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
    registerAddress: [
      { required: true, message: t('companyInfo.validation.registerAddress'), trigger: 'blur' },
    ],
    brFileList: [{ validator: validateFileList, trigger: 'change' }],
    nar1FileList: [{ validator: validateFileList, trigger: 'change' }],
    registerProv: [
      { required: true, message: t('companyInfo.validation.registerProv'), trigger: 'blur' },
    ],
    registerCity: [
      { required: true, message: t('companyInfo.validation.registerCity'), trigger: 'blur' },
    ],
    registerPostalCode: [
      { required: true, message: t('companyInfo.validation.registerPostalCode'), trigger: 'blur' },
    ],
    businessProv: [
      { required: true, message: t('companyInfo.validation.businessProv'), trigger: 'blur' },
    ],
    businessCity: [
      { required: true, message: t('companyInfo.validation.businessCity'), trigger: 'blur' },
    ],
    businessPostalCode: [
      { required: true, message: t('companyInfo.validation.businessPostalCode'), trigger: 'blur' },
    ],
  };
  return allRules;
});

const companyForm = ref<FormInstance | null>(null);

// 表单验证
const submitForm = async (validate: boolean) => {
  if (validate) {
    await companyForm.value?.validate();
  }
  const fileSeqIds = [
    ...(form.value.ciFileList || []).map((item) => item.fileSeqId),
    ...(form.value.brFileList || []).map((item) => item.fileSeqId),
    ...(form.value.nar1FileList || []).map((item) => item.fileSeqId),
  ];

  // 业务地址与注册地址一致，清空业务地址相关字段
  if (form.value.businessAddrEqualRegisterAddr === 'Y') {
    form.value.businessAddress = '';
    form.value.businessApartment = '';
    form.value.businessProv = '';
    form.value.businessCity = '';
    form.value.businessPostalCode = '';
  }

  const params = {
    moduleType: 'QUALIFICATION',
    modifyCorpQualificationInfoVO: {
      fileSeqIds,
      chineseName: form.value.chineseName,
      englishName: form.value.englishName,
      establishDate: form.value.establishDate,
      registCertificateNumber: form.value.registCertificateNumber,
      registerCode: form.value.registerCode,
      businessCertificateNumber: form.value.businessCertificateNumber,
      businessAddress: form.value.businessAddress,
      businessCertificateEndTime: form.value.businessCertificateEndTime,
      phone: form.value.phone,
      pastFiveYearsChangeAme: form.value.pastFiveYearsChangeAme,
      formerName: form.value.formerName,
      corporationType: form.value.corporationType,
      registerCapital: form.value.registerCapital,
      registerAddress: form.value.registerAddress,
      businessSeqId: form.value.businessSeqId,
      phoneAreaCode: form.value.phoneAreaCode,
      businessAddrEqualRegisterAddr: form.value.businessAddrEqualRegisterAddr,
      businessProv: form.value.businessProv,
      businessCity: form.value.businessCity,
      businessApartment: form.value.businessApartment,
      businessPostalCode: form.value.businessPostalCode,
      registerProv: form.value.registerProv,
      registerCity: form.value.registerCity,
      registerApartment: form.value.registerApartment,
      registerPostalCode: form.value.registerPostalCode,
      fiatCcy: form.value.fiatCcy,
    },
  };

  return params;
};

// 在 script setup 部分添加这个方法
const downloadTemplate = async (url: string) => {
  if (localStorage.getItem('locale') === 'zh-CN' && !url.includes('http')) {
    url = 'https://files.dingx.tech/kyc/cn' + url;
  } else if (localStorage.getItem('locale') === 'zh-HK' && !url.includes('http')) {
    url = 'https://files.dingx.tech/kyc/hk' + url;
  } else if (!url.includes('http')) {
    url = 'https://files.dingx.tech/kyc/en' + url;
  }
  if (typeof window !== 'undefined') {
    window.open(url, '_blank', 'noopener,noreferrer');
  }
};

// 监听 fileList 的变化
watch(
  () => form.value,
  (newValue) => {
    if (newValue.ciFileList && newValue.ciFileList.length > 0) {
      companyForm.value?.clearValidate('ciFileList');
    }
    if (newValue.brFileList && newValue.brFileList.length > 0) {
      companyForm.value?.clearValidate('brFileList');
    }
    if (newValue.nar1FileList && newValue.nar1FileList.length > 0) {
      companyForm.value?.clearValidate('nar1FileList');
    }
  },
  { deep: true }
);

// 监听 phoneCodes 变化，确保 filteredOptions 正确初始化
watch(
  () => phoneCodes.value,
  (newPhoneCodes) => {
    if (newPhoneCodes && newPhoneCodes.length > 0) {
      filteredOptions.value = newPhoneCodes;
    }
  },
  { immediate: true }
);

watch(
  [() => form.value.phone, () => form.value.phoneAreaCode],
  ([newPhone, newPhoneAreaCode]) => {
    if (newPhone && newPhoneAreaCode) {
      // Both fields have values, clear validation errors
      companyForm.value?.clearValidate('phone');
    }
  },
  { immediate: false }
);

watch(
  [() => form.value.fiatCcy, () => form.value.registerCapital],
  ([newFiatCcy, newRegisterCapital]) => {
    if (newFiatCcy && newRegisterCapital) {
      // Both fields have values, clear validation errors
      companyForm.value?.clearValidate(['registerCapital']);
    }
  },
  { immediate: false }
);

defineExpose<ComponentExposed>({
  submitForm,
});
</script>

<style lang="scss" scoped>
.custom-uploader :deep(.el-upload) {
  width: 100%;
  background-color: #f5f5f5;
}
.custom-uploader :deep(.el-upload-dragger) {
  width: 100%;
}

.custom-uploader :deep(.el-upload__tip) {
  color: #8a8a8a;
  margin-top: 8px;
  text-align: center;
}

.el-upload__text {
  color: #8a8a8a;
}

.bank-form .el-form-item {
  margin-bottom: 24px;
}
</style>
