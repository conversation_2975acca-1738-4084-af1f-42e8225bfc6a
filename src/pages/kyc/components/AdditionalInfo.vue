<template>
  <div class="additional-info-container">
    <div class="page-title">{{ t('additionalInfo.title') }}</div>
    <el-form :model="form" label-position="top" class="additional-form" ref="formRef" :rules="rules"
      hide-required-asterisk>
      <!-- 添加勾选框区域 -->
      <div class="agreement-section">
        <el-form-item prop="financialAgreementFlag" class="agreement-checkbox">
          <el-checkbox v-model="form.financialAgreementFlag">
            <span class="agreement-text">
              {{ t('additionalInfo.agreement.text') }}
              <el-button link @click="openAgreement(t('additionalInfo.downloadTemplateUrl'))" class="agreement-link">
                {{ t('additionalInfo.agreement.consultationService') }}
              </el-button>
              {{ t('additionalInfo.agreement.andOther') }}
            </span>
          </el-checkbox>
        </el-form-item>
      </div>
      <div class="section-divider mt-32px"></div>
      <!-- 组织大纲及章程 -->
      <div class="form-section">
        <el-form-item prop="maaoaFileList">
          <div class="form-label-section">
            <label class="section-label">
              {{ t('companyInfo.qualification.maaoa.title') }}
            </label>
            <el-button link @click="downloadTemplate(t('additionalInfo.downloadMaaoaUrl'))" class="template-link"
              size="small">
              {{ t('companyInfo.qualification.maaoa.downloadTemplate') }}
            </el-button>
          </div>
          <UploadFiles v-model:fileList="form.maaoaFileList" :limit="1" :fileType="'MAAOA'" />
        </el-form-item>
      </div>
      <div class="section-divider"></div>

      <!-- 业务真实性材料 -->
      <div class="form-section">
        <el-form-item prop="bamFileList">
          <div class="form-label-section">
            <label class="section-label">
              {{ t('companyInfo.qualification.bam.title') }}
            </label>
            <el-tooltip placement="right" effect="dark">
              <template #content>
                <p class="tooltip-content">{{ t('companyInfo.qualification.bam.tooltip') }}</p>
              </template>
              <svg-icon name="icon-info" class="text-13.3px color-[#A7ADB0] ml-3px" />
            </el-tooltip>
          </div>
          <UploadFiles v-model:fileList="form.bamFileList" :limit="1" :fileType="'BAM'" />
        </el-form-item>
      </div>

      <!-- 补充材料 -->
      <div class="form-section">
        <div class="section-divider"></div>
        <div class="subsection-title">
          {{ t('additionalInfo.supplementaryMaterials') }}
        </div>
        <div class="other-label">{{ t('additionalInfo.supplementaryMaterialsDesc') }}</div>
        <UploadFiles fileType="OTHER" :limit="10" :fileList="form.supplementFileSeqIds" class="supplementary-upload" />
      </div>

      <div class="bottom-divider"></div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { type FormInstance, type FormRules } from 'element-plus';
import { useRouter } from 'vue-router';
import UploadFiles from '@/common/components/UploadFiles/index.vue';
import { CorpModuleEnumType, CorpSupplementInfoVO } from '../apis/type';
import { ComponentExposed } from '../apis/type';
import { useI18n } from 'vue-i18n';

// 使用 i18n
const { t } = useI18n();

const props = withDefaults(
  defineProps<{
    additionalInfo?: CorpSupplementInfoVO;
  }>(),
  {
    additionalInfo: () => ({}),
  }
);

const router = useRouter();

const formRef = ref<FormInstance>();

const rules = computed(() => {
  const allRules: FormRules = {
    maaoaFileList: [
      { required: true, message: t('additionalInfo.rules.maaoaFileList'), trigger: 'change' },
    ],
    bamFileList: [
      { required: true, message: t('additionalInfo.rules.bamFileList'), trigger: 'change' },
    ],
    financialAgreementFlag: [
      {
        validator: (rule, value, callback) => {
          if (value !== 'Y' && value !== true) {
            callback(new Error(t('additionalInfo.rules.financialAgreementFlag')));
          } else {
            callback();
          }
        },
        trigger: 'change',
      },
    ],
  };
  return allRules;
});

const form = ref({
  supplementFileSeqIds: [] as any[],
  businessSeqId: '',
  maaoaFileList: [] as any[],
  bamFileList: [] as any[],
  financialAgreementFlag: false, // 改为boolean类型
});

// 下载模板函数
const downloadTemplate = (url: string) => {
  window.open(url, '_blank');
};

// 打开协议声明页面
const openAgreement = (url: string) => {
  // 可以根据需要打开不同的协议页面
  window.open(url, '_blank');
};

watch(
  () => props.additionalInfo,
  (newVal) => {
    if (newVal) {
      form.value.businessSeqId = newVal.businessSeqId || '';
      form.value.supplementFileSeqIds =
        newVal.fileInfos?.filter((item: any) => item.fileType === 'OTHER') || [];
      form.value.maaoaFileList =
        newVal.fileInfos?.filter((item: any) => item.fileType === 'MAAOA') || [];
      form.value.bamFileList =
        newVal.fileInfos?.filter((item: any) => item.fileType === 'BAM') || [];
      form.value.financialAgreementFlag = newVal.financialAgreementFlag === 'Y';
      formRef.value?.validate();
    }
  },
  { immediate: true }
);

const submitForm = async (validate: boolean) => {
  if (validate) {
    await formRef.value?.validate();
  }
  return {
    moduleType: CorpModuleEnumType.SUPPLEMENT,
    modifyCorpSupplementInfoVO: {
      financialAgreementFlag: form.value.financialAgreementFlag ? 'Y' : 'N',
      fileSeqIds: [
        ...form.value.supplementFileSeqIds,
        ...form.value.maaoaFileList,
        ...form.value.bamFileList,
      ].map((item: any) => item.fileSeqId),
      businessSeqId: form.value.businessSeqId,
    },
  };
};

defineExpose<ComponentExposed>({
  submitForm,
});
</script>

<style scoped>
/* :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #FF0064;
  border-color: #FF0064;
} */

/* Keep pink border color on hover for checked state */
/* :deep(.el-checkbox__input.is-checked .el-checkbox__inner:hover) {
  border-color: #FF0064;
} */

/* Keep original border color on hover for unchecked state */
/* :deep(.el-checkbox__input:not(.is-checked) .el-checkbox__inner:hover) {
  border-color: var(--el-border-color);
} */

.additional-info-container {
  background-color: white;
  height: 100%;
  overflow: auto;
  padding: 0;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #222527;
  line-height: 1.2;
}

.additional-form {
  width: 100%;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-label-section {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.section-label {
  font-size: 18px;
  font-weight: 600;
  color: #222527;
  font-family: 'PingFang SC', sans-serif;
  margin: 0;
}

.template-link {
  margin-left: 8px;
  padding: 0;
  font-size: 14px;
  font-weight: 400;
  color: #ff0064;
  text-decoration: none;
}

.template-link:hover {
  color: #ff0064;
  text-decoration: none;
}

.info-icon {
  margin-left: 8px;
  color: #909399;
  cursor: help;
}

.tooltip-content {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}

.section-divider {
  width: 100%;
  height: 1px;
  background-color: #ededee;
  margin-bottom: 24px;
}

.subsection-title {
  font-size: 18px;
  font-weight: 600;
  color: #222527;
  font-family: 'PingFang SC', sans-serif;
}

.supplementary-upload {
  width: 100%;
}

.other-label {
  margin-top: 8px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #6b7275;
  font-family: PingFangSC, sans-serif;
  font-weight: 400;
}

.bottom-divider {
  width: 100%;
  height: 1px;
  background-color: #ededee;
  margin-top: 32px;
}

/* Form item styles */
.additional-form :deep(.el-form-item) {
  margin-bottom: 24px;
}

.additional-form :deep(.el-form-item__label) {
  color: #222527;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  margin-bottom: 6px;
  padding: 0;
}

/* Upload component styles */
.additional-form :deep(.el-upload) {
  width: 100%;
  background-color: #f5f5f5;
}

.additional-form :deep(.el-upload-dragger) {
  width: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #f5f5f5;
  transition: border-color 0.3s;
}

.additional-form :deep(.el-upload-dragger:hover) {
  border-color: #ff0064;
}

.additional-form :deep(.el-upload__tip) {
  color: #8a8a8a;
  margin-top: 8px;
  text-align: center;
  font-size: 12px;
}

.additional-form :deep(.el-upload__text) {
  color: #8a8a8a;
  font-size: 14px;
}

/* Agreement section styles */
.agreement-section {
  margin-top: 24px;
}

.agreement-checkbox {
  position: relative;
}

.agreement-checkbox :deep(.el-form-item__content) {
  max-width: 690px;
  margin-left: 0;
  /* Remove default margin */
}

.agreement-checkbox :deep(.el-form-item__error) {
  position: absolute;
  top: 100%;
  left: 24px;
  /* 与 checkbox 文字左对齐 */
  margin-left: 0 !important;
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
}

.agreement-checkbox :deep(.el-checkbox) {
  width: 100%;
  max-width: 690px;
  align-items: flex-start;
  /* 让整个checkbox容器顶部对齐 */
}

.agreement-checkbox :deep(.el-checkbox__inner) {
  width: 16px;
  height: 16px;
}

.agreement-checkbox :deep(.el-checkbox__inner::after) {
  width: 4px;
  height: 8px;
  left: 5px;
  top: 1px;
}

.agreement-checkbox :deep(.el-checkbox__label) {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
  max-width: calc(690px - 20px);
  /* 减去checkbox图标的宽度 */
  vertical-align: top;
  /* 确保标签与顶部对齐 */
}

.agreement-text {
  color: #6b7275;
  font-size: 12px;
  font-family: PingFangSC, sans-serif;
  font-weight: 400;
  display: inline-block;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
}

:deep(.agreement-link) {
  margin: 0 !important;
  padding: 0 !important;
  font-size: 12px !important;
  color: #ff0064 !important;
  text-decoration: none;
  font-weight: 400 !important;
  vertical-align: baseline !important;
  display: inline !important;
  line-height: 1.5 !important;
  height: auto !important;
  min-height: auto !important;
  border: none !important;
  background: none !important;
  outline: none !important;
  box-shadow: none !important;
}

:deep(.agreement-link:hover) {
  color: #ff0064 !important;
  text-decoration: underline;
  background: none !important;
  border: none !important;
}

:deep(.agreement-link .el-button__text) {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1.5 !important;
  font-size: 12px !important;
}
</style>
