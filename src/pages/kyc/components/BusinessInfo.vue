<template>
  <div class="bg-white h-[100%] pb-10px">
    <div class="text-28px font-semibold text-[#222527]">{{ t('businessInfo.title') }}</div>

    <el-form ref="businessRef" :hide-required-asterisk="true" :model="form" :rules="campanyRules" label-position="top"
      class="bank-form">
      <el-row :gutter="170" class="mt-24px">
        <el-col :span="24">
          <el-form-item :label="t('businessInfo.businessAddrEqualRegisterAddr')">
            <el-switch v-model="form.businessAddrEqualRegisterAddr" active-value="Y" inactive-value="N" />
          </el-form-item>
        </el-col>

        <el-col :span="24" v-if="form.businessAddrEqualRegisterAddr !== 'Y'">
          <el-form-item :label="t('companyInfo.qualification.registerAddress')" prop="businessAddress">
            <el-input v-model="form.businessAddress" maxlength="100"
              :placeholder="t('companyInfo.placeholder.input')"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="form.businessAddrEqualRegisterAddr !== 'Y'">
          <el-form-item :label="t('companyInfo.qualification.businessApartment')" prop="businessApartment">
            <el-input v-model="form.businessApartment" :placeholder="t('companyInfo.placeholder.input')"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="form.businessAddrEqualRegisterAddr !== 'Y'">
          <el-form-item :label="t('companyInfo.qualification.businessCity')" prop="businessCity">
            <el-input v-model="form.businessCity" :placeholder="t('companyInfo.placeholder.input')"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="form.businessAddrEqualRegisterAddr !== 'Y'">
          <el-form-item :label="t('companyInfo.qualification.businessProv')" prop="businessProv">
            <el-input v-model="form.businessProv" :placeholder="t('companyInfo.placeholder.input')"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12" v-if="form.businessAddrEqualRegisterAddr !== 'Y'">
          <el-form-item prop="businessPostalCode">
            <div class="flex items-center">
              <span class="mr-4px">{{ t('companyInfo.qualification.businessPostalCode') }}</span>
              <el-tooltip placement="right">
                <template #content>
                  <p style="margin: 0">
                    {{ t('companyInfo.validation.businessPostalCodeTooltip') }}
                  </p>
                </template>
                <svg-icon name="icon-info" class="text-13.3px color-[#A7ADB0] ml-3px" />
              </el-tooltip>
            </div>
            <el-input v-model="form.businessPostalCode" :placeholder="t('companyInfo.placeholder.input')"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="t('businessInfo.website')" prop="website">
            <el-input v-model="form.website" :placeholder="t('businessInfo.placeholder.website')"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="t('businessInfo.industryInfo')" prop="industryList">
            <el-cascader :placeholder="t('businessInfo.placeholder.select')" class="w-100%" v-model="form.industryList"
              :props="customProps" :options="industrys" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item :label="t('businessInfo.mainBusinessAddress')" prop="mainBusinessAddress">
            <el-select v-model="form.mainBusinessAddress" filterable multiple collapse-tags collapse-tags-tooltip
              :max-collapse-tags="20" :placeholder="t('businessInfo.placeholder.select')" style="width: 100%">
              <el-option v-for="info in regions" :key="info.enumCode" :label="info.enumDescCn"
                :value="info.enumCode"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="t('businessInfo.firstFundingSource')" prop="firstFundingSource">
            <el-select v-model="form.firstFundingSource" :placeholder="t('businessInfo.placeholder.select')"
              style="width: 100%">
              <el-option v-for="info in firstFundingSource" :key="info.enumCode" :label="info.enumDescCn"
                :value="info.enumCode"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="t('businessInfo.wealthSource')" prop="wealthSource">
            <el-select v-model="form.wealthSource" :placeholder="t('businessInfo.placeholder.select')"
              style="width: 100%">
              <el-option v-for="info in wealthSource" :key="info.enumCode" :label="info.enumDescCn"
                :value="info.enumCode"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="t('businessInfo.continuousFundingSource')" prop="continuousFundingSource">
            <el-select v-model="form.continuousFundingSource" :placeholder="t('businessInfo.placeholder.select')"
              style="width: 100%">
              <el-option v-for="info in continuousFundingSource" :key="info.enumCode" :label="info.enumDescCn"
                :value="info.enumCode"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="t('businessInfo.salesLastYear')">
            <el-input v-model="form.salesLastYear" :placeholder="t('businessInfo.placeholder.input')"
              :formatter="moneyFormatInputExpose" :parser="moneyParseInputExpose">
              <template #prepend>
                <el-select filterable v-model="form.fiatCcy" :placeholder="t('companyInfo.placeholder.select')"
                  style="width: 88px">
                  <el-option v-for="info in fiatCcyOptions" :key="info.enumCode" :label="info.extendField"
                    :value="info.enumCode">
                    <p style="display: flex; margin: 0">
                      <span>{{ info.enumDescCn }}</span>
                    </p>
                  </el-option>
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="t('businessInfo.staffNumber')">
            <el-input v-model="form.staffNumber" :placeholder="t('businessInfo.placeholder.input')"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="t('businessInfo.settlePurpose')" prop="settlePurpose">
            <el-select v-model="form.settlePurpose" :placeholder="t('businessInfo.placeholder.select')"
              style="width: 100%">
              <el-option v-for="info in settlePurpose" :key="info.enumCode" :label="info.enumDescCn"
                :value="info.enumCode"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { CorpAuthData, ComponentExposed } from '../apis/type';
import { useI18n } from 'vue-i18n'; // 添加 i18n 引用
import { moneyFormatInput, moneyParseInput } from '@@/utils/format';
// 使用 i18n
const { t } = useI18n();

const moneyFormatInputExpose = moneyFormatInput;
const moneyParseInputExpose = moneyParseInput;
interface businessInfoForm {
  website?: string;
  businessAddress: string;
  mainBusinessAddress: string[] | string;
  industryList: string[] | string;
  firstFundingSource: string;
  wealthSource: string;
  continuousFundingSource: string;
  salesLastYear?: string;
  staffNumber?: string;
  settlePurpose: string;
  subIndustry?: any;
  industry?: any;
  businessAddrEqualRegisterAddr: string;
  businessApartment: string;
  businessCity: string;
  businessProv: string;
  businessPostalCode: string;
  fiatCcy: string;
}
const form = ref<businessInfoForm>({
  website: '',
  businessAddress: '',
  mainBusinessAddress: [],
  industryList: [],
  firstFundingSource: '',
  wealthSource: '',
  continuousFundingSource: '',
  salesLastYear: '',
  staffNumber: '',
  settlePurpose: '',
  businessAddrEqualRegisterAddr: 'N',
  businessApartment: '',
  businessCity: '',
  businessProv: '',
  businessPostalCode: '',
  fiatCcy: '',
});

// 定义 props
const props = withDefaults(
  defineProps<{
    kycInfo: CorpAuthData;
  }>(),
  {}
);

/**
 * 网址格式验证函数
 * @param rule 验证规则
 * @param value 输入值
 * @param callback 回调函数
 */
const validateWebsite = (rule: any, value: string, callback: any) => {
  if (!value) {
    // 网址为空时不进行验证（因为是选填字段）
    callback();
    return;
  }

  // URL正则表达式，支持http、https协议
  const urlRegex = /^(https?:\/\/)([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;

  if (!urlRegex.test(value)) {
    return false;
  } else {
    return true;
  }
};

const campanyRules = computed(() => {
  const allRules: FormRules = {
    businessAddress: [
      { required: true, message: t('businessInfo.rules.businessAddress'), trigger: 'blur' },
    ],
    mainBusinessAddress: [
      { required: true, message: t('businessInfo.rules.mainBusinessAddress'), trigger: 'change' },
    ],
    industryList: [
      { required: true, message: t('businessInfo.rules.industryList'), trigger: 'change' },
    ],
    firstFundingSource: [
      { required: true, message: t('businessInfo.rules.firstFundingSource'), trigger: 'change' },
    ],
    wealthSource: [
      { required: true, message: t('businessInfo.rules.wealthSource'), trigger: 'change' },
    ],
    continuousFundingSource: [
      {
        required: true,
        message: t('businessInfo.rules.continuousFundingSource'),
        trigger: 'change',
      },
    ],
    settlePurpose: [
      { required: true, message: t('businessInfo.rules.settlePurpose'), trigger: 'change' },
    ],
    businessCity: [
      { required: true, message: t('businessInfo.rules.businessCity'), trigger: 'blur' },
    ],
    businessProv: [
      { required: true, message: t('businessInfo.rules.businessProv'), trigger: 'blur' },
    ],
    businessPostalCode: [
      { required: true, message: t('businessInfo.rules.businessPostalCode'), trigger: 'blur' },
    ],
    website: [
      { validator: validateWebsite, message: t('businessInfo.rules.website'), trigger: 'blur' },
    ],
  };
  return allRules;
});

const businessRef = ref<FormInstance | null>(null);

const countryList = ref<any[]>([]);

// 行业自定义字段映射
const customProps = {
  value: 'enumCode', // 指定 value 字段为 id
  label: 'enumDescCn', // 指定 label 字段为 name
  children: 'children', // 指定 children 字段为 subs
};

// 获取枚举
interface EnumItem {
  enumCode: string;
  enumDescCn: string;
}
import { BusinessEnumType } from '@@/apis/common/type';
import { useEnumStore } from '@/pinia/stores/enumStore';
const enumStore = useEnumStore();
const regions = enumStore.getEnumList(BusinessEnumType.MAIN_BUSINESS_ADDR_NATIONALITY);
const industrys = enumStore.getEnumList(BusinessEnumType.INDUSTRY);
const firstFundingSource = enumStore.getEnumList(BusinessEnumType.FIRST_FUNDING_SOURCE);
const continuousFundingSource = enumStore.getEnumList(BusinessEnumType.CONTINUOUS_FUNDING_SOURCE);
const wealthSource = enumStore.getEnumList(BusinessEnumType.WEALTH_SOURCE);
const settlePurpose = enumStore.getEnumList(BusinessEnumType.SETTLE_PURPOSE);
const fiatCcyOptions = enumStore.getEnumList(BusinessEnumType.ALL_FIAT_CCY);

// 判断注册地址是否有值
const isRegisterAddressValid = computed(() => {
  return (
    props.kycInfo?.corpQualificationInfoVO?.registerAddress &&
    props.kycInfo?.corpQualificationInfoVO?.registerProv &&
    props.kycInfo?.corpQualificationInfoVO?.registerCity &&
    props.kycInfo?.corpQualificationInfoVO?.registerPostalCode
  );
});

onMounted(async () => {
  const businessInfo = props.kycInfo?.corpBusinessInfoVO || {};
  if (businessInfo) {
    form.value.website = businessInfo.website;
    form.value.businessAddress = businessInfo.businessAddress || '';
    form.value.mainBusinessAddress =
      (businessInfo.mainBusinessAddress && businessInfo.mainBusinessAddress.split(',')) || [];
    form.value.industryList =
      businessInfo.industry && businessInfo.subIndustry
        ? [businessInfo.industry, businessInfo.subIndustry]
        : [];
    form.value.firstFundingSource = businessInfo.firstFundingSource || '';
    form.value.wealthSource = businessInfo.wealthSource || '';
    form.value.continuousFundingSource = businessInfo.continuousFundingSource || '';
    form.value.salesLastYear = businessInfo.salesLastYear;
    form.value.staffNumber = businessInfo.staffNumber;
    form.value.settlePurpose = businessInfo.settlePurpose || '';
    form.value.businessAddrEqualRegisterAddr = businessInfo.businessAddrEqualRegisterAddr || 'N';
    form.value.businessApartment = businessInfo.businessApartment || '';
    form.value.businessCity = businessInfo.businessCity || '';
    form.value.businessProv = businessInfo.businessProv || '';
    form.value.businessPostalCode = businessInfo.businessPostalCode || '';
    form.value.fiatCcy = businessInfo.fiatCcy || '';
  }
  if (businessInfo && Object.keys(businessInfo).length > 0) {
    businessRef.value?.validate();
  }
});

const submitForm = async (validate: boolean) => {
  if (validate) {
    await businessRef.value?.validate();
  }
  const modifyCorpBusinessInfoVO = { ...form.value };
  if (Array.isArray(modifyCorpBusinessInfoVO.mainBusinessAddress)) {
    modifyCorpBusinessInfoVO.mainBusinessAddress =
      modifyCorpBusinessInfoVO.mainBusinessAddress.join(',');
  }
  // 业务地址与注册地址一致，清空业务地址相关字段
  if (modifyCorpBusinessInfoVO.businessAddrEqualRegisterAddr === 'Y') {
    modifyCorpBusinessInfoVO.businessAddress = '';
    modifyCorpBusinessInfoVO.businessApartment = '';
    modifyCorpBusinessInfoVO.businessProv = '';
    modifyCorpBusinessInfoVO.businessCity = '';
    modifyCorpBusinessInfoVO.businessPostalCode = '';
  }
  const params = {
    moduleType: 'BUSINESS',
    modifyCorpBusinessInfoVO,
  };
  const [industry, subIndustry] = params?.modifyCorpBusinessInfoVO.industryList as string[];
  params.modifyCorpBusinessInfoVO.industry = industry;
  params.modifyCorpBusinessInfoVO.subIndustry = subIndustry;
  return params;
};

defineExpose<ComponentExposed>({
  submitForm,
});
</script>

<style scoped>
.custom-uploader :deep(.el-upload) {
  width: 100%;
  background-color: #f5f5f5;
}

.custom-uploader :deep(.el-upload-dragger) {
  width: 100%;
}

.custom-uploader :deep(.el-upload__tip) {
  color: #8a8a8a;
  margin-top: 8px;
  text-align: center;
}

.el-upload__text {
  color: #8a8a8a;
}

.bank-form .el-form-item {
  margin-bottom: 24px;
}
</style>
