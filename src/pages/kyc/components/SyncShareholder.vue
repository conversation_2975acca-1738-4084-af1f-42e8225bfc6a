<template>
  <el-drawer 
    v-model="showDrawer" 
    size="400px" 
    style="--el-drawer-padding-primary: 0px"
    header-class="custom-drawer-header"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :close-on-press-escape="false"
  >
    <template #header>
      <div class="h-60px border-b-1 mb-0 flex-row flex-items-center text-18px font-500 color-[#222527] pt-18px pb-18px pl-24px">
        <span>{{ t('directorInfo.syncShareholderInfo') }}</span>
      </div>
    </template>
    <template #default>
      <div class="p-b-70px p-24px">
        <p class="text-14px font-400 color-[#6B7275] mt-6px line-height-20px">
          {{ t('directorInfo.syncShareholderDesc') }}
        </p>
        <el-form ref="shareholderForm" hide-required-asterisk label-position="top" class="mt-24px">
          <el-form-item :label="t('directorInfo.selectShareholder')">
            <el-select 
              v-model="selectedShareholder" 
              :placeholder="t('shareholderInfo.selectPlaceholder')" 
              style="width: 100%;"
              :value-key="'businessSeqId'"
            >
              <el-option
                v-for="item in props.shareholderList"
                :key="item.businessSeqId"
                :label="item.enFirstName + ' ' + item.enLastName"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div 
        style="border-top: 1px solid #E5E6EB;"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
          <el-button @click="cancelShowDrawer" class="w-68px h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px">{{ t('shareholderInfo.btnCancel') }}</el-button>
          <el-button @click="addShareholder" color="white" class="w-68px h-32px bg-[#030814] rounded-6px" type="primary" plain>{{ t('shareholderInfo.btnAddConfirm') }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import type { FormRules, FormInstance } from "element-plus"
import { EditShareholderInfo, EditDirectorInfo, IdTypeEnum, GenderEnum, StatusEnum } from '../apis/type';
import { t } from '@@/i18n';
import { isReallyEmpty } from '@/common/utils/validate'
import { useEnumStore } from '@/pinia/stores/enumStore'
import moment from 'moment';

const props = withDefaults(defineProps<{
  shareholderList?: EditShareholderInfo[]
  modelValue: boolean
}>(), {
  modelValue: true,
  shareholderList: () => []
});

const emit = defineEmits<{
  (e: 'submit', payload: EditDirectorInfo): void
  (e: 'cancel'): void
  (e: 'update:modelValue', value: boolean): void
}>()

// 使用计算属性实现双向绑定
const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const selectedShareholder = ref<EditShareholderInfo | undefined>(undefined)

watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    selectedShareholder.value = undefined
  }
}, { immediate: true })

onMounted(async() => {
  console.log(">>>props>>>", props.shareholderList)
})

// 添加股东
const addShareholder = () => {
  if (!isReallyEmpty(selectedShareholder.value)) {
    emit('submit', {
      shareholdingSeqId: selectedShareholder.value!.businessSeqId,
      shareholdingSynFlag: 'Y',
      status: StatusEnum.D,
      fileInfos: selectedShareholder.value!.fileInfos,
      nationality: selectedShareholder.value!.nationality,
      idType: selectedShareholder.value!.idType,
      enFirstName: selectedShareholder.value!.enFirstName,
      enLastName: selectedShareholder.value!.enLastName,
      cnFirstName: selectedShareholder.value!.cnFirstName,
      cnLastName: selectedShareholder.value!.cnLastName,
      expirationStart: selectedShareholder.value!.expirationStart,
      expirationEnd: selectedShareholder.value!.expirationEnd,
      gender: selectedShareholder.value!.gender,
      idNumber: selectedShareholder.value!.idNumber,
      residenceCountry: selectedShareholder.value!.residenceCountry,
      residenceAddress: selectedShareholder.value!.residenceAddress,
    })
  }
}

const cancelShowDrawer = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}
.custom-name-input {
  display: flex;
  flex-direction: row;
  border: 1px solid #E5E6EB;
  border-radius: 6px;
  width: 100%;
  :deep(.el-input__wrapper) {
    box-shadow: none;
    border: none;
  }
}
:deep(.el-input__wrapper) {
  border: 1px solid #E5E6EB;
  box-shadow: none;
}
.custom-append-input {
  :deep(.el-input__wrapper) {
    border: 1px solid #E5E6EB;
    border-right: none;
  }
  :deep(.el-input-group__append) {
    background-color: transparent;
    border: 1px solid #E5E6EB;
    box-shadow: none;
    border-left: none;
  }
}
:deep(.el-switch__core) {
  height: 24px;
  border-radius: 12px;
}
:deep(.el-form-item) {
  margin-bottom: 0;
}
:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
}
:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }
  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
</style>
