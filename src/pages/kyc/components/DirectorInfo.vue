<template>
  <div class="bg-white pb-136px">
    <div class="text-28px font-semibold text-[#222527]">{{ t('directorInfo.title') }}</div>
    <template v-if="registerCode !== 'HK' && registerCode">
      <h3 class="mt-24px text-18px font-semibold text-[#222527]">{{ t('directorInfo.coiTitle') }}</h3>
      <p class="mt-16px text-14px text-[#222527] line-height-20px">
        {{ t('directorInfo.coiDesc') }}
      </p>
      <UploadFiles fileType="DCOFOECM" :limit="1" v-model:fileList="coiFileInfos" class="w-full"/>
      <div v-if="showCoiFileidError" class="text-#FD3627 text-12px mt-12px">{{ t('directorInfo.coiUploadTip') }}</div>
      <div class="mt-24px mb-24px w-100% bg-#EDEDEE h-1px" /> 
    </template>
    <p class="mt-16px  mb-32px text-14px text-[#6B7275] line-height-20px">
      {{ t('directorInfo.description') }}
    </p>
    <div class="flex-justify-items-center">
      <!-- 董事列表 -->
      <el-table :data="localDirectorList" style="width: 100%">
        <el-table-column :label="t('shareholderInfo.index')" width="90">
          <template #default="{ row, $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="type" :label="t('directorInfo.directorType')">
          <template #default="{ row }">
            <div>
              <el-row>{{ t('directorInfo.directorTypeDesc') }} {{ row.enFirstName }}{{ row.enLastName }}</el-row>
              <el-row v-if="row.shareholdingSeqId">({{ t('directorInfo.dispatchSyncStatusDesc') }} {{ row.enFirstName }}{{ row.enLastName }})</el-row>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="t('shareholderInfo.progress')" width="130">
          <template #default="{ row }">
            <div class="flex items-center">
              <div :class="['status-dot']" :style="{ backgroundColor: getDirectorStatusClass(StatusEnum.D) }" />
              <div>{{ getDirectorStatusText(StatusEnum.D) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="t('shareholderInfo.action')" width="130">
          <template #default="scope">
            <el-tooltip v-if="scope.row.shareholdingSynFlag === 'Y'" :content="t('shareholderInfo.preview')" placement="top" effect="light">
              <el-button text @click="handlePreview(scope.row, scope.$index)">
                <SvgIcon name="icon-kyc-watch" class="w-16px h-16px color-[#030814]" />
              </el-button>
            </el-tooltip>
            <el-tooltip v-else :content="t('shareholderInfo.action')" placement="top" effect="light">
              <el-button text @click="handleEdit(scope.row, scope.$index)">
                <SvgIcon name="icon-edit" class="w-16px h-16px color-[#030814]" />
              </el-button>
            </el-tooltip>
            <el-tooltip :content="t('shareholderInfo.delete')" placement="top" effect="light">
              <el-button :disabled="localDirectorList.length < 2" text @click="handleDelete(scope.row, scope.$index)">
                <SvgIcon name="icon-deleted" class="w-16px h-16px color-[#030814]" />
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <template #empty>
          <div class="flex flex-col flex-items-center flex-justify-center mt-48px">
            <img src="@/common/assets/icons/table-empty.svg" alt="table-empty" class="w-100px h-100px" />
            <p class="text-14px text-[#6B7275] mt-8px">{{ t('shareholderInfo.tableEmpty') }}</p>
          </div>
        </template>
      </el-table>

      <div class="mt-24px text-right">
        <el-button :disabled="localDirectorList.length >= 10" @click="handleAddDirector" class="min-w-104px min-h-32px bg-[#FFFFFF] add-btn">
          <SvgIcon name="icon-add" class="w-16px h-16px color-[#030814]" />
          <span class="text-14px font-400 color-[#030814] ml-8px">{{ t('directorInfo.addDirector') }}</span>
        </el-button>
        <el-button :disabled="localDirectorList.length >= 10" @click="handleSyncShareholder" class="min-w-104px min-h-32px bg-[#FFFFFF] add-btn">
          <SvgIcon name="icon-sync-info" class="w-16px h-16px color-[#030814]" />
          <span class="text-14px font-400 color-[#030814] ml-8px">{{ t('directorInfo.syncShareholderInfo') }}</span>
        </el-button>
      </div>
    </div>

    <template v-if="currentDirectorInfo">
       <EditDirector 
        v-model="showEditDirector" 
        :directorInfo="currentDirectorInfo"
        :is-edit="editIsEdit"
        @submit="handleAddDirectorSubmit" 
        @cancel="handleCancelEditDirector"
      />
    </template>
    
    <SyncShareholder 
      v-model="showSyncDirector" 
      :shareholderList="syncShareholderList"
      @submit="handleSyncDirectorSubmit" 
      @cancel="handleCancelSyncShareholder"
    />
  </div>
</template>

<script setup lang="ts">
import EditDirector from './EditDirector.vue';
import SyncShareholder from './SyncShareholder.vue';
import { EditDirectorInfo, EditShareholderInfo, StatusEnum, ApproveStatusEnum, CorpModuleEnumType, ComponentExposed, CorpDirectorInfoVO, FileInfo } from '../apis/type';
import { t } from '@@/i18n';
import { cloneDeep } from 'lodash-es';
import { isReallyEmpty } from '@/common/utils/validate'
import { useComponent } from '../composables/useComponent';
import DialogService from '@/common/components/Dialog/DialogService';
import UploadFiles from '@@/components/UploadFiles/index.vue'

const defaultDirectorInfo: EditDirectorInfo = {
  fileSeqIds: [],
  nationality: '',
  idType: '',
  enFirstName: '',
  enLastName: '',
  cnFirstName: '',
  cnLastName: '',
  expirationStart: '',
  expirationEnd: '',
  gender: '',
  idNumber: '',
  residenceCountry: '',
  residenceAddress: '',
  shareholdingSeqId: '',
  shareholdingSynFlag: '',
  status: StatusEnum.I,
  approveStatus: ApproveStatusEnum.I,
  opinions: '',
}

const props = withDefaults(defineProps<{
  directorInfo?: CorpDirectorInfoVO
  shareholderList?: EditShareholderInfo[]
  registerCode: string
}>(), {
  shareholderList: () => [],
  registerCode: ''
});

const { getStatusClass, getStatusText } = useComponent()

const showEditDirector = ref(false)
const showSyncDirector = ref(false)
const localDirectorList = ref<EditDirectorInfo[]>(props.directorInfo?.corpDirectorInfoVOs || []);
const coiFileInfos = ref(props.directorInfo?.fileInfos || []) // 董事在职证明
const coiFileids = ref<string[]>([]) // 董事在职证明
const showCoiFileidError = ref(false)
const localShareholderList = ref<EditShareholderInfo[]>([]); // 可以用来同步的股东数据，原始数据
const syncShareholderList = ref<EditShareholderInfo[]>([]); // 可以用来同步的股东数据
const currentDirectorInfo = ref<EditDirectorInfo | null>(null)
const currentIndex = ref(-1)
// 编辑董事信息页面是否可以编辑 默认可以
const editIsEdit = ref(true)

const getDirectorStatusClass = getStatusClass;
const getDirectorStatusText = getStatusText;

watch(() => props.directorInfo, (newVal) => {
  if (newVal) {
    localDirectorList.value = []
    const directorList = newVal.corpDirectorInfoVOs?.map(item => {
      if (item?.idNumber) {
        return {
          ...item,
          fileSeqIds: item.fileInfos?.map(file => file.fileSeqId || '').filter(Boolean) || []
        }
      }
      return null
    }).filter(Boolean)
    const temp = (directorList || []) as EditDirectorInfo[]
    localDirectorList.value.push(...temp)
    coiFileInfos.value = newVal.fileInfos || []
  }

  console.log(">>localDirectorList>>>初始",localDirectorList)
}, { immediate: true })

// 监听股东信息必须是已完成的数据
watch(() => props.shareholderList, (newVal) => {
  if (newVal) {
    localShareholderList.value = [...newVal]
  }
}, { immediate: true });

// 监听董事在职证明文件
watch(() => coiFileInfos.value, (newVal) => {
  if (newVal && newVal.length > 0) {
    showCoiFileidError.value = false
    coiFileids.value = newVal.map(item => item.fileSeqId || '').filter(Boolean)
  }else{
    coiFileids.value = []
    showCoiFileidError.value = true
  }
}, { deep: true, immediate: true });

const handleEdit = (row: EditDirectorInfo, index: number) => {
  currentDirectorInfo.value = cloneDeep(row)
  currentIndex.value = index
  showEditDirector.value = true
  editIsEdit.value = true
};

const handleDelete = async (row: EditDirectorInfo, index: number) => {
  const confirmed = await DialogService.confirm('确定要删除该董事信息吗？', '删除确认')
  if (confirmed) {
    localDirectorList.value.splice(index, 1)
  }
};
// 添加董事信息
const handleAddDirector = () => {
  if (localDirectorList.value.length < 10) {
    currentDirectorInfo.value = cloneDeep(defaultDirectorInfo)
    showEditDirector.value = true
    editIsEdit.value = true
  }
};
// 预览同步股东的董事信息
const handlePreview = (row: EditDirectorInfo, index: number) => {
  currentDirectorInfo.value = cloneDeep(row)
  currentIndex.value = index
  showEditDirector.value = true
  editIsEdit.value = false
}
// 同步股东信息
const handleSyncShareholder = () => {
  if (localDirectorList.value.length < 10) {
    // 去掉已经被选为董事的股东
    syncShareholderList.value = localShareholderList.value.filter(item => !localDirectorList.value.find(director => director.shareholdingSeqId === item.businessSeqId))
    showSyncDirector.value = true
  }
}
const handleAddDirectorSubmit = (newDirector: EditDirectorInfo) => {
  // 检查股东数组中是否包含已经存在的证件号,如果存在则不能添加 提示错误
  if (localDirectorList.value.some(item => item.idNumber === newDirector.idNumber && item.businessSeqId !== newDirector.businessSeqId)) {
    ElMessage.warning(t('directorInfo.formValidateIdNumberExist'))
    return
  }

  if (currentIndex.value !== -1) {
    localDirectorList.value.splice(currentIndex.value, 1, {
      ...newDirector,
      shareholdingSynFlag: '',
    })
    currentIndex.value = -1
  }else{
    localDirectorList.value.push({
      ...newDirector,
      shareholdingSynFlag: '',
    });
  }
  showEditDirector.value = false

  console.log(">>Add>>newDirector>>>>", newDirector)
  console.log(">>Add>>localDirectorList>>>", localDirectorList.value)
}

const handleSyncDirectorSubmit = (newDirector: EditDirectorInfo) => {
  // 检查股东数组中是否包含已经存在的证件号,如果存在则不能添加 提示错误
  if (localDirectorList.value.some(item => item.idNumber === newDirector.idNumber)) {
    ElMessage.warning(t('directorInfo.formValidateIdNumberExist'))
    return
  }

  if (currentIndex.value !== -1) {
    localDirectorList.value.splice(currentIndex.value, 1, {
      ...newDirector,
      shareholdingSynFlag: 'Y',
    })
    currentIndex.value = -1
  }else{
    localDirectorList.value.push({
      ...newDirector,
      shareholdingSynFlag: 'Y',
    });
  }
  showSyncDirector.value = false

  console.log(">>Sync>>newDirector>>>>", newDirector)
  console.log(">>Sync>>localDirectorList>>>", localDirectorList.value)
}

const handleCancelEditDirector = () => {
  showEditDirector.value = false
  currentIndex.value = -1
}

const handleCancelSyncShareholder = () => {
  showSyncDirector.value = false
}


const submitForm = async (validate: boolean) => {
  if(validate){
    // 如果没有任何股东数据则提示错误
    if (localDirectorList.value.length === 0) {
      return false;
    }
    if (props.registerCode !== 'HK' && coiFileInfos.value.length === 0) {
      showCoiFileidError.value = true
      return false
    }
  }
  const directorList = localDirectorList.value.map(item => {
    return {
      fileSeqIds: item.shareholdingSynFlag === 'Y' ? [] : isReallyEmpty(item.fileSeqIds) ? [] : item.fileSeqIds,
      businessSeqId: item.businessSeqId,
      nationality: item.shareholdingSynFlag === 'Y' ? "" : item.nationality,
      idType: item.shareholdingSynFlag === 'Y' ? "" : item.idType,
      enFirstName: item.shareholdingSynFlag === 'Y' ? "" : item.enFirstName,
      enLastName: item.shareholdingSynFlag === 'Y' ? "" : item.enLastName,
      cnFirstName: item.shareholdingSynFlag === 'Y' ? "" : item.cnFirstName,
      cnLastName: item.shareholdingSynFlag === 'Y' ? "" : item.cnLastName,
      expirationStart: item.shareholdingSynFlag === 'Y' ? "" : item.expirationStart,
      expirationEnd: item.shareholdingSynFlag === 'Y' ? "" : item.expirationEnd,
      gender: item.shareholdingSynFlag === 'Y' ? "" : item.gender,
      idNumber: item.shareholdingSynFlag === 'Y' ? "" : item.idNumber,
      residenceCountry: item.shareholdingSynFlag === 'Y' ? "" : item.residenceCountry,
      residenceAddress: item.shareholdingSynFlag === 'Y' ? "" : item.residenceAddress,
      shareholdingSynFlag: item.shareholdingSynFlag,
      shareholdingSeqId: item.shareholdingSynFlag === 'Y' ? item.shareholdingSeqId : ""
    }
  });
  return {
    moduleType: CorpModuleEnumType.DIRECTOR,
    modifyCorpDirectorVO: {
      fileSeqIds: coiFileids.value,
      businessSeqId: props.directorInfo?.businessSeqId,
      modifyCorpDirectorInfoVOs: isReallyEmpty(directorList) ? [{}] : directorList,
    }
  }
}

// 定义子组件暴露给父组件的接口
defineExpose<ComponentExposed>({
  submitForm
})

</script>

<style lang="scss" scoped>
p {
  margin: 0;
}
:deep(.el-table) {
  --el-table-header-bg-color: #F8F9FA;
  --el-table-header-text-color: #6B7275;
  --el-table-border-color: none;
  thead tr {
    --el-table-border: none;
  }

  .el-table__header-wrapper {
    border-radius: 12px;
    font-weight: 600;
    font-size: 12px;
    color: #6B7275;
  }
}
.add-btn {
  padding: 6px 12px;
  border: 1px dashed #030814;
  border-radius: 6px;
}
:deep(.el-button.is-text) {
  padding: 0
}
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}
</style> 
