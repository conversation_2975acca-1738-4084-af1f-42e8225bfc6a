<template>
  <el-drawer v-model="showDrawer" size="600px" style="--el-drawer-padding-primary: 0"
    header-class="custom-drawer-header" :destroy-on-close="true">
    <template #header>
      <div
        class="h-60px border-b-1 mb-0 flex-row flex-items-center text-18px font-500 color-[#222527] pt-18px pb-18px pl-24px">
        <span>{{ t('opinions.title') }}</span>
      </div>
    </template>
    <template #default>
      <div class="px-24px">
        <el-collapse v-model="activeNames">
          <el-collapse-item v-for="(item, index) in opinions" :key="index" :name="index">
            <template #title>
              <i18n-t keypath="opinions.suggestion" tag="div" class="collapse-title">
                <template #moduleName>
                  <span class="module-name">{{ item.moduleName }}</span>
                </template>
              </i18n-t>
            </template>
            <div class="collapse-content">
              {{ item.opinion }}
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { t } from '@@/i18n';

interface Opinion {
  moduleName: string;
  opinion: string;
}

const props = withDefaults(defineProps<{
  modelValue: boolean;
  opinions: Opinion[];
}>(), {
  modelValue: false,
  opinions: () => []
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const activeNames = ref<number[]>([]);

watch(() => props.opinions, (newVal) => {
  if (newVal) {
    activeNames.value = newVal.map((_, index) => index);
  }
}, { immediate: true, deep: true });

</script>

<style lang="scss" scoped>
.collapse-title {
  font-size: 14px;
  font-weight: 400;
  color: #222527;
  font-family: PingFangSC;
  position: relative;
  padding-left: 14px;
  margin-top: 24px;
  text-align: left;
  line-height: 20px;

  &::before {
    content: "";
    position: absolute;
    left: -5px;
    top: 50%;
    transform: translateY(-50%) rotate(-90deg);
    transition: transform .3s;
    width: 16px;
    height: 16px;
    background-image: url('@/common/assets/icons/icon-kyc-arrow.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  .module-name {
    color: #FF0064;
    font-weight: 500;
  }
}

.collapse-content {
  font-weight: 400;
  font-size: 14px;
  color: #6B7275;
  font-family: PingFangSC-Regular;
  white-space: pre-wrap;
  margin-top: 16px;
  line-height: 20px;
}

:deep(.el-collapse) {
  border: none;
}

:deep(.el-collapse-item) {
  background-color: transparent;
  border-radius: 0;
  border: none;
  padding-bottom: 32px;
  border-bottom: 0.5px solid #E5E6EB;

  &:last-of-type {
    border-bottom: none;
  }
}

:deep(.el-collapse-item__header) {
  background-color: transparent;
  height: auto;
  border-bottom: none;

  .el-collapse-item__arrow {
    display: none;
  }

  &.is-active {
    .collapse-title::before {
      transform: translateY(-50%) rotate(0deg);
    }
  }
}

:deep(.el-collapse-item__wrap) {
  background-color: transparent;
  border-bottom: none;
}

:deep(.el-collapse-item__content) {
  padding-left: 14px;
  padding-bottom: 0px;
}

:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;

  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }

  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
</style>