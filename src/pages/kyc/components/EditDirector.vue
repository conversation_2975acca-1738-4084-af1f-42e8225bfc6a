<template>
  <el-drawer 
    v-model="showDrawer" 
    size="684px" 
    style="--el-drawer-padding-primary: 0px;"
    header-class="custom-drawer-header"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :close-on-press-escape="false"
  >
    <template #header>
      <div class="h-60px border-b-1 mb-0 flex-row flex-items-center text-18px font-500 color-[#222527] pt-18px pb-18px pl-24px">
        <span>{{ t('directorInfo.addDirector') }}</span>
      </div>
    </template>
    <template #default>
      <div class="custom-drawer-body pb-136px p-24px">
        <el-form ref="directorForm" :disabled="!props.isEdit" :model="formData" :rules="directorRules" hide-required-asterisk label-position="top">
          <el-row class="flex flex-row justify-between">
            <el-form-item prop="nationality" :label="t('shareholderInfo.nationality')">
              <el-select v-model="formData.nationality" filterable :placeholder="t('shareholderInfo.nationality')" style="width: 100%;">
                <el-option v-for="info in regions" :key="info.enumCode" :label="info.enumDescCn" :value="info.enumCode"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="idType" :label="t('shareholderInfo.idType')">
              <el-select v-model="formData.idType" :placeholder="t('shareholderInfo.idType')">
                <el-option
                  v-for="item in idTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-row>

          <el-form-item prop="fileSeqIds" class="w-100%">
            <el-row class="mb-12px mt-24px line-height-20px">
              <p class="color-[#222527] font-400 text-14px">{{ t('shareholderInfo.identityAuthentication') }}</p>
              <p class="font-400 text-14px color-[#6B7275] mt-6px">
                {{ t('shareholderInfo.identityAuthenticationDescription') }}
              </p>
              <p class="font-400 text-14px color-[#6B7275]">
                {{ t('shareholderInfo.identityAuthenticationDescription1') }}
              </p>
              <p class="font-400 text-14px color-[#6B7275]">
                {{ t('shareholderInfo.identityAuthenticationDescription2') }}
              </p>
            </el-row>
            
            <UploadFiles fileType="DIAD" :limit="1" v-model:fileList="formData.fileInfos" class="w-full"/>
          </el-form-item>

          <!-- 姓名信息 -->
          <el-row class="mt-24px flex flex-row justify-between">
            <el-form-item prop="enFirstName" :label="t('shareholderInfo.enFirstName')">
              <div class="custom-name-input">
                <el-input v-model="formData.enFirstName" :placeholder="t('shareholderInfo.firstName')"></el-input>
                <el-input v-model="formData.enLastName" :placeholder="t('shareholderInfo.lastName')"></el-input>
              </div>
            </el-form-item>
            <el-form-item prop="cnFirstName" :label="`${t('shareholderInfo.cnFirstName')}(${t('shareholderInfo.notRequired')})`">
              <div class="custom-name-input">
                <el-input v-model="formData.cnFirstName" :placeholder="t('shareholderInfo.firstName')"></el-input>
                <el-input v-model="formData.cnLastName" :placeholder="t('shareholderInfo.lastName')"></el-input>
              </div>
            </el-form-item>
          </el-row>

          <!-- 证件有效期 -->
          <el-row class="mt-24px flex flex-row justify-between">
              <el-form-item :label="t('shareholderInfo.expirationLongTerm')">
                <el-switch 
                  v-model="validitySwitch"
                  @change="handleSwitchChange"
                  style="--el-switch-on-color: #FD3627; --el-switch-off-color: #F5F5F5"
                />
              </el-form-item>
              <el-form-item prop="expirationStart" :label="formData.expirationEnd === '99991231' ? t('shareholderInfo.expirationStart') : t('shareholderInfo.expiration')">
                <div class="custom-name-input items-center pr-12px" v-if="!validitySwitch">
                  <el-date-picker
                    v-model="formData.expirationStart"
                    type="date"
                    value-format="YYYYMMDD"
                    format="YYYY-MM-DD"
                    :placeholder="t('shareholderInfo.expirationStartAbb')"
                    style="flex: 1"
                    class="custom-date-picker"
                  />
                  <span class="color-#E5E6EB">-</span>
                  <el-date-picker
                    v-model="formData.expirationEnd"
                    type="date"
                    value-format="YYYYMMDD"
                    format="YYYY-MM-DD"
                    :placeholder="t('shareholderInfo.expirationEndAbb')"
                    style="flex: 1"
                    class="custom-date-picker"
                  />
                  <SvgIcon name="time-icon" style="font-size: 16px" />
                </div>
                <div class="custom-name-input items-center pr-12px" v-else>
                  <el-date-picker
                    v-model="formData.expirationStart"
                    type="date"
                    value-format="YYYYMMDD"
                    format="YYYY-MM-DD"
                    :placeholder="t('shareholderInfo.expirationStartAbb')"
                    style="flex: 1"
                    class="custom-date-picker"
                  />
                  <SvgIcon name="time-icon" style="font-size: 16px" />
                </div>
              </el-form-item>
          </el-row>

          <!-- 性别和证件号码 -->
          <el-row class="mt-24px flex flex-row justify-between">
            <el-form-item prop="gender" :label="t('shareholderInfo.gender')">
              <el-select v-model="formData.gender" :placeholder="t('shareholderInfo.gender')">
                <el-option
                  v-for="item in genderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="idNumber" :label="t('shareholderInfo.idNumber')">
              <el-input v-model="formData.idNumber" :placeholder="t('shareholderInfo.idNumber')"></el-input>
            </el-form-item>
          </el-row>

          <!-- 居住地址 -->
          <el-row class="mt-24px flex flex-row justify-between">
            <el-form-item prop="residenceCountry" :label="t('shareholderInfo.residenceCountry')">
              <el-select v-model="formData.residenceCountry" filterable :placeholder="t('shareholderInfo.residenceCountry')" style="width: 100%;">
                <el-option v-for="info in regions" :key="info.enumCode" :label="info.enumDescCn" :value="info.enumCode"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="residenceAddress" :label="t('shareholderInfo.residenceAddress')">
              <el-input v-model="formData.residenceAddress" :placeholder="t('shareholderInfo.residenceAddress')"></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      
      <!-- 底部按钮 -->
      <div 
        v-if="props.isEdit"
        style="border-top: 1px solid #E5E6EB;"
        class="bg-[#fff] absolute bottom-0 right-0 flex flex-row flex-items-center flex-justify-end p-24px w-full h-64px">
        <el-button @click="emit('cancel')" class="w-68px h-32px bg-[#FFFFFF] color-[#222527] border border-[#E5E6EB] rounded-6px">取消</el-button>
        <el-button @click="addDirector" color="white" class="w-68px h-32px bg-[#030814] rounded-6px" type="primary" plain>添加</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import UploadFiles from '@/common/components/UploadFiles/index.vue';
import type { FormRules, FormInstance } from "element-plus"
import { EditDirectorInfo, IdTypeEnum, GenderEnum, StatusEnum } from '../apis/type';
import { t } from '@@/i18n';
import { cloneDeep } from 'lodash-es';
import { useEnumStore } from '@/pinia/stores/enumStore'
import moment from 'moment';
import { useComponent } from '../composables/useComponent';

const props = withDefaults(defineProps<{
  directorInfo: EditDirectorInfo
  modelValue: boolean,
  isEdit: boolean
}>(), {
  modelValue: true,
  isEdit: true
});

const emit = defineEmits<{
  (e: 'submit', payload: EditDirectorInfo): void
  (e: 'cancel'): void
  (e: 'update:modelValue', value: boolean): void
}>()

// 使用计算属性实现双向绑定
const showDrawer = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

import { BusinessEnumType } from '@@/apis/common/type';
const { getIdTypeOptions, getGenderOptions } = useComponent()

const validitySwitch = ref(false)

const formData = ref<EditDirectorInfo>(cloneDeep(props.directorInfo));

const enumStore = useEnumStore();
const regions = enumStore.getEnumList(BusinessEnumType.SH_DIRECTOR_MANAGER_NATIONALITY)

// 性别选项
const genderOptions = computed(() => getGenderOptions())

// 监听父组件传入的数据变化
watch(() => props.directorInfo, (newVal) => {
  if (props.modelValue) {
    formData.value = cloneDeep(newVal)
  }
}, { immediate: true })


// 证件类型枚举
const idTypeOptions = computed({
  get: () => {
    const org = getIdTypeOptions(formData.value.nationality || '')
    return org
  },
  set: (value) => {

  }
})
// 监听国籍修改可选证件类型
watch(() => formData.value.nationality, (newVal) => {
  // 新的国籍是否支持选择的证件类型
  if (!idTypeOptions.value.some(item => item.value === formData.value.idType)) {
    formData.value.idType = ''
  }
}, { immediate: true })

// 监听有效期变化
watch(() => [formData.value.expirationStart, formData.value.expirationEnd], ([start, end]) => {
  if (end === '99991231') {
    validitySwitch.value = true
  }else{
    validitySwitch.value = false
  }
}, { immediate: true })

// 监听图片上传
watch(() => formData.value.fileInfos, (newValue) => {
  if (newValue && newValue.length > 0) {
    // 取出 fileseqid 去掉 ''
    formData.value.fileSeqIds = newValue.map(item => item.fileSeqId ? item.fileSeqId : '').filter(Boolean)
    directorForm.value?.clearValidate('fileSeqIds');
  }else{
    formData.value.fileSeqIds = []
  }
}, { deep: true })

const directorForm = ref<FormInstance | null>(null)
const directorRules = computed(() => {
  const allRules: FormRules = {
    nationality: [
      { required: true, message: t('shareholderInfo.formValidateNationality'), trigger: "blur" },
    ],
    idType: [
      { required: true, message: t('shareholderInfo.formValidateIdType'), trigger: "blur" },
    ],
    fileSeqIds: [
      { required: true, message: t('shareholderInfo.formValidateFile'), trigger: "blur" },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value.length === 0) {
            callback(new Error(t('shareholderInfo.formValidateFile')))
          } else {
            callback()
          }
        },
        trigger: "blur"
      }
    ],
    enFirstName: [
      { required: true, message: t('shareholderInfo.formValidateEnName'), trigger: "blur" },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value === "") {
            callback(new Error(t('shareholderInfo.formValidateEnName')))
          } else if (formData.value.enLastName === "") {
            callback(new Error(t('shareholderInfo.formValidateEnName')))
          } else {
            callback()
          }
        },
        trigger: "blur"
      }
    ],
    enLastName: [
      { required: true, message: t('shareholderInfo.formValidateEnLastName'), trigger: "blur" },
    ],
    // cnFirstName: [
    //   { required: true, message: t('shareholderInfo.formValidateCnFirstName'), trigger: "blur" },
    // ],
    // cnLastName: [
    //   { required: true, message: t('shareholderInfo.formValidateCnLastName'), trigger: "blur" },
    // ],
    expirationStart: [
      { required: true, message: t('shareholderInfo.formValidateExpirationError'), trigger: "blur" },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value === "") {
            callback(new Error(t('shareholderInfo.formValidateExpirationError')))
            // 不是长期有效但又没有选择结束时间
          } else if (formData.value.expirationEnd !== '99991231' && !formData.value.expirationEnd) {
            callback(new Error(t('shareholderInfo.formValidateExpirationError')))
          } else {
            callback()
          }
        },
        trigger: "blur"
      }
    ],
    expirationEnd: [
      { required: true, message: t('shareholderInfo.formValidateExpirationEnd'), trigger: "blur" },
    ],
    gender: [
      { required: true, message: t('shareholderInfo.formValidateGender'), trigger: "blur" },
    ],
    idNumber: [
      { required: true, message: t('shareholderInfo.formValidateIdNumber'), trigger: "blur" },
    ],
    residenceCountry: [
      { required: true, message: t('shareholderInfo.formValidateResidenceCountry'), trigger: "blur" },
    ],
    residenceAddress: [
      { required: true, message: t('shareholderInfo.formValidateResidenceAddress'), trigger: "blur" },
    ],
  }
  return allRules;
});

const handleSwitchChange = (value: string | number | boolean) => {
  if (value) {
    formData.value.expirationEnd = '99991231';
  }else{
    formData.value.expirationEnd = '';
  }
}

// 添加董事
const addDirector = () => {
  directorForm.value?.validate((valid) => {
    if (valid) {
      emit('submit', formData.value)
    }
  })
}

</script>

<style lang="scss" scoped>
p {
  margin: 0;
}
.custom-name-input {
  display: flex;
  flex-direction: row;
  border: 1px solid #E5E6EB;
  border-radius: 6px;
  width: 100%;
  :deep(.el-input__wrapper) {
    box-shadow: none;
    border: none;
  }
}
:deep(.el-input__wrapper) {
  border: 1px solid #E5E6EB;
  box-shadow: none;
}
:deep(.el-switch__core) {
  height: 24px;
  border-radius: 12px;
}
:deep(.el-form-item) {
  margin-bottom: 0;
  width: 280px;
}
:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
}
:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }
  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
:deep(.custom-date-picker) {
  .el-input__prefix {
    display: none;
  }
}
</style>
