<template>
  <div class="bg-white pb-136px">
    <div class="text-28px font-semibold text-[#222527]">{{ t('shareholderInfo.title') }}</div>
    <p class="mt-16px mb-32px text-14px text-[#6B7275] line-height-20px">
      {{ t('shareholderInfo.description') }}
    </p>
    <div class="flex-justify-items-center">
      <!-- 股东列表 -->
      <el-table :data="localShareholderList" style="width: 100%">
        <el-table-column :label="t('shareholderInfo.index')" width="90">
          <template #default="{ row, $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="type" :label="t('shareholderInfo.shareholderType')">
          <template #default="{ row }">
            {{ t('shareholderInfo.shareholderTypeDesc') }} {{ row.enFirstName }}{{ row.enLastName }}
          </template>
        </el-table-column>
        <el-table-column :label="t('shareholderInfo.progress')" width="130">
          <template #default="{ row }">
            <div class="flex items-center">
              <!-- 列表只要有值就肯定是已完成的 -->
              <div :class="['status-dot']" :style="{ backgroundColor: getShareholderStatusClass(StatusEnum.D) }" />
              <div>{{ getShareholderStatusText(StatusEnum.D) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="t('shareholderInfo.action')" width="130">
          <template #default="scope">
            <el-tooltip :content="t('shareholderInfo.action')" placement="top" effect="light">
              <el-button text @click="handleEdit(scope.row, scope.$index)">
                <SvgIcon name="icon-edit" class="w-16px h-16px color-[#030814]" />
              </el-button>
            </el-tooltip>
            <el-tooltip :content="t('shareholderInfo.delete')" placement="top" effect="light">
              <el-button :disabled="localShareholderList.length < 2" text @click="handleDelete(scope.row, scope.$index)">
                <SvgIcon name="icon-deleted" class="w-16px h-16px color-[#030814]" />
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <template #empty>
          <div class="flex flex-col flex-items-center flex-justify-center mt-48px">
            <img src="@/common/assets/icons/table-empty.svg" alt="table-empty" class="w-100px h-100px" />
            <p class="text-14px text-[#6B7275] mt-8px">{{ t('shareholderInfo.tableEmpty') }}</p>
          </div>
        </template>
      </el-table>

      <div class="mt-24px text-right">
        <el-button :disabled="localShareholderList.length >= 10" @click="handleAddShareholder" class="min-w-104px min-h-32px bg-[#FFFFFF] add-btn">
          <SvgIcon name="icon-add" class="w-16px h-16px color-[#030814]" />
          <span class="text-14px font-400 color-[#030814] ml-8px">{{ t('shareholderInfo.addShareholder') }}</span>
        </el-button>
      </div>

      <div class="mt-64px float-left text-[#6B7275] flex flex-col self-start">
        <el-checkbox class="ensure-checkbox" v-model="shareholderEnsure" :trueValue="'Y'" :falseValue="'N'" :label="t('shareholderInfo.ensureDesc')" size="large" />
        <div v-if="shareholderEnsure === 'N'" class="text-#FD3627 text-12px ml-22px">{{ t('shareholderInfo.ensure') }}</div>
      </div>
    </div>

    <template v-if="currentShareholderInfo">
       <EditShareholder 
        v-model="showEditShareholder" 
        :shareholderInfo="currentShareholderInfo"
        @submit="handleAddShareholderSubmit" 
        @cancel="handleCancelEditShareholder"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import EditShareholder from './EditShareholder.vue';
import { ComponentExposed, CorpModuleEnumType, CorpShareholderVO, EditDirectorInfo, EditManagerInfo, EditShareholderInfo, StatusEnum } from '../apis/type';
import { t } from '@@/i18n';
import { cloneDeep } from 'lodash-es';
import { isReallyEmpty } from '@/common/utils/validate'
import { useComponent } from '../composables/useComponent';
import DialogService from '@/common/components/Dialog'

const defaultShareholderInfo: EditShareholderInfo = {
  fileSeqIds: [],
  shareholdingRatio: '',
  nationality: '',
  idType: '',
  enFirstName: '',
  enLastName: '',
  cnFirstName: '',
  cnLastName: '',
  expirationStart: '',
  expirationEnd: '',
  gender: '',
  idNumber: '',
  residenceCountry: '',
  residenceAddress: '',
  city: '',
  state: '',
  postalCode: '',
  birthDate: '',
}

const props = withDefaults(defineProps<{
  shareholderInfo?: CorpShareholderVO,
  directorList?: EditDirectorInfo[],
  managerInfo?: EditManagerInfo,
}>(), {
  
});

const showEditShareholder = ref(false)
const localShareholderList = ref<EditShareholderInfo[]>(props.shareholderInfo?.corpShareholderInfoVOs || []);
const currentShareholderInfo = ref<EditShareholderInfo | null>(null)
const shareholderEnsure = ref(props.shareholderInfo?.shareholderEnsure || '')
const currentIndex = ref(-1)

const { getStatusClass, getStatusText } = useComponent()
const getShareholderStatusClass = getStatusClass;
const getShareholderStatusText = getStatusText;


import { BusinessEnumType } from '@@/apis/common/type';
import { useEnumStore } from '@/pinia/stores/enumStore';

const enumStore = useEnumStore();
const idTypesOptions = enumStore.getEnumList(BusinessEnumType.ID_TYPE)

watch(() => props.shareholderInfo, (newVal) => {
  if (newVal) {
    localShareholderList.value = []
    const shareholderList = newVal.corpShareholderInfoVOs?.map(item => {
      // 排掉非正常的数据
      if (item?.idNumber) {
        return {
          ...item,
          fileSeqIds: item.fileInfos?.map(file => file.fileSeqId || '').filter(Boolean) || []
        }
      }
      return null
    }).filter(Boolean) || []
    const temp = (shareholderList || []) as EditShareholderInfo[]
    localShareholderList.value.push(...temp)
    shareholderEnsure.value = newVal.shareholderEnsure || ''
  }
}, { immediate: true })

const handleEdit = (row: EditShareholderInfo, index: number) => {
  currentShareholderInfo.value = cloneDeep(row)
  currentIndex.value = index
  showEditShareholder.value = true
};

const handleDelete = async (row: EditShareholderInfo, index: number) => {
  const confirmed = await DialogService.confirm(
    t('shareholderInfo.deleteConfirmContent'),
    t('shareholderInfo.deleteConfirmTitle')
  )
  if (confirmed) {
    localShareholderList.value.splice(index, 1)
  }
};

const handleAddShareholder = async () => {
  if (localShareholderList.value.length < 10) {
    currentShareholderInfo.value = cloneDeep(defaultShareholderInfo)
    showEditShareholder.value = true
  }
};

const handleAddShareholderSubmit = (newShareholder: EditShareholderInfo) => {
  // 检查股东数组中是否包含已经存在的证件号,如果存在则不能添加 提示错误
  if (localShareholderList.value.some(item => item.idNumber === newShareholder.idNumber && item.businessSeqId !== newShareholder.businessSeqId)) {
    ElMessage.warning(t('shareholderInfo.formValidateIdNumberExist'))
    return
  }
  if (currentIndex.value !== -1) {
    // 更新了股东信息之后，检查股东是否同步了管理者
    if (props.managerInfo?.shareholdingSeqId === newShareholder.businessSeqId 
      && parseFloat(newShareholder.shareholdingRatio || '0') < 10) {
        DialogService.warning(
          t('shareholderInfo.managerSyncWarningContent'),
          t('shareholderInfo.managerSyncWarningTitle'),
          {
          onConfirm: () => {
            localShareholderList.value.splice(currentIndex.value, 1, newShareholder)
            currentIndex.value = -1
            showEditShareholder.value = false
          }
        })
    }else{
      localShareholderList.value.splice(currentIndex.value, 1, newShareholder)
      currentIndex.value = -1
      showEditShareholder.value = false
    }
  }else{
    localShareholderList.value.push(newShareholder);
    showEditShareholder.value = false
  }
}

const handleCancelEditShareholder = () => {
  showEditShareholder.value = false
  currentIndex.value = -1
}


const submitForm = async (validate: boolean) => {
  if(validate){
    // // 如果没有任何股东数据则提示错误
    if (localShareholderList.value.length === 0) {
      return false;
    }
    if (shareholderEnsure.value !== 'Y') {
      shareholderEnsure.value = 'N';
      return false;
    }
  }
  const shareholderList = localShareholderList.value.map(item => {
    return {
      fileSeqIds: isReallyEmpty(item.fileSeqIds) ? [] : item.fileSeqIds,
      businessSeqId: item.businessSeqId,
      shareholdingRatio: item.shareholdingRatio,
      nationality: item.nationality,
      idType: item.idType,
      enFirstName: item.enFirstName,
      enLastName: item.enLastName,
      cnFirstName: item.cnFirstName,
      cnLastName: item.cnLastName,
      expirationStart: item.expirationStart,
      expirationEnd: item.expirationEnd,
      gender: item.gender,
      idNumber: item.idNumber,
      residenceCountry: item.residenceCountry,
      residenceAddress: item.residenceAddress,
      city: item.city,
      state: item.state,
      postalCode: item.postalCode,
      birthDate: item.birthDate,
    }
  });
  return {
    moduleType: CorpModuleEnumType.SHAREHOLDER,
    modifyCorpShareholderVO: {
      shareholderEnsure: shareholderEnsure.value || 'N',
      businessSeqId: props.shareholderInfo?.businessSeqId,
      modifyCorpShareholderInfoVOs: isReallyEmpty(shareholderList) ? [{}] : shareholderList,
    }
  }
}

// 定义子组件暴露给父组件的接口
defineExpose<ComponentExposed>({
  submitForm
})

</script>

<style lang="scss" scoped>
p {
  margin: 0;
}
:deep(.el-table) {
  --el-table-header-bg-color: #F8F9FA;
  --el-table-header-text-color: #6B7275;

  thead tr {
    --el-table-border: none;
  }
  .el-table__header-wrapper {
    border-radius: 12px;
    font-weight: 600;
    font-size: 12px;
    color: #6B7275;
  }
}
.add-btn {
  padding: 6px 12px;
  border: 1px dashed #030814;
  border-radius: 6px;
}
:deep(.el-button.is-text) {
  padding: 0
}
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}
.ensure-checkbox {
  :deep(.el-checkbox__label) {
    margin-top: 2px;
    font-size: 12px;
    font-weight: 400;
    color: #6B7275;
    font-family: PingFangSC-Regular;
    max-width: 600px;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
  }
  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #FF0064;
    border-color: #FF0064;
    border-color: #FF0064;
  }
}
:deep(.el-table) {
  --el-table-border-color: none;
}
</style> 
