<template>
  <div class="bg-white pb-136px">
    <div class="text-28px font-semibold text-[#222527]">{{ t('managerInfo.title') }}</div>
    <p class="mt-16px text-14px text-[#6B7275] line-height-20px">
      {{ t('managerInfo.description') }}
      <span class="text-color-[#FD3627]">{{ t('managerInfo.description2') }}</span>
    </p>
    <div class="flex-justify-items-center mt-24px">
      <el-form class="w-100%" ref="managerForm" :model="formData" :rules="managerRules" hide-required-asterisk label-position="top">
        <el-form-item class="w-280px" :label="t('managerInfo.managerType')" prop="managerType">
          <el-select 
            v-model="formData.managerType" 
            :placeholder="t('managerInfo.managerType')" 
            style="width: 100%;"
            @change="handleManagerTypeChange"
            @visible-change="handleManagerTypeVisibleChange"
          >
            <el-option
              v-for="item in managerTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-divider class="mt-32px mb-24px" />

        <!-- 选择股东/董事/实际控制人(10%以上) -->
        <section v-if="formData.managerType === ManagerTypeEnum.S">
          <h4 class="text-18px font-semibold text-[#222527]">{{ t('managerInfo.managerTypeDesc') }}</h4>

          <el-form-item class="w-280px" :label="t('managerInfo.shareholderName')" prop="shareholdingSeqId">
            <el-select 
              v-model="managerObj" 
              :placeholder="t('shareholderInfo.selectPlaceholder')" 
              style="width: 100%;"
              :value-key="'businessSeqId'"
              @change="handleShareholderChange"
              @visible-change="handleShareholderVisibleChange"
            >
              <el-option
                v-for="item in shareholderListOpts"
                :key="item.businessSeqId"
                :label="item.enFirstName + ' ' + item.enLastName"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </section>

        <section v-if="formData.managerType === ManagerTypeEnum.M">
          <span class="text-18px font-semibold text-[#222527]">{{ t('managerInfo.managerTypeDesc2') }}</span>
          
          <el-form-item prop="alfamFileInfos" class="w-100%">
            <el-row class="mb-6px mt-24px line-height-20px">
              <p class="font-400 text-14px color-[#6B7275] mt-6px">
                {{ t('managerInfo.authorizationLetter') }}
                <el-button @click="downloadTemplate('/授权委托书.pdf')" type="text" class="ml-8px p-0 text-14px text-[#FF0064] font-400" size="small">{{ t('managerInfo.downloadTemplate') }}</el-button>
              </p>
            </el-row>
            <UploadFiles fileType="ALFAM" :limit="1" v-model:fileList="formData.alfamFileInfos" class="w-full"/>
          </el-form-item>

          <el-row class="mt-24px flex flex-row justify-between">
            <el-form-item prop="nationality" :label="t('shareholderInfo.nationality')">
              <el-select 
                v-model="formData.nationality" 
                filterable 
                :placeholder="t('shareholderInfo.nationality')" 
                style="width: 100%;"
                @visible-change="handleNationalityVisibleChange"
              >
                <el-option v-for="info in regions" :key="info.enumCode" :label="info.enumDescCn" :value="info.enumCode"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="idType" :label="t('shareholderInfo.idType')">
              <el-select 
                v-model="formData.idType" 
                :placeholder="t('shareholderInfo.idType')" 
                @visible-change="handleIdTypeVisibleChange"
              >
                <el-option
                  v-for="item in idTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-row>

          <el-form-item prop="miadFileInfos" class="w-100%">
            <el-row class="mb-12px mt-24px line-height-20px">
              <p class="color-[#222527] font-400 text-14px">{{ t('shareholderInfo.identityAuthentication') }}</p>
              <p class="font-400 text-14px color-[#6B7275] mt-6px">
                {{ t('shareholderInfo.identityAuthenticationDescription') }}
              </p>
              <p class="font-400 text-14px color-[#6B7275]">
                {{ t('shareholderInfo.identityAuthenticationDescription1') }}
              </p>
              <p class="font-400 text-14px color-[#6B7275]">
                {{ t('shareholderInfo.identityAuthenticationDescription2') }}
              </p>
            </el-row>
            
            <UploadFiles fileType="MIAD" :limit="1" v-model:fileList="formData.miadFileInfos" class="w-full"/>
          </el-form-item>

          <!-- 姓名信息 -->
          <el-row class="mt-24px flex flex-row justify-between">
            <el-form-item prop="enFirstName" :label="t('shareholderInfo.enFirstName')">
              <div class="custom-name-input">
                <el-input v-model="formData.enFirstName" :placeholder="t('shareholderInfo.firstName')"></el-input>
                <el-input v-model="formData.enLastName" :placeholder="t('shareholderInfo.lastName')"></el-input>
              </div>
            </el-form-item>
            <el-form-item prop="cnFirstName" :label="`${t('shareholderInfo.cnFirstName')}(${t('shareholderInfo.notRequired')})`">
              <div class="custom-name-input">
                <el-input v-model="formData.cnFirstName" :placeholder="t('shareholderInfo.firstName')"></el-input>
                <el-input v-model="formData.cnLastName" :placeholder="t('shareholderInfo.lastName')"></el-input>
              </div>
            </el-form-item>
          </el-row>

          <!-- 证件有效期 -->
          <el-row class="mt-24px flex flex-row justify-between">
            <el-form-item :label="t('shareholderInfo.expirationLongTerm')">
              <el-switch 
                v-model="validitySwitch"
                @change="handleSwitchChange"
                style="--el-switch-on-color: #FD3627; --el-switch-off-color: #F5F5F5"
              />
            </el-form-item>
            <el-form-item prop="expirationStart" :label="formData.expirationEnd === '99991231' ? t('shareholderInfo.expirationStart') : t('shareholderInfo.expiration')">
              <div class="custom-name-input items-center pr-12px" v-if="!validitySwitch">
                <el-date-picker
                  v-model="formData.expirationStart"
                  type="date"
                  value-format="YYYYMMDD"
                  format="YYYY-MM-DD"
                  :placeholder="t('shareholderInfo.expirationStartAbb')"
                  style="flex: 1"
                  class="custom-date-picker"
                />
                <span class="color-#E5E6EB">-</span>
                <el-date-picker
                  v-model="formData.expirationEnd"
                  type="date"
                  value-format="YYYYMMDD"
                  format="YYYY-MM-DD"
                  :placeholder="t('shareholderInfo.expirationEndAbb')"
                  style="flex: 1"
                  class="custom-date-picker"
                />
                <SvgIcon name="time-icon" style="font-size: 16px" />
              </div>
              <div class="custom-name-input items-center pr-12px" v-else>
                <el-date-picker
                  v-model="formData.expirationStart"
                  type="date"
                  value-format="YYYYMMDD"
                  format="YYYY-MM-DD"
                  :placeholder="t('shareholderInfo.expirationStartAbb')"
                  style="flex: 1"
                  class="custom-date-picker"
                />
                <SvgIcon name="time-icon" style="font-size: 16px" />
              </div>
            </el-form-item>
          </el-row>

          <!-- 性别和证件号码 -->
          <el-row class="mt-24px flex flex-row justify-between">
            <el-form-item prop="gender" :label="t('shareholderInfo.gender')">
              <el-select 
                v-model="formData.gender" 
                :placeholder="t('shareholderInfo.gender')"
                @visible-change="handleGenderVisibleChange"
              >
                <el-option
                  v-for="item in genderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="idNumber" :label="t('shareholderInfo.idNumber')">
              <el-input v-model="formData.idNumber" :placeholder="t('shareholderInfo.idNumber')"></el-input>
            </el-form-item>
          </el-row>

          <!-- 居住地址 -->
          <el-row class="mt-24px flex flex-row justify-between">
            <el-form-item prop="residenceCountry" :label="t('shareholderInfo.residenceCountry')">
              <el-select 
                v-model="formData.residenceCountry" 
                filterable 
                :placeholder="t('shareholderInfo.residenceCountry')" 
                style="width: 100%;"
                @visible-change="handleResidenceCountryVisibleChange"
              >
                <el-option v-for="info in regions" :key="info.enumCode" :label="info.enumDescCn" :value="info.enumCode"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="residenceAddress" :label="t('shareholderInfo.residenceAddress')">
              <el-input v-model="formData.residenceAddress" :placeholder="t('shareholderInfo.residenceAddress')"></el-input>
            </el-form-item>
          </el-row>
        </section>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { t } from '@@/i18n';
import { isReallyEmpty } from '@/common/utils/validate'
import { ApproveStatusEnum, ComponentExposed, CorpModuleEnumType, EditManagerInfo, EditShareholderInfo, FileInfo, GenderEnum, IdTypeEnum, ManagerTypeEnum, StatusEnum } from '../apis/type';
import UploadFiles from '@/common/components/UploadFiles/index.vue';
import { BusinessEnumType } from '@/common/apis/common/type';
import { useEnumStore } from '@/pinia/stores/enumStore'
import moment from 'moment';
import { FormInstance, FormRules } from 'element-plus';
import { useComponent } from '../composables/useComponent';

const defaultDirectorInfo: EditManagerInfo = {
  fileInfos: [],
  fileSeqIds: [],
  managerType: '',
  managerName: '',
  shareholdingSeqId: '',
  nationality: '',
  idType: '',
  enFirstName: '',
  enLastName: '',
  cnFirstName: '',
  cnLastName: '',
  expirationStart: '',
  expirationEnd: '',
  gender: '',
  idNumber: '',
  residenceCountry: '',
  residenceAddress: '',
  status: StatusEnum.I,
  approveStatus: ApproveStatusEnum.I,
  opinions: '',
}

const props = withDefaults(defineProps<{
  managerInfo?: EditManagerInfo,
  shareholderList?: EditShareholderInfo[]
}>(), {
  
});
interface EnumItem {
  enumCode: string;
  enumDescCn: string;
}

const validitySwitch = ref(false)

const formData = ref<EditManagerInfo & {
  // 授权文件
  alfamFileInfos: FileInfo[],
  miadFileInfos: FileInfo[],
}>({
  ...defaultDirectorInfo,
  alfamFileInfos: [],
  miadFileInfos: [],
});
const shareholderListOpts = ref<EditShareholderInfo[]>([])
const managerObj = ref<EditShareholderInfo | undefined>(undefined)

const enumStore = useEnumStore();
const regions = enumStore.getEnumList(BusinessEnumType.SH_DIRECTOR_MANAGER_NATIONALITY)
const { getIdTypeOptions, getGenderOptions, getManagerTypeOptions } = useComponent()

watch(() => props.managerInfo, (newVal) => {
  if (newVal) {
    formData.value = {
      ...newVal,
      alfamFileInfos: [],
      miadFileInfos: [],
    }
    formData.value.alfamFileInfos = formData.value.fileInfos?.filter(item => item?.fileType === 'ALFAM') || []
    formData.value.miadFileInfos = formData.value.fileInfos?.filter(item => item?.fileType === 'MIAD') || []

    formData.value.fileSeqIds = formData.value.fileInfos?.map(item => item.fileSeqId || '').filter(Boolean)

    // 检查是否存在股东是管理者
    if (formData.value.shareholdingSeqId) {
      managerObj.value = props.shareholderList?.find(item => item.businessSeqId === formData.value.shareholdingSeqId) || undefined
    }
  }
}, { immediate: true })


// 性别选项
const genderOptions = computed(() => getGenderOptions())
// 管理者类型枚举
const managerTypeOptions = computed(() => getManagerTypeOptions())
// 证件类型枚举
const idTypeOptions = computed({
  get: () => {
    const org = getIdTypeOptions(formData.value.nationality || '')
    return org
  },
  set: (value) => {

  }
})
// 监听国籍修改可选证件类型
watch(() => formData.value.nationality, (newVal) => {
  // 新的国籍是否支持选择的证件类型
  if (!idTypeOptions.value.some(item => item.value === formData.value.idType)) {
    formData.value.idType = ''
  }
}, { immediate: true })

// 监听管理者类型变化
watch(() => formData.value.managerType, (newVal) => {
  setTimeout(() => {
    managerForm.value?.validate().catch(() => {
      // 此处调用 validate 是为了在加载数据后立即在界面上显示校验状态，
      // Promise 校验失败是预期行为，因此只需捕获异常防止报错，无需处理。
    });
  }, 0);
}, { immediate: true })

// 筛选 股东信息数据，去掉股份小于10的数据
watch(() => props.shareholderList, (newVal) => {
  shareholderListOpts.value = newVal?.filter(item => parseFloat(item.shareholdingRatio || '0') >= 10) || []
}, { immediate: true })

// 监听有效期变化
watch(() => [formData.value.expirationStart, formData.value.expirationEnd], ([start, end]) => {
  if (end === '99991231') {
    validitySwitch.value = true
  } else {
    validitySwitch.value = false
  }
}, { immediate: true })

watch(() => formData.value.alfamFileInfos, (newValue) => {
  if (newValue && newValue.length > 0) {
    managerForm.value?.clearValidate('alfamFileInfos');
  }
}, { deep: true })

watch(() => formData.value.miadFileInfos, (newValue) => {
  if (newValue && newValue.length > 0) {
    managerForm.value?.clearValidate('miadFileInfos');
  }
}, { deep: true })

const managerForm = ref<FormInstance | null>(null)
const managerRules = computed(() => {
  const allRules: FormRules = {
    managerType: [
      { required: true, message: t('managerInfo.formValidateManagerType'), trigger: ['change', 'blur'] },
    ],
    shareholdingSeqId: [
      { required: true, message: t('managerInfo.formValidateManagerName'), trigger: ['change', 'blur'] },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value === '') {
            callback(new Error(`${t('managerInfo.shareholderName')}${t('shareholderInfo.selectPlaceholder')}`))
          } else {
            callback()
          }
        },
        trigger: "blur"
      }
    ],
    alfamFileInfos: [
      { required: true, message: t('managerInfo.formValidateAuthorizationLetter'), trigger: "blur" },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value.length === 0) {
            callback(new Error(t('managerInfo.formValidateAuthorizationLetter')))
          } else {
            callback()
          }
        },
        trigger: "blur"
      }
    ],
    miadFileInfos: [
      { required: true, message: t('shareholderInfo.formValidateFile'), trigger: "blur" },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value.length === 0) {
            callback(new Error(t('shareholderInfo.formValidateFile')))
          } else {
            callback()
          }
        },
        trigger: "blur"
      }
    ],
    nationality: [
      { required: true, message: t('shareholderInfo.formValidateNationality'), trigger: ['change', 'blur'] },
    ],
    idType: [
      { required: true, message: t('shareholderInfo.formValidateIdType'), trigger: ['change', 'blur'] },
    ],
    enFirstName: [
      { required: true, message: t('shareholderInfo.formValidateEnFirstName'), trigger: "blur" },
    ],
    enLastName: [
      { required: true, message: t('shareholderInfo.formValidateEnLastName'), trigger: "blur" },
    ],
    expirationStart: [
      { required: true, message: t('shareholderInfo.formValidateExpirationError'), trigger: "blur" },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value === "") {
            callback(new Error(t('shareholderInfo.formValidateExpirationError')))
            // 不是长期有效但又没有选择结束时间
          } else if (formData.value.expirationEnd !== '99991231' && !formData.value.expirationEnd) {
            callback(new Error(t('shareholderInfo.formValidateExpirationError')))
          } else {
            callback()
          }
        },
        trigger: "blur"
      }
    ],
    expirationEnd: [
      { required: true, message: t('shareholderInfo.formValidateExpirationEnd'), trigger: "blur" },
    ],
    gender: [
      { required: true, message: t('shareholderInfo.formValidateGender'), trigger: ['change', 'blur'] },
    ],
    idNumber: [
      { required: true, message: t('shareholderInfo.formValidateIdNumber'), trigger: "blur" },
    ],
    residenceCountry: [
      { required: true, message: t('shareholderInfo.formValidateResidenceCountry'), trigger: ['change', 'blur'] },
    ],
    residenceAddress: [
      { required: true, message: t('shareholderInfo.formValidateResidenceAddress'), trigger: "blur" },
    ],
  }
  return allRules;
});

// 以下方法是关于 el-select 组件的属性判断
const handleIdTypeVisibleChange = (visible: boolean) => {
  if (!visible) {
    // 这里可以主动触发验证
    managerForm.value?.validateField('idType')
  }
}
const handleNationalityVisibleChange = (visible: boolean) => {
  if (!visible) {
    managerForm.value?.validateField('nationality')
  }
}
const handleGenderVisibleChange = (visible: boolean) => {
  if (!visible) {
    managerForm.value?.validateField('gender')
  }
}
const handleResidenceCountryVisibleChange = (visible: boolean) => {
  if (!visible) {
    managerForm.value?.validateField('residenceCountry')
  }
}
const handleManagerTypeVisibleChange = (visible: boolean) => {
  if (!visible) {
    managerForm.value?.validateField('managerType')
  }
}
const handleShareholderVisibleChange = (visible: boolean) => {
  if (!visible) {
    managerForm.value?.validateField('shareholdingSeqId')
  }
}

// 在 script setup 部分添加这个方法
const downloadTemplate = async (url: string) => {
  if (localStorage.getItem('locale') === 'zh-CN' && !url.includes('http')) {
    url = 'https://files.dingx.tech/kyc/cn' + url;
  } else if (localStorage.getItem('locale') === 'zh-HK' && !url.includes('http')) {
    url = 'https://files.dingx.tech/kyc/hk' + url;
  } else if (!url.includes('http')) {
    url = 'https://files.dingx.tech/kyc/en' + url;
  }
  if (typeof window !== 'undefined') {
    window.open(url, '_blank', 'noopener,noreferrer');
  }
};

// 选择管理者类型
const handleManagerTypeChange = (value: ManagerTypeEnum) => {
  formData.value.managerType = value
  if (value === ManagerTypeEnum.S) {
    managerObj.value = undefined
    formData.value.managerName = ''
    formData.value.shareholdingSeqId = ''
  }
}

// 选择管理者类型的值
const handleShareholderChange = (value: EditShareholderInfo) => {
  formData.value.managerName = `${value.enFirstName} ${value.enLastName}`
  formData.value.shareholdingSeqId = value.businessSeqId
}

const handleSwitchChange = (value: string | number | boolean) => {
  if (value) {
    formData.value.expirationEnd = '99991231';
  }else{
    formData.value.expirationEnd = '';
  }
}

onMounted(() => {
  setTimeout(() => {
    managerForm.value?.validate().catch(() => {
      // 此处调用 validate 是为了在加载数据后立即在界面上显示校验状态，
      // Promise 校验失败是预期行为，因此只需捕获异常防止报错，无需处理。
    });
  }, 0);
})

const submitForm = async (validate: boolean) => {
  try {
    formData.value.fileInfos = [...formData.value.alfamFileInfos, ...formData.value.miadFileInfos]
    // 取出 fileseqid 去掉 ''
    formData.value.fileSeqIds = formData.value.fileInfos.map(item => item.fileSeqId || '').filter(Boolean)
    if (validate) {
      await managerForm.value?.validate()
    }
    // 如果选择了股东为管理者，必须传 shareholdingSeqId
    if (formData.value.managerType === ManagerTypeEnum.S && !formData.value.shareholdingSeqId) {
      return false
    }
    return {
      moduleType: CorpModuleEnumType.MANAGER,
      modifyCorpManagerInfoVO: {
        managerType: formData.value.managerType,
        fileSeqIds: formData.value.managerType === ManagerTypeEnum.S ? [] : isReallyEmpty(formData.value.fileSeqIds) ? [] : formData.value.fileSeqIds,
        managerName: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.managerName,
        businessSeqId: formData.value.businessSeqId,
        nationality: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.nationality,
        idType: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.idType,
        enFirstName: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.enFirstName,
        enLastName: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.enLastName,
        cnFirstName: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.cnFirstName,
        cnLastName: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.cnLastName,
        expirationStart: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.expirationStart,
        expirationEnd: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.expirationEnd,
        gender: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.gender,
        idNumber: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.idNumber,
        residenceCountry: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.residenceCountry,
        residenceAddress: formData.value.managerType === ManagerTypeEnum.S ? "" : formData.value.residenceAddress,
        shareholdingSeqId: formData.value.managerType === ManagerTypeEnum.S ? formData.value.shareholdingSeqId : "",
      }
    }
    // 提交逻辑
  } catch (error) {
    return false
  }
}
// 定义子组件暴露给父组件的接口
defineExpose<ComponentExposed>({
  submitForm
})
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}
.custom-name-input {
  display: flex;
  flex-direction: row;
  border: 1px solid #E5E6EB;
  border-radius: 6px;
  width: 100%;
  :deep(.el-input__wrapper) {
    box-shadow: none;
    border: none;
  }
}
:deep(.el-input__wrapper) {
  border: 1px solid #E5E6EB;
  box-shadow: none;
}
:deep(.el-switch__core) {
  height: 24px;
  border-radius: 12px;
}
:deep(.el-form-item) {
  margin-bottom: 0;
  width: 280px;
}
:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #222527;
}
:global(.custom-drawer-header) {
  font-weight: 500;
  font-size: 18px;
  padding: 0;
  margin-bottom: 0;
  .el-drawer__header {
    border-bottom: 1px solid #E5E6EB;
  }
  .el-drawer__close-btn {
    padding-right: 24px;
  }
}
:deep(.custom-date-picker) {
  .el-input__prefix {
    display: none;
  }
}
</style> 
