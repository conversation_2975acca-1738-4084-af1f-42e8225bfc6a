<template>
  <div>
    <div
      class="max-w-[249px] bg-white p-24px-0-24px-24px border-l-1 border-l-solid border-l-[#E5E6EB] top-32px"
    >
      <div
        class="text-14px font-semibold ml-[24px] text-[#6B7275] font-family-[PingFangSC-Semibold]"
      >
        {{ t('certificationNav.title') }}
      </div>
      <ul class="relative mt-[16px] list-none p-0">
        <li
          v-for="item in menuItems"
          :key="item.id"
          class="menu-item group relative flex cursor-pointer items-center text-14px h-[48px] transition-all duration-300"
          @click="handleItemClick(item)"
        >
          <div
            v-if="activeItem === item.id"
            class="rounded-[1px] w-[2px] h-[22px] bg-[#222527] transition-all duration-300"
          />
          <div
            v-if="activeItem !== item.id"
            class="rounded-[1px] w-[2px] h-[22px] bg-transparent transition-all duration-300"
          />
          <div
            class="flex items-center w-[190px] h-full ml-[7px] pl-[17px] hover:bg-[#F8F9FA] transition-all duration-300 rounded-[6px]"
            :class="{
              'bg-[#F8F9FA] font-semibold': activeItem === item.id,
            }"
          >
            <SvgIcon
              :name="getIconName(item)"
              class="text-[17px] flex-shrink-0 transition-all duration-300"
              :class="getIconColor(item)"
            />
            <span
              class="ml-8.5px font-family-[PingFangSC-Regular] text-[16px] mr-6px transition-all duration-300"
              :class="getMenuItemClass(item)"
              >{{ item.name }}</span
            >
            <div
              v-if="
                item.approvalStatus === ApproveStatusEnum.E &&
                item.status === EnumMenuItemStatus.COMPLETED
              "
              class="w-[4px] h-[4px] bg-[#FD3627] rounded-full"
            ></div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<!-- <style scoped> 
.menu-item::before {
  content: '';
  position: absolute;
  left: -1.5px;
  top: 0;
  height: calc(50% - 13px); /* 10px icon radius + 9px gap */
  width: 1px;
  background: #E5E6EB;
  border-radius: 1px;
}

.menu-item::after {
  content: '';
  position: absolute;
  left: -1.5px;
  bottom: 0;
  height: calc(50% - 13px); /* 10px icon radius + 9px gap */
  width: 1px;
  background: #E5E6EB;
  border-radius: 1px;
}

.menu-item:first-child::before {
  display: none;
}

.menu-item:last-child::after {
  display: none;
}
</style>-->

<script setup lang="ts">
import { ApproveStatusEnum, EnumMenuItemStatus } from '../apis/type';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
export type MenuItem = {
  id: string;
  name: string;
  iconBase: string;
  status: EnumMenuItemStatus;
  approvalStatus: ApproveStatusEnum;
  info?: any;
  serverStatus?: string;
};

const props = defineProps<{
  activeItem: string;
  itemClick: (item: MenuItem) => void;
  menuItems: MenuItem[];
}>();

const emit = defineEmits<{
  (e: 'update:activeItem', id: string): void;
}>();

const handleItemClick = (item: MenuItem) => {
  props.itemClick(item);
};

const getIconColor = (item: MenuItem) => {
  return {
    'font-semibold text-[#222527]': props.activeItem === item.id,
    'text-[#6B7275]': item.status === EnumMenuItemStatus.PENDING && props.activeItem !== item.id,
    'text-[#3EB342]': item.status === EnumMenuItemStatus.COMPLETED,
    'text-[#FD3627]': item.status === EnumMenuItemStatus.ERROR,
  };
};

const getMenuItemClass = (item: MenuItem) => {
  return {
    'font-family-[PingFangSC-Semibold] text-[#222527]': props.activeItem === item.id,
    ' text-[#FD3627]': item.status === EnumMenuItemStatus.ERROR,
    ' text-[#222527]': item.status === EnumMenuItemStatus.COMPLETED,
    ' text-[#6B7275]': item.status === EnumMenuItemStatus.PENDING && props.activeItem !== item.id,
  };
};

const getIconName = (item: MenuItem): any => {
  if (item.status === EnumMenuItemStatus.COMPLETED) {
    return 'icon-cert-suc';
  }
  if (item.status === EnumMenuItemStatus.ERROR) {
    return 'icon-cert-warning';
  }

  const isActive = props.activeItem === item.id;

  switch (item.iconBase) {
    case 'company':
      return isActive ? 'icon-cert-company' : 'icon-cert-company-default';
    case 'business':
      return isActive ? 'icon-cert-business' : 'icon-cert-business-default';
    case 'shareholder':
      return isActive ? 'icon-cert-shareholder' : 'icon-cert-shareholder-default';
    case 'director':
      return isActive ? 'icon-cert-director' : 'icon-cert-director-default';
    case 'manager':
      return isActive ? 'icon-cert-manager' : 'icon-cert-manager-default';
    case 'additional':
      return isActive ? 'icon-cert-additional' : 'icon-cert-additional-default';
    default:
      return '';
  }
};
</script>
