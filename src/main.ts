/* eslint-disable perfectionist/sort-imports */

// core
import { createApp } from 'vue';
import { pinia } from '@/pinia';
import { router } from '@/router';
import { installPlugins } from '@/plugins';
import i18n, { setLocale, LocaleType } from '@@/i18n'; // 导入 i18n 实例和 setLocale 函数
import App from '@/App.vue';
import '@@/components/Dialog/DialogService'; // 导入对话框服务

// css - 按需加载，优化首屏性能
import 'normalize.css';
import 'nprogress/nprogress.css';
// 动态导入非关键CSS
const loadNonCriticalStyles = async () => {
  await import('element-plus/theme-chalk/dark/css-vars.css');
  await import('vxe-table/lib/style.css');
  await import('@@/assets/styles/index.scss');
  await import('virtual:uno.css');
};

// 性能监控
const performanceObserver = () => {
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.entryType === 'navigation') {
          const navigationEntry = entry as PerformanceNavigationTiming;
          console.log(
            'Page load time:',
            navigationEntry.loadEventEnd - navigationEntry.fetchStart,
            'ms'
          );
        }
      });
    });
    observer.observe({ entryTypes: ['navigation'] });
  }
};

// 应用初始化函数
async function initApp() {
  try {
    // 开始性能监控
    if (import.meta.env.DEV) {
      performanceObserver();
    }

    // 创建应用实例
    const app = createApp(App);

    // 全局错误处理
    app.config.errorHandler = (err, vm, info) => {
      console.error('Vue Error:', err, info);
      // 可以在这里添加错误上报逻辑
    };

    // 开发环境配置
    if (import.meta.env.DEV) {
      app.config.performance = true;
      const win: any = window;
      if ('__VUE_DEVTOOLS_GLOBAL_HOOK__' in win) {
        win.__VUE_DEVTOOLS_GLOBAL_HOOK__.Vue = app;
      }
    }

    // 安装插件（全局组件、自定义指令等）
    installPlugins(app);

    // 安装 pinia 和 router
    app.use(pinia).use(router).use(i18n);

    // 获取保存的语言设置
    const savedLocale = (localStorage.getItem('locale') as LocaleType) || 'en-US';

    // 并行加载语言资源和非关键样式
    const [,] = await Promise.all([
      setLocale(savedLocale),
      loadNonCriticalStyles(),
      router.isReady(),
    ]);

    // 挂载应用
    app.mount('#app');

    // // 移除加载屏幕
    // const loadingEl = document.getElementById('app-loading')
    // if (loadingEl) {
    //   loadingEl.classList.add('loading-fade-out')
    //   setTimeout(() => {
    //     loadingEl.remove()
    //   }, 500)
    // }

    console.log('🚀 应用初始化完成');
  } catch (error) {
    console.error('应用初始化失败:', error);
  }
}

// 启动应用
initApp();
