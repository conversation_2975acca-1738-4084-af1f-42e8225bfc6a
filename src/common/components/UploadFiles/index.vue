<script setup lang="ts">
import { ref, watch, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import type { UploadRequestOptions, UploadInstance } from 'element-plus';
import { AnyColumn } from 'element-plus/es/components/table-v2/src/common';
import { uploadFile } from '@@/apis/common';
import pdfIcon from '@@/assets/icons/files/PDF.webp';
import pngIcon from '@@/assets/icons/files/PNG.webp';
import jpegIcon from '@@/assets/icons/files/JPEG.webp';
import jpgIcon from '@@/assets/icons/files/JPG.webp';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
// 定义 props
const props = defineProps({
  accept: {
    type: String,
    default: 'image/png,image/jpeg,image/jpg,application/pdf',
  },
  // 文件类型 FRV-法币充值凭证、ERC-企业注册证书、BRC-商业登记证、NAR1-NAR1、NNC1-NNC1、BASF-近6个月银行账户流水文件、SIAD-股东身份认证文件、DIAD-董事身份认证文件、ALFAM-授权管理人授权书、MIAD-管理者身份认证文件、DCOFOECM-董事在职证明书或同等证明材料（非香港地区）、MAAOA-组织大纲及章程、BAM-业务真实性材料、OTHER-其他
  fileType: {
    type: String,
    required: true,
  },
  limit: {
    type: Number,
    default: 1,
  },
  fileList: {
    type: Object,
    default: () => [],
  },
  fileSize: {
    type: Number,
    default: 10,
  },
});

// 定义 emits
const emits = defineEmits(['update:fileList']);

// 使用 ref 包装 props.fileList
const localFileList = ref(props.fileList || []);
// 上传中列表
const uploadList = ref<
  { uid: any; name: any; status: string; size: number; type: string; error?: boolean }[]
>([]);
// 控制预览对话框的显示与隐藏
const dialogImageUrl = ref('');
const dialogVisible = ref(false);

// 存储所有上传请求的 AbortController
const uploadControllers = new Map();

watch(
  localFileList,
  (newValue) => {
    emits('update:fileList', newValue);
  },
  { deep: true }
);
// 监听 props.fileList 的变化
watch(
  () => props.fileList,
  (newValue) => {
    localFileList.value = newValue;
  },
  { deep: true }
);

// 根据类型返回icon
const fileTypeToIcon = (name: string) => {
  if (name.toLowerCase().includes('.pdf')) {
    return pdfIcon;
  } else if (name.toLowerCase().includes('.png')) {
    return pngIcon;
  } else if (name.toLowerCase().includes('.jpeg')) {
    return jpegIcon;
  } else if (name.toLowerCase().includes('.jpg')) {
    return jpgIcon;
  } else {
    return '';
  }
};

// 字节转换为MB
const sizeToMB = (size: number) => {
  if (size < 1024) return size.toFixed(2) + 'B';
  if (size < 1024 * 1024) return (size / 1024).toFixed(2) + 'KB';
  return (size / 1024 / 1024).toFixed(2) + 'MB';
};

const upload = ref<UploadInstance>();
// 删除正在上传的文件
const closeUploading = (file: any) => {
  if (uploadControllers.has(file.uid)) {
    uploadControllers.get(file.uid).abort();
    uploadControllers.delete(file.uid);
  }
  const index = uploadList.value.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    uploadList.value.splice(index, 1);
  }
};

// 文件超出限制时的回调
const handleExceed = (files: any) => {
  ElMessage.error(t('common.upload.limit', { limit: props.limit }));
  return false;
};

// 文件移除时的回调
const handleRemove = (file: any) => {
  const index = localFileList.value.findIndex(
    (item: { fileSeqId: string | number }) => item.fileSeqId === file.fileSeqId
  );
  if (index !== -1) {
    localFileList.value.splice(index, 1);
  }
};

// 文件预览时的回调
const handlePreview = (file: any) => {
  if (file.fileOriginName.toLowerCase().includes('.pdf')) {
    window.open(file.fileUrl);
  } else {
    dialogImageUrl.value = file.fileUrl;
    dialogVisible.value = true;
  }
};

// 上传方法
const customUpload = async (options: UploadRequestOptions) => {
  if (localFileList.value.length + uploadList.value.length >= props.limit) {
    ElMessage.error(t('common.upload.limit', { limit: props.limit }));
    return false;
  }
  const { file, onSuccess, onError } = options;
  const controller = new AbortController();
  // 存储 controller（以文件唯一标识 uid 为 key）
  uploadControllers.set(file.uid, controller);
  // 加入上传中列表
  uploadList.value.push({
    uid: file.uid,
    name: file.name,
    status: 'P',
    size: file.size,
    type: file.type,
    error: false,
  });

  try {
    const formData = new FormData();
    formData.append('multipartFile', file);
    // 可以添加额外参数
    formData.append('fileType', props.fileType);
    // 假设 UploadRequestData 类型定义如下，需要将 FormData 转换为该类型
    interface UploadRequestData {
      fileType: string;
      multipartFile: File;
    }

    const requestData: UploadRequestData = {
      fileType: props.fileType,
      multipartFile: file,
    };

    await uploadFile(requestData, controller.signal).then(({ data }) => {
      onSuccess(data);
      uploadControllers.delete(file.uid); // 请求完成后移除
    });
  } catch (error: any) {
    // 标记为上传失败
    ElMessage.error(t('common.upload.failed'));

    const index = uploadList.value.findIndex((item) => item.uid === file.uid);
    uploadList.value.splice(index, 1);
    // if (index !== -1) {
    //   uploadList.value[index].error = true;
    // }
    onError(error); // 触发错误回调
  }
};

// 文件上传成功时的回调
const handleSuccess = (response: any, file: any) => {
  if (response && response.fileUrl) {
    localFileList.value.push({
      fileOriginName: file.name,
      fileSize: file.size,
      type: file.raw.type,
      fileUrl: response.fileUrl,
      fileSeqId: response.fileSeqId,
    });
  }
  // 移除上传中列表
  const index = uploadList.value.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    uploadList.value.splice(index, 1);
  }
};

// 文件上传失败时的回调
const handleError = (error: any, file: any, fileList: any) => {
  // 已在 customUpload 中处理错误状态
};

// 重试上传
const retryUpload = (file: any) => {
  // 移除错误状态
  const index = uploadList.value.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    // uploadList.value[index].error = false;
    ElMessage.error(t('common.upload.failed'));
  }

  // 重新上传文件
  if (file.raw) {
    customUpload({
      file: file.raw,
      onSuccess: (response) => handleSuccess(response, file.raw),
      onError: (err) => handleError(err, file.raw, []),
      onProgress: () => {},
      action: '',
      method: 'post',
      data: {},
      filename: 'file',
      headers: {},
      withCredentials: false,
      signal: new AbortController().signal,
    } as UploadRequestOptions);
  }
};

const uploadDisabled = computed(() => {
  return localFileList.value.length >= props.limit;
});

// 文件上传前的校验
const beforeUpload = (file: File) => {
  if (localFileList.value.length >= props.limit) {
    ElMessage.error(t('common.upload.limit', { limit: props.limit }));
    return false;
  }

  const isPDF = file.type === 'application/pdf';
  const isPNG = file.type === 'image/png';
  const isJPG = file.type === 'image/jpeg';
  const isJPEG = file.type === 'image/jpeg';

  const isLt10M = file.size / 1024 / 1024 < props.fileSize;

  if (!isPDF && !isPNG && !isJPG && !isJPEG) {
    ElMessage.error(t('common.upload.format'));
  }
  if (!isLt10M) {
    ElMessage.error(t('common.upload.size', { fileSize: props.fileSize }));
  }
  return (isPDF || isPNG || isJPG || isJPEG) && isLt10M;
};
</script>
<script lang="ts">
export default {
  name: 'UploadFiles',
};
</script>

<template>
  <div class="max-w-[690px] w-100%">
    <div>
      <el-upload
        v-if="localFileList.length < props.limit"
        ref="upload"
        class="upload-container"
        action="#"
        drag
        multiple
        :disabled="uploadDisabled"
        :accept="accept"
        :on-preview="handlePreview"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-exceed="handleExceed"
        :before-upload="beforeUpload"
        :http-request="customUpload"
        :show-file-list="false"
      >
        <i class="bg-[url(@@/assets/icons/files/add.webp)] block w-16px h-16px mx-auto"></i>
        <div class="el-upload__text">
          <p class="text-14px font-400 color-[#222527] m-0 leading-20px mt-14px">
            {{ t('common.upload.clickOrDrag') }}
          </p>
          <p class="text-12px font-400 color-[#6B7275] m-0 leading-20px mt-6px">
            {{ t('common.upload.tips', { limit: props.limit, fileSize: props.fileSize }) }}
          </p>
        </div>
      </el-upload>

      <ul class="p-0 m-0">
        <li
          v-for="file in uploadList"
          :key="file.uid"
          class="mt-12px w-100% h-52px p-8px bg-[#f5f5f5] rounded-[6px] flex justify-between items-center"
        >
          <div class="w-36px h-36px p-10px bg-[#fff] rounded-[4px]">
            <img class="w-100%" :src="fileTypeToIcon(file.name)" alt="" />
          </div>
          <div class="flex-1 m-8px overflow-hidden">
            <div class="overflow-hidden flex justify-between items-center">
              <p class="m-0 truncate text-14px font-500 color-[#1D2129] leading-20px">
                {{ file.name }}
              </p>
              <span class="text-12px font-400 color-[#6B7275] leading-20px">{{
                sizeToMB(file.size)
              }}</span>
            </div>

            <div v-if="!file.error" class="mt-6px">
              <el-progress
                :percentage="100"
                status="success"
                :show-text="false"
                :indeterminate="true"
                :duration="3"
              >
              </el-progress>
            </div>
            <div v-else class="flex items-center">
              <span class="flex items-center text-12px font-400 color-[#FD3627] mr-8px h-24px block"
                ><svg-icon name="icon-danger" class="mr-2px" />{{
                  $t('common.upload.failed')
                }}</span
              >
              <el-button
                type="primary"
                size="small"
                text
                @click="retryUpload(file)"
                class="text-12px color-[#FF0064] p-0"
                >{{ $t('common.upload.retry') }}</el-button
              >
            </div>
          </div>

          <el-icon class="text-18px color-[#6B7275] cursor-pointer" @click="closeUploading(file)">
            <Close />
          </el-icon>
        </li>

        <li
          v-for="file in localFileList"
          :key="file.uid"
          class="cursor-pointer mt-12px w-100% h-52px p-8px bg-[#f5f5f5] rounded-[6px] flex justify-between items-center"
        >
          <div class="w-36px h-36px p-10px bg-[#fff] rounded-[4px]">
            <img class="w-100%" :src="fileTypeToIcon(file.fileOriginName)" alt="" />
          </div>

          <div class="flex-1 m-8px overflow-hidden" @click="handlePreview(file)">
            <div class="overflow-hidden flex justify-between items-center">
              <p class="m-0 truncate text-14px font-500 color-[#1D2129] leading-20px">
                {{ file.fileOriginName }}
              </p>
              <span class="text-12px font-400 color-[#6B7275] leading-20px">{{
                sizeToMB(file.fileSize)
              }}</span>
            </div>
          </div>

          <el-icon class="text-18px color-[#6B7275] cursor-pointer" @click="handleRemove(file)">
            <Close />
          </el-icon>
        </li>
      </ul>
    </div>

    <el-dialog v-model="dialogVisible">
      <img width="100%" :src="dialogImageUrl" />
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.upload-container {
  :deep(.el-upload-dragger) {
    padding: 22px;
    background-color: #f5f5f5;
  }

  :deep(.el-upload-dragger:hover) {
    border-color: #ff0064;
  }
}
</style>
