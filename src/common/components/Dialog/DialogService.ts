import { ref, h, render, createVNode, defineComponent, reactive } from 'vue'
import CommonDialog from './CommonDialog.vue'
import { t } from '@@/i18n';

export interface DialogOptions {
  title: string
  message: string
  iconClass?: string
  showCloseButton?: boolean
  confirmButtonText?: string
  cancelButtonText?: string
  hideCancelButton?: boolean
  customClass?: string
  contentComponent?: any
  contentProps?: Record<string, any>
  onConfirm?: () => void
  onCancel?: () => void
}

// 创建一个容器来挂载对话框
const createDialogContainer = () => {
  const container = document.createElement('div')
  document.body.appendChild(container)
  return container
}

export const DialogService = {
  show(options: DialogOptions): Promise<boolean> {
    return new Promise((resolve) => {
      const container = createDialogContainer()
      const visible = ref(false)
      
      // 创建对话框的虚拟节点
      const dialogVNode = createVNode(CommonDialog, {
        visible: true,
        title: options.title,
        message: options.message,
        iconClass: options.iconClass || '',
        showCloseButton: options.showCloseButton !== false,
        confirmButtonText: options.confirmButtonText || t('common.dialog.confirmButtonText'),
        cancelButtonText: options.cancelButtonText || t('common.dialog.cancelButtonText'),
        hideCancelButton: options.hideCancelButton || false,
        customClass: options.customClass || '',
        'onUpdate:visible': (value: boolean) => {
          visible.value = value
          if (!value) {
            // 当对话框关闭时，移除DOM
            setTimeout(() => {
              render(null, container)
              container.remove()
            }, 300) // 等待过渡动画完成
          }
        },
        onConfirm: () => {
          if (options.onConfirm) options.onConfirm()
          resolve(true)
          visible.value = false
        },
        onCancel: () => {
          if (options.onCancel) options.onCancel()
          resolve(false)
          visible.value = false
        }
      }, options.contentComponent ? {
        content: () => h(options.contentComponent, options.contentProps || {})
      } : undefined)
      
      // 渲染对话框
      render(dialogVNode, container)
      
      // 显示对话框
      visible.value = true
    })
  },
  
  // 快捷方法
  confirm(message: string, title = t('common.dialog.confirmTitle'), options: Partial<DialogOptions> = {}): Promise<boolean> {
    return this.show({
      title,
      message,
      iconClass: 'info',
      ...options
    })
  },
  
  warning(message: string, title = t('common.dialog.warningTitle'), options: Partial<DialogOptions> = {}): Promise<boolean> {
    return this.show({
      title,
      message,
      iconClass: 'warning',
      ...options
    })
  },
  
  success(message: string, title = t('common.dialog.successTitle'), options: Partial<DialogOptions> = {}): Promise<boolean> {
    return this.show({
      title,
      message,
      iconClass: 'success',
      ...options
    })
  }
}

// 为了兼容性，将方法挂载到 CommonDialog 上
CommonDialog.show = DialogService.show
CommonDialog.confirm = DialogService.confirm
CommonDialog.warning = DialogService.warning
CommonDialog.success = DialogService.success

export default DialogService