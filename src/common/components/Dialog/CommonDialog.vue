<template>
  <transition name="dialog-fade">
    <div v-if="visible" class="dialog-overlay" @click.self="handleOverlayClick">
      <div :class="['dialog-box', customClass]">
        <div class="dialog-header">
          <img v-if="iconDetails" :src="iconDetails.src" :alt="iconDetails.alt" class="dialog-icon" />
          <span v-else-if="iconClass" :class="['dialog-icon', iconClass]"></span>
          <span class="dialog-title">{{ title }}</span>
          <button v-if="showCloseButton" class="dialog-close-button" @click="handleCancel">
            <img src="@/common/assets/icons/icon-close.svg" alt="Close" />
          </button>
        </div>

        <div class="dialog-content">
          <slot name="content" :message="message">
            <span v-html="message" class="dialog-message"></span>
          </slot>
        </div>

        <div class="dialog-footer">
          <button v-if="!hideCancelButton" class="dialog-button cancel-button" @click="handleCancel">{{
            cancelButtonTextData }}</button>
          <button class="dialog-button confirm-button" @click="handleConfirm">{{ confirmButtonTextData }}</button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { defineProps, defineEmits, watch, toRefs, onBeforeUnmount, computed, ref } from 'vue';
import iconWarning from '@/common/assets/icons/icon-warning.svg';
import iconInfo from '@/common/assets/icons/icon-dlg-info.svg';
import iconSuccess from '@/common/assets/icons/icon-dlg-success.svg';
import { t } from '@@/i18n';

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  message: {
    type: String,
    required: true
  },
  iconClass: {
    type: String,
    default: ''
  },
  showCloseButton: {
    type: Boolean,
    default: true
  },
  confirmButtonText: {
    type: String,
    default: "" // t('common.dialog.confirmButtonText')
  },
  cancelButtonText: {
    type: String,
    default: "" //t('common.dialog.cancelButtonText')
  },
  hideCancelButton: {
    type: Boolean,
    default: false
  },
  customClass: {
    type: String,
    default: ''
  },
  closeOnClickOutside: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

const { visible } = toRefs(props);

const iconMap = {
  warning: { src: iconWarning, alt: 'Warning' },
  info: { src: iconInfo, alt: 'Info' },
  success: { src: iconSuccess, alt: 'Success' }
};

const iconDetails = computed(() => {
  return iconMap[props.iconClass];
});

const confirmButtonTextData = ref(props.confirmButtonText || t('common.dialog.confirmButtonText'));
const cancelButtonTextData = ref(props.cancelButtonText || t('common.dialog.cancelButtonText'));

watch(() => props.confirmButtonText, (newVal) => {
  confirmButtonTextData.value = newVal;
});

watch(() => props.cancelButtonText, (newVal) => {
  cancelButtonTextData.value = newVal;
});

const handleConfirm = () => {
  emit('confirm');
  emit('update:visible', false);
};

const handleCancel = () => {
  emit('cancel');
  emit('update:visible', false);
};

const handleOverlayClick = () => {
  if (props.closeOnClickOutside) {
    handleCancel();
  }
};

watch(visible, (newVal) => {
  if (typeof document !== 'undefined') {
    if (newVal) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }
});

onBeforeUnmount(() => {
  if (typeof document !== 'undefined') {
    if (document.body.style.overflow === 'hidden') {
      document.body.style.overflow = '';
    }
  }
});

</script>

<style scoped>
/* 启用弹框淡入淡出动画 */
.dialog-fade-enter-active,
.dialog-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dialog-fade-enter-from,
.dialog-fade-leave-to {
  opacity: 0;
}

/* 弹框内容缩放动画 */
.dialog-fade-enter-active .dialog-box {
  animation: dialog-zoom-in 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dialog-fade-leave-active .dialog-box {
  animation: dialog-zoom-out 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dialog-zoom-in {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes dialog-zoom-out {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  /* 添加背景遮罩动画 */
  animation: overlay-fade-in 0.3s ease;
}

@keyframes overlay-fade-in {
  from {
    background-color: rgba(0, 0, 0, 0);
  }

  to {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.dialog-box {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 400px;
  max-width: 90%;
  padding: 24px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.dialog-header {
  display: flex;
  align-items: center;
  position: relative;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 8px;
  color: #222527;
}

.dialog-icon {
  margin-right: 14px;
  font-size: 18px;
  line-height: 1;
}

img.dialog-icon {
  width: 20px;
  height: 20px;
  vertical-align: middle;
}

.dialog-title {
  flex-grow: 1;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 18px;
  color: #222527;
}

.dialog-close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  position: absolute;
  /* 使其可以精确放置 */
  top: 50%;
  /* 修改：垂直居中 */
  right: 0px;
  transform: translateY(-50%);
  /* 新增：配合top:50%实现垂直居中 */
  width: 16px;
  /* 图标宽度 */
  height: 16px;
  /* 图标高度 */
  display: flex;
  /* 用于居中img */
  align-items: center;
  /* 用于居中img */
  justify-content: center;
  /* 用于居中img */
}

.dialog-close-button img {
  display: block;
  /* 移除图片下方的额外空间 */
  width: 100%;
  height: 100%;
}

.dialog-close-button:hover {
  /* 可以为SVG图标添加悬停效果，例如改变透明度或使用不同的SVG */
  opacity: 0.7;
}

/* 添加内容渐显动画 */
.dialog-content {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #222527;
  padding-left: 36px;
  margin-bottom: 24px;
  /* 根据图片调整 */
  line-height: 1.6;
  white-space: pre-wrap
}

/* 消息内容自动换行样式 */
.dialog-message {
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  display: inline-block;
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  /* 根据图片调整 */
}

/* 添加按钮悬停动画 */
.dialog-button {
  padding: 8px 20px;
  /* 根据图片调整 */
  border-radius: 6px;
  /* 根据图片调整 */
  cursor: pointer;
  height: 32px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  transition: transform 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
  border: 1px solid transparent;
  /* 添加一个透明边框以便悬停时保持大小 */
}

.cancel-button {
  border: 1px solid #E5E6EB;
  background-color: #fff;
  color: #222527;
}

.confirm-button {
  background-color: #030814;
  color: white;
  border-color: #030814;
}

.cancel-button:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.confirm-button:hover {
  background-color: #374151;
  border-color: #374151;
}
</style>
