import CommonDialog from './CommonDialog.vue'
import { DialogService } from './DialogService'

// 扩展 CommonDialog 类型
type DialogWithService = typeof CommonDialog & {
  show: typeof DialogService.show
  confirm: typeof DialogService.confirm
  warning: typeof DialogService.warning
  success: typeof DialogService.success
}

// 将服务方法挂载到组件上
(CommonDialog as DialogWithService).show = DialogService.show
;(CommonDialog as DialogWithService).confirm = DialogService.confirm
;(CommonDialog as DialogWithService).warning = DialogService.warning
;(CommonDialog as DialogWithService).success = DialogService.success

export default CommonDialog as DialogWithService