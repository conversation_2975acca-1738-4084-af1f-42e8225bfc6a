import BindTFADrawer from './BindTFADrawer.vue'
import { BindTFAService } from './BindTFAService'

// 扩展 BindTFADrawer 类型
type BindTFAWithService = typeof BindTFADrawer & {
  show: typeof BindTFAService.show
  bind: typeof BindTFAService.bind
  showUnbind: typeof BindTFAService.showUnbind
  unbind: typeof BindTFAService.unbind
}

// 将服务方法挂载到组件上
(BindTFADrawer as BindTFAWithService).show = BindTFAService.show
;(BindTFADrawer as BindTFAWithService).bind = BindTFAService.bind
;(BindTFADrawer as BindTFAWithService).showUnbind = BindTFAService.showUnbind
;(BindTFADrawer as BindTFAWithService).unbind = BindTFAService.unbind

export default BindTFADrawer as BindTFAWithService

// 也导出类型和服务，方便使用
export { 
  BindTFAService, 
  type Bind2FAData, 
  type BindTFAOptions, 
  type UnbindTFAOptions,
  TFAOperationType
} from './BindTFAService'
export { default as BindTFADrawer } from './BindTFADrawer.vue'