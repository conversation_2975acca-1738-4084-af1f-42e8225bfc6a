<template>
  <el-drawer 
    v-model="showDrawer" 
    size="470px" 
    style="--el-drawer-padding-primary: 0px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :close-on-press-escape="false"
  >
    <template #header>
      <div style="
            font-family: PingFangSC-Medium; 
            font-size: 18px; 
            color: #222527; 
            height: 60px; 
            margin-bottom: 0; 
            display: flex; 
            flex-direction: row; 
            align-items: center; 
            padding-top: 18px; padding-bottom: 18px; padding-left: 24px;"
          >
        <span>
          {{ isUnbindMode ? (
            t('setting.personal.unbind')
          ) : (
            currentStep === 1 ? t('setting.personal.verifyTitle') : t('setting.personal.verifyTitle')
          ) }}
        </span>
      </div>
    </template>
    <template #default>
      <!-- 步骤组件 - 仅在绑定模式显示 -->
      <StepComponent v-if="isBindMode" v-model="currentStep" :steps="stepConfig"
        @step-change="handleStepChange" @complete="handleStepComplete" />
      
      <!-- 第一步：密码验证 -->
      <Transition name="step-fade" mode="out-in">
        <div v-if="currentStep === 1" class="step-container" key="password-step">
          <div style="padding: 24px; padding-top: 0px;">
            <div style="
              color: #6B7275; 
              font-size: 14px; 
              line-height: 20px; 
              margin-bottom: 24px;
            ">
              {{ isUnbindMode ? (
                t('setting.personal.verifyPasswordDesc2') || '请输入密码以确认解绑操作'
              ) : (
                t('setting.personal.verifyPasswordDesc')
              ) }}
            </div>
            <!-- 密码表单 -->
            <el-form ref="passwordFormRef" :model="passwordFormData" :rules="passwordRules" label-position="top" hide-required-asterisk>
              <el-form-item :label="t('setting.personal.password')" prop="currentPassword" class="custom-form-item mb-20px">
                <el-input 
                  v-model="passwordFormData.currentPassword" 
                  type="password" 
                  show-password
                  :placeholder="t('setting.editPassword.placeholder')" 
                  class="custom-input" 
                  size="large"
                  :disabled="isProcessing"
                  @keyup.enter="handlePasswordSubmit"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 第二步：绑定验证器（仅绑定模式） -->
        <div v-else-if="currentStep === 2 && isBindMode" class="step-container" key="bind-step">
          <div style="padding: 24px; padding-top: 0px;">
            <div style="color: #6B7275; font-size: 14px; line-height: 20px">{{ t('setting.setValidator.validatorDesc') }}</div>
            <div style="position: relative; margin-top: 24px;">
              <div class="dashed-line" style="position: absolute; top: 18px; left: 3px; height: 30px;" />
              <div style="display: flex; flex-direction: row; justify-content: space-between; align-items: center; font-family: PingFangSC-Regular; font-size: 14px; color: #222527; font-weight: 400;">
                <span>{{ t('setting.setValidator.step1') }}</span>
                <el-popover placement="bottom-end" :width="247" trigger="click">
                  <template #reference>
                    <span style="cursor: pointer; color: #FF0064; text-align: right;">{{ t('setting.setValidator.viewSupportedApps') }}</span>
                  </template>
                  <div>
                    <el-row style="display: flex; flex-direction: row; align-items: center;">
                       <div style="border: 1px solid #E5E6EB; border-radius: 4px; padding: 5px; padding-bottom: 0px;">
                        <img src="@@/assets/icons/icon-micro-auth.png" alt="" style="width: 21px; height: 24px;">
                       </div>
                       <span style="margin-left: 12px;">Microsoft Authenticator</span>
                    </el-row>
                    <el-row style="display: flex; flex-direction: row; align-items: center; margin-top: 24px;">
                       <div style="border: 1px solid #E5E6EB; border-radius: 4px; padding: 5px; padding-bottom: 0px;">
                        <img src="@@/assets/icons/icon-google-auth.png" alt="" style="width: 24px; height: 21px;">
                       </div>
                       <span style="margin-left: 12px;">Google Authenticator</span>
                    </el-row>
                  </div>
                </el-popover>
              </div>
            </div>
            <div style="margin-top: 32px;">
              <div style="font-family: PingFangSC-Regular; font-size: 14px; color: #222527; font-weight: 400;">
                <span>{{ t('setting.setValidator.step2') }}</span>
              </div>
              <div style="display: flex; flex-direction: row; justify-content: center; align-items: center; position: relative;">
                <div class="dashed-line" style="position: absolute; top: 6px; left: 3px; height: 200px;" />
                <div style="border: 1px solid #E5E6EB; border-radius: 6px; display: flex; flex-direction: column; justify-content: center; align-items: center; width: 176px; height: 176px; margin-top: 16px;">
                  <qrcode-vue v-if="bindData.url" :value="bindData.url" :size="152" level="H" />
                </div>
              </div>
            </div>
            <div style="margin-top: 24px;">
              <div style="font-family: PingFangSC-Regular; font-size: 14px; color: #222527; font-weight: 400; margin-bottom: 6px;">
                <span>{{ t('setting.setValidator.step3') }}</span>
              </div>
              <el-form ref="bindFormRef" :model="bindFormData" :rules="bindRules" label-position="top" hide-required-asterisk>
                <el-form-item 
                  prop="confirmCode" 
                  class="custom-form-item"
                  style="margin-bottom: 6px;"
                >
                  <el-input 
                    v-model="bindFormData.confirmCode" 
                    type="text"
                    :placeholder="t('setting.setValidator.placeholder')" 
                    class="custom-input"
                    maxlength="6"
                    :disabled="isProcessing"
                    @keyup.enter="handleBindSubmit"
                  />
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </Transition>
    </template>
    <template #footer>
      <div
        style="border-top: 1px solid #E5E6EB; background-color: #fff;
          position: absolute; bottom: 0; right: 0; 
          display: flex; flex-direction: row; align-items: center; 
          justify-content: flex-end; padding: 24px; width: 100%; height: 64px;"
        >
        
        <!-- 步骤 1 的按钮 -->
        <template v-if="currentStep === 1">
          <el-button 
            class="cancel-btn" 
            style="width: 68px; height: 32px; font-size: 14px; color: #222527; font-family: PingFangSC-Regular; margin-right: 12px;"
            @click="handleCancel"
            :disabled="isProcessing"
          >
            {{ t('setting.editPassword.cancel') }}
          </el-button>
          <el-button 
            class="confirm-btn"
            style="width: 68px; height: 32px; font-size: 14px; color: #fff; font-family: PingFangSC-Regular;" 
            @click="handlePasswordSubmit"
            :loading="isProcessing"
          >
            {{ isUnbindMode ? (
              t('setting.personal.unbind')
            ) : (
              t('setting.editPassword.confirm')
            ) }}
          </el-button>
        </template>
        
        <!-- 步骤 2 的按钮（仅绑定模式） -->
        <template v-else-if="currentStep === 2 && isBindMode">
          <el-button 
            class="cancel-btn" 
            style="width: 68px; height: 32px; font-size: 14px; color: #222527; font-family: PingFangSC-Regular; margin-right: 12px;"
            @click="handleCancel"
            :disabled="isProcessing"
          >
            {{ t('setting.editPassword.cancel') }}
          </el-button>
          <el-button 
            class="confirm-btn"
            style="width: 68px; height: 32px; font-size: 14px; color: #fff; font-family: PingFangSC-Regular;" 
            @click="handleBindSubmit"
            :loading="isProcessing"
          >
            {{ t('setting.editPassword.confirm') }}
          </el-button>
        </template>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { t } from '@@/i18n'
import { ElMessage } from 'element-plus'
import QrcodeVue from "qrcode.vue"
import { bind2FAApi, bind2FAConfirmApi, unbind2FAApi } from '../../../pages/setting/apis'
import { encryptPasswordMD5 } from '@@/utils/crypto'
import type { Bind2FAData } from './BindTFAService'
import { TFAOperationType } from './BindTFAService'
import StepComponent from '@/pages/setting/components/StepComponent'

defineOptions({
  name: 'BindTFADrawer'
})

// 密码表单接口
interface PasswordInfo {
  currentPassword: string
}

// 绑定表单接口
interface BindInfo {
  confirmCode: string
}

const passwordFormRef = ref<FormInstance>()
const bindFormRef = ref<FormInstance>()

// Props 接收
const props = withDefaults(defineProps<{
  visible: boolean
  operationType: TFAOperationType // 操作类型：绑定或解绑
  sysSeqId?: string // 解绑时需要的流水号
}>(), {
  visible: false,
  operationType: TFAOperationType.BIND
})

const emit = defineEmits<{
  (e: 'bind-success'): void
  (e: 'unbind-success'): void
  (e: 'cancel'): void
  (e: 'update:visible', value: boolean): void
}>()

// 步骤管理（解绑模式始终为步骤 1）
const currentStep = ref<1 | 2>(1) // 1: 密码验证, 2: 绑定验证器

// 步骤配置
const stepConfig = computed(() => [
  { title: t('setting.setValidator.validator') },
  { title: t('setting.setValidator.setValidator') }
])

// 内部状态管理
const bindData = ref<Bind2FAData>({
  sysSeqId: '',
  url: ''
})

const passwordFormData = ref<PasswordInfo>({
  currentPassword: ''
})

const bindFormData = ref<BindInfo>({
  confirmCode: ''
})

const isProcessing = ref(false)

const showDrawer = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 是否为绑定模式
const isBindMode = computed(() => props.operationType === TFAOperationType.BIND)
// 是否为解绑模式
const isUnbindMode = computed(() => props.operationType === TFAOperationType.UNBIND)

// 密码验证规则
const passwordRules = computed(() => reactive<FormRules<PasswordInfo>>({
  currentPassword: [
    { required: true, message: t('setting.editPassword.currentPasswordRequired'), trigger: 'blur' }
  ]
}))

// 绑定验证规则
const bindRules = computed(() => reactive<FormRules<BindInfo>>({
  confirmCode: [
    { required: true, message: t('setting.setValidator.placeholder'), trigger: 'blur' }
  ]
}))

// 监听 visible 变化，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 重置步骤和表单
    currentStep.value = 1
    passwordFormData.value = {
      currentPassword: ''
    }
    bindFormData.value = {
      confirmCode: ''
    }
    bindData.value = {
      sysSeqId: '',
      url: ''
    }
    isProcessing.value = false
    nextTick(() => {
      passwordFormRef.value?.clearValidate()
      bindFormRef.value?.clearValidate()
    })
  }
})

// 密码验证提交
const handlePasswordSubmit = async () => {
  if (!passwordFormRef.value || isProcessing.value) return
  
  try {
    // 验证表单
    await passwordFormRef.value.validate()
    
    isProcessing.value = true
    
    if (isBindMode.value) {
      // 绑定模式：调用 API 验证密码并获取绑定数据
      const res = await bind2FAApi(encryptPasswordMD5(passwordFormData.value.currentPassword))
      
      // 保存绑定数据
      bindData.value = res.data
      
      // 切换到第二步
      currentStep.value = 2
    } else if (isUnbindMode.value) {
      // 解绑模式：直接调用解绑 API
      await unbind2FAApi(
        props.sysSeqId || '', 
        encryptPasswordMD5(passwordFormData.value.currentPassword)
      )
      
      // 发出解绑成功事件
      emit('unbind-success')
      
      // 解绑成功后关闭抽屉
      showDrawer.value = false
    }
    
  } catch (error) {
    console.error(`${isBindMode.value ? 'Password verification' : 'Unbind'} failed:`, error)
  } finally {
    isProcessing.value = false
  }
}

/**
 * 处理步骤变化事件
 */
const handleStepChange = (step: number) => {
  currentStep.value = step as 1 | 2
}

/**
 * 处理步骤完成事件
 */
const handleStepComplete = () => {
  if (currentStep.value === 1) {
    handlePasswordSubmit()
  } else if (currentStep.value === 2) {
    handleBindSubmit()
  }
}

// 绑定验证器提交（仅绑定模式）
const handleBindSubmit = async () => {
  if (!bindFormRef.value || isProcessing.value || !isBindMode.value) return
  
  try {
    // 验证表单
    await bindFormRef.value.validate()
    
    isProcessing.value = true
    
    // 调用绑定确认 API
    await bind2FAConfirmApi(bindData.value.sysSeqId, bindFormData.value.confirmCode)
    
    // 发出成功事件
    emit('bind-success')
    
    // 绑定成功后关闭抽屉
    showDrawer.value = false
    
  } catch (error) {
    console.error('2FA binding failed:', error)
  } finally {
    isProcessing.value = false
  }
}

// 返回上一步（仅绑定模式）
const handleBack = () => {
  if (!isBindMode.value) return
  
  currentStep.value = 1
  // 清空绑定表单
  bindFormData.value = {
    confirmCode: ''
  }
  nextTick(() => {
    bindFormRef.value?.clearValidate()
  })
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
  showDrawer.value = false
}
</script>

<style lang="scss" scoped>
p {
  margin: 0;
}

// 步骤切换动画
.step-container {
  animation: step-fade-in 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

@keyframes step-fade-in {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.custom-form-item {
  :deep(.el-form-item__label) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #222527;
    margin-bottom: 8px;
    line-height: 20px;
  }

  &.is-error :deep(.el-input__wrapper) {
    background: #FFF2EE;
    border: 1px solid #FD3627;
    box-shadow: none;
  }
}

.dashed-line {
  width: 1px;
  height: 87px;
  background-image: linear-gradient(to bottom, #E5E6EB 0%, #E5E6EB 80%, transparent 50%);
  background-size: 100% 10px;
  background-repeat: repeat-y
}

.custom-input {
  width: 100%;
  
  :deep(.el-input__wrapper) {
    border: 1px solid #E5E6EB;
    box-shadow: none;
    border-radius: 6px;
    height: 40px;
    padding: 0 12px;
    background-color: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &:hover {
      border-color: #C0C4CC;
    }
    
    &.is-focus {
      border-color: #030814;
    }
  }
  
  :deep(.el-input__inner) {
    font-family: 'PingFangSC-Regular', sans-serif;
    font-size: 14px;
    color: #222527;
    
    &::placeholder {
      color: #A8ABB2;
    }
  }
  
  :deep(.el-input__suffix) {
    .el-input__suffix-inner {
      .el-input__icon {
        color: #A8ABB2;
        
        &:hover {
          color: #606266;
        }
      }
    }
  }
}

:deep(.el-form-item__error) {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #FD3627FF;
}

.cancel-btn {
  background-color: #FFFFFF !important;
  border: 1px solid #E5E6EB !important;
  color: #222527 !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.cancel-btn:hover {
  background-color: #F8F9FA !important;
  border-color: #E5E6EB !important;
  transform: scale(1.02);
}

.confirm-btn {
  background-color: #030814 !important;
  border: 1px solid #030814 !important;
  color: #FFFFFF !important;
  border-radius: 6px !important;
  font-family: 'PingFangSC-Regular', sans-serif;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.confirm-btn:hover {
  background-color: #1a1f2e !important;
  border-color: #1a1f2e !important;
  transform: scale(1.02);
}
:global(.el-drawer__header) {
  border-bottom: 1px solid #E5E6EB;
}
</style>