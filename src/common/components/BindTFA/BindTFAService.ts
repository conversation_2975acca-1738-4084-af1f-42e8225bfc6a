import { ref, createVNode, render } from 'vue'
import BindTFADrawer from './BindTFADrawer.vue'
import { t } from '@@/i18n'

export interface Bind2FAData {
  sysSeqId: string
  url: string
}

export interface BindTFAOptions {
  onSuccess?: () => void
  onCancel?: () => void
}

export interface UnbindTFAOptions {
  sysSeqId: string  // 解绑需要的流水号
  onSuccess?: () => void
  onCancel?: () => void
}

// 操作类型枚举
export enum TFAOperationType {
  BIND = 'bind',
  UNBIND = 'unbind'
}

// 创建一个容器来挂载抽屉
const createBindTFAContainer = () => {
  const container = document.createElement('div')
  document.body.appendChild(container)
  return container
}

export const BindTFAService = {
  /**
   * 显示 2FA 绑定抽屉（两步流程：密码验证 -> 验证器绑定）
   * @param options 绑定选项
   * @returns Promise<boolean> - true表示绑定成功，false表示取消
   */
  show(options: BindTFAOptions = {}): Promise<boolean> {
    return new Promise((resolve) => {
      const container = createBindTFAContainer()
      const visible = ref(false)
      
      // 创建抽屉的虚拟节点
      const drawerVNode = createVNode(BindTFADrawer, {
        visible: true,
        operationType: TFAOperationType.BIND,
        'onUpdate:visible': (value: boolean) => {
          visible.value = value
          if (!value) {
            // 当抽屉关闭时，移除DOM
            setTimeout(() => {
              render(null, container)
              container.remove()
            }, 300) // 等待过渡动画完成
          }
        },
        onBindSuccess: () => {
          if (options.onSuccess) options.onSuccess()
          setTimeout(() => resolve(true), 100)
        },
        onCancel: () => {
          if (options.onCancel) options.onCancel()
          setTimeout(() => resolve(false), 100)
        }
      })
      
      // 渲染抽屉
      render(drawerVNode, container)
      
      // 显示抽屉
      visible.value = true
    })
  },
  
  /**
   * 显示 2FA 解绑抽屉（单步流程：仅密码验证）
   * @param options 解绑选项
   * @returns Promise<boolean> - true表示解绑成功，false表示取消
   */
  showUnbind(options: UnbindTFAOptions): Promise<boolean> {
    return new Promise((resolve) => {
      const container = createBindTFAContainer()
      const visible = ref(false)
      
      // 创建抽屉的虚拟节点
      const drawerVNode = createVNode(BindTFADrawer, {
        visible: true,
        operationType: TFAOperationType.UNBIND,
        sysSeqId: options.sysSeqId,
        'onUpdate:visible': (value: boolean) => {
          visible.value = value
          if (!value) {
            // 当抽屉关闭时，移除DOM
            setTimeout(() => {
              render(null, container)
              container.remove()
            }, 300) // 等待过渡动画完成
          }
        },
        onUnbindSuccess: () => {
          if (options.onSuccess) options.onSuccess()
          setTimeout(() => resolve(true), 100)
        },
        onCancel: () => {
          if (options.onCancel) options.onCancel()
          setTimeout(() => resolve(false), 100)
        }
      })
      
      // 渲染抽屉
      render(drawerVNode, container)
      
      // 显示抽屉
      visible.value = true
    })
  },
  
  /**
   * 绑定 2FA 的快捷方法
   * @param options 可选配置
   * @returns Promise<boolean>
   */
  bind(options: BindTFAOptions = {}): Promise<boolean> {
    return this.show(options)
  },
  
  /**
   * 解绑 2FA 的快捷方法
   * @param sysSeqId 解绑需要的流水号
   * @param options 可选配置
   * @returns Promise<boolean>
   */
  unbind(sysSeqId: string, options: Partial<UnbindTFAOptions> = {}): Promise<boolean> {
    return this.showUnbind({
      sysSeqId,
      ...options
    })
  }
}

export default BindTFAService