<script lang="ts" setup>
import { ref, watch, onMounted, nextTick, computed } from 'vue'

// 组件属性定义
interface Props {
  value: string | number // 目标数值
  duration?: number // 动画持续时间（毫秒）
  decimals?: number // 小数位数
  separator?: string // 千分位分隔符
  prefix?: string // 前缀
  suffix?: string // 后缀
  fontSize?: string // 字体大小
  fontWeight?: string // 字体粗细
  color?: string // 字体颜色
  fontFamily?: string // 字体族
}

const props = withDefaults(defineProps<Props>(), {
  duration: 2000,
  decimals: 2,
  separator: ',',
  prefix: '',
  suffix: '',
  fontSize: '28px',
  fontWeight: '600',
  color: '#222527',
  fontFamily: 'ftdin'
})

// 响应式数据
const displayValue = ref('0')
const isAnimating = ref(false)
const containerRef = ref<HTMLElement>()

/**
 * 格式化数字，添加千分位分隔符（直接截断，不四舍五入）
 * @param num 数字
 * @param decimals 小数位数
 * @param separator 分隔符
 * @returns 格式化后的字符串
 */
const formatNumber = (num: number, decimals: number, separator: string): string => {
  // 直接截断小数位，不进行四舍五入
  const factor = Math.pow(10, decimals)
  const truncatedNum = Math.floor(num * factor) / factor

  const parts = truncatedNum.toFixed(decimals).split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator)
  return parts.join('.')
}

/**
 * 数字翻转动画核心函数
 * @param startVal 起始值
 * @param endVal 结束值
 * @param duration 动画时长
 */
const animateNumber = (startVal: number, endVal: number, duration: number) => {
  if (startVal === endVal) {
    displayValue.value = formatNumber(endVal, props.decimals, props.separator)
    return
  }

  isAnimating.value = true
  const startTime = performance.now()
  const difference = endVal - startVal

  // 使用 easeOutCubic 缓动函数，让动画更自然
  const easeOutCubic = (t: number): number => {
    return 1 - Math.pow(1 - t, 3)
  }

  const step = (currentTime: number) => {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)
    const easedProgress = easeOutCubic(progress)

    const currentValue = startVal + (difference * easedProgress)
    displayValue.value = formatNumber(currentValue, props.decimals, props.separator)

    if (progress < 1) {
      requestAnimationFrame(step)
    } else {
      isAnimating.value = false
      displayValue.value = formatNumber(endVal, props.decimals, props.separator)
    }
  }

  requestAnimationFrame(step)
}

/**
 * 启动动画
 * @param newValue 新的目标值
 */
const startAnimation = (newValue: string | number) => {
  const numValue = typeof newValue === 'string' ? parseFloat(newValue) || 0 : newValue
  const currentValue = parseFloat(displayValue.value.replace(/[^\d.-]/g, '')) || 0

  animateNumber(currentValue, numValue, props.duration)
}

// 监听 value 变化，触发动画
watch(
  () => props.value,
  (newValue) => {
    if (newValue !== undefined && newValue !== null) {
      startAnimation(newValue)
    }
  },
  { immediate: false }
)

// 组件挂载时初始化并启动动画
onMounted(async () => {
  await nextTick()
  // 初始化显示值为 0，然后立即启动动画到目标值
  displayValue.value = formatNumber(0, props.decimals, props.separator)

  // 延迟一帧后启动动画，确保初始值已渲染
  requestAnimationFrame(() => {
    if (props.value !== undefined && props.value !== null) {
      startAnimation(props.value)
    }
  })
})

// 计算样式
const computedStyle = computed(() => ({
  fontSize: props.fontSize,
  fontWeight: props.fontWeight,
  color: props.color,
  fontFamily: props.fontFamily,
  transition: 'all 0.3s ease'
}))
</script>

<template>
  <span ref="containerRef" :style="computedStyle" class="number-flip-animation" :class="{ 'animating': isAnimating }">
    {{ prefix }}{{ displayValue }}{{ suffix }}
  </span>
</template>

<style lang="scss" scoped>
.number-flip-animation {

  &.animating {
    transform: scale(1.02);
  }
}
</style>