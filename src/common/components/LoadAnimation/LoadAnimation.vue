<template>
  <div 
    class="loading-dots" 
    :class="[
      `loading-dots--${size}`, 
      { 'loading-dots--dark': dark, 'loading-dots--inline': inline }
    ]"
    :style="{ color }"
  >
    <div class="loading-dots__dot" :style="dotStyle"></div>
    <div class="loading-dots__dot" :style="dotStyle"></div>
    <div class="loading-dots__dot" :style="dotStyle"></div>
    <span v-if="text" class="loading-dots__text">{{ text }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  // 是否使用深色主题
  dark: {
    type: Boolean,
    default: false
  },
  // 点的大小：small, medium, large
  size: {
    type: String,
    default: 'medium',
    validator: (value: string) => ['small', 'medium', 'large'].includes(value)
  },
  // 自定义颜色
  color: {
    type: String,
    default: ''
  },
  // 自定义背景色
  backgroundColor: {
    type: String,
    default: ''
  },
  // 是否内联显示
  inline: {
    type: Boolean,
    default: true
  },
  // 显示的文本
  text: {
    type: String,
    default: ''
  }
});

// 计算点的样式
const dotStyle = computed(() => {
  const style: Record<string, string> = {};
  
  if (props.backgroundColor) {
    style.backgroundColor = props.backgroundColor;
  }
  
  return style;
});
</script>

<style lang="scss" scoped>
.loading-dots {
  display: inline-flex;
  align-items: center;
  
  &--inline {
    display: inline-flex;
  }
  
  &:not(&--inline) {
    display: flex;
    justify-content: center;
    width: 100%;
  }
  
  &__dot {
    border-radius: 50%;
    background-color: currentColor;
    animation: dot-flashing 1s infinite alternate;
    
    &:nth-child(1) {
      animation-delay: 0s;
    }
    
    &:nth-child(2) {
      animation-delay: 0.3s;
    }
    
    &:nth-child(3) {
      animation-delay: 0.6s;
    }
  }
  
  &__text {
    margin-left: 8px;
    font-size: 14px;
  }
  
  // 小尺寸
  &--small {
    gap: 3px;
    
    .loading-dots__dot {
      width: 6px;
      height: 6px;
    }
    
    .loading-dots__text {
      font-size: 12px;
    }
  }
  
  // 中尺寸（默认）
  &--medium {
    gap: 4px;
    
    .loading-dots__dot {
      width: 8px;
      height: 8px;
    }
    
    .loading-dots__text {
      font-size: 14px;
    }
  }
  
  // 大尺寸
  &--large {
    gap: 6px;
    
    .loading-dots__dot {
      width: 12px;
      height: 12px;
    }
    
    .loading-dots__text {
      font-size: 16px;
    }
  }
  
  // 深色主题
  &--dark {
    color: #fff;
    
    .loading-dots__dot {
      background-color: #fff;
    }
    
    .loading-dots__text {
      color: #fff;
    }
  }
}

@keyframes dot-flashing {
  0% {
    opacity: 0.2;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>