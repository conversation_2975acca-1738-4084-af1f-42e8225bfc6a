import { createVNode, render } from 'vue'
import LottieLoading from './index.vue'

let container: HTMLElement | null = null
let refCount = 0

const createLoadingContainer = () => {
  const containerElement = document.createElement('div')
  document.body.appendChild(containerElement)
  return containerElement
}

export const LoadingService = {
  show() {
    refCount++
    if (container) {
      return
    }
    container = createLoadingContainer()
    const loadingVNode = createVNode(LottieLoading)
    render(loadingVNode, container)
  },

  hide() {
    refCount--
    if (refCount < 0) {
      refCount = 0
    }
    if (refCount === 0 && container) {
      render(null, container)
      container.remove()
      container = null
    }
  }
}

export default LoadingService 