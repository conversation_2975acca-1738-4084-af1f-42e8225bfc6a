<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import lottie, { type AnimationItem } from 'lottie-web'
import animationData from '@/common/assets/anima/loading.json'
import { t } from '@@/i18n'

const container = ref<HTMLElement>()
let anim: AnimationItem | null = null

onMounted(() => {
  if (container.value) {
    anim = lottie.loadAnimation({
      container: container.value,
      renderer: 'svg',
      loop: true,
      autoplay: true,
      animationData
    })
    lottie.setSpeed(2)
  }
})

onUnmounted(() => {
  anim?.destroy()
})
</script>

<template>
  <div class="loading-container">
    <div class="loading-content">
      <div ref="container" :style="{ width: '150px', height: '150px' }" />
      <div class="loading-text">{{ t('common.loading.loadingText') }}</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  .loading-content {
    width: 248px;
    height: 200px;
    background: #FFFFFF;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .loading-text {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #222527;
    letter-spacing: 0;
    text-align: center;
    margin-top: -10px;
  }
}

</style>
