<template>
  <div>
    <el-form ref="identityVerifyFormRef" :model="formData" :rules="identityVerifyFormRules" hide-required-asterisk label-position="top" class="">
      <el-form-item :label="t('withdrawal.emil')">
        <div class="w-100%">
          <el-input
            disabled
            :value="userStore.email"
            :placeholder="t('withdrawal.emailPlaceholder')"
            type="text"
          />
        </div>
      </el-form-item>
      <el-form-item prop="verifyCode" :label="t('withdrawal.emilCode')" :error="customEmailErrorMesg">
        <div class="w-100%">
          <el-input
            v-model.trim="formData.verifyCode"
            :placeholder="t('withdrawal.verifyCodePlaceholder')"
            type="text"
            maxlength="6"
            ref="verifyCodeInputRef"
          >
            <template #suffix>
              <el-button :loading="countdownLoading" text class="sendcode-btn pr-0px" @click.prevent="sendEmailRequest" :disabled="countdown > 0">
                {{ sendBtnMesg }}
              </el-button>
            </template>
          </el-input>
        </div>
      </el-form-item>
      <el-form-item prop="tfaCode" :label="t('withdrawal.thirdPartyVerifyCode')" :error="customTfaErrorMesg">
        <div class="w-100%">
          <el-input
            v-model.trim="formData.tfaCode"
            :placeholder="t('withdrawal.thirdPartyVerifyCodePlaceholder')"
            type="text"
            maxlength="6"
            ref="thirdPartyVerifyCodeInputRef"
          >
          </el-input>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>


<script setup lang="ts">
import { t } from '@@/i18n';
import type { FormRules, FormInstance, FormItemInstance, InputInstance } from "element-plus"
import { useUserStore } from "@/pinia/stores/user"
import { sendEmailForIdentityVerifyApi, verifyEmailAndThirdPartyForIdentityVerifyApi } from '@/pages/withdrawal/apis'
import { FeeEnumType } from '@/pages/exchange/apis/type';
import { IdentityVerifyFormData } from '@/pages/withdrawal/apis/type';

const userStore = useUserStore()

const props = withDefaults(defineProps<{
  businessType: FeeEnumType
}>(), {
  
});

const identityVerifyFormRef = ref<FormInstance | null>(null)
const verifyCodeInputRef = ref<InputInstance | null>(null)
const thirdPartyVerifyCodeInputRef = ref<InputInstance | null>(null)
const countdownLoading = ref(false)
const countdown = ref(0)
const customEmailErrorMesg = ref('')
const customTfaErrorMesg = ref('')

const formData = reactive<IdentityVerifyFormData>({
  sendSeqId: '',
  verifyCode: '',
  tfaCode: '',
  businessType: props.businessType,
})

const sendBtnMesg = computed(() => {
  if (countdownLoading.value) return t('withdrawal.sendingCode');

  return countdown.value > 0 ? `${t('withdrawal.resendCode')} (${countdown.value}s)` : t('withdrawal.sendCode');
});

const identityVerifyFormRules: FormRules = {
  verifyCode: [
    { required: true, message: t('withdrawal.verifyCodePlaceholder'), trigger: ["blur"] },
    { validator: (rule: any, value: any, callback: any) => {
        if (value === "") {
          // 当没有输入内容的时候检查是否有自定义的错误信息
          if (customEmailErrorMesg.value) {
            callback(new Error(customEmailErrorMesg.value))
          }else{
            if (formData.sendSeqId === "") {
              callback(new Error(t('withdrawal.verifyCodeEmptyError')))
            }else{
              callback(new Error(t('withdrawal.verifyCodeError')))
            }
          }
        } else {
          callback()
        }
      },
      trigger: "blur"
    }
  ],
  tfaCode: [
    { required: true, message: t('withdrawal.thirdPartyVerifyCodePlaceholder'), trigger: ["blur"] },
    { validator: (rule: any, value: any, callback: any) => {
        if (value === "") {
          // 当没有输入内容的时候检查是否有自定义的错误信息
          if (customTfaErrorMesg.value) {
            callback(new Error(customTfaErrorMesg.value))
          }else{
            if (formData.sendSeqId === "") {
              callback(new Error(t('withdrawal.thirdPartyVerifyCodePlaceholder')))
            }else{
              callback(new Error(t('withdrawal.thirdPartyVerifyCodeError')))
            }
          }
        } else {
          callback()
        }
      },
      trigger: "blur"
    }
  ]
}

/** 发送验证码 */
const sendEmailRequest = () => {
  countdownLoading.value = true
  sendEmailForIdentityVerifyApi(props.businessType).then(({data}) => {
    formData.sendSeqId = data.sendSeqId
    countdown.value = 60;
    // 启动倒计时
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
        countdown.value = 0;
      }
    }, 1000)
  }).catch(error => {
    customEmailErrorMesg.value = error.message;
  }).finally(() => {
    countdownLoading.value = false
    verifyCodeInputRef.value?.focus();
  })
}

/** 
 * 验证邮箱验证码
 * tfaSeqId 三方验证器流水id
 * sendSeqId 邮箱验证码流水id
 */
const verifyEmailCodeResp  = (): Promise<{ tfaSeqId: string; sendSeqId: string }> => {
  return new Promise((resolve, reject) => {
    identityVerifyFormRef.value?.validate(async (valid: boolean) => {
      if (!valid) {
        return
      }
      customEmailErrorMesg.value = "";
      customTfaErrorMesg.value = "";
      try {
        const res = await verifyEmailAndThirdPartyForIdentityVerifyApi(formData)
        const tfaSeqId = res.data.sysSeqId
        resolve({tfaSeqId, sendSeqId: formData.sendSeqId})
      } catch (error: any) {
        if (error.code === 'C00012') {
          // 邮箱验证码错误
          customEmailErrorMesg.value = error.message;
        }else{
          customTfaErrorMesg.value = error.message;
        }
        reject(error)
      }
    })
  });
}

defineExpose({
  verifyEmailCodeResp
})
</script>

<style lang="scss" scoped>
:deep(.el-button.text-btn) {
  background: transparent;
  margin-left: 0;
  font-size: 14px;
  color: #222527;
  margin-top: 24px;
}
:deep(.el-button.sendcode-btn) {
  background: transparent;
  font-size: 14px;
  color: #FF0064;
  margin-top: 0;
  padding: 0;
}
:deep(.el-button.is-text:hover) {
  background: transparent;
}
:deep(.el-input) {
  --el-input-focus-border-color: #E5E6EB;
  border: #E5E6EB 1px solid;
  border-radius: 6px;
}
:deep(.el-input__inner) {
  height: 32px !important;
  padding: 0px 0px !important;
}
:deep(.el-input__wrapper) {
  border-radius: 6px;
  background-color: transparent;
  padding: 0px 12px !important;
  box-shadow: none;
}
:deep(.el-input.is-disabled .el-input__wrapper) {
  border-radius: 6px;
  padding: 0px 12px !important;
  box-shadow: none;
}
:deep(.el-form-item.is-error .el-form-item__content .el-input .el-input__wrapper) {
  background-color: #FFF2EE;
} 

</style>
