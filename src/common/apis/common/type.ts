export interface UploadRequestData {
  fileType: string;
  multipartFile: File;
}

export type UploadResponseData = ApiResponseData<{
  fileSeqId: string;
  fileUrl: string;
}>;

export enum BusinessEnumType {
  /** 国家地区 */
  NATIONALITY = 'NATIONALITY',
  /** 股东/董事/实际控制人 国籍或居住地枚举  */
  SH_DIRECTOR_MANAGER_NATIONALITY = 'SH_DIRECTOR_MANAGER_NATIONALITY',
  /** 行业一级 */
  INDUSTRY = 'INDUSTRY',
  /** 行业二级 */
  SUB_INDUSTRY = 'SUB_INDUSTRY',
  /** 企业类型 */
  CORPORATION_TYPE = 'CORPORATION_TYPE',
  /** 原始资金 */
  FIRST_FUNDING_SOURCE = 'FIRST_FUNDING_SOURCE',
  /** 持续资金 */
  CONTINUOUS_FUNDING_SOURCE = 'CONTINUOUS_FUNDING_SOURCE',
  /** 财富来源 */
  WEALTH_SOURCE = 'WEALTH_SOURCE',
  /** 开户目的 */
  SETTLE_PURPOSE = 'SETTLE_PURPOSE',
  /** 证件类型 */
  ID_TYPE = 'ID_TYPE',
  /** 性别 */
  GENDER = 'GENDER',
  /** 账户类型 */
  ACCOUNT_TYPE = 'ACCOUNT_TYPE',
  /** 币种 */
  CURRENCY = 'MER_CURRENCY',
  /** 转账方式 */
  TRANSFER_METHOD = 'TRANSFER_METHOD',
  /** 管理者类型 */
  MANAGER_TYPE = 'MANAGER_TYPE',
  /** 是否 */
  FLAG = 'FLAG',
  /** 文件类型 */
  FILE_TYPE = 'FILE_TYPE',
  /** 区块链网络 */
  CRYPTO_NET = 'CRYPTO_NET',
  PAYEE_ACCT_TYPE = 'PAYEE_ACCT_TYPE',
  /** 法币币种 */
  FIAT_CCY = 'FIAT_CCY',
  /** 所有法币币种 */
  ALL_FIAT_CCY = 'ALL_FIAT_CCY',
  /** 手机区号 */
  PHONE_AREA_CODE = 'PHONE_AREA_CODE',
  /** 交易类型 */
  TRADE_TYPE_MER_CONSOLE = 'TRADE_TYPE_MER_CONSOLE',
  /** 交易状态 */
  TRADE_STATUS = 'TRADE_STATUS',
  /** 交易币种 */
  CCY = 'CCY',
  /** 充值状态 */
  RECHARGE_STAT = 'RECHARGE_STAT',
  /** 手机区号（中国大陆、美国） */
  QUALIFICATION_PHONE_AREA_CODE = 'QUALIFICATION_PHONE_AREA_CODE',
  /** 国家地区（美国） */
  MAIN_BUSINESS_ADDR_NATIONALITY = 'MAIN_BUSINESS_ADDR_NATIONALITY',
  /** 交易类型 */
  TRANS_TYPE = 'TRANS_TYPE',
  /** 账户状态 */
  ACCOUNT_STAT = 'ACCOUNT_STAT',
  /** 交易目的 */
  TRANSACTION_PURPOSE = 'TRANSACTION_PURPOSE',
  /** 账户扣款类型 */
  ACCOUNT_DEDUCTION_TYPE = 'ACCOUNT_DEDUCTION_TYPE',
}

export type BusinessEnumRequestData = ApiRequestData<{
  /** 业务类型 */
  businessTypes: string[];
}>;

export type BusinessEnumResponseData = ApiResponseData<{
  /** 业务类型 */
  businessEnumList: Array<{
    businessType: string;
    enumCode: string;
    parentEnumCode: string;
    enumDescCn: string;
    enumDescHk: string;
    enumDescEn: string;
    extendField?: string;
    children: Array<{
      businessType: string;
      enumCode: string;
      parentEnumCode: string;
      enumDescCn: string;
      enumDescHk: string;
      enumDescEn: string;
      extendField?: string;
    }>;
  }>;
}>;

export type ExchangeRateResponseData = ApiResponseData<{
  /** 查询汇率信息 */
  exchangeRateInfoVOs: Array<{
    currency: string;
    exchangeRate: string;
    fluctuation: string;
  }>;
}>;

export type WalletAccftResponseData = ApiResponseData<{
  balanceAvl: string;
  balanceFreeze: string;
  balanceWay: string;
  balanceTotal: string;
}>;

export type FeeQuotaResponseData = ApiResponseData<{
  /** 商户编号 */
  merCustId: string;
  /** 推荐充值额度min */
  recmRechargeQuota: number;
  /** 推荐数币取现额度min */
  recmCryptoWithdrawQuota: number;
  /** 推荐法币取现额度min */
  recmFiatWithdrawQuota: number;
  /** 日数兑法额度 */
  dailyCryptoToFiat: number;
  /** 日法兑数额度 */
  dailyFiatToCrypto: number;
  /** 日数币取现额度 */
  dailyCryptoWithdraw: number;
  /** 日法币取现额度 */
  dailyFiatWithdraw: number;
  /** 月度数兑法额度 */
  monthlyCryptoToFiat: number;
  /** 月度法兑数额度 */
  monthlyFiatToCrypto: number;
  /** 月度数币提现额度 */
  monthlyCryptoWithdraw: number;
  /** 月度法币取现额度 */
  monthlyFiatWithdraw: number;
  /** 数币提现当日已使用额度 */
  cryptoWithdrawDailyUsed: number;
  /** 数币提现当月已使用额度 */
  cryptoWithdrawMonthlyUsed: number;
  /** 数币提现过去30天已使用额度 */
  cryptoWithdrawLast30DaysUsed: number;
  /** 法币提现当日已使用额度 */
  fiatWithdrawDailyUsed: number;
  /** 法币提现当月已使用额度 */
  fiatWithdrawMonthlyUsed: number;
  /** 法币提现过去30天已使用额度 */
  fiatWithdrawLast30DaysUsed: number;
  /** 数兑法当日已使用额度 */
  cryptoExchangeFiatDailyUsed: number;
  /** 数兑法当月已使用额度 */
  cryptoExchangeFiatMonthlyUsed: number;
  /** 数兑法过去30天已使用额度 */
  cryptoExchangeFiatLast30DaysUsed: number;
  /** 法兑数当日已使用额度 */
  fiatExchangeCryptoDailyUsed: number;
  /** 法兑数当月已使用额度 */
  fiatExchangeCryptoMonthlyUsed: number;
  /** 法兑数过去30天已使用额度 */
  fiatExchangeCryptoLast30DaysUsed: number;
}>;
