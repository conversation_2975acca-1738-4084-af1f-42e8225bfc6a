import type * as common from './type';
import { request } from '@/http/axios';

/** 上传文件 */
export function uploadFile(data: common.UploadRequestData, signal?: AbortSignal) {
  return request<common.UploadResponseData>(
    {
      url: 'file/upload',
      method: 'post',
      data,
      signal,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
    { showError: false }
  );
}

/** 业务枚举接口 */
export function businessEnumApi(data: common.BusinessEnumRequestData) {
  return request<common.BusinessEnumResponseData>(
    {
      url: 'business/enum/query',
      method: 'post',
      data,
    },
    { showLoading: false, showError: false }
  );
}

/** 查询汇率信息 */
export function exchangeRateApi() {
  return request<common.ExchangeRateResponseData>(
    {
      url: 'exchangeRate/query',
      method: 'post',
      data: {},
    },
    { showError: false }
  );
}

/** 查询账户余额信息 */
export function walletAcctApi() {
  return request<common.WalletAccftResponseData>(
    {
      url: 'walletAcct/query',
      method: 'post',
      data: {},
    },
    { showError: false }
  );
}

/** 查询限额信息 */
export function feeQuotaApi() {
  return request<common.FeeQuotaResponseData>(
    {
      url: 'fee/quota',
      method: 'post',
      data: {},
    },
    { showError: false }
  );
}
