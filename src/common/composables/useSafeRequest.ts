import axios from "axios"

/** 请求验证码响应数据 */
export type GetTimestampResponseData = ApiResponseData<{
  timestamp: string
}>
/** 获取时间戳 */
const getServerTimestampApi = () => {
  return axios.create({
    baseURL: import.meta.env.VITE_BASE_URL,
    timeout: 30000,
    withCredentials: false
  }).get("timestamp");
}


// 时间戳，如果获取过就直接返回，没有值就请求接口获取
let timestamp = 0;

// 获取当前服务器时间戳
export const getTimestamp = async () => {
  try {
    if (!timestamp) {
      const tsRes = await getServerTimestampApi();
      if (tsRes.data && tsRes.data.data.timestamp) {
        timestamp = tsRes.data.data.timestamp;
      }
    }
  } catch (error) {
    console.error("获取服务器时间戳失败", error);
  }

  return timestamp;
}

/** 关于请求通用的的 Composable */
export function useAuth() {
  return { getTimestamp }
}
