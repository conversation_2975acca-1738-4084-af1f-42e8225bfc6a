# 数学计算工具函数

## 概述

这个工具函数库专门用于解决JavaScript中浮点数运算精度丢失的问题，特别适用于金融计算、金额处理等对精度要求较高的场景。

## 问题背景

JavaScript使用IEEE 754标准表示浮点数，这会导致一些看似简单的计算出现精度问题：

```javascript
// 经典的精度问题
0.1 + 0.2 = 0.30000000000000004  // 期望: 0.3
0.3 - 0.1 = 0.19999999999999998  // 期望: 0.2
```

## 解决方案

通过将浮点数转换为整数进行计算，然后再转换回浮点数，避免精度丢失：

```javascript
// 使用工具函数
add(0.1, 0.2) = 0.3        // 精确结果
subtract(0.3, 0.1) = 0.2   // 精确结果
```

## 核心函数

### 基础运算

#### `add(a, b, precision = 2)`
精确加法运算
```javascript
import { add } from '@@/utils/math'

add(0.1, 0.2)        // 0.3
add(100.01, 0.01)    // 100.02
add(123.456, 789.123, 3)  // 912.579
```

#### `subtract(a, b, precision = 2)`
精确减法运算
```javascript
import { subtract } from '@@/utils/math'

subtract(0.3, 0.1)        // 0.2
subtract(100.01, 0.01)    // 100.00
subtract(1000, 0.01, 2)   // 999.99
```

#### `multiply(a, b, precision = 2)`
精确乘法运算
```javascript
import { multiply } from '@@/utils/math'

multiply(0.1, 0.2)        // 0.02
multiply(100, 0.01)       // 1.00
multiply(123.456, 2, 3)   // 246.912
```

#### `divide(a, b, precision = 2)`
精确除法运算
```javascript
import { divide } from '@@/utils/math'

divide(0.3, 0.1)          // 3.00
divide(100, 3, 4)         // 33.3333
divide(10, 0)             // 抛出错误: 除数不能为零
```

### 取整和格式化

#### `floor(num, precision = 2)`
向下取整到指定精度
```javascript
import { floor } from '@@/utils/math'

floor(99.999, 2)    // 99.99
floor(100.001, 2)   // 100.00
```

#### `ceil(num, precision = 2)`
向上取整到指定精度
```javascript
import { ceil } from '@@/utils/math'

ceil(99.001, 2)     // 99.01
ceil(99.999, 2)     // 100.00
```

#### `round(num, precision = 2)`
四舍五入到指定精度
```javascript
import { round } from '@@/utils/math'

round(99.995, 2)    // 100.00
round(99.994, 2)    // 99.99
```

#### `truncate(num, precision = 2)`
截断到指定精度（不四舍五入）
```javascript
import { truncate } from '@@/utils/math'

truncate(99.999, 2)  // 99.99
truncate(100.001, 2) // 100.00
```

#### `formatNumber(num, precision = 2, locale = 'en-US')`
格式化数字为字符串，支持千分位分隔符
```javascript
import { formatNumber } from '@@/utils/math'

formatNumber(1234567.89)           // "1,234,567.89"
formatNumber(123.456, 6)           // "123.456000"
formatNumber(1234567.89, 2, 'zh-CN') // "1,234,567.89"
```

### 工具函数

#### `safeNumber(value, defaultValue = 0)`
安全的数字转换，处理字符串和数字类型
```javascript
import { safeNumber } from '@@/utils/math'

safeNumber('123.45')      // 123.45
safeNumber('1,234.56')    // 1234.56
safeNumber('abc', 0)      // 0
safeNumber(null, 100)     // 100
safeNumber(undefined, 50) // 50
```

#### `isEqual(a, b, precision = 2)`
比较两个数字是否相等（考虑精度）
```javascript
import { isEqual } from '@@/utils/math'

isEqual(0.1 + 0.2, 0.3)   // true
isEqual(0.3 - 0.1, 0.2)   // true
isEqual(100.001, 100.002, 2) // true (在2位小数精度下相等)
```

#### `isZero(num, precision = 2)`
检查数字是否为零（考虑精度）
```javascript
import { isZero } from '@@/utils/math'

isZero(0.000001, 2)       // true
isZero(0.01, 2)           // false
```

#### `getDecimalPlaces(num)`
获取数字的小数位数
```javascript
import { getDecimalPlaces } from '@@/utils/math'

getDecimalPlaces(123.456)  // 3
getDecimalPlaces(123)      // 0
getDecimalPlaces(123.0)    // 0
```

### 业务计算

#### `percentage(value, total, precision = 2)`
计算百分比
```javascript
import { percentage } from '@@/utils/math'

percentage(15, 100)        // 15.00
percentage(150, 1000)      // 15.00
percentage(0, 100)         // 0.00
```

#### `growthRate(current, previous, precision = 2)`
计算增长率
```javascript
import { growthRate } from '@@/utils/math'

growthRate(150, 100)       // 50.00
growthRate(50, 100)        // -50.00
growthRate(100, 0)         // 100.00 (当previous为0时)
```

## 实际应用场景

### 1. 提现计算

```javascript
import { subtract, multiply, formatNumber } from '@@/utils/math'

// 提现金额计算
const withdrawalAmount = 1000.00
const feeRate = 0.01 // 1%
const feeAmount = multiply(withdrawalAmount, feeRate)
const actualAmount = subtract(withdrawalAmount, feeAmount)

console.log('提现金额:', formatNumber(withdrawalAmount))  // 1,000.00
console.log('手续费:', formatNumber(feeAmount))           // 10.00
console.log('实际到账:', formatNumber(actualAmount))      // 990.00
```

### 2. 兑换计算

```javascript
import { multiply, formatNumber } from '@@/utils/math'

// 货币兑换
const exchangeAmount = 100.00
const exchangeRate = 7.123456
const convertedAmount = multiply(exchangeAmount, exchangeRate, 6)

console.log('兑换金额:', formatNumber(exchangeAmount))     // 100.00
console.log('转换后金额:', formatNumber(convertedAmount, 6)) // 712.345600
```

### 3. 批量计算

```javascript
import { add, safeNumber } from '@@/utils/math'

// 批量金额求和
const amounts = ['100.01', '200.02', '300.03', '400.04', '500.05']
const total = amounts.reduce((sum, amount) => add(sum, safeNumber(amount)), 0)

console.log('精确求和:', total) // 1500.15
```

### 4. 表单验证

```javascript
import { isEqual, isZero } from '@@/utils/math'

// 验证输入金额
const validateAmount = (inputAmount, minAmount = 0) => {
  const amount = safeNumber(inputAmount)
  
  if (isZero(amount)) {
    return '金额不能为零'
  }
  
  if (amount < minAmount) {
    return `金额不能小于${minAmount}`
  }
  
  return null
}
```

## 在Vue组件中使用

### 替换原有的计算逻辑

```javascript
// 原来的代码
const amount = parseFloat(value)
const result = amount - feeAmount

// 使用工具函数
import { subtract, safeNumber } from '@@/utils/math'

const amount = safeNumber(value)
const result = subtract(amount, feeAmount)
```

### 格式化显示

```javascript
// 原来的代码
const formatted = amount.toLocaleString('en-US', {
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
})

// 使用工具函数
import { formatNumber } from '@@/utils/math'

const formatted = formatNumber(amount, 2)
```

## 注意事项

1. **精度参数**: 所有函数都支持自定义精度，默认是2位小数
2. **错误处理**: 除法运算会检查除数是否为零
3. **类型安全**: `safeNumber`函数可以安全处理各种输入类型
4. **性能考虑**: 对于大量计算，建议批量处理以提高性能

## 测试

运行示例文件查看效果：

```bash
# 在浏览器控制台或Node.js环境中运行
node src/common/utils/math-examples.ts
```

## 迁移指南

1. 识别项目中使用`parseFloat`、`Number()`、`Math.floor`等原生方法的地方
2. 替换为对应的工具函数
3. 更新格式化逻辑使用`formatNumber`
4. 测试确保计算结果正确

## 常见问题

**Q: 为什么需要指定精度？**
A: 不同业务场景对精度要求不同，USD通常2位小数，USDT通常6位小数。

**Q: 如何处理大数字？**
A: 工具函数基于整数运算，可以处理较大的数字，但建议在合理范围内使用。

**Q: 性能如何？**
A: 相比原生运算会有轻微性能损失，但在金融计算中精度比性能更重要。 