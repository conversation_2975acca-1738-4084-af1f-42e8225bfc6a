export const createSignSignature = (data: any) => { 
    const stack = [{ key: '', value: data }];
    const flatMap: Record<string, string> = {};
  
    while (stack.length > 0) {
      const { key: prefix, value } = stack.pop() as { key: string; value: any };
  
      if (value === null || value === undefined) continue;
  
      if (Array.isArray(value)) {
        value.forEach((item, index) => {
          const newKey = `${prefix}[${index}]`;
          stack.push({ key: newKey, value: item });
        });
      } else if (typeof value === 'object') {
        Object.keys(value).forEach((k) => {
          const newKey = prefix ? `${prefix}.${k}` : k;
          stack.push({ key: newKey, value: value[k] });
        });
      } else {
        flatMap[prefix] = String(value);
      }
    }
    // 字典序排序后拼接
    const paramString = Object.keys(flatMap)
      .sort()
      .map(k => `${k}=${flatMap[k]}`)
      .join('&');

    return paramString;
}

export const createOrderSignature = (data: any) => { 
    const stack = [{ key: '', value: data }];
    const flatMap: Record<string, string> = {};
  
    while (stack.length > 0) {
      const { key: prefix, value } = stack.pop() as { key: string; value: any };
  
      if (value === null || value === undefined) continue;
  
      if (Array.isArray(value)) {
        value.forEach((item, index) => {
          const newKey = `${prefix}[${index}]`;
          stack.push({ key: newKey, value: item });
        });
      } else if (typeof value === 'object') {
        Object.keys(value).forEach((k) => {
          const newKey = prefix ? `${prefix}.${k}` : k;
          let propValue = value[k];
          if (k === 'fileInfos' && Array.isArray(propValue)) {
            propValue = propValue.map(file => (file ? { fileSeqId: file.fileSeqId } : null)).filter(Boolean);
          }
          stack.push({ key: newKey, value: propValue });
        });
      } else {
        flatMap[prefix] = String(value);
      }
    }
    // 字典序排序后拼接
    const paramString = Object.keys(flatMap)
      .sort()
      .map(k => `${k}=${flatMap[k]}`)
      .join('&');

    return paramString;
}

