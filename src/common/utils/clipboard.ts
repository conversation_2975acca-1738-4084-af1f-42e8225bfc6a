import { ElMessage } from 'element-plus'
import { t } from '@/common/i18n'
export const copyText = async (text: string) => {
    let success = false;
    if (navigator.clipboard && window.isSecureContext) {
        try {
            await navigator.clipboard.writeText(text);
            success = true;
        } catch (e) {
            console.error('copy error', e);
            success = false;
        }
    } else {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed'; // Prevent scrolling to bottom of page in MS Edge.
        textarea.style.top = '-9999px';
        textarea.style.left = '-9999px';
        document.body.appendChild(textarea);
        textarea.focus();
        textarea.select();
        try {
            // Using document.execCommand is deprecated but still works as a fallback
            success = document.execCommand('copy');
        } catch (e) {
            console.error('copy error', e);
            success = false;
        } finally {
            document.body.removeChild(textarea);
        }
    }
    if (success) {
        ElMessage.success(t('common.copySuccess'));
    } else {
        ElMessage.error(t('common.copyFailed'));
    }

    return success
};