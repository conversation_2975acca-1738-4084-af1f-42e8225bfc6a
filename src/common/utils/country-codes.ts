/**
 * 国家代码与区号映射表模块
 * 提供各国手机号格式化规则
 */

/**
 * 国家代码规则类型定义
 */
export interface CountryCodeRule {
  /** 国家区号 */
  code: string;
  /** 手机号验证正则表达式 */
  pattern: RegExp;
  /** 手机号格式化函数 */
  format: (phone: string) => string;
}

/**
 * 国家代码映射表类型
 */
export type CountryCodesMap = Record<string, CountryCodeRule>;

/**
 * 国家代码规则配置
 */
export const COUNTRY_CODES: CountryCodesMap = {
  CN: {
    code: '+86',
    pattern: /^1[3-9]\d{9}$/,
    format: (phone: string): string => {
      if (phone.length <= 3) return phone;
      if (phone.length <= 7) return `${phone.slice(0, 3)}-${phone.slice(3)}`;
      return `${phone.slice(0, 3)}-${phone.slice(3, 7)}-${phone.slice(7)}`;
    },
  },
  US: {
    code: '+1',
    pattern: /^[2-9]\d{2}[2-9]\d{2}\d{4}$/,
    format: (phone: string): string => {
      if (phone.length <= 3) return phone;
      if (phone.length <= 6) return `${phone.slice(0, 3)}-${phone.slice(3)}`;
      return `${phone.slice(0, 3)}-${phone.slice(3, 6)}-${phone.slice(6)}`;
    },
  },
  UK: {
    code: '+44',
    pattern: /^[1-9]\d{8,9}$/,
    format: (phone: string): string => {
      if (phone.length === 10) {
        if (phone.length <= 4) return phone;
        if (phone.length <= 7) return `${phone.slice(0, 4)}-${phone.slice(4)}`;
        return `${phone.slice(0, 4)}-${phone.slice(4, 7)}-${phone.slice(7)}`;
      } else {
        if (phone.length <= 3) return phone;
        if (phone.length <= 6) return `${phone.slice(0, 3)}-${phone.slice(3)}`;
        return `${phone.slice(0, 3)}-${phone.slice(3, 6)}-${phone.slice(6)}`;
      }
    },
  },
  JP: {
    code: '+81',
    pattern: /^[789]0\d{8}$/,
    format: (phone: string): string => {
      if (phone.length <= 2) return phone;
      if (phone.length <= 6) return `${phone.slice(0, 2)}-${phone.slice(2)}`;
      return `${phone.slice(0, 2)}-${phone.slice(2, 6)}-${phone.slice(6)}`;
    },
  },
  KR: {
    code: '+82',
    pattern: /^1[0-9]\d{7,8}$/,
    format: (phone: string): string => {
      if (phone.length === 9) {
        if (phone.length <= 2) return phone;
        if (phone.length <= 5) return `${phone.slice(0, 2)}-${phone.slice(2)}`;
        return `${phone.slice(0, 2)}-${phone.slice(2, 5)}-${phone.slice(5)}`;
      } else {
        if (phone.length <= 3) return phone;
        if (phone.length <= 6) return `${phone.slice(0, 3)}-${phone.slice(3)}`;
        return `${phone.slice(0, 3)}-${phone.slice(3, 6)}-${phone.slice(6)}`;
      }
    },
  },
  HK: {
    code: '+852',
    pattern: /^[5-9]\d{7}$/,
    format: (phone: string): string => {
      if (phone.length <= 4) return phone;
      return `${phone.slice(0, 4)}-${phone.slice(4)}`;
    },
  },
  MO: {
    code: '+853',
    pattern: /^6\d{7}$/,
    format: (phone: string): string => {
      if (phone.length <= 4) return phone;
      return `${phone.slice(0, 4)}-${phone.slice(4)}`;
    },
  },
  TW: {
    code: '+886',
    pattern: /^9\d{8}$/,
    format: (phone: string): string => {
      if (phone.length <= 3) return phone;
      if (phone.length <= 6) return `${phone.slice(0, 3)}-${phone.slice(3)}`;
      return `${phone.slice(0, 3)}-${phone.slice(3, 6)}-${phone.slice(6)}`;
    },
  },
  SG: {
    code: '+65',
    pattern: /^[89]\d{7}$/,
    format: (phone: string): string => {
      if (phone.length <= 4) return phone;
      return `${phone.slice(0, 4)}-${phone.slice(4)}`;
    },
  },
  DE: {
    code: '+49',
    pattern: /^1[5-7]\d{8,9}$/,
    format: (phone: string): string => {
      if (phone.length === 10) {
        if (phone.length <= 3) return phone;
        if (phone.length <= 6) return `${phone.slice(0, 3)}-${phone.slice(3)}`;
        return `${phone.slice(0, 3)}-${phone.slice(3, 6)}-${phone.slice(6)}`;
      } else {
        if (phone.length <= 3) return phone;
        if (phone.length <= 7) return `${phone.slice(0, 3)}-${phone.slice(3)}`;
        return `${phone.slice(0, 3)}-${phone.slice(3, 7)}-${phone.slice(7)}`;
      }
    },
  },
  FR: {
    code: '+33',
    pattern: /^[67]\d{8}$/,
    format: (phone: string): string => {
      if (phone.length <= 2) return phone;
      if (phone.length <= 4) return `${phone.slice(0, 2)}-${phone.slice(2)}`;
      if (phone.length <= 6) return `${phone.slice(0, 2)}-${phone.slice(2, 4)}-${phone.slice(4)}`;
      if (phone.length <= 8)
        return `${phone.slice(0, 2)}-${phone.slice(2, 4)}-${phone.slice(4, 6)}-${phone.slice(6)}`;
      return `${phone.slice(0, 2)}-${phone.slice(2, 4)}-${phone.slice(4, 6)}-${phone.slice(6, 8)}-${phone.slice(8)}`;
    },
  },
  IN: {
    code: '+91',
    pattern: /^[6-9]\d{9}$/,
    format: (phone: string): string => {
      if (phone.length <= 5) return phone;
      return `${phone.slice(0, 5)}-${phone.slice(5)}`;
    },
  },
  CA: {
    code: '+1',
    pattern: /^[2-9]\d{2}[2-9]\d{2}\d{4}$/,
    format: (phone: string): string => {
      if (phone.length <= 3) return phone;
      if (phone.length <= 6) return `${phone.slice(0, 3)}-${phone.slice(3)}`;
      return `${phone.slice(0, 3)}-${phone.slice(3, 6)}-${phone.slice(6)}`;
    },
  },
  BR: {
    code: '+55',
    pattern: /^[1-9]\d{10}$/,
    format: (phone: string): string => {
      if (phone.length <= 2) return phone;
      if (phone.length <= 7) return `${phone.slice(0, 2)}-${phone.slice(2)}`;
      return `${phone.slice(0, 2)}-${phone.slice(2, 7)}-${phone.slice(7)}`;
    },
  },
  AU: {
    code: '+61',
    pattern: /^4\d{8}$/,
    format: (phone: string): string => {
      if (phone.length <= 3) return phone;
      if (phone.length <= 6) return `${phone.slice(0, 3)}-${phone.slice(3)}`;
      return `${phone.slice(0, 3)}-${phone.slice(3, 6)}-${phone.slice(6)}`;
    },
  },
  RU: {
    code: '+7',
    pattern: /^9\d{9}$/,
    format: (phone: string): string => {
      if (phone.length <= 3) return phone;
      if (phone.length <= 6) return `${phone.slice(0, 3)}-${phone.slice(3)}`;
      return `${phone.slice(0, 3)}-${phone.slice(3, 6)}-${phone.slice(6)}`;
    },
  },
};

/**
 * 获取指定国家的区号
 * @param countryCode - 国家代码
 * @returns 区号
 */
export const getCountryCode = (countryCode: string): string => {
  return COUNTRY_CODES[countryCode]?.code || '';
};

/**
 * 验证手机号是否符合指定国家格式
 * @param countryCode - 国家代码
 * @param phone - 手机号
 * @returns 是否有效
 */
export const validatePhone = (countryCode: string, phone: string): boolean => {
  const rule = COUNTRY_CODES[countryCode];
  return rule ? rule.pattern.test(phone) : false;
};

/**
 * 格式化手机号
 * @param countryCode - 国家代码
 * @param phone - 手机号
 * @returns 格式化后的手机号
 */
export const formatPhone = (countryCode: string, phone: string): string => {
  const rule = COUNTRY_CODES[countryCode];
  return rule ? rule.format(phone) : phone;
};

/**
 * 获取所有支持的国家代码列表
 * @returns 国家代码数组
 */
export const getSupportedCountries = (): string[] => {
  return Object.keys(COUNTRY_CODES);
};

/**
 * 获取国家规则的完整信息
 * @param countryCode - 国家代码
 * @param defaultCountryCode - 默认国家代码
 * @returns 国家规则对象
 */
export const getCountryRule = (
  countryCode: string,
  defaultCountryCode: string
): CountryCodeRule => {
  return COUNTRY_CODES[countryCode] || COUNTRY_CODES[defaultCountryCode];
};
