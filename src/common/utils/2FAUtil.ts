import { DialogService } from '@@/components/Dialog/DialogService';
import { queryCustomerApi, queryCustomerApiNoLoading } from '@/pages/setting/apis';
import { t } from '@@/i18n';
import BindTFADrawer from '../components/BindTFA';

/**
 * 2FA验证配置选项
 */
export interface TwoFactorAuthOptions {
  /** 对话框标题 */
  title?: string;
  /** 对话框消息内容 */
  message?: string;
  /** 确认按钮文本 */
  confirmButtonText?: string;
  /** 取消按钮文本 */
  cancelButtonText?: string;
  /** 点击确认按钮事件 */
  onConfirm?: () => void;
}

/**
 * 2FA验证结果
 */
export interface TwoFactorAuthResult {
  /** 是否已绑定2FA */
  isBound: boolean;
  /** 2FA状态 */
  status: string;
  /** 系统序列ID */
  sysSeqId: string;
}

/**
 * 2FA验证工具类
 * 提供统一的双因素验证检查和处理功能
 */
export class TwoFactorAuthUtil {
  /**
   * 检查用户2FA绑定状态
   * @returns Promise<TwoFactorAuthResult> 2FA验证结果
   */
  static async checkTwoFactorAuthStatus(): Promise<TwoFactorAuthResult> {
    try {
      const res = await queryCustomerApiNoLoading({});
      const twoFactorAuthStatus = res.data.twoFactorAuthStatus || '0';
      const twoFactorAuthSysSeqId = res.data.twoFactorAuthSysSeqId || '';

      return {
        isBound: twoFactorAuthStatus === '1',
        status: twoFactorAuthStatus,
        sysSeqId: twoFactorAuthSysSeqId,
      };
    } catch (error) {
      console.error('获取2FA状态失败:', error);
      throw new Error(t('common.twoFactorAuth.checkStatusFailed'));
    }
  }

  /**
   * 执行2FA验证检查，如果未绑定则显示警告对话框
   * @param callback 验证通过后的回调函数
   * @param options 可选的对话框配置
   * @returns Promise<void>
   */
  static async handleAuthVerification(
    callback?: () => void,
    options?: TwoFactorAuthOptions
  ): Promise<void> {
    try {
      const authResult = await this.checkTwoFactorAuthStatus();

      if (!authResult.isBound) {
        // 默认配置
        const defaultOptions: TwoFactorAuthOptions = {
          title: t('common.twoFactorAuth.title'),
          message: t('common.twoFactorAuth.message'),
          confirmButtonText: t('common.twoFactorAuth.confirmButtonText'),
          cancelButtonText: t('common.twoFactorAuth.cancelButtonText'),
        };

        // 合并用户配置
        const finalOptions = { ...defaultOptions, ...options };

        // 显示警告对话框
        DialogService.warning(finalOptions.message!, finalOptions.title!, {
          confirmButtonText: finalOptions.confirmButtonText!,
          cancelButtonText: finalOptions.cancelButtonText!,
          onConfirm: finalOptions.onConfirm
            ? finalOptions.onConfirm
            : () => {
                BindTFADrawer.show({
                  onSuccess: () => {
                    ElMessage.success(t('setting.setValidator.bindSuccess'));
                    // 2FA已绑定，执行回调函数
                    if (callback && typeof callback === 'function') {
                      callback();
                    }
                  },
                });
              },
        });
        return;
      }

      // 2FA已绑定，执行回调函数
      if (callback && typeof callback === 'function') {
        callback();
      }
    } catch (error) {
      console.error(t('common.twoFactorAuth.verificationFailed'), error);
      // 可以选择显示错误提示或者直接执行回调
      if (callback && typeof callback === 'function') {
        callback();
      }
    }
  }

  /**
   * 简化版本的2FA验证，使用默认配置
   * @param callback 验证通过后的回调函数
   * @returns Promise<void>
   */
  static async verifyTwoFactorAuth(callback?: () => void): Promise<void> {
    return this.handleAuthVerification(callback);
  }

  /**
   * 检查2FA状态但不显示对话框
   * @returns Promise<boolean> 是否已绑定2FA
   */
  static async isTwoFactorAuthBound(): Promise<boolean> {
    try {
      const result = await this.checkTwoFactorAuthStatus();
      return result.isBound;
    } catch (error) {
      console.error(t('common.twoFactorAuth.checkStatusFailed'), error);
      return false;
    }
  }
}

/**
 * 导出便捷函数，用于快速调用2FA验证
 */
export const verifyTwoFactorAuth = TwoFactorAuthUtil.verifyTwoFactorAuth.bind(TwoFactorAuthUtil);
export const checkTwoFactorAuthStatus =
  TwoFactorAuthUtil.checkTwoFactorAuthStatus.bind(TwoFactorAuthUtil);
export const isTwoFactorAuthBound = TwoFactorAuthUtil.isTwoFactorAuthBound.bind(TwoFactorAuthUtil);
export const handleAuthVerification =
  TwoFactorAuthUtil.handleAuthVerification.bind(TwoFactorAuthUtil);
