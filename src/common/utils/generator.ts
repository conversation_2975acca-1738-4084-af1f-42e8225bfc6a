export function generateSysSeqId(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, "0")
  const day = now.getDate().toString().padStart(2, "0")
  const hours = now.getHours().toString().padStart(2, "0")
  const minutes = now.getMinutes().toString().padStart(2, "0")
  const seconds = now.getSeconds().toString().padStart(2, "0")
  const milliseconds = now.getMilliseconds().toString().padStart(3, "0")

  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`

  const randomPart = Math.random().toString().substring(2, 8)

  return `${timestamp}${randomPart}`
} 