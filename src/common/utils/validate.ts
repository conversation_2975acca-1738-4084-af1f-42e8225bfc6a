import { isEmpty as _isEmpty, isNil } from 'lodash-es'

/** 判断是否为数组 */
export function isArray<T>(arg: T) {
  return Array.isArray ? Array.isArray(arg) : Object.prototype.toString.call(arg) === "[object Array]"
}

/** 判断是否为字符串 */
export function isString(str: unknown) {
  return typeof str === "string" || str instanceof String
}

/** 判断是否为外链 */
export function isExternal(path: string) {
  const reg = /^(https?:|mailto:|tel:)/
  return reg.test(path)
}

export function isReallyEmpty(value: any): boolean {
  if (isNil(value)) return true // null or undefined
  if (typeof value === 'string') return value.trim() === ''
  // if (typeof value === 'number') return isNaN(value) || value === 0
  if (typeof value === 'boolean') return false // false 不算“空”
  if (Array.isArray(value)) return value.length === 0
  if (value instanceof Date) return isNaN(value.getTime())
  if (typeof value === 'object') return _isEmpty(value) // 对象、Map、Set
  return false
}