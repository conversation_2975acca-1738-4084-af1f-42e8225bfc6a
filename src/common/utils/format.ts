// 导入国家代码规则模块
import { getCountryRule } from './country-codes';

/**
 * 手机号格式化输入函数
 * 功能特性：
 * 1. 当用户输入"-"或空格时，显示"-"
 * 2. 禁止连续输入"-"或空格，若检测到连续输入则自动忽略后续的"-"或空格
 * 3. 确保输入格式规范，不允许出现多个连续的"-"或空格组合
 * @param phoneAreaCode 手机区号
 * @param phoneNumber 手机号码
 * @returns 格式化后的手机号字符串
 */
export function phoneFormatInput(phoneAreaCode: string, phoneNumber: string): string {
  if (!phoneNumber) return '';
  let countryInfo = getCountryRule(phoneAreaCode, 'CN'); //保底CN
  // 清理手机号，只保留数字
  const cleanPhone = phoneNumber.replace(/\D/g, '');
  // 格式化手机号
  const formattedPhone = countryInfo.format(cleanPhone);
  //如果当前手机号位数不足，则无需保留后面的分割线
  // 返回最终格式化结果
  return formattedPhone;
}

// 解析实际值（移除所有空格）
export function phoneParseInput(value: string) {
  return value.replace(/[^\d]/g, '');
}

export function moneyFormatInput(value: string) {
  if (!value) return '';

  // 移除所有非数字字符（保留小数点和负号）
  const numStr = value.toString().replace(/[^\d.-]/g, '');

  // 分割整数和小数部分
  const parts = numStr.split('.');
  let integerPart = parts[0];
  const decimalPart = parts.length > 1 ? `.${parts[1]}` : '';

  // 处理负号
  const isNegative = integerPart.startsWith('-');
  if (isNegative) {
    integerPart = integerPart.substring(1);
  }

  // 添加千分位逗号
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // 重新组合
  return (isNegative ? '-' : '') + formattedInteger + decimalPart;
}

// 解析实际值（移除所有空格）
export function moneyParseInput(value: string) {
  return value.replace(/,/g, '');
}
