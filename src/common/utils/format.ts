export function phoneFormatInput(value: string) {
  if (!value) return '';

  const cleanValue = value.replace(/\s+/g, '');
  const length = cleanValue.length;
  let result = [];

  if (length % 2 === 1) {
    // 单数长度
    if (length > 3) {
      result.push(cleanValue.substr(0, 3));
      let remaining = cleanValue.substr(3);
      // 每4位分割
      for (let i = 0; i < remaining.length; i += 4) {
        result.push(remaining.substr(i, 4));
      }
    } else {
      return cleanValue;
    }
  } else {
    // 双数长度
    // 每4位分割
    for (let i = 0; i < cleanValue.length; i += 4) {
      result.push(cleanValue.substr(i, 4));
    }
  }

  return result.join(' ');
}

// 解析实际值（移除所有空格）
export function phoneParseInput(value: string) {
  return value.replace(/\s+/g, '');
}

export function moneyFormatInput(value: string) {
  if (!value) return '';

  // 移除所有非数字字符（保留小数点和负号）
  const numStr = value.toString().replace(/[^\d.-]/g, '');

  // 分割整数和小数部分
  const parts = numStr.split('.');
  let integerPart = parts[0];
  const decimalPart = parts.length > 1 ? `.${parts[1]}` : '';

  // 处理负号
  const isNegative = integerPart.startsWith('-');
  if (isNegative) {
    integerPart = integerPart.substring(1);
  }

  // 添加千分位逗号
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // 重新组合
  return (isNegative ? '-' : '') + formattedInteger + decimalPart;
}

// 解析实际值（移除所有空格）
export function moneyParseInput(value: string) {
  return value.replace(/,/g, '');
}
