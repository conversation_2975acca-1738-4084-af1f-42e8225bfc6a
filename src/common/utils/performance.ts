/**
 * 性能优化工具类
 */

// 图片懒加载
export class ImageLazyLoader {
  private observer: IntersectionObserver | null = null
  private images: Set<HTMLImageElement> = new Set()

  constructor() {
    this.initObserver()
  }

  private initObserver() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement
              this.loadImage(img)
              this.observer?.unobserve(img)
              this.images.delete(img)
            }
          })
        },
        {
          rootMargin: '50px 0px',
          threshold: 0.01
        }
      )
    }
  }

  private loadImage(img: HTMLImageElement) {
    const src = img.dataset.src
    if (src) {
      img.src = src
      img.removeAttribute('data-src')
    }
  }

  observe(img: HTMLImageElement) {
    if (this.observer) {
      this.images.add(img)
      this.observer.observe(img)
    } else {
      // 兜底：直接加载
      this.loadImage(img)
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
      this.images.clear()
    }
  }
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: number | null = null

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }

    const callNow = immediate && !timeout

    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait) as unknown as number

    if (callNow) func(...args)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean

  return function executedFunction(this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

// 资源预加载
export class ResourcePreloader {
  private cache = new Map<string, Promise<any>>()

  // 预加载图片
  preloadImage(src: string): Promise<HTMLImageElement> {
    if (this.cache.has(src)) {
      return this.cache.get(src)!
    }

    const promise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve(img)
      img.onerror = reject
      img.src = src
    })

    this.cache.set(src, promise)
    return promise
  }

  // 预加载多个图片
  preloadImages(srcs: string[]): Promise<HTMLImageElement[]> {
    return Promise.all(srcs.map(src => this.preloadImage(src)))
  }

  // 预加载脚本
  preloadScript(src: string): Promise<void> {
    if (this.cache.has(src)) {
      return this.cache.get(src)!
    }

    const promise = new Promise<void>((resolve, reject) => {
      const script = document.createElement('script')
      script.onload = () => resolve()
      script.onerror = reject
      script.src = src
      document.head.appendChild(script)
    })

    this.cache.set(src, promise)
    return promise
  }

  // 预加载样式
  preloadStyle(href: string): Promise<void> {
    if (this.cache.has(href)) {
      return this.cache.get(href)!
    }

    const promise = new Promise<void>((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.onload = () => resolve()
      link.onerror = reject
      link.href = href
      document.head.appendChild(link)
    })

    this.cache.set(href, promise)
    return promise
  }
}

// 内存管理
export class MemoryManager {
  private cleanupTasks: (() => void)[] = []

  // 添加清理任务
  addCleanupTask(task: () => void) {
    this.cleanupTasks.push(task)
  }

  // 执行清理
  cleanup() {
    this.cleanupTasks.forEach(task => {
      try {
        task()
      } catch (error) {
        console.error('Cleanup task failed:', error)
      }
    })
    this.cleanupTasks = []
  }

  // 监听内存压力（如果支持）
  watchMemoryPressure() {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      if (memory.usedJSHeapSize / memory.totalJSHeapSize > 0.8) {
        console.warn('High memory usage detected, consider cleanup')
        this.cleanup()
      }
    }
  }
}

// 网络状态监听
export class NetworkObserver {
  private callbacks: ((isOnline: boolean) => void)[] = []

  constructor() {
    this.init()
  }

  private init() {
    window.addEventListener('online', () => this.notify(true))
    window.addEventListener('offline', () => this.notify(false))
  }

  private notify(isOnline: boolean) {
    this.callbacks.forEach(callback => callback(isOnline))
  }

  // 订阅网络状态变化
  subscribe(callback: (isOnline: boolean) => void) {
    this.callbacks.push(callback)
    // 立即通知当前状态
    callback(navigator.onLine)
  }

  // 取消订阅
  unsubscribe(callback: (isOnline: boolean) => void) {
    const index = this.callbacks.indexOf(callback)
    if (index > -1) {
      this.callbacks.splice(index, 1)
    }
  }

  // 获取网络信息（如果支持）
  getNetworkInfo() {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt
      }
    }
    return null
  }
}

// 全局实例
export const imageLoader = new ImageLazyLoader()
export const resourcePreloader = new ResourcePreloader()
export const memoryManager = new MemoryManager()
export const networkObserver = new NetworkObserver()

// Vue指令：图片懒加载
export const vLazyImg = {
  mounted(el: HTMLImageElement, binding: any) {
    el.dataset.src = binding.value
    el.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNGNUY1RjUiLz48L3N2Zz4='
    imageLoader.observe(el)
  },
  unmounted(el: HTMLImageElement) {
    imageLoader.disconnect()
  }
}

// 页面可见性监听
export function onVisibilityChange(callback: (isVisible: boolean) => void) {
  const handleVisibilityChange = () => {
    callback(!document.hidden)
  }

  document.addEventListener('visibilitychange', handleVisibilityChange)

  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  }
}

// 性能指标收集
export class PerformanceCollector {
  private metrics = new Map<string, number>()

  // 记录时间指标
  mark(name: string) {
    this.metrics.set(name, performance.now())
  }

  // 计算耗时
  measure(name: string, startMark: string) {
    const startTime = this.metrics.get(startMark)
    if (startTime) {
      const duration = performance.now() - startTime
      console.log(`${name}: ${duration.toFixed(2)}ms`)
      return duration
    }
    return 0
  }

  // 收集核心网页指标
  collectWebVitals() {
    // FCP (First Contentful Paint)
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        console.log('FCP:', entry.startTime)
      }
    }).observe({ entryTypes: ['paint'] })

    // LCP (Largest Contentful Paint)
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries()
      const lastEntry = entries[entries.length - 1]
      console.log('LCP:', lastEntry.startTime)
    }).observe({ entryTypes: ['largest-contentful-paint'] })

    // FID (First Input Delay)
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        console.log('FID:', (entry as any).processingStart - entry.startTime)
      }
    }).observe({ entryTypes: ['first-input'] })
  }
}

export const performanceCollector = new PerformanceCollector() 