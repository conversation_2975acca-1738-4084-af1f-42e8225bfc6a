/**
 * 数学计算工具函数
 * 解决JavaScript浮点数运算精度丢失问题
 */

/**
 * 将数字转换为整数进行计算，避免浮点数精度问题
 * @param num 要转换的数字
 * @param precision 精度位数，默认2位小数
 * @returns 转换后的整数
 */
function toInteger(num: number, precision: number = 2): number {
  return Math.round(num * Math.pow(10, precision))
}

/**
 * 将整数转换回浮点数
 * @param integer 整数
 * @param precision 精度位数，默认2位小数
 * @returns 转换后的浮点数
 */
function fromInteger(integer: number, precision: number = 2): number {
  return integer / Math.pow(10, precision)
}

/**
 * 精确加法运算
 * @param a 第一个数
 * @param b 第二个数
 * @param precision 精度位数，默认2位小数
 * @returns 精确的加法结果
 */
export function add(a: number, b: number, precision: number = 2): number {
  const aInt = toInteger(a, precision)
  const bInt = toInteger(b, precision)
  return fromInteger(aInt + bInt, precision)
}

/**
 * 精确减法运算
 * @param a 被减数
 * @param b 减数
 * @param precision 精度位数，默认2位小数
 * @returns 精确的减法结果
 */
export function subtract(a: number, b: number, precision: number = 2): number {
  const aInt = toInteger(a, precision)
  const bInt = toInteger(b, precision)
  return fromInteger(aInt - bInt, precision)
}

/**
 * 精确乘法运算
 * @param a 第一个数
 * @param b 第二个数
 * @param precision 精度位数，默认2位小数
 * @returns 精确的乘法结果
 */
export function multiply(a: number, b: number, precision: number = 2): number {
  const aInt = toInteger(a, precision)
  const bInt = toInteger(b, precision)
  const result = aInt * bInt
  return fromInteger(result, precision * 2)
}

/**
 * 精确除法运算
 * @param a 被除数
 * @param b 除数
 * @param precision 精度位数，默认2位小数
 * @returns 精确的除法结果
 */
export function divide(a: number, b: number, precision: number = 2): number {
  if (b === 0) {
    throw new Error('除数不能为零')
  }
  const aInt = toInteger(a, precision)
  const bInt = toInteger(b, precision)
  const result = aInt / bInt
  return fromInteger(result, precision)
}

/**
 * 向下取整到指定精度
 * @param num 要处理的数字
 * @param precision 精度位数，默认2位小数
 * @returns 向下取整后的数字
 */
export function floor(num: number, precision: number = 2): number {
  const factor = Math.pow(10, precision)
  return Math.floor(num * factor) / factor
}

/**
 * 向上取整到指定精度
 * @param num 要处理的数字
 * @param precision 精度位数，默认2位小数
 * @returns 向上取整后的数字
 */
export function ceil(num: number, precision: number = 2): number {
  const factor = Math.pow(10, precision)
  return Math.ceil(num * factor) / factor
}

/**
 * 四舍五入到指定精度
 * @param num 要处理的数字
 * @param precision 精度位数，默认2位小数
 * @returns 四舍五入后的数字
 */
export function round(num: number, precision: number = 2): number {
  const factor = Math.pow(10, precision)
  return Math.round(num * factor) / factor
}

/**
 * 格式化数字为字符串，支持千分位分隔符
 * @param num 要格式化的数字
 * @param precision 精度位数，默认2位小数
 * @param locale 地区设置，默认'en-US'
 * @returns 格式化后的字符串
 */
export function formatNumber(
  num: number, 
  precision: number = 2, 
  locale: string = 'en-US',
  clacType: 'floor' | 'ceil' | 'round' | 'truncate' = 'truncate'
): string {
  if(clacType === 'floor'){
    num = floor(num, precision)
  }else if(clacType === 'ceil'){
    num = ceil(num, precision)
  }else if(clacType === 'round'){
    num = round(num, precision)
  }else if(clacType === 'truncate'){
    num = truncate(num, precision)
  }else{
    num = truncate(num, precision)
  }
  return num.toLocaleString(locale, {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  })
}

/**
 * 安全的数字转换，处理字符串和数字类型
 * @param value 要转换的值
 * @param defaultValue 默认值，当转换失败时返回
 * @returns 转换后的数字
 */
export function safeNumber(value: any, defaultValue: number = 0): number {
  if (typeof value === 'number') {
    return isNaN(value) ? defaultValue : value
  }
  if (typeof value === 'string') {
    const num = parseFloat(value.replace(/,/g, ''))
    return isNaN(num) ? defaultValue : num
  }
  return defaultValue
}

/**
 * 比较两个数字是否相等（考虑精度）
 * @param a 第一个数字
 * @param b 第二个数字
 * @param precision 精度位数，默认2位小数
 * @returns 是否相等
 */
export function isEqual(a: number, b: number, precision: number = 2): boolean {
  return Math.abs(a - b) < Math.pow(10, -precision)
}

/**
 * 检查数字是否为零（考虑精度）
 * @param num 要检查的数字
 * @param precision 精度位数，默认2位小数
 * @returns 是否为零
 */
export function isZero(num: number, precision: number = 2): boolean {
  return Math.abs(num) < Math.pow(10, -precision)
}

/**
 * 获取数字的小数位数
 * @param num 要检查的数字
 * @returns 小数位数
 */
export function getDecimalPlaces(num: number): number {
  const str = num.toString()
  const decimalIndex = str.indexOf('.')
  return decimalIndex === -1 ? 0 : str.length - decimalIndex - 1
}

/**
 * 限制数字的小数位数（截断，不四舍五入）
 * @param num 要处理的数字
 * @param precision 精度位数，默认2位小数
 * @returns 处理后的数字
 */
export function truncate(num: number, precision: number = 2): number {
  const factor = Math.pow(10, precision)
  // 根据符号决定是加还是减 一个极小值
  const adjusted = num + Math.sign(num) * Number.EPSILON
  return Math.trunc(adjusted * factor) / factor
}

/**
 * 计算百分比
 * @param value 当前值
 * @param total 总值
 * @param precision 精度位数，默认2位小数
 * @returns 百分比值
 */
export function percentage(value: number, total: number, precision: number = 2): number {
  if (total === 0) return 0
  return round((value / total) * 100, precision)
}

/**
 * 计算增长率
 * @param current 当前值
 * @param previous 之前的值
 * @param precision 精度位数，默认2位小数
 * @returns 增长率百分比
 */
export function growthRate(current: number, previous: number, precision: number = 2): number {
  if (previous === 0) return current > 0 ? 100 : 0
  return round(((current - previous) / previous) * 100, precision)
} 