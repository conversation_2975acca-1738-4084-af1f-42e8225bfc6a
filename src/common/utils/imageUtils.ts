/**
 * 图片工具函数
 */

/**
 * 根据加密货币代码获取对应的图片路径
 * @param cryptoCode 加密货币代码
 * @returns 图片路径
 */
export const getCryptoIcon = (cryptoCode: string): string => {
  // 直接返回远程 CDN 地址
  return `https://files.dingx.tech/icons/crypto/${cryptoCode.toUpperCase()}.png`;
};

/**
 * 根据货币代码获取对应的图片路径
 * @param currencyCode 货币代码
 * @returns 图片路径
 */
export const getCurrencyIcon = (currencyCode: string): string => {
  // 直接返回远程 CDN 地址
  return `https://files.dingx.tech/icons/currency/${currencyCode.toUpperCase()}.png`;
};

/**
 * 根据区域代码获取对应的图片路径
 * @param regionCode 区域代码
 * @returns 图片路径
 */
export const getRegionIcon = (regionCode: string): string => {
  // 直接返回远程 CDN 地址
  return `https://files.dingx.tech/icons/flags/${regionCode.toLowerCase()}.svg`;
};
