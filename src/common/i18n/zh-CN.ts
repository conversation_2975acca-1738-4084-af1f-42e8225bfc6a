export default {
  router: {
    login: '登录',
    register: '注册',
    forgotPassword: '忘记密码',
    kycRejected: '进件拒绝',
    dashboard: '首页',
    deposit: '充值',
    depositRecord: '充值记录',
    exchange: '兑换',
    exchangeResult: '兑换结果',
    withdrawal: '提现',
    withdrawalResult: '提现结果',
    tradeRecord: '交易流水',
    kyc: '进件',
    kycInitial: '未进件',
    setting: '设置',
    tradeFlow: '账户流水',
    globalAccount: '全球账户',
    gotoHome: '返回首页',
  },
  common: {
    upload: {
      limit: '最多上传{limit}份文件',
      format: '上传文件只能是 PDF、PNG、JPG 或 JPEG 格式!',
      size: '上传文件大小不能超过 {fileSize}MB!',
      clickOrDrag: '点击或拖拽进行上传',
      tips: '至多{limit}份；支持PNG、JPG、JPEG、PDF格式；每份不超过{fileSize}MB',
      failed: '上传失败',
      retry: '重试',
    },
    navigationBar: {
      welcome: '欢迎',
      accountInfo: '个人账户信息',
      logout: '退出登录',
    },
    loading: {
      loadingText: '正在努力加载中，请稍后...',
    },
    dialog: {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      confirmTitle: '确认',
      warningTitle: '警告',
      successTitle: '成功',
    },
    copySuccess: '复制成功',
    copyFailed: '复制失败',
    copyAllInfo: '复制所有信息',
    twoFactorAuth: {
      title: '请添加2FA验证！',
      message:
        '为保证您的资金安全，该操作需要绑定第三方验证器进行双因素验证，建议您提前进行绑定。<br/><br/>提示：若您选择暂时不添加，请在操作前至页面右上角"个人账户信息 - 账号安全信息"进行绑定。',
      confirmButtonText: '去添加',
      cancelButtonText: '取消',
      checkStatusFailed: '获取2FA状态失败',
      verificationFailed: '2FA验证检查失败',
    },
  },
  tips: {
    compliance: {
      title: '合规提示',
      content:
        '为保障您的资金安全与交易权益，请勿与任何被监管机构制裁的实体进行交易，否则将对您 Ding 账户的访问权限造成影响。',
    },
  },
};
