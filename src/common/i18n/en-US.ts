export default {
  router: {
    login: 'Login',
    register: 'Register',
    forgotPassword: 'Forgot Password',
    kycRejected: 'KYC Rejected',
    dashboard: 'Dashboard',
    deposit: 'Deposit',
    depositRecord: 'Deposit Record',
    exchange: 'Conversion',
    exchangeResult: 'Conversion Result',
    withdrawal: 'Withdrawal',
    withdrawalResult: 'Withdrawal Result',
    tradeRecord: 'Transaction Flow',
    kyc: 'KYC',
    kycInitial: 'KYC Initial',
    setting: 'Settings',
    tradeFlow: 'Account Flow',
    globalAccount: 'Global Account',
    gotoHome: 'Back',
  },
  common: {
    upload: {
      limit: 'You can upload up to {limit} files',
      format: 'Only PDF, PNG, JPG, or JPEG files are allowed!',
      size: 'File size must not exceed {fileSize}MB!',
      clickOrDrag: 'Click or drag to upload',
      tips: 'Up to {limit} files; supports PNG, JPG, JPEG, PDF; each file no more than {fileSize}MB',
      failed: 'Upload failed',
      retry: 'Retry',
    },

    navigationBar: {
      welcome: 'Welcome',
      accountInfo: 'Account Info',
      logout: 'Logout',
    },
    loading: {
      loadingText: 'Loading...',
    },
    dialog: {
      confirmButtonText: 'Confirm',
      cancelButtonText: 'Cancel',
      confirmTitle: 'Confirm',
      warningTitle: 'Warning',
      successTitle: 'Success',
    },
    copySuccess: 'Copy success',
    copyFailed: 'Copy failed',
    copyAllInfo: 'Copy all info',
    twoFactorAuth: {
      title: 'Please add 2FA verification!',
      message:
        'To ensure the security of your funds, this operation requires binding a third-party authenticator for two-factor authentication. We recommend you bind it in advance.<br/><br/>Tip: If you choose not to add it temporarily, please go to "Personal Account Information - Account Security Information" in the upper right corner of the page to bind it before operating.',
      confirmButtonText: 'Go to Add',
      cancelButtonText: 'Cancel',
      checkStatusFailed: 'Failed to get 2FA status',
      verificationFailed: '2FA verification check failed',
    },
  },
  tips: {
    compliance: {
      title: 'Compliance Notice',
      content:
        'To protect your fund security and trading rights, please do not trade with any entities sanctioned by regulatory authorities, otherwise it will affect your access to the Ding account.',
    },
  },
};
