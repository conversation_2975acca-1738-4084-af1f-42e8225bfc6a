import { createI18n } from 'vue-i18n'
import zhC<PERSON> from './zh-CN'
import zhHK from './zh-HK'
import enUS from './en-US'

const DEFAULT_LOCALE = 'zh-CN'

export enum LocaleType {
  zhCN = 'zh-CN',
  zhHK = 'zh-HK',
  enUS = 'en-US'
}

// 创建i18n实例
const i18n = createI18n({
  legacy: false,
  locale: DEFAULT_LOCALE,
  fallbackLocale: DEFAULT_LOCALE,
  messages: {
    'zh-CN': zhCN,
    'zh-HK': zhHK,
    'en-US': enUS
  }
})


// 导出全局 t 函数
export const t = i18n.global.t

// 动态导入语言包
export async function loadLocaleMessages(locale: string) {
  // 动态加载语言文件
  const modules = import.meta.glob('@/pages/**/i18n/*.ts')
  
  // 当前语言的所有消息
  let hasNewMessages = false
  
  // 加载并合并语言文件
  for (const path in modules) {
    if (path.endsWith(`${locale}.ts`)) {
      try {
        const mod: any = await modules[path]()
        
        // 合并到全局消息中
        i18n.global.mergeLocaleMessage(locale, mod.default)
        hasNewMessages = true
      } catch (error) {
        console.error(`Failed to load language file: ${path}`, error)
      }
    }
  }
  
  return hasNewMessages
}

export async function setLocale(locale: LocaleType) {
  // 设置当前语言
  i18n.global.locale.value = locale
  
  // 保存语言设置到本地存储
  localStorage.setItem('locale', locale)
  
  // 可选：动态加载语言包
  await loadLocaleMessages(locale)

  // 设置 Element Plus 的语言
  const elementLang = await import(
    locale.indexOf('en') !== -1
      ? 'element-plus/es/locale/lang/en'
      : locale.indexOf('hk') !== -1 || locale.indexOf('HK') !== -1
        ? 'element-plus/es/locale/lang/zh-hk'
        : 'element-plus/es/locale/lang/zh-cn'
  )
  return elementLang.default
}

export function getLocale() {
  return i18n.global.locale.value
}

export default i18n
