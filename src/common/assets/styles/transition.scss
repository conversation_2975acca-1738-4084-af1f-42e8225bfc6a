// https://cn.vuejs.org/guide/built-ins/transition

// fade-transform
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}
.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}
.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

// layout-logo-fade
.layout-logo-fade-enter-active,
.layout-logo-fade-leave-active {
  transition: opacity 1.5s;
}
.layout-logo-fade-enter-from,
.layout-logo-fade-leave-to {
  opacity: 0;
}

// 按钮悬停放大动画
.btn-hover-scale {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  cursor: pointer;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: scale(0.98);
  }
}

// 按钮悬停轻微放大动画（更温和的效果）
.btn-hover-scale-sm {
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  cursor: pointer;
  
  &:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: scale(0.99);
  }
}

// 按钮悬停明显放大动画（更明显的效果）
.btn-hover-scale-lg {
  transition: transform 0.25s ease-in-out, box-shadow 0.25s ease-in-out;
  cursor: pointer;
  
  &:hover {
    transform: scale(1.08);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

// 按钮悬停弹跳效果
.btn-hover-bounce {
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  cursor: pointer;
  
  &:hover {
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.98);
  }
}
