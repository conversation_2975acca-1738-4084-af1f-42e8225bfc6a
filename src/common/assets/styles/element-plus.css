/**
 * @description dark-blue 主题模式下的 Element Plus CSS 变量
 * @description 在此查阅所有可自定义的变量：https://github.com/element-plus/element-plus/blob/dev/packages/theme-chalk/src/common/var.scss
 * @description 也可以打开浏览器控制台选择元素，查看要覆盖的变量名
 */

/* 基础颜色 */
html.dark-blue {
  /* color-primary */
  --el-color-primary: #00bb99;
  --el-color-primary-light-3: #00bb99b3;
  --el-color-primary-light-5: #00bb9980;
  --el-color-primary-light-7: #00bb994d;
  --el-color-primary-light-8: #00bb9933;
  --el-color-primary-light-9: #00bb991a;
  --el-color-primary-dark-2: #00bb99;
  /* color-success */
  --el-color-success: #67c23a;
  --el-color-success-light-3: #67c23ab3;
  --el-color-success-light-5: #67c23a80;
  --el-color-success-light-7: #67c23a4d;
  --el-color-success-light-8: #67c23a33;
  --el-color-success-light-9: #67c23a1a;
  --el-color-success-dark-2: #67c23a;
  /* color-warning */
  --el-color-warning: #e6a23c;
  --el-color-warning-light-3: #e6a23cb3;
  --el-color-warning-light-5: #e6a23c80;
  --el-color-warning-light-7: #e6a23c4d;
  --el-color-warning-light-8: #e6a23c33;
  --el-color-warning-light-9: #e6a23c1a;
  --el-color-warning-dark-2: #e6a23c;
  /* color-danger */
  --el-color-danger: #FD3627FF;
  --el-color-danger-light-3: #f56c6cb3;
  --el-color-danger-light-5: #f56c6c80;
  --el-color-danger-light-7: #f56c6c4d;
  --el-color-danger-light-8: #f56c6c33;
  --el-color-danger-light-9: #f56c6c1a;
  --el-color-danger-dark-2: #f56c6c;
  /* color-error */
  --el-color-error: #f56c6c;
  --el-color-error-light-3: #f56c6cb3;
  --el-color-error-light-5: #f56c6c80;
  --el-color-error-light-7: #f56c6c4d;
  --el-color-error-light-8: #f56c6c33;
  --el-color-error-light-9: #f56c6c1a;
  --el-color-error-dark-2: #f56c6c;
  /* color-info */
  --el-color-info: #909399;
  --el-color-info-light-3: #909399b3;
  --el-color-info-light-5: #90939980;
  --el-color-info-light-7: #9093994d;
  --el-color-info-light-8: #90939933;
  --el-color-info-light-9: #9093991a;
  --el-color-info-dark-2: #909399;
  /* text-color */
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  --el-text-color-placeholder: #8d9095;
  --el-text-color-disabled: #6c6e72;
  /* border-color */
  --el-border-color-darker: #003380;
  --el-border-color-dark: #003380;
  --el-border-color: #003380;
  --el-border-color-light: #003380;
  --el-border-color-lighter: #003380;
  --el-border-color-extra-light: #003380;
  /* fill-color */
  --el-fill-color-darker: #002b6b;
  --el-fill-color-dark: #002b6b;
  --el-fill-color: #002b6b;
  --el-fill-color-light: #002359;
  --el-fill-color-lighter: #002359;
  --el-fill-color-blank: #001b44;
  --el-fill-color-extra-light: #001b44;
  /* bg-color */
  --el-bg-color-page: #001535;
  --el-bg-color: #001b44;
  --el-bg-color-overlay: #002359;
  /* mask-color */
  --el-mask-color: rgba(0, 0, 0, 0.5);
  --el-mask-color-extra-light: rgba(0, 0, 0, 0.3);
}

/* button */
html.dark-blue .el-button {
  --el-button-disabled-text-color: rgba(255, 255, 255, 0.5);
}
