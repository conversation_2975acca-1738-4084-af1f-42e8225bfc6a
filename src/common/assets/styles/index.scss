// 全局 CSS 变量
@import './variables.css';
// Transition
@import './transition.scss';
// Element Plus
@import './element-plus.css';
@import './element-plus.scss';
// Vxe Table
@import './vxe-table.css';
@import './vxe-table.scss';
// 注册多主题
@import './theme/register.scss';
// Mixins
@import './mixins.scss';
// View Transition
@import './view-transition.scss';

@font-face {
  font-family: 'ftdin'; /* 自定义字体名称 */
  src: url('@@/assets/fonts/FTDIN-Regular.OTF') format('opentype'); /* 路径和格式声明 */
  font-weight: normal; /* 默认字重 */
  font-style: normal; /* 默认样式 */
  font-display: swap; /* 避免文字闪烁（可选） */
}

// 业务页面几乎都应该在根元素上挂载 class="app-container"，以保持页面美观
.app-container {
  padding: 32px 40px;
}

html {
  height: 100%;
  // 灰色模式
  &.grey-mode {
    filter: grayscale(1);
  }
  // 色弱模式
  &.color-weakness {
    filter: invert(0.8);
  }
}

body {
  height: 100%;
  color: var(--v3-body-text-color);
  background-color: var(--v3-body-bg-color);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden;
  font-family:
    'PingFang SC', Inter, 'Helvetica Neue', Helvetica, 'Hiragino Sans GB', 'Microsoft YaHei',
    '微软雅黑', Arial, sans-serif;
  @extend %scrollbar;
}

#app {
  height: 100%;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

a,
a:focus,
a:hover {
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.app-wrapper {
  .el-button--primary {
    --el-button-bg-color: #030814;
    --el-button-border-color: #030814;
    --el-button-hover-bg-color: #030814;
    --el-button-hover-border-color: #030814;
    --el-button-disabled-bg-color: #d2d2d2;
    --el-button-disabled-border-color: #d2d2d2;
    --el-border-radius-base: 6px;
    --el-button-active-color: #030814;
    --el-button-active-bg-color: #030814;
  }

  .el-button--info {
    --el-button-bg-color: #fff;
    --el-button-hover-bg-color: #fff;
    --el-button-text-color: #222527;
    --el-button-hover-text-color: #222527;
  }

  .el-form {
    --el-border-radius-base: 6px;

    .el-form-item__error {
      line-height: 16px;
    }

    .el-form-item__label {
      color: #222527;
      margin-bottom: 6px;
    }
  }

  .el-switch {
    --el-switch-on-color: #ff0064;
    --el-switch-off-color: #f5f5f5;
  }

  .date-picker-with-prefix {
    border-radius: 6px !important;
  }

  .el-checkbox {
    --el-color-primary: #ff0064;
  }

  .el-button.is-text:not(.is-disabled):hover {
    background-color: transparent;
  }

  // 按钮悬停动画效果 - 确保禁用状态下不生效
  .el-button {
    &.btn-hover-scale,
    &.btn-hover-scale-sm,
    &.btn-hover-scale-lg,
    &.btn-hover-bounce {
      &.is-disabled {
        &:hover {
          transform: none !important;
          box-shadow: none !important;
        }
      }
    }
  }

  .el-input-group__append,
  .el-input-group__prepend {
    background: #fff !important;
  }

  .drawer-header {
    border-bottom: 1px solid #e5e6eb;
    margin-bottom: 0;
  }
}

.el-dropdown__popper {
  --el-dropdown-menuItem-hover-fill: #f5f5f5 !important;
  --el-dropdown-menuItem-hover-color: #464b4e !important;
}

:root {
  .el-input {
    --el-input-focus-border: #030814;
    --el-input-focus-border-color: #030814;
    --el-input-text-color: #222527;
  }

  .el-select__wrapper {
    --el-color-primary: #030814;
  }

  .el-select-dropdown__item.is-selected {
    --el-color-primary: #ff0064;
  }

  .el-date-range-picker {
    --el-datepicker-active-color: #ff0064;
  }

  .el-popper {
    --el-popper-border-radius: 6px !important;
    border: none !important;
  }

  .el-pager {
    --el-pagination-hover-color: #030814;
    .is-active {
      background: #f5f5f5;
      border-radius: 4px;
    }
  }
}

/*在Chrome下移除input[number]的上下箭头*/
.no-number::-webkit-outer-spin-button,
.no-number::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none !important;
}

.no-number input[type='number']::-webkit-outer-spin-button,
.no-number input[type='number']::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none !important;
}

/*在firefox下移除input[number]的上下箭头*/
.no-number {
  -moz-appearance: textfield;
}

.no-number input[type='number'] {
  -moz-appearance: textfield;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
