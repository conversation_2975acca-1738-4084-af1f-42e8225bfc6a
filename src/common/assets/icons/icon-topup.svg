<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>置顶</title>
    <g id="迭代" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="03-06/kyc-附加信息" transform="translate(-1374, -827)" fill="#030814">
            <g id="置顶" transform="translate(1374, 827)">
                <path d="M0,0 L16,0 L16,16 L0,16 L0,0 Z" id="路径" opacity="0"></path>
                <path d="M2,0.666666667 L14,0.666666667 C14.3681898,0.666666667 14.6666667,0.9651435 14.6666667,1.33333333 C14.6666667,1.70152317 14.3681898,2 14,2 L2,2 C1.63181017,2 1.33333333,1.70152317 1.33333333,1.33333333 C1.33333333,0.9651435 1.63181017,0.666666667 2,0.666666667 Z M6.8236566,3.36452426 C7.52495763,2.85448715 8.47504237,2.85448715 9.1763434,3.36452426 L14.0674397,6.92168522 C14.6629765,7.3548029 14.7946432,8.18869212 14.3615256,8.78422893 C14.110622,9.12922128 13.7097929,9.33333333 13.2832108,9.33333333 L12,9.33333333 L12,13.3333333 C12,14.3984539 11.1673867,15.2691073 10.1175152,15.3299382 L10,15.3333333 L6,15.3333333 C4.8954305,15.3333333 4,14.4379028 4,13.3333333 L4,9.33333333 L2.71678921,9.33333333 C2.01388135,9.33333333 1.43801246,8.78941481 1.38711303,8.09950823 L1.38345588,8 C1.38345588,7.57341789 1.58756793,7.17258875 1.93256028,6.92168522 L6.8236566,3.36452426 Z M8.39211447,4.44283904 C8.15834746,4.27282667 7.84165254,4.27282667 7.60788553,4.44283904 L2.71678921,8 L4.66666667,8 C5.0348565,8 5.33333333,8.29847683 5.33333333,8.66666667 L5.33333333,13.3333333 C5.33333333,13.7015232 5.63181017,14 6,14 L10,14 C10.3681898,14 10.6666667,13.7015232 10.6666667,13.3333333 L10.6666667,8.66666667 C10.6666667,8.29847683 10.9651435,8 11.3333333,8 L13.2832108,8 L8.39211447,4.44283904 Z" id="形状结合"></path>
            </g>
        </g>
    </g>
</svg>