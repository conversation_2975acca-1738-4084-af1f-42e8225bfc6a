import type { RouteRecordRaw } from "vue-router"
import { pinia } from "@/pinia"
import { constantRoutes, dynamicRoutes as orginDynamicRoutes, router as rootRouter } from "@/router"
import { routerConfig } from "@/router/config"
import { flatMultiLevelRoutes } from "@/router/helper"

function hasPermission(roles: string[], route: RouteRecordRaw) {
  const routeRoles = route.meta?.roles
  return routeRoles ? roles.some(role => routeRoles.includes(role)) : true
}

function filterDynamicRoutes(routes: RouteRecordRaw[], roles: string[]) {
  const res: RouteRecordRaw[] = []
  routes.forEach((route) => {
    const tempRoute = { ...route }
    if (hasPermission(roles, tempRoute)) {
      if (tempRoute.children) {
        tempRoute.children = filterDynamicRoutes(tempRoute.children, roles)
      }
      res.push(tempRoute)
    }
  })
  return res
}

export const usePermissionStore = defineStore("permission", () => {
  const routes = ref<RouteRecordRaw[]>(constantRoutes)
  const dynamicRoutes = ref<RouteRecordRaw[]>([]) // 添加dynamicRoutes属性

  // 设置动态路由
  const setDynamicRoutes = (newRoutes: RouteRecordRaw[]) => {
    dynamicRoutes.value = newRoutes
  }

  // 合并路由
  const setRoutes = () => {
    routes.value = [...constantRoutes, ...dynamicRoutes.value]
  }

  // 初始化所有路由
  const setAllRoutes = () => {
    setDynamicRoutes(orginDynamicRoutes)
    setRoutes()

    // 添加路由到router实例
    dynamicRoutes.value.forEach(route => {
      if (route.name && !rootRouter.hasRoute(route.name)){
        rootRouter.addRoute(route)
      }
    })
  }

  return { routes, dynamicRoutes, setRoutes, setAllRoutes, setDynamicRoutes }
})


/**
 * @description 在 SPA 应用中可用于在 pinia 实例被激活前使用 store
 * @description 在 SSR 应用中可用于在 setup 外使用 store
 */
export function usePermissionStoreOutside() {
  return usePermissionStore(pinia)
}
