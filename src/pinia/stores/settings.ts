import type { Ref } from "vue"
import { pinia } from "@/pinia"

type SettingsStore = {
  
}

type SettingsStoreKey = keyof SettingsStore

export const useSettingsStore = defineStore("settings", () => {
  // 状态对象
  const state = {} as SettingsStore

  return state
})

/**
 * @description 在 SPA 应用中可用于在 pinia 实例被激活前使用 store
 * @description 在 SSR 应用中可用于在 setup 外使用 store
 */
export function useSettingsStoreOutside() {
  return useSettingsStore(pinia)
}
