import { pinia } from '@/pinia';
import { resetRouter } from '@/router';
import { routerConfig } from '@/router/config';
import { setToken as _setToken, getToken, removeToken } from '@@/utils/cache/cookies';
import { useSettingsStore } from './settings';
import { getCurrentUserApi } from '@@/apis/users';
import { router } from '@/router';
export const useUserStore = defineStore('user', () => {
  const token = ref<string>(getToken() || '');
  const roles = ref<string[]>([]);
  const nickName = ref<string>('');
  const kycStatus = ref<string>('');
  const email = ref<string>('');

  // const tagsViewStore = useTagsViewStore()
  const settingsStore = useSettingsStore();

  // 设置 Token
  const setToken = (value: string) => {
    _setToken(value);
    token.value = value;
  };

  // 获取用户详情
  const getInfo = async () => {
    const { data } = await getCurrentUserApi();
    nickName.value = data.nickName;
    kycStatus.value = data.kycStatus;
    email.value = data.email;
  };

  // 模拟角色变化
  const changeRoles = (role: string) => {
    const newToken = `token-${role}`;
    token.value = newToken;
    _setToken(newToken);
    // 用刷新页面代替重新登录
    location.reload();
  };

  // 登出
  const logout = () => {
    token.value = '';
    roles.value = [];
    kycStatus.value = '';
    nickName.value = '';
    email.value = '';
    removeToken();
    resetRouter();
  };

  // 重置 Token
  const resetToken = () => {
    removeToken();
    token.value = '';
    roles.value = [];
  };

  return {
    token,
    roles,
    nickName,
    kycStatus,
    email,
    setToken,
    getInfo,
    changeRoles,
    logout,
    resetToken,
  };
});

/**
 * @description 在 SPA 应用中可用于在 pinia 实例被激活前使用 store
 * @description 在 SSR 应用中可用于在 setup 外使用 store
 */
export function useUserStoreOutside() {
  return useUserStore(pinia);
}
