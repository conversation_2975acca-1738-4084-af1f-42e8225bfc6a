import { pinia } from "@/pinia"
import { DeviceEnum, SIDEBAR_CLOSED, SIDEBAR_OPENED } from "@@/constants/app-key"

interface Sidebar {
  opened: string
  withoutAnimation: boolean
}

export const useAppStore = defineStore("app", () => {
  // 侧边栏状态
  const sidebar: Sidebar = reactive({
    opened: SIDEBAR_OPENED,
    withoutAnimation: false
  })

  // 设备类型
  const device = ref<DeviceEnum>(DeviceEnum.Desktop)

  // 监听侧边栏 opened 状态
  watch(
    () => sidebar.opened,
    (opened) => {
      
    }
  )

  // 切换侧边栏
  const toggleSidebar = (withoutAnimation: boolean) => {
    sidebar.opened = sidebar.opened === SIDEBAR_OPENED ? SIDEBAR_CLOSED : SIDEBAR_OPENED
    sidebar.withoutAnimation = withoutAnimation
  }

  // 关闭侧边栏
  const closeSidebar = (withoutAnimation: boolean) => {
    sidebar.opened = SIDEBAR_CLOSED
    sidebar.withoutAnimation = withoutAnimation
  }
  
  return { device, sidebar, toggleSidebar, closeSidebar }
})

/**
 * @description 在 SPA 应用中可用于在 pinia 实例被激活前使用 store
 * @description 在 SSR 应用中可用于在 setup 外使用 store
 */
export function useAppStoreOutside() {
  return useAppStore(pinia)
}
