// stores/useEnumStore.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { businessEnumApi } from '@@/apis/common'
import { BusinessEnumType } from '@@/apis/common/type';
import { LocaleType } from '@/common/i18n'
import i18n from '@/common/i18n'

export type EnumItem = {
  enumCode: string
  parentEnumCode: string
  enumDescCn: string
  enumDescHk: string
  enumDescEn: string
  extendField?: string
  children?: Array<EnumItem>
}

export const useEnumStore = defineStore('enum', () => {
  const enumList = ref<Map<string, EnumItem[]>>(new Map())
  const error = ref<Error | null>(null)

  const fetchEnumList = async (businessType: BusinessEnumType) => {
    try { 
      if (enumList.value.has(businessType)) return;
      const res:any = await businessEnumApi({
        businessTypes: [businessType],
      })
      enumList.value.set(businessType, res.data[businessType] || []);
    } catch (err) {
      error.value = err as Error
    }
  }

  const getEnumList = (businessType: BusinessEnumType) => {
    fetchEnumList(businessType)
    return computed(() => {
      const locale = i18n.global.locale.value
      const descKey =
        {
          'zh-CN': 'enumDescCn',
          'zh-HK': 'enumDescHk',
          'en-US': 'enumDescEn'
        }[locale as LocaleType] || 'enumDescCn'
      const mapList = (items: EnumItem[]): EnumItem[] =>
        items.map((item: EnumItem) => ({
          ...item,
          enumDescCn: item[descKey as keyof EnumItem] as string,
          children: item.children ? mapList(item.children) : undefined
        }))
      return mapList(enumList.value.get(businessType) || [])
    })
  }

  return { enumList, error, getEnumList, fetchEnumList }
})