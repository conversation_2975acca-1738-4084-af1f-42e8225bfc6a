// stores/digitCurrencyStore.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { businessEnumApi } from '@@/apis/common'
import { BusinessEnumType } from '@@/apis/common/type';

export const useDigitCurrencyStore = defineStore('digitCurrency', () => {
  const currencies = ref<any[]>([])
  const error = ref<Error | null>(null)
  const initialized = ref(false) // 新增初始化标志

  const fetchCurrencies = async () => {
    try { 
      if (currencies.value.length > 0 || initialized.value) return;
      const res:any = await businessEnumApi({
        businessTypes: [BusinessEnumType.CURRENCY],
      })
      currencies.value = res.data[BusinessEnumType.CURRENCY] || [];
      initialized.value = true
    } catch (err) {
      error.value = err as Error
    }
  }

   // 计算属性：访问时自动确保数据已加载
   const currenciesWithAutoLoad = computed({
    get: () => {
      if (!initialized.value) {
        fetchCurrencies()
      }
      return currencies.value
    },
    set: (val) => {
      currencies.value = val
    }
  })

  const digitCurrenciesData = computed(() => currenciesWithAutoLoad.value)

  return { currencies:currenciesWithAutoLoad, error,digitCurrenciesData, fetchCurrencies }
})