<script lang="ts" setup>
import { useI18n } from "vue-i18n"
import { computed } from "vue"
import en from "element-plus/es/locale/lang/en"
import zhCn from "element-plus/es/locale/lang/zh-cn"
import zhHk from "element-plus/es/locale/lang/zh-hk"

const i18n = useI18n()
const locale = computed(() => {
  // 根据当前语言返回对应的 Element Plus 本地化配置
  switch (i18n.locale.value) {
    case "zh-CN":
      return zhCn
    case "en-US":
      return en
    case "zh-HK":
      return zhHk
    default:
      return zhCn
  }
})
</script>

<template>
  <el-config-provider :locale="locale">
    <router-view />
  </el-config-provider>
</template>
