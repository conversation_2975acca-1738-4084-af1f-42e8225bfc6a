/// <reference types="vitest/config" />

import { resolve } from "node:path"
import vue from "@vitejs/plugin-vue"
import vueJsx from "@vitejs/plugin-vue-jsx"
import UnoCSS from "unocss/vite"
import AutoImport from "unplugin-auto-import/vite"
import { ElementPlusResolver } from "unplugin-vue-components/resolvers"
import Components from "unplugin-vue-components/vite"
import { defineConfig, loadEnv } from "vite"
import svgComponent from "unplugin-svg-component/vite"

// Configuring Vite: https://cn.vite.dev/config
export default defineConfig(({ mode }) => {
  const { VITE_PUBLIC_PATH } = loadEnv(mode, process.cwd(), "")
  const isProduction = mode === 'production'
  
  return {
    // 开发或打包构建时用到的公共基础路径
    base: VITE_PUBLIC_PATH,
    resolve: {
      alias: {
        // @ 符号指向 src 目录
        "@": resolve(__dirname, "src"),
        // @@ 符号指向 src/common 通用目录
        "@@": resolve(__dirname, "src/common")
      }
    },
    // 开发环境服务器配置
    server: {
      // 是否监听所有地址
      host: true,
      // 端口号
      port: 30816,
      // 端口被占用时，是否直接退出
      strictPort: false,
      // 是否自动打开浏览器
      open: true,
      // 反向代理
      proxy: {
        "/ding-customer-ser": {
          target: "http://**************:30809",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/ding-customer-ser/, ''),
          // 打印实际请求地址
          configure: (proxy, options) => {
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('[Proxy] Actual Request URL:', 
                `${options.target}${req.url}`) // 打印真实请求地址
            })
          }
        }
      },
      // 是否允许跨域
      cors: true,
      // 预热常用文件，提高初始页面加载速度
      warmup: {
        clientFiles: [
          "./src/layouts/**/*.*",
          "./src/pinia/**/*.*",
          "./src/router/**/*.*"
        ]
      }
    },
    // 构建配置
    build: {
      target: 'es2022',
      // 启用CSS代码分割
      cssCodeSplit: true,
      // 启用源码映射（生产环境建议关闭）
      sourcemap: !isProduction,
      // 启用压缩
      minify: isProduction ? 'esbuild' : false,
      // 自定义底层的 Rollup 打包配置
      rollupOptions: {
        output: {
          /**
           * @name 优化的分块策略
           * @description 更细粒度的代码分割，提高缓存命中率
           */
          manualChunks: {
            // Vue 核心包
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            // Element Plus 核心
            'element-vendor': ['element-plus'],
            // Element Plus 图标
            'element-icons': ['@element-plus/icons-vue'],
            // 表格组件（较大）
            'table-vendor': ['vxe-table'],
            // 工具库
            'utils-vendor': ['lodash-es', 'dayjs', 'axios'],
            // 国际化
            'i18n-vendor': ['vue-i18n'],
            // 加密和工具
            'crypto-vendor': ['crypto-js', 'js-cookie'],
            // 媒体和图表
            'media-vendor': ['lottie-web', 'qrcode.vue'],
            // CSS框架
            'css-vendor': ['normalize.css']
          },
          // 为静态资源添加哈希
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.')
            let extType = info[info.length - 1]
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
              extType = 'media'
            } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name)) {
              extType = 'img'
            } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
              extType = 'fonts'
            }
            return `assets/${extType}/[name].[hash:8][extname]`
          },
          // 为JS文件添加哈希
          chunkFileNames: 'assets/js/[name].[hash:8].js',
          entryFileNames: 'assets/js/[name].[hash:8].js'
        },
        // 忽略某些警告
        onwarn(warning, warn) {
          // 忽略循环依赖警告（常见于某些第三方库）
          if (warning.code === 'CIRCULAR_DEPENDENCY') return
          warn(warning)
        }
      },
      // 调整 chunk 大小限制
      chunkSizeWarningLimit: 1000,
      // 构建性能优化
      reportCompressedSize: false,
      // CommonJS 选项
      commonjsOptions: {
        include: [/node_modules/],
        transformMixedEsModules: true
      },
      // 启用预加载
      assetsInlineLimit: 4096
    },
    // 依赖优化
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'element-plus/es',
        'vue-i18n'
      ],
      exclude: ['@iconify/icons-ep']
    },
    // 混淆器
    esbuild: mode === "development" ? undefined : {
      // 打包构建时移除 console.log
      pure: ["console.log"],
      // 打包构建时移除 debugger
      drop: ["debugger"],
      // 打包构建时移除所有注释
      legalComments: "none",
      // 压缩选项
      minifyIdentifiers: true,
      minifySyntax: true,
      minifyWhitespace: true
    },
    // 插件配置
    plugins: [
      vue(),
      // 支持 JSX、TSX 语法
      vueJsx(),
      // 支持将 SVG 文件导入为 Vue 组件
      svgComponent({
        iconDir: resolve(__dirname, "src/common/assets/icons"),
        dts: true,
        dtsDir: resolve(__dirname, "types/auto"),
        componentName: "SvgIcon",
        symbolId: "icon-[dir]-[name]",
        vueVersion: 3,
        // 生产环境优化SVG
        svgo: isProduction
      }),
      // 原子化 CSS
      UnoCSS(),
      // 自动按需导入 API
      AutoImport({
        imports: ["vue", "vue-router", "pinia"],
        dts: "types/auto/auto-imports.d.ts",
        resolvers: [ElementPlusResolver()],
        // 自动导入指令
        // vueTemplate: true
      }),
      // 自动按需导入组件
      Components({
        dts: "types/auto/components.d.ts",
        resolvers: [ElementPlusResolver()],
        // 导入指令
        directives: true
      })
    ],
    // Configuring Vitest: https://cn.vitest.dev/config
    test: {
      include: ["tests/**/*.test.{ts,js}"],
      environment: "happy-dom",
      server: {
        deps: {
          inline: ["element-plus"]
        }
      }
    }
  }
})

